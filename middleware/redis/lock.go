package redis

import (
	"context"
	"time"

	"micro-service/initialize/redis"

	"github.com/spf13/cast"
)

// Lock 尝试获取锁
// flag为进行加锁进程的唯一标识
func Lock(key string, timeOut time.Duration, flag ...string) bool {
	var value = "0"
	if len(flag) > 0 {
		value = flag[0]
	}

	lock, err := redis.GetInstance().SetNX(context.TODO(), key, value, timeOut).Result()
	if err != nil {
		return false
	}
	return lock
}

// UnLock 释放共享锁
//
// flag不为空时先判断是否自己的锁，避免锁自动释放后提前释放其他进程的锁
func UnLock(key string, flag ...string) bool {
	if len(flag) == 0 {
		return DelKey(context.TODO(), key) != nil
	}
	// lua 脚本
	c := `if redis.call("GET",KEYS[1]) == ARGV[1]
then
    return redis.call("DEL",KEYS[1])
else
    return 0
end`
	r, err := redis.GetInstance().Eval(context.TODO(), c, []string{key}, flag[0]).Result()
	if err != nil {
		return false
	}
	return cast.ToInt(r) == 1
}
