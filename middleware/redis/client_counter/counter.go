package client_counter

import (
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/now"

	redislib "micro-service/middleware/redis"
	"micro-service/pkg/utils"
)

type ClientCounterModel interface {
	Counter(ctx context.Context, key string) error
	Get(ctx context.Context, key string) (map[string]int64, error)
	Keys(ctx context.Context) ([]string, error)
	Set(ctx context.Context, key, hkey string) error
}

type defaultClientCounterModel struct {
	*redis.Client
}

func NewClientCounterModel(clients ...*redis.Client) ClientCounterModel {
	return &defaultClientCounterModel{
		Client: redislib.GetClient(clients...),
	}
}

func keyPrefix(month string) string {
	return "req_counter:" + month + ":client_id:"
}

// GenKey req_counter:2023-01:client_id:12
func GenKey(clientId string) string {
	month := time.Now().Format("2006-01")
	return keyPrefix(month) + clientId
}

// GetClientId return clientId from key
func GetClientId(key string) string {
	month := time.Now().Format("2006-01")
	prefix := keyPrefix(month)
	if !strings.HasPrefix(key, prefix) {
		return ""
	}
	return key[len(prefix):]
}

func (d *defaultClientCounterModel) Counter(ctx context.Context, key string) error {
	err := d.Client.Incr(ctx, key).Err()

	// set expired duration for counter key
	d.Client.Expire(ctx, key, 35*utils.Day)
	return err
}

func (d *defaultClientCounterModel) Get(ctx context.Context, keys string) (map[string]int64, error) {
	ms, err := d.Client.HGetAll(ctx, keys).Result()
	if err != nil {
		return nil, err
	}
	var m = make(map[string]int64, len(ms))
	for k, v := range ms {
		i, _ := strconv.Atoi(v)
		m[k] = int64(i)
	}
	return m, nil
}

func (d *defaultClientCounterModel) Keys(ctx context.Context) ([]string, error) {
	prefix := keyPrefix(time.Now().Format("2006-01"))

	var cursor uint64
	var keys []string
	for {
		var scanKeys []string
		var err error
		scanKeys, cursor, err = d.Client.Scan(ctx, cursor, prefix+"*", 50).Result()
		if err != nil {
			return nil, err
		}
		keys = append(keys, scanKeys...)
		if cursor == 0 {
			break
		}
	}
	return keys, nil
}

func (d *defaultClientCounterModel) Set(ctx context.Context, key, hkey string) error {
	err := d.Client.HIncrBy(ctx, key, hkey, 1).Err()
	if err != nil {
		return err
	}

	expireAt := now.BeginningOfMonth().Add(33 * utils.Day)
	err = d.Client.ExpireAt(ctx, key, expireAt).Err()
	if err != nil {
		return err
	}
	return nil
}
