package system_detect

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	rediscfg "micro-service/initialize/redis"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
)

func Init() {
	cfg.InitLoadCfg()
	_ = rediscfg.GetInstance(cfg.LoadRedis())
}

var ctx = context.Background()

func TestProgress_SetValue(t *testing.T) {
	Init()

	key := utils.RandString(10)
	client := NewBusinessSystemModel()
	err := client.SetValue(ctx, key, "my redis", 30*time.Second)
	assert.Nil(t, err)
}

func TestProgress_GetValue(t *testing.T) {
	Init()

	key := utils.RandString(50)
	client := NewBusinessSystemModel()

	_, err := client.GetValue(ctx, key)
	assert.Equal(t, err, redis.Nil)

	value := strings.Repeat(key, 5)
	err = client.SetValue(ctx, key, value, time.Minute)
	assert.Nil(t, err)

	s, err := client.GetValue(ctx, key)
	assert.Nil(t, err)
	assert.Equal(t, value, s)
}
