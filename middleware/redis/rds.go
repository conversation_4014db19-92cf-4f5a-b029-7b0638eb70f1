package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/initialize/redis"
	"sync"
	"time"

	"micro-service/pkg/cfg"

	goRedis "github.com/go-redis/redis/v8"
)

const Nil = goRedis.Nil

var rdsList sync.Map

func GetClientByDB(db int) *goRedis.Client {
	if db == 0 {
		return redis.GetInstance()
	} else if rdsCli, ok := rdsList.Load(db); ok {
		return rdsCli.(*goRedis.Client)
	} else {
		conf := cfg.LoadRedis()
		rdsCliDb := goRedis.NewClient(&goRedis.Options{
			Addr:        fmt.Sprintf("%s:%d", conf.Address, conf.Port), // redis服务ip:port
			Password:    conf.Password,                                 // redis的认证密码
			DB:          db,                                            // 连接的database库
			IdleTimeout: 150,                                           // 默认Idle超时时间
			PoolSize:    3,                                             // 连接池
		})
		if _, err := rdsCliDb.Ping(context.Background()).Result(); err != nil {
			panic(err)
		}
		rdsList.Store(db, rdsCliDb)
		return rdsCliDb
	}
	return nil
}

func GetClient(clients ...*goRedis.Client) *goRedis.Client {
	if len(clients) > 0 {
		return clients[0]
	}
	return redis.GetInstance()
}

func GetCache(key string, obj interface{}) bool {
	err := Get(context.Background(), key, obj)
	return err == nil
}

func SetCache(key string, ttl time.Duration, obj interface{}) bool {
	if jsonBytes, err := json.Marshal(obj); err == nil {
		err := redis.GetInstance().Set(context.Background(), key, jsonBytes, ttl).Err()
		return err == nil
	} else {
		err := redis.GetInstance().Set(context.Background(), key, obj, ttl).Err()
		return err == nil
	}
}

func Get(ctx context.Context, key string, obj interface{}) error {
	redisVal, err := redis.GetInstance().Get(ctx, key).Result()
	if err != nil {
		return err
	}
	if redisVal == "" {
		return nil
	}
	if err = json.Unmarshal([]byte(redisVal), obj); err != nil {
		return err
	}
	return nil
}

func Set(ctx context.Context, key string, val any, ttl time.Duration) error {
	return GetClient().Set(ctx, key, val, ttl).Err()
}

func DelKey(ctx context.Context, key ...string) error {
	if len(key) == 0 {
		return nil
	}
	return redis.GetInstance().Del(ctx, key...).Err()
}
