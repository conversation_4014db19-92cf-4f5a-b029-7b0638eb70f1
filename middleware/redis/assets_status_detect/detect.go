package assets_status_detect

import (
	"context"
	"encoding/json"
	"fmt"
	goRedis "github.com/go-redis/redis/v8"
	"micro-service/initialize/redis"
	"strconv"
	"time"
)

const keyFormat = "assets_status_detect:status:%d"

type (
	AssetsStatusDetectCache interface {
		GetCache(string) (StatusDetect, error)
		SetCache(key string, TaskID uint, Progress float64, Status int, TaskType string) error
	}

	defaultAssetsStatusDetectCache struct {
		*goRedis.Client
	}

	StatusDetect struct {
		Code    int    `json:"status,omitempty"`
		Message string `json:"message,omitempty"`
		Cmd     string `json:"cmd,omitempty"`
		Data    struct {
			TaskID   uint    `json:"task_id"`
			Progress float64 `json:"progress"`
			Status   int     `json:"status"`    // 1:进行中 2:完成 3:错误
			TaskType string  `json:"task_type"` // 任务类型 1、资产状态检测任务 2、url状态检查任务
		} `json:"data"`
	}
)

const Cmd = "assets_status_detect_progress"

func GetKey(k uint) string {
	return fmt.Sprintf(keyFormat, k)
}

func GetProgress(processedData, importAssets float64) float64 {
	f := strconv.FormatFloat(processedData/importAssets, 'f', 2, 64)
	p, _ := strconv.ParseFloat(f, 64)
	return p
}

func (s *StatusDetect) Byte() ([]byte, error) {
	jsonBytes, err := json.Marshal(s)
	if err != nil {
		return []byte{}, err
	}
	return jsonBytes, nil
}

func (s *StatusDetect) FormatErr(Code int, Message string) ([]byte, error) {
	obj := StatusDetect{Code: Code, Message: Message, Cmd: "assets_status_detect_progress"}

	jsonBytes, err := json.Marshal(obj)
	if err != nil {
		return []byte{}, err
	}
	return jsonBytes, nil
}

func (s *StatusDetect) Format(Code int, Message string, TaskID uint, Progress float64, Status int, TaskType string) ([]byte, error) {
	obj := StatusDetect{Code: Code, Message: Message, Cmd: "assets_status_detect_progress"}
	obj.Data.TaskID = TaskID
	obj.Data.Progress = Progress
	obj.Data.Status = Status
	obj.Data.TaskType = TaskType

	jsonBytes, err := json.Marshal(obj)
	if err != nil {
		return []byte{}, err
	}
	return jsonBytes, nil
}

// {
//	"cmd": "assets_status_detect_pre",
//	"data": {
//		"task_id": 123,
//		"progress": 10.23,
//		"status": 1
//	}
//}

func NewAssetsStatusDetectCache(c *goRedis.Client) AssetsStatusDetectCache {
	return &defaultAssetsStatusDetectCache{
		c,
	}
}

func (d *defaultAssetsStatusDetectCache) GetCache(key string) (StatusDetect, error) {
	redisVal, err := d.Client.Get(context.Background(), key).Result()
	if err != nil {
		return StatusDetect{}, err
	}

	var obj StatusDetect
	if err = json.Unmarshal([]byte(redisVal), &obj); err != nil {
		return StatusDetect{}, nil
	}
	return obj, nil
}

//func SetKey() string {
//	return fmt.Sprintf(keyFormat, time.Now().Unix())
//}

func (d *defaultAssetsStatusDetectCache) SetCache(key string, TaskID uint, Progress float64, Status int, TaskType string) error {
	obj := StatusDetect{}
	jsonBytes, err := obj.Format(200, "success", TaskID, Progress, Status, TaskType)
	if err != nil {
		return err
	}
	return redis.GetInstance().Set(context.Background(), key, jsonBytes, 10000*time.Hour).Err()
}
