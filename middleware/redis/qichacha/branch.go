package qichacha

import (
	"context"
	"time"

	redismiddle "micro-service/initialize/redis"
	"micro-service/pkg/utils"

	"github.com/go-redis/redis/v8"
)

type QiChaCha interface {
	GenCacheKey(string, string) string
	GetBranchCache(keyword string) (string, error)
	SetBranchCache(keyword, content string, d time.Duration) error
	GetCache(string) (string, error)
	SetCache(string, string, time.Duration) error
}

const (
	qccCacheNameTopPrefix = "foradar_cache:qichacha:"

	BranchPrefix            = "branch"
	NameSearchPrefix        = "name_search"
	InvestmentThroughPrefix = "investment_through"
)

type redisCache struct {
	client *redis.Client
}

func NewCacheClient(db int) QiChaCha {
	return redisCache{
		client: redismiddle.GetInstance(),
	}
}

func (rc redisCache) GenCacheKey(prefix string, s string) string {
	md5Str := utils.Md5sHash(s, false)

	return qccCacheNameTopPrefix + prefix + ":" + md5Str
}

func (rc redisCache) GetBranchCache(keyword string) (string, error) {
	key := rc.GenCacheKey(BranchPrefix, keyword)

	return rc.GetCache(key)
}

func (rc redisCache) SetBranchCache(keyword, content string, d time.Duration) error {
	key := rc.GenCacheKey(BranchPrefix, keyword)

	return rc.SetCache(key, content, d)
}

func (rc redisCache) GetCache(key string) (string, error) {
	return rc.client.Get(context.Background(), key).Result()
}

func (rc redisCache) SetCache(key, content string, d time.Duration) error {
	err := rc.client.Set(context.Background(), key, content, d).Err()

	return err
}
