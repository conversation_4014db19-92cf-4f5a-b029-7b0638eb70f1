package stat_processed

import (
	"context"
	"fmt"

	goRedis "github.com/go-redis/redis/v8"
)

const StatProcessedKey = "stat:processed:%s"

func GetStatProcessedKey(key string) string {
	return fmt.Sprintf(StatProcessedKey, key)
}

type (
	StatProcessedCache interface {
		GetCache(string) (string, error)
	}

	defaultStatProcessedCache struct {
		*goRedis.Client
	}
)

func NewStatProcessedCache(c *goRedis.Client) StatProcessedCache {
	return &defaultStatProcessedCache{
		c,
	}
}

func (d *defaultStatProcessedCache) GetCache(key string) (string, error) {
	redisVal, err := d.Client.Get(context.Background(), key).Result()
	if err != nil {
		return "", err
	}
	return redisVal, nil
}
