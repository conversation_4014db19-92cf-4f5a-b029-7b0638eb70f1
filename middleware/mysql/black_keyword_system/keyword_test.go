package black_keyword_system

import (
	"database/sql"
	"testing"
	"time"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dbx"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func init() {
	cfg.InitLoadCfg()
}

// setupTest 创建一个新的 mock 对象和模型
func setupTest(t *testing.T) (Model, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)

	// Mock GORM 初始化时的查询
	mock.ExpectQuery("SELECT VERSION\\(\\)").WillReturnRows(sqlmock.NewRows([]string{"VERSION()"}).AddRow("8.0.0"))

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: false,
	}))
	assert.NoError(t, err)

	model := &defaultImpl{DB: gormDB}
	return model, mock
}

func TestKeyword_TableName(t *testing.T) {
	keyword := &Keyword{}
	assert.Equal(t, TableName, keyword.TableName())
}

func TestNewModel(t *testing.T) {
	_ = initmysql.GetMockInstance()

	model := NewModel()
	assert.NotNil(t, model)
}

func TestDefaultImpl_Create(t *testing.T) {
	model, mock := setupTest(t)

	keyword := &Keyword{
		Model:   dbx.Model{Id: 1},
		Keyword: "test_keyword",
		TypeId:  1,
		Status:  1,
	}

	// 测试成功创建
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `black_keyword_system`").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), keyword.Keyword, keyword.TypeId, keyword.Status, keyword.Id).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	err := model.Create(keyword)
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试创建失败
	model, mock = setupTest(t)

	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `black_keyword_system`").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), keyword.Keyword, keyword.TypeId, keyword.Status, keyword.Id).
		WillReturnError(sql.ErrConnDone)
	mock.ExpectRollback()

	err = model.Create(keyword)
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultImpl_Upsert(t *testing.T) {
	model, mock := setupTest(t)

	// 测试空列表
	err := model.Upsert()
	assert.NoError(t, err)

	// 测试成功upsert
	keywords := []*Keyword{
		{Model: dbx.Model{Id: 1}, Keyword: "test1", TypeId: 1, Status: 1},
		{Model: dbx.Model{Id: 2}, Keyword: "test2", TypeId: 2, Status: 1},
	}

	mock.ExpectBegin()
	// 使用正则表达式匹配 SQL 语句，因为实际的 SQL 语句包含 ON DUPLICATE KEY UPDATE 部分
	mock.ExpectExec("INSERT INTO `black_keyword_system`.*ON DUPLICATE KEY UPDATE.*").
		WithArgs(
			sqlmock.AnyArg(), sqlmock.AnyArg(), "test1", int64(1), 1, uint64(1),
			sqlmock.AnyArg(), sqlmock.AnyArg(), "test2", int64(2), 1, uint64(2),
			sqlmock.AnyArg(), // 额外的 updated_at 参数
		).
		WillReturnResult(sqlmock.NewResult(2, 2))
	mock.ExpectCommit()

	err = model.Upsert(keywords...)
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试upsert失败
	model, mock = setupTest(t)

	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `black_keyword_system`.*ON DUPLICATE KEY UPDATE.*").
		WithArgs(
			sqlmock.AnyArg(), sqlmock.AnyArg(), "test1", int64(1), 1, uint64(1),
			sqlmock.AnyArg(), sqlmock.AnyArg(), "test2", int64(2), 1, uint64(2),
			sqlmock.AnyArg(), // 额外的 updated_at 参数
		).
		WillReturnError(sql.ErrConnDone)
	mock.ExpectRollback()

	err = model.Upsert(keywords...)
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultImpl_First(t *testing.T) {
	model, mock := setupTest(t)

	// 测试成功查询
	expectedKeyword := Keyword{
		Model:   dbx.Model{Id: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		Keyword: "test_keyword",
		TypeId:  1,
		Status:  1,
	}

	rows := sqlmock.NewRows([]string{"id", "keyword", "type_id", "status", "created_at", "updated_at"}).
		AddRow(expectedKeyword.Id, expectedKeyword.Keyword, expectedKeyword.TypeId, expectedKeyword.Status, expectedKeyword.CreatedAt, expectedKeyword.UpdatedAt)

	mock.ExpectQuery("SELECT (.+) FROM `black_keyword_system`").
		WillReturnRows(rows)

	result, err := model.First()
	assert.NoError(t, err)
	assert.Equal(t, expectedKeyword.Id, result.Id)
	assert.Equal(t, expectedKeyword.Keyword, result.Keyword)
	assert.Equal(t, expectedKeyword.TypeId, result.TypeId)
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试查询失败
	model, mock = setupTest(t)

	mock.ExpectQuery("SELECT (.+) FROM `black_keyword_system`").
		WillReturnError(sql.ErrNoRows)

	_, err = model.First()
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultImpl_List(t *testing.T) {
	model, mock := setupTest(t)

	// 测试分页查询
	expectedKeywords := []*Keyword{
		{Model: dbx.Model{Id: 1}, Keyword: "test1", TypeId: 1, Status: 1},
		{Model: dbx.Model{Id: 2}, Keyword: "test2", TypeId: 2, Status: 1},
	}

	// 计数查询
	countRows := sqlmock.NewRows([]string{"count"}).AddRow(2)
	mock.ExpectQuery("SELECT count(.+) FROM `black_keyword_system`").
		WillReturnRows(countRows)

	// 数据查询
	dataRows := sqlmock.NewRows([]string{"id", "keyword", "type_id", "status", "created_at", "updated_at"})
	for _, keyword := range expectedKeywords {
		dataRows.AddRow(keyword.Id, keyword.Keyword, keyword.TypeId, keyword.Status, time.Now(), time.Now())
	}

	mock.ExpectQuery("SELECT (.+) FROM `black_keyword_system`").
		WillReturnRows(dataRows)

	list, total, err := model.List(1, 10)
	assert.NoError(t, err)
	assert.Equal(t, int64(2), total)
	assert.Len(t, list, 2)
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试查询失败
	model, mock = setupTest(t)

	mock.ExpectQuery("SELECT count(.+) FROM `black_keyword_system`").
		WillReturnError(sql.ErrConnDone)

	_, _, err = model.List(1, 10)
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultImpl_DeleteByIds(t *testing.T) {
	model, mock := setupTest(t)

	// 测试空ID列表
	err := model.DeleteByIds()
	assert.NoError(t, err)

	// 测试成功删除
	ids := []uint64{1, 2, 3}
	mock.ExpectBegin()
	mock.ExpectExec("DELETE FROM `black_keyword_system` WHERE `id` IN (.+)").
		WithArgs(ids[0], ids[1], ids[2]).
		WillReturnResult(sqlmock.NewResult(0, 3))
	mock.ExpectCommit()

	err = model.DeleteByIds(ids...)
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试删除失败
	model, mock = setupTest(t)

	mock.ExpectBegin()
	mock.ExpectExec("DELETE FROM `black_keyword_system` WHERE `id` IN (.+)").
		WithArgs(ids[0], ids[1], ids[2]).
		WillReturnError(sql.ErrConnDone)
	mock.ExpectRollback()

	err = model.DeleteByIds(ids...)
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultImpl_FirstOrCreate(t *testing.T) {
	// 测试查询到现有记录
	model, mock := setupTest(t)

	existingKeyword := Keyword{
		Model:   dbx.Model{Id: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		Keyword: "existing_keyword",
		TypeId:  1,
		Status:  1,
	}

	// 使用正确的 SQL 查询模式
	mock.ExpectQuery("SELECT (.+) FROM `black_keyword_system` WHERE `keyword`=(.+) AND `type_id`=(.+) ORDER BY (.+) LIMIT 1").
		WithArgs("existing_keyword", int64(1)).
		WillReturnRows(sqlmock.NewRows([]string{"id", "keyword", "type_id", "status", "created_at", "updated_at"}).
			AddRow(existingKeyword.Id, existingKeyword.Keyword, existingKeyword.TypeId, existingKeyword.Status, existingKeyword.CreatedAt, existingKeyword.UpdatedAt))

	result, err := model.FirstOrCreate("existing_keyword", 1)
	assert.NoError(t, err)
	assert.Equal(t, existingKeyword.Id, result.Id)
	assert.Equal(t, existingKeyword.Keyword, result.Keyword)
	assert.Equal(t, existingKeyword.TypeId, result.TypeId)
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试查询失败
	model, mock = setupTest(t)

	mock.ExpectQuery("SELECT (.+) FROM `black_keyword_system` WHERE `keyword`=(.+) AND `type_id`=(.+) ORDER BY (.+) LIMIT 1").
		WithArgs("error_keyword", int64(3)).
		WillReturnError(sql.ErrConnDone)

	_, err = model.FirstOrCreate("error_keyword", 3)
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultImpl_ListAllActiveKeyword(t *testing.T) {
	model, mock := setupTest(t)

	// 测试成功查询
	keywords := []string{"active1", "active2", "active3"}
	rows := sqlmock.NewRows([]string{"keyword"})
	for _, k := range keywords {
		rows.AddRow(k)
	}

	mock.ExpectQuery("SELECT `keyword` FROM `black_keyword_system` WHERE status = ?").
		WithArgs(1).
		WillReturnRows(rows)

	result, err := model.ListAllActiveKeyword()
	assert.NoError(t, err)
	assert.Len(t, result, 3)
	assert.Contains(t, result, "active1")
	assert.Contains(t, result, "active2")
	assert.Contains(t, result, "active3")
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试查询失败
	model, mock = setupTest(t)

	mock.ExpectQuery("SELECT `keyword` FROM `black_keyword_system` WHERE status = ?").
		WithArgs(1).
		WillReturnError(sql.ErrConnDone)

	_, err = model.ListAllActiveKeyword()
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultImpl_ListAllActiveKeywordWithTypeId(t *testing.T) {
	model, mock := setupTest(t)

	// 测试成功查询
	rows := sqlmock.NewRows([]string{"keyword", "type_id"}).
		AddRow("active1", 1).
		AddRow("active2", 2).
		AddRow("active3", 1)

	mock.ExpectQuery("SELECT keyword, type_id FROM `black_keyword_system` WHERE status = ?").
		WithArgs(1).
		WillReturnRows(rows)

	result, err := model.ListAllActiveKeywordWithTypeId()
	assert.NoError(t, err)
	assert.Len(t, result, 3)
	assert.Equal(t, int64(1), result["active1"])
	assert.Equal(t, int64(2), result["active2"])
	assert.Equal(t, int64(1), result["active3"])
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试查询失败
	model, mock = setupTest(t)

	mock.ExpectQuery("SELECT keyword, type_id FROM `black_keyword_system` WHERE status = ?").
		WithArgs(1).
		WillReturnError(sql.ErrConnDone)

	_, err = model.ListAllActiveKeywordWithTypeId()
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultImpl_ListAllActiveKeyword_EmptyResult(t *testing.T) {
	model, mock := setupTest(t)

	// 测试空结果
	rows := sqlmock.NewRows([]string{"keyword"})
	mock.ExpectQuery("SELECT `keyword` FROM `black_keyword_system` WHERE status = ?").
		WithArgs(1).
		WillReturnRows(rows)

	result, err := model.ListAllActiveKeyword()
	assert.NoError(t, err)
	assert.Empty(t, result)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultImpl_ListAllActiveKeywordWithTypeId_EmptyResult(t *testing.T) {
	model, mock := setupTest(t)

	// 测试空结果
	rows := sqlmock.NewRows([]string{"keyword", "type_id"})
	mock.ExpectQuery("SELECT keyword, type_id FROM `black_keyword_system` WHERE status = ?").
		WithArgs(1).
		WillReturnRows(rows)

	result, err := model.ListAllActiveKeywordWithTypeId()
	assert.NoError(t, err)
	assert.Empty(t, result)
	assert.NoError(t, mock.ExpectationsWereMet())
}

// 测试状态常量
func TestKeywordStatus(t *testing.T) {
	// 测试状态值
	keyword := &Keyword{Status: 1}
	assert.Equal(t, 1, keyword.Status) // 启用状态

	keyword.Status = 2
	assert.Equal(t, 2, keyword.Status) // 禁用状态
}

// 测试TypeId字段
func TestKeywordTypeId(t *testing.T) {
	keyword := &Keyword{TypeId: 123}
	assert.Equal(t, int64(123), keyword.TypeId)
}
