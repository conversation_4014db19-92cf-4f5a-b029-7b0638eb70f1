package black_keyword_system

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

type Model interface {
	Create(item *Keyword) error
	Upsert(item ...*Keyword) error
	First(opts ...mysql.HandleFunc) (Keyword, error)
	List(page, size int, opts ...mysql.HandleFunc) ([]*Keyword, int64, error)
	DeleteByIds(ids ...uint64) error
	FirstOrCreate(keyword string, typeId int64) (Keyword, error)
	ListAllActiveKeyword() ([]string, error)
	ListAllActiveKeywordWithTypeId() (map[string]int64, error)
}

type Keyword struct {
	dbx.Model
	Keyword string `gorm:"column:keyword"` // 关键字
	TypeId  int64  `gorm:"column:type_id"` // 分类ID
	Status  int    `gorm:"column:status"`  // 1-启用 2-禁用
}

const TableName = "black_keyword_system"

func (*Keyword) TableName() string {
	return TableName
}

func NewModel(clients ...*gorm.DB) Model {
	return &defaultImpl{DB: mysql.GetDbClient(clients...)}
}

type defaultImpl struct{ *gorm.DB }

func (d *defaultImpl) Create(item *Keyword) error {
	return d.DB.Create(item).Error
}

func (d *defaultImpl) Upsert(items ...*Keyword) error {
	if len(items) == 0 {
		return nil
	}
	return d.DB.Save(items).Error
}

func (d *defaultImpl) First(opts ...mysql.HandleFunc) (Keyword, error) {
	q := d.DB.Model(&Keyword{})
	for _, opt := range opts {
		opt(q)
	}

	var info Keyword
	err := q.First(&info).Error
	return info, err
}

func (d *defaultImpl) List(page, size int, opts ...mysql.HandleFunc) ([]*Keyword, int64, error) {
	q := d.DB.Model(&Keyword{})
	for _, opt := range opts {
		opt(q)
	}

	var total int64
	if !mysql.IsPageAll(page, size) {
		q.Count(&total).Scopes(mysql.PageLimit(page, size))
	}

	var list []*Keyword
	err := q.Find(&list).Error
	return list, total, err
}

func (d *defaultImpl) DeleteByIds(ids ...uint64) error {
	if len(ids) == 0 {
		return nil
	}
	return d.DB.Where("`id` IN (?)", ids).Delete(&Keyword{}).Error
}

func (d *defaultImpl) FirstOrCreate(keyword string, typeId int64) (Keyword, error) {
	var info Keyword
	q := d.DB.Model(&Keyword{}).Where("`keyword`=? AND `type_id`=?", keyword, typeId)
	err := q.FirstOrCreate(&info).Error
	return info, err
}

func (d *defaultImpl) ListAllActiveKeyword() ([]string, error) {
	var keywords []string
	err := d.DB.Model(&Keyword{}).Select("keyword").Where("status = ?", 1).Find(&keywords).Error
	if err != nil {
		return nil, err
	}
	return keywords, nil
}

func (d *defaultImpl) ListAllActiveKeywordWithTypeId() (map[string]int64, error) {
	var keywords []*Keyword
	err := d.DB.Model(&Keyword{}).Select("keyword, type_id").Where("status = ?", 1).Find(&keywords).Error
	if err != nil {
		return nil, err
	}

	result := make(map[string]int64, len(keywords))
	for _, k := range keywords {
		result[k.Keyword] = k.TypeId
	}
	return result, nil
}
