package black_keyword_system

import (
	"gorm.io/gorm"
)

// WithKeyword 按关键词查询
func WithKeyword(keyword string) func(q *gorm.DB) *gorm.DB {
	return func(q *gorm.DB) *gorm.DB {
		return q.Where("keyword = ?", keyword)
	}
}

// WithStatus 按状态查询
func WithStatus(status int) func(q *gorm.DB) *gorm.DB {
	return func(q *gorm.DB) *gorm.DB {
		return q.Where("status = ?", status)
	}
}

// WithTypeId 按类型ID查询
func WithTypeId(typeId int64) func(q *gorm.DB) *gorm.DB {
	return func(q *gorm.DB) *gorm.DB {
		return q.Where("type_id = ?", typeId)
	}
}

// WithId 按ID查询
func WithId(id uint64) func(q *gorm.DB) *gorm.DB {
	return func(q *gorm.DB) *gorm.DB {
		return q.Where("id = ?", id)
	}
}
