package geo_areas

import (
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
)

func init() {
	cfg.InitLoadCfg()
	// 设置测试环境标志
	initmysql.SetTestEnv(true)

	// 初始化数据库连接
	initmysql.GetInstance(cfg.LoadMysql())

	// 初始化日志
	log.Init()
}

// setupTestModel 创建测试用的模型实例
func setupTestModel(t *testing.T) (GeoAreasModel, sqlmock.Sqlmock) {
	// 为每个测试创建新的Mock实例
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to create mock: %v", err)
	}

	// Mock GORM 初始化时的查询
	mock.ExpectQuery("SELECT VERSION\\(\\)").WillReturnRows(sqlmock.NewRows([]string{"VERSION()"}).AddRow("8.0.0"))

	// 创建GORM实例
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: false,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to create gorm instance: %v", err)
	}

	model := &defaultGeoAreasModel{
		DB:    gormDB,
		table: geoatlasTable,
	}
	return model, mock
}

func TestGeoAreas_TableName(t *testing.T) {
	area := &GeoAreas{}
	assert.Equal(t, geoatlasTable, area.TableName())
}

func TestNewModel(t *testing.T) {
	model := NewModel()
	assert.NotNil(t, model)
}

func TestDefaultGeoAreasModel_FindAll(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		now := time.Now()
		expectedAreas := []GeoAreas{
			{
				ID:        1,
				Lft:       1,
				Rgt:       50,
				ParentID:  0,
				Name:      "中国",
				Depth:     0,
				Level:     LEVEL_COUNTRY,
				Adcode:    "100000",
				Initials:  "ZG",
				Pinyin:    "zhongguo",
				CreatedAt: &now,
				UpdatedAt: &now,
			},
			{
				ID:        2,
				Lft:       2,
				Rgt:       5,
				ParentID:  1,
				Name:      "北京市",
				Depth:     1,
				Level:     LEVEL_PROVINCE,
				Adcode:    "110000",
				Initials:  "BJS",
				Pinyin:    "beijingshi",
				CreatedAt: &now,
				UpdatedAt: &now,
			},
		}

		rows := sqlmock.NewRows([]string{
			"id", "_lft", "_rgt", "parent_id", "name", "depth", "level",
			"adcode", "initials", "pinyin", "created_at", "updated_at",
		})
		for _, area := range expectedAreas {
			rows.AddRow(
				area.ID, area.Lft, area.Rgt, area.ParentID, area.Name,
				area.Depth, area.Level, area.Adcode, area.Initials,
				area.Pinyin, area.CreatedAt, area.UpdatedAt,
			)
		}

		mock.ExpectQuery("SELECT (.+) FROM `geoatlas`").
			WillReturnRows(rows)

		results, err := model.FindAll()
		assert.NoError(t, err)
		assert.Len(t, results, 2)
		assert.Equal(t, expectedAreas[0].ID, results[0].ID)
		assert.Equal(t, expectedAreas[0].Name, results[0].Name)
		assert.Equal(t, expectedAreas[1].ID, results[1].ID)
		assert.Equal(t, expectedAreas[1].Name, results[1].Name)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("empty result", func(t *testing.T) {
		model, mock := setupTestModel(t)

		rows := sqlmock.NewRows([]string{
			"id", "_lft", "_rgt", "parent_id", "name", "depth", "level",
			"adcode", "initials", "pinyin", "created_at", "updated_at",
		})

		mock.ExpectQuery("SELECT (.+) FROM `geoatlas`").
			WillReturnRows(rows)

		results, err := model.FindAll()
		assert.NoError(t, err)
		assert.Len(t, results, 0)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		mock.ExpectQuery("SELECT (.+) FROM `geoatlas`").
			WillReturnError(sql.ErrConnDone)

		results, err := model.FindAll()
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultGeoAreasModel_GetProvinceWithCities(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		now := time.Now()
		expectedProvinces := []GeoAreas{
			{
				ID:        2,
				Lft:       2,
				Rgt:       5,
				ParentID:  1,
				Name:      "北京市",
				Depth:     1,
				Level:     LEVEL_PROVINCE,
				Adcode:    "110000",
				Initials:  "BJS",
				Pinyin:    "beijingshi",
				CreatedAt: &now,
				UpdatedAt: &now,
			},
			{
				ID:        3,
				Lft:       6,
				Rgt:       9,
				ParentID:  1,
				Name:      "上海市",
				Depth:     1,
				Level:     LEVEL_PROVINCE,
				Adcode:    "310000",
				Initials:  "SHS",
				Pinyin:    "shanghaishi",
				CreatedAt: &now,
				UpdatedAt: &now,
			},
		}

		expectedCities := []GeoAreas{
			{
				ID:        4,
				Lft:       3,
				Rgt:       4,
				ParentID:  2,
				Name:      "朝阳区",
				Depth:     2,
				Level:     LEVEL_DISTRICT,
				Adcode:    "110105",
				Initials:  "CYQ",
				Pinyin:    "chaoyangqu",
				CreatedAt: &now,
				UpdatedAt: &now,
			},
			{
				ID:        5,
				Lft:       7,
				Rgt:       8,
				ParentID:  3,
				Name:      "浦东新区",
				Depth:     2,
				Level:     LEVEL_DISTRICT,
				Adcode:    "310115",
				Initials:  "PDXQ",
				Pinyin:    "pudongxinqu",
				CreatedAt: &now,
				UpdatedAt: &now,
			},
		}

		provinceRows := sqlmock.NewRows([]string{
			"id", "_lft", "_rgt", "parent_id", "name", "depth", "level",
			"adcode", "initials", "pinyin", "created_at", "updated_at",
		})
		for _, province := range expectedProvinces {
			provinceRows.AddRow(
				province.ID, province.Lft, province.Rgt, province.ParentID, province.Name,
				province.Depth, province.Level, province.Adcode, province.Initials,
				province.Pinyin, province.CreatedAt, province.UpdatedAt,
			)
		}

		cityRows := sqlmock.NewRows([]string{
			"id", "_lft", "_rgt", "parent_id", "name", "depth", "level",
			"adcode", "initials", "pinyin", "created_at", "updated_at",
		})
		for _, city := range expectedCities {
			cityRows.AddRow(
				city.ID, city.Lft, city.Rgt, city.ParentID, city.Name,
				city.Depth, city.Level, city.Adcode, city.Initials,
				city.Pinyin, city.CreatedAt, city.UpdatedAt,
			)
		}

		mock.ExpectQuery("SELECT (.+) FROM `geoatlas` WHERE level = ?").
			WithArgs(LEVEL_PROVINCE).
			WillReturnRows(provinceRows)

		mock.ExpectQuery("SELECT (.+) FROM `geoatlas` WHERE level = \\? OR \\(depth = \\? AND level = \\?\\)").
			WithArgs(LEVEL_CITY, 2, LEVEL_DISTRICT).
			WillReturnRows(cityRows)

		provinces, cities, err := model.GetProvinceWithCities()
		assert.NoError(t, err)
		assert.Len(t, provinces, 2)
		assert.Len(t, cities, 2)
		assert.Equal(t, expectedProvinces[0].ID, provinces[0].ID)
		assert.Equal(t, expectedProvinces[1].ID, provinces[1].ID)
		assert.Equal(t, expectedCities[0].ID, cities[0].ID)
		assert.Equal(t, expectedCities[1].ID, cities[1].ID)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("province query error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		mock.ExpectQuery("SELECT (.+) FROM `geoatlas` WHERE level = ?").
			WithArgs(LEVEL_PROVINCE).
			WillReturnError(sql.ErrConnDone)

		provinces, cities, err := model.GetProvinceWithCities()
		assert.Error(t, err)
		assert.Nil(t, provinces)
		assert.Nil(t, cities)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("city query error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		provinceRows := sqlmock.NewRows([]string{
			"id", "_lft", "_rgt", "parent_id", "name", "depth", "level",
			"adcode", "initials", "pinyin", "created_at", "updated_at",
		}).AddRow(1, 1, 2, 0, "北京市", 1, LEVEL_PROVINCE, "110000", "BJS", "beijingshi", time.Now(), time.Now())

		mock.ExpectQuery("SELECT (.+) FROM `geoatlas` WHERE level = ?").
			WithArgs(LEVEL_PROVINCE).
			WillReturnRows(provinceRows)

		mock.ExpectQuery("SELECT (.+) FROM `geoatlas` WHERE level = \\? OR \\(depth = \\? AND level = \\?\\)").
			WithArgs(LEVEL_CITY, 2, LEVEL_DISTRICT).
			WillReturnError(sql.ErrConnDone)

		provinces, cities, err := model.GetProvinceWithCities()
		assert.Error(t, err)
		assert.Nil(t, provinces)
		assert.Nil(t, cities)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestWithID(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	id := uint64(1)

	mock.ExpectQuery("SELECT (.+) FROM `geoatlas` WHERE id = ?").
		WithArgs(id).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.FindAll(WithID(id))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithParentID(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	parentID := uint64(1)

	mock.ExpectQuery("SELECT (.+) FROM `geoatlas` WHERE parent_id = ?").
		WithArgs(parentID).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(2))

	_, err := model.FindAll(WithParentID(parentID))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithLevel(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	level := uint8(LEVEL_PROVINCE)

	mock.ExpectQuery("SELECT (.+) FROM `geoatlas` WHERE level = ?").
		WithArgs(level).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.FindAll(WithLevel(level))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithName(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	name := "北京"

	mock.ExpectQuery("SELECT (.+) FROM `geoatlas` WHERE name LIKE ?").
		WithArgs("%" + name + "%").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.FindAll(WithName(name))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}
