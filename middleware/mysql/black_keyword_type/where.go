package black_keyword_type

import (
	"gorm.io/gorm"
)

// WithStatus 按状态查询
func WithStatus(status int) func(q *gorm.DB) *gorm.DB {
	return func(q *gorm.DB) *gorm.DB {
		return q.Where("status = ?", status)
	}
}

// WithId 按ID查询
func WithId(id uint64) func(q *gorm.DB) *gorm.DB {
	return func(q *gorm.DB) *gorm.DB {
		return q.Where("id = ?", id)
	}
}

// WithName 按名称查询
func WithName(name string) func(q *gorm.DB) *gorm.DB {
	return func(q *gorm.DB) *gorm.DB {
		return q.Where("name = ?", name)
	}
}
