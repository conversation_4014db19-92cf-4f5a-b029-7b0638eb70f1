package task

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

const TableName = "task_hosts"

type TaskHost struct {
	dbx.Model
	TaskId uint64 `gorm:"column:task_id;type:bigint(20) unsigned;comment:任务表id;NOT NULL" json:"task_id,string"`
	Urls   string `gorm:"column:urls;type:text;comment:扫描URL;NOT NULL" json:"urls"` // 字符串数组，例：["baidu.com"]
}

type defaultTaskHostModel struct {
	*gorm.DB
	table string
}

type TaskHostsModel interface {
	Create(taskHost *TaskHost) error
	CreateBatch(taskHosts []*TaskHost) error
	ListAll(fields []string, opts ...mysql.HandleFunc) ([]*TaskHost, error)
}

func (m *TaskHost) TableName() string {
	return TableName
}

func NewTaskHostsModel(conn ...*gorm.DB) TaskHostsModel {
	return &defaultTaskHostModel{mysql.GetDbClient(conn...), TableName}
}

func (d *defaultTaskHostModel) Create(taskHost *TaskHost) error {
	return d.DB.Create(taskHost).Error
}

func (d *defaultTaskHostModel) CreateBatch(taskHosts []*TaskHost) error {
	if len(taskHosts) == 0 {
		return nil
	}
	return d.DB.CreateInBatches(taskHosts, 100).Error
}

func (d *defaultTaskHostModel) ListAll(fields []string, opts ...mysql.HandleFunc) ([]*TaskHost, error) {
	db := d.DB.Model(&TaskHost{})
	for _, opt := range opts {
		opt(db)
	}
	var taskHosts []*TaskHost
	if len(fields) > 0 {
		db = db.Select(fields)
	}
	if err := db.Find(&taskHosts).Error; err != nil {
		return nil, err
	}
	return taskHosts, nil
}
