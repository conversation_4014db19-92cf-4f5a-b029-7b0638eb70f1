package task

import (
	"errors"
	"micro-service/middleware/mysql"
	"time"

	"gorm.io/gorm"
)

type TaskModel interface {
	FindAll(...mysql.HandleFunc) ([]Task, error)
	First(...mysql.HandleFunc) (*Task, error)
	Create(*Task, ...mysql.HandleFunc) error
}

const taskTableName = "scan_tasks"
const SCAN_TYPE_ACCURATE = 0
const SCAN_TYPE_SPEED = 1
const SCAN_TYPE_FOFA = 2

const ALL_PORT_DEFINE_PORT = 65536

type Task struct {
	gorm.Model
	UserID                     uint64     `gorm:"column:user_id;type:bigint(20) unsigned;comment:用户id;NOT NULL" json:"user_id"`
	CompanyID                  *uint64    `gorm:"column:company_id;type:bigint(20) unsigned;comment:企业ID" json:"company_id"`
	Name                       string     `gorm:"column:name;type:varchar(255);comment:任务名称;NOT NULL" json:"name"`
	Bandwidth                  string     `gorm:"column:bandwidth;type:varchar(12);comment:扫描带宽;NOT NULL" json:"bandwidth"`
	Node                       *string    `gorm:"column:node;type:varchar(255);comment:任务执行节点" json:"node"`
	ProtocolConcurrency        string     `gorm:"column:protocol_concurrency;type:varchar(16);comment:并发数;NOT NULL;default:2" json:"protocol_concurrency"`
	TaskType                   int8       `gorm:"column:task_type;type:tinyint(4);comment:任务分类 默认1 1 资产扫描 2 漏洞扫描;NOT NULL;default:0" json:"task_type"`
	AssetType                  int8       `gorm:"column:asset_type;type:tinyint(4);comment:扫描资产类型: 0/1 手动扫描/云端推荐;NOT NULL;default:0" json:"asset_type"`
	IPType                     int8       `gorm:"column:ip_type;type:tinyint(4);comment:IP类型: 1.IPv4 2.IPv6;NOT NULL;default:1" json:"ip_type"`
	Type                       int8       `gorm:"column:type;type:tinyint(4);comment:是否为周期任务 默认0 否 1 是周期任务;NOT NULL;default:0" json:"type"`
	Progress                   float64    `gorm:"column:progress;type:decimal(11,2);comment:任务进度;NOT NULL;default:0.00" json:"progress"`
	AssetNum                   *string    `gorm:"column:asset_num;type:varchar(10);comment:资产ip数量" json:"asset_num"`
	ThreatNum                  *string    `gorm:"column:threat_num;type:varchar(10);comment:漏洞数量" json:"threat_num"`
	RuleNum                    *string    `gorm:"column:rule_num;type:varchar(10);comment:资产扫描任务的组件数量" json:"rule_num"`
	Order                      uint64     `gorm:"column:order;type:bigint(20) unsigned;comment:任务执行的顺序 asc order为每次新建时此表的记录条数;NOT NULL" json:"order"`
	PingSwitch                 int8       `gorm:"column:ping_switch;type:tinyint(4);comment:开启Ping识别资产 0不开启 1开启;NOT NULL;default:0" json:"ping_switch"`
	WebLogoSwitch              int8       `gorm:"column:web_logo_switch;type:tinyint(4);comment:网站首页截图是否开启 0不开启 1开启;NOT NULL;default:0" json:"web_logo_switch"`
	UseSeconds                 *string    `gorm:"column:use_seconds;type:varchar(255);comment:任务扫描完成需要了多长时间，单位秒" json:"use_seconds"`
	Status                     int8       `gorm:"column:status;type:tinyint(4);comment:状态 0/1/2/3/4 默认/扫描中/扫描完成/扫描失败/暂停扫描;NOT NULL;default:0" json:"status"`
	Step                       int8       `gorm:"column:step;type:tinyint(4);comment:任务进行到哪一步了 默认0 没开始或者是资产扫描未完成 1:正在同步数据 2:正在打标签 3:正在漏洞扫描 4:扫描完成;NOT NULL;default:0" json:"step"`
	ScanType                   int8       `gorm:"column:scan_type;type:tinyint(4);comment:扫描模式 0为精准 1为极速;NOT NULL;default:1" json:"scan_type"`
	PocScanType                int8       `gorm:"column:poc_scan_type;type:tinyint(4);comment:poc范围 0全部poc  1指定poc 2/poc分组 3/暴力破解poc 4/系统扫描 5/web扫描 6/默认（scan_engine为2的时候）;NOT NULL;default:1" json:"poc_scan_type"`
	ScanRange                  *int8      `gorm:"column:scan_range;type:tinyint(4);comment:扫描范围,0手动输入的ip 1上传的ip 2已经认证的资产ip 3根据ip段筛选,给前端回显用的字段;NOT NULL;default:1" json:"scan_range"`
	StartAt                    *time.Time `gorm:"column:start_at;type:timestamp;comment:任务下发时间" json:"start_at"`
	EndAt                      *time.Time `gorm:"column:end_at;type:timestamp;comment:任务完成时间" json:"end_at"`
	PauseAt                    *time.Time `gorm:"column:puase_at;type:timestamp;comment:暂停任务的时间" json:"pause_at"`
	FileName                   string     `gorm:"column:file_name;type:varchar(250);comment:上传ip扫描的时候，记录的文件名;default:''" json:"file_name"`
	TaskFrom                   int8       `gorm:"column:task_from;type:tinyint(4);comment:0/用户自己下发的扫描任务 1/安服下发的扫描任务;NOT NULL;default:0" json:"task_from"`
	CronID                     int64      `gorm:"column:cron_id;type:bigint(20);comment:周期任务ID;default:0" json:"cron_id"`
	OpID                       int64      `gorm:"column:op_id;type:bigint(20);comment:操作人;default:0" json:"op_id"`
	IsAudit                    int8       `gorm:"column:is_audit;type:tinyint(4);comment:审核状态 0/1/2 待审核/审核通过/审核失败;default:0" json:"is_audit"`
	ForbidStatus               int8       `gorm:"column:forbid_status;type:tinyint(4);comment:禁扫状态: 1/2 禁扫暂停,重新入队;default:0" json:"forbid_status"`
	Reason                     string     `gorm:"column:reason;type:varchar(255);comment:审核驳回原因;default:''" json:"reason"`
	IsDefinePort               int8       `gorm:"column:is_define_port;type:tinyint(4);comment:是否是自定义端口资产扫描0/1 否/是;default:0" json:"is_define_port"`
	PortRange                  *int8      `gorm:"column:port_range;type:tinyint(4);comment:扫描端口类型,0:推荐数据指定端口探活" json:"port_range"`
	Flag                       *string    `gorm:"column:flag;type:varchar(64);comment:推荐任务ID" json:"flag"`
	DetectAssetsTasksID        *uint64    `gorm:"column:detect_assets_tasks_id;type:bigint(20) unsigned;comment:资产测绘任务ID" json:"detect_assets_tasks_id"`
	AuditTime                  *string    `gorm:"column:audit_time;type:varchar(255);comment:漏洞的审核时间" json:"audit_time"`
	ScanEngine                 string     `gorm:"column:scan_engine;type:varchar(20);comment:漏洞扫描的扫描引擎 空值/goscanner  1/天融信扫描器 2/goscanner和天融信都选择了;default:''" json:"scan_engine"`
	BatchID                    string     `gorm:"column:batch_id;type:varchar(250);comment:下发扫描的时候， 如果goscanner和 transcanner都选择了，这个值是2个任务的批次id;default:''" json:"batch_id"`
	IsTranscanner              int8       `gorm:"column:is_transcanner;type:tinyint(4);comment:是否是天融信的漏扫任务  1/是 0/不是;default:0" json:"is_transcanner"`
	TianrongxinTaskID          string     `gorm:"column:tianrongxin_task_id;type:varchar(250);comment:天融信漏扫的任务id;default:''" json:"tianrongxin_task_id"`
	OrganizationDiscoverTaskID *uint64    `gorm:"column:organization_discover_task_id;type:bigint(20) unsigned;comment:组织架构测绘任务的任务表-organization_discover_task的id" json:"organization_discover_task_id"`
	AssetsSyncNode             *string    `gorm:"column:assets_sync_node;type:varchar(128);comment:执行扫描任务50%进度以后的任务节点" json:"assets_sync_node"`
}

func (*Task) TableName() string {
	return taskTableName
}

type defaultTask struct {
	*gorm.DB
	tableName string
}

func NewModel(conn ...*gorm.DB) TaskModel {
	return &defaultTask{
		DB:        mysql.GetDbClient(conn...),
		tableName: taskTableName,
	}
}

func (d *defaultTask) FindAll(opts ...mysql.HandleFunc) ([]Task, error) {
	query := d.DB.Model(&Task{})
	for _, f := range opts {
		f(query)
	}

	var list = make([]Task, 0)
	err := query.Find(&list).Error
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (d *defaultTask) First(opts ...mysql.HandleFunc) (*Task, error) {
	query := d.DB.Model(&Task{})
	for _, f := range opts {
		f(query)
	}

	var info Task
	err := query.First(&info).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &info, nil
}

func (d *defaultTask) Create(task *Task, opts ...mysql.HandleFunc) error {
	query := d.DB.Model(&Task{})
	for _, f := range opts {
		f(query)
	}

	err := query.Create(&task).Error
	if err != nil {
		return err
	}
	return nil
}

// WithUserID 添加用户ID查询条件
func WithUserID(userID uint64) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("user_id = ?", userID)
	}
}

func WithID(id uint64) mysql.HandleFunc {
	return func(db *gorm.DB) {
		db.Where("id = ?", id)
	}
}

// WithDetectAssetsTasksID 添加资产测绘任务ID查询条件
func WithDetectAssetsTasksID(taskID uint64) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("detect_assets_tasks_id = ?", taskID)
	}
}

// WithTrashed 包含已删除的记录
func WithTrashed() mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Unscoped()
	}
}
