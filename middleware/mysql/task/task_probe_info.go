package task

import "database/sql"

// CREATE TABLE `task_probe_infos` (
//   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
//   `task_id` bigint(20) unsigned DEFAULT NULL COMMENT '任务表id',
//   `ip` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扫描IP',
//   `port` int(11) NOT NULL COMMENT '扫描端口',
//   `base_protocol` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '基础协议',
//   `created_at` timestamp NULL DEFAULT NULL,
//   `updated_at` timestamp NULL DEFAULT NULL,
//   PRIMARY KEY (`id`),
//   KEY `task_probe_infos_task_id_index` (`task_id`)
// ) ENGINE=InnoDB AUTO_INCREMENT=133947 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推荐数据指定端口探活信息表';

type TaskProbeInfo struct {
	Id           uint64         `gorm:"column:id;type:bigint(20) unsigned;AUTO_INCREMENT;primary_key" json:"id,string"`
	TaskId       uint64         `gorm:"column:task_id;type:bigint(20) unsigned;comment:任务表id;NOT NULL" json:"task_id,string"`
	Ip           sql.NullString `gorm:"column:ip;type:varchar(255);comment:扫描IP" json:"ip"`
	Port         int            `gorm:"column:port;type:int(11);comment:扫描端口;NOT NULL" json:"port"`
	BaseProtocol string         `gorm:"column:base_protocol;type:varchar(255);comment:基础协议;NOT NULL" json:"base_protocol"`
	CreatedAt    sql.NullTime   `gorm:"column:created_at;type:timestamp" json:"created_at"`
	UpdatedAt    sql.NullTime   `gorm:"column:updated_at;type:timestamp" json:"updated_at"`
}

func (m *TaskProbeInfo) TableName() string {
	return "task_probe_infos"
}
