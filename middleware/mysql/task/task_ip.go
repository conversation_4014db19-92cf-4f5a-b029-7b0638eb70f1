package task

import (
	"database/sql"
	"micro-service/middleware/mysql"

	"gorm.io/gorm"
)

const TaskIpsTableName = "task_ips"

// IP类型: 1.IPv4 2.IPv6
const IP_TYPE_V4 = 1
const IP_TYPE_V6 = 2

// 任务的ip表
type (
	TaskIpsModel interface {
		CountByTaskID([]uint64) (int64, error)
		FindByQuerys(opts ...mysql.HandleFunc) ([]*TaskIps, error)
		CreateBatch(taskIps []*TaskIps) error
	}

	defaultTaskIpsModel struct {
		*gorm.DB
		table string
	}

	TaskIps struct {
		Id        uint64         `gorm:"column:id;type:bigint(20) unsigned;AUTO_INCREMENT;primary_key" json:"id"`
		TaskId    uint64         `gorm:"column:task_id;type:bigint(20) unsigned;comment:任务表id;NOT NULL" json:"task_id"`
		Ip        sql.NullString `gorm:"column:ip;type:varchar(255);comment:ip" json:"ip"`
		CreatedAt sql.NullTime   `gorm:"column:created_at;type:timestamp" json:"created_at"`
		UpdatedAt sql.NullTime   `gorm:"column:updated_at;type:timestamp" json:"updated_at"`
	}
)

func (m *TaskIps) TableName() string {
	return TaskIpsTableName
}

func NewTaskIpsModel(conn ...*gorm.DB) TaskIpsModel {
	return &defaultTaskIpsModel{mysql.GetDbClient(conn...), TaskIpsTableName}
}

func (d *defaultTaskIpsModel) CreateBatch(taskIps []*TaskIps) error {
	return d.DB.CreateInBatches(taskIps, 100).Error
}

func (d *defaultTaskIpsModel) CountByTaskID(in []uint64) (int64, error) {
	var resp int64
	query := d.DB.Table(d.table)
	e := query.Select("id").Where("task_id in (?)", in).Count(&resp).Error
	return resp, e
}

func (d *defaultTaskIpsModel) FindByQuerys(opts ...mysql.HandleFunc) ([]*TaskIps, error) {
	var resp []*TaskIps
	query := d.DB.Table(d.table)
	for _, f := range opts {
		f(query)
	}
	err := query.Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}
