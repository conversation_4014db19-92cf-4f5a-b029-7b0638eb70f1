package scan_cert

import (
	"encoding/json"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"time"
)

const ScanCertTableName = "scan_cert"

type (
	ScanCertModel interface {
		FindByDomain(domain []string, updatedAt string) ([]ScanCert, error)
		DelByDomain(domain []string) error
		AddCerts(list []ScanCert) error
		SaveCerts(c []ScanCert) error
	}

	defaultScanCertModelModel struct {
		*gorm.DB
		table string
	}

	ScanCert struct {
		dbx.Model
		Domain     string `gorm:"column:domain;type:varchar(255);comment:域名;NOT NULL;uniqueIndex:uk_domain" json:"domain"`
		DomainEx   string `gorm:"column:domain_ex;type:varchar(255);comment:协议+域名;NOT NULL" json:"domain_ex"`
		IssuerCn   string `gorm:"column:issuer_cn;type:varchar(255);" json:"issuer_cn"`
		SubjectOrg string `gorm:"column:subject_org;type:varchar(255);" json:"subject_org"`
		NotBefore  string `gorm:"column:not_before;type:varchar(255);" json:"not_before"`
		IssuerCns  string `gorm:"column:issuer_cns;type:varchar(255);" json:"issuer_cns"`
		Raw        string `gorm:"column:cert;type:MEDIUMTEXT;NULL" json:"cert"`
		SigAlth    string `gorm:"column:sig_alth;type:varchar(255);" json:"sig_alth" `
		NotAfter   string `gorm:"column:not_after;type:varchar(255);" json:"not_after"`
		SubjectCn  string `gorm:"column:subject_cn;type:varchar(255);" json:"subject_cn"`
		IssuerOrg  string `gorm:"column:issuer_org;type:varchar(255);" json:"issuer_org"`
		V          string `gorm:"column:v;type:varchar(255);" json:"v"`
		ValidType  string `gorm:"column:valid_type;type:varchar(255);" json:"valid_type"`
		IsValid    bool   `gorm:"column:is_valid;type:tinyint(1);default:0" json:"is_valid"`
		SN         string `gorm:"column:sn;type:varchar(255);" json:"sn"`
		SubjectKey string `gorm:"column:subject_key;type:varchar(255);" json:"subject_key"`
		CertDate   string `gorm:"column:cert_date;type:varchar(255)" json:"cert_date"`

		SubjectOrgArray []string `gorm:"-" json:"-"`
		IssuerCnsArray  []string `gorm:"-" json:"-"`
		IssuerOrgArray  []string `gorm:"-" json:"-"`
	}
)

func (m *ScanCert) TableName() string {
	return "scan_cert"
}

func NewScanCertModel(conn ...*gorm.DB) *defaultScanCertModelModel {
	return &defaultScanCertModelModel{
		mysql.GetDbClient(conn...), ScanCertTableName,
	}
}

func (ds *defaultScanCertModelModel) FindByDomain(domain []string, updatedAt string) ([]ScanCert, error) {

	datas := []ScanCert{}
	err := ds.DB.Table(ds.table).Where("domain in (?) and created_at >= ?", domain, updatedAt).Find(&datas).Error

	for i := range datas {
		datas[i].SubjectOrgArray = split(datas[i].SubjectOrg)
		datas[i].IssuerCnsArray = split(datas[i].IssuerCns)
		datas[i].IssuerOrgArray = split(datas[i].IssuerOrg)
	}

	return datas, err
}

func (ds *defaultScanCertModelModel) DelByDomain(domain []string) error {
	return ds.DB.Table(ds.table).Where("domain in (?) ", domain).Delete(&ScanCert{}).Error
}

func (ds *defaultScanCertModelModel) AddCerts(list []ScanCert) error {
	for i := range list {
		list[i].SubjectOrg = join(list[i].SubjectOrgArray)
		list[i].IssuerCns = join(list[i].IssuerCnsArray)
		list[i].IssuerOrg = join(list[i].IssuerOrgArray)
	}

	return ds.DB.Table(ds.table).Clauses(clause.Insert{Modifier: "IGNORE"}).CreateInBatches(list, len(list)).Error
}

func SubDayTime(day time.Duration) string {
	return time.Now().Add(day * -24 * time.Hour).Format("2006-01-02 15:04:05")
}

func (ds *defaultScanCertModelModel) SaveCerts(c []ScanCert) error {
	domain := make([]string, 0, len(c))
	for i := range c {
		domain = append(domain, c[i].Domain)
	}

	if err := ds.DelByDomain(domain); err != nil {
		return err
	}

	return ds.AddCerts(c)
}

func join(sliceData []string) string {
	d, err := json.Marshal(sliceData)
	if err != nil {
		return ""
	}
	return string(d)
}

func split(str string) []string {
	var result []string
	err := json.Unmarshal([]byte(str), &result)
	if err != nil {
		return []string{}
	}
	return result
}
