package cron

import (
	"github.com/stretchr/testify/assert"
	"micro-service/initialize/mysql"
	mysql2 "micro-service/middleware/mysql"
	"micro-service/pkg/cfg"
	"testing"
)

func initCfg() {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())
}

func Test_Cron_CRUD(t *testing.T) {
	initCfg()

	db := NewCronModel()
	item := &Cron{
		UserId:  1,
		Name:    "Cron-1",
		Status:  1,
		Type:    TypeSystem,
		Spec:    "0 30 * * * *",
		Method:  "SyncClient",
		Params:  "",
		Running: 1,
		Reason:  "",
	}
	//	create
	err := db.Create(item)
	assert.Nil(t, err)

	//	first
	info, err := db.First(mysql2.WithColumnValue("`id`", item.Id))
	assert.Nil(t, err)

	// updates
	info.Params = `{"user_id: 10"}`
	info.Running = 0
	info.Spec = ""
	err = db.Updates(*info, mysql2.WithSelect("running", "params"))
	assert.Nil(t, err)

	// delete
	err = db.Delete([]uint64{item.Id})
	assert.Nil(t, err)
}
