package cron

import (
	"gorm.io/gorm"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"time"
)

type (
	HistoryModel interface {
	}

	defaultHistoryModel struct {
		*gorm.DB
		table string
	}

	History struct {
		dbx.Model
		CronId  uint64    `gorm:"column:cron_id;type:bigint(20);comment:Cron任务ID;index;NOT NULL" json:"cron_id"`
		StartAt time.Time `gorm:"comment:'任务开始时间';index;" json:"start_at"`
		EndAt   time.Time `gorm:"comment:'任务结束时间';index;" json:"end_at"`
		Status  int       `gorm:"column:status;type:tinyint(4);comment:任务执行状态 0/1 失败/成功;DEFAULT 1" json:"status"`
		Result  string    `gorm:"column:result;type:text;comment:任务执行结果;DEFAULT NULL" json:"result"`
	}
)

const (
	historyTable  = "cron_history"
	StatusFail    = 0
	StatusSuccess = 1
)

// TableName 表名
func (h *History) TableName() string {
	return historyTable
}

func NewHistoryModel(clients ...*gorm.DB) *defaultHistoryModel {
	return &defaultHistoryModel{mysql.GetDbClient(clients...), historyTable}
}

func (dtr *defaultHistoryModel) List(page, size int, opts ...mysql.HandleFunc) ([]History, int64, error) {
	query := dtr.Table(historyTable).Select("*")
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if !mysql.IsPageAll(page, size) {
		if err := query.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		query.Scopes(mysql.PageLimit(page, size))
	}

	var list = make([]History, 0)
	if err := query.Find(&list).Error; err != nil {
		return nil, 0, err
	}

	return list, total, nil
}
