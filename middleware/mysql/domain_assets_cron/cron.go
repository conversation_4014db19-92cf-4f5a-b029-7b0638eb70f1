package domain_assets_cron

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"time"

	"gorm.io/gorm"
)

type Croner interface {
	First(opts ...mysql.HandleFunc) (Cron, error)
	Create(*Cron) error
	Updates(Cron) error
	Delete(id uint64) error
}

const DomainAssetsCronTableName = "domain_assets_cron"

const (
	Enable  = iota + 1 // 启用
	Disable            // 禁用
)

const (
	ModeImmediately = iota + 1 // 立即执行模式
	ModeCron                   // 定时执行模式
)

type Cron struct {
	dbx.ModelFull
	UserId         uint64    `gorm:"column:user_id"`
	OperateUserId  uint64    `gorm:"operate_user_id"`
	CronId         uint64    `gorm:"column:cron_id"`         // 定时任务表-任务ID
	Ex             uint64    `gorm:"column:ex"`              // 执行间隔 30/60/90天
	Mode           int       `gorm:"column:mode"`            // 执行模式
	CurrentUsed    int       `gorm:"column:current_used"`    // 立即模式当天已使用
	Status         int       `gorm:"column:status"`          // 定时任务状态
	CronExpression string    `gorm:"column:cron_expression"` // 定时表达式
	LastUpdatedAt  time.Time `gorm:"last_updated_at"`        // 立即更新模式，上次更新时间
}

func (*Cron) TableName() string {
	return DomainAssetsCronTableName
}

func NewCroner(dbs ...*gorm.DB) Croner {
	return &defaultCron{DB: mysql.GetDbClient(dbs...)}
}

type defaultCron struct{ *gorm.DB }

func (d *defaultCron) First(opts ...mysql.HandleFunc) (Cron, error) {
	query := d.DB.Model(&Cron{})
	for _, f := range opts {
		f(query)
	}

	var item Cron
	err := query.First(&item).Error
	return item, err
}

func (d *defaultCron) List(page, size int, opts ...mysql.HandleFunc) ([]Cron, int64, error) {
	query := d.DB.Model(&Cron{})
	for _, f := range opts {
		f(query)
	}

	var total int64
	var items = make([]Cron, 0)
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Find(&items).Error
	return items, total, err
}

func (d *defaultCron) Create(item *Cron) error {
	return d.DB.Create(item).Error
}

func (d *defaultCron) Updates(item Cron) error {
	return d.DB.Model(&Cron{}).Where("id = ?", item.ID).Updates(item).Error
}

func (d *defaultCron) Delete(id uint64) error {
	return d.DB.Where("`id` = ?", id).Delete(&Cron{}).Error
}
