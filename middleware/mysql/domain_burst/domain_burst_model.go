package domain_burst

import (
	"time"

	"database/sql"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

const DomainBurstTableName = "domain_bursts"

type (
	DomainBurstModel interface {
		FindById(id interface{}) *DomainBurst
		Create(burst *DomainBurst) error
		FirstOrCreate(dest, conds *DomainBurst) *gorm.DB
	}

	defaultDomainBurstModel struct {
		*gorm.DB
		table string
	}

	DomainBurst struct {
		dbx.Model
		FromDomain  string          `gorm:"size:300;comment:'爆破目标域名'"`
		Domain      string          `gorm:"size:300;comment:'爆破结果域名'"`
		CompanyName *sql.NullString `gorm:"size:300;comment:'企业名称'"`
		BurstTime   time.Time       `json:"burst_time"`
		CreatedAt   time.Time       `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;<-:create" json:"created_at,omitempty"`
		UpdatedAt   time.Time       `gorm:"column:updated_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP  on update current_timestamp" json:"updated_at,omitempty"`
	}
)

// TableName 表名
func (u *DomainBurst) TableName() string {
	return DomainBurstTableName
}

func NewDomainBurstModel(conn ...*gorm.DB) DomainBurstModel {
	return &defaultDomainBurstModel{mysql.GetDbClient(conn...), DomainBurstTableName}
}

func (d *defaultDomainBurstModel) Create(item *DomainBurst) error {
	return d.DB.Create(item).Error
}

func (u *defaultDomainBurstModel) FindById(id interface{}) *DomainBurst {
	var DomainBurst DomainBurst
	err := u.DB.Where("(id = ?)", id).Find(&DomainBurst).Error
	if err != nil {
		return nil
	}
	return &DomainBurst
}

func (u *defaultDomainBurstModel) FirstOrCreate(dest, conds *DomainBurst) *gorm.DB {
	return u.DB.Table(u.table).FirstOrCreate(dest, conds)
}
