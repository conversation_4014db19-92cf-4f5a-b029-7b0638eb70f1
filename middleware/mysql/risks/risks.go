package risks

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

const TableName = "risks"

// 风险类型；1漏洞风险 2数据泄漏 3资产风险 4资产信息
const TYPE_LOOPHOLE = 1
const TYPE_INFO_LEAKAGE = 2
const TYPE_ASSET_RISK = 3
const TYPE_ASSET_INFO = 4

// 风险事件列表
type Risks struct {
	dbx.ModelFull
	UserID    int64  `gorm:"column:user_id;type:bigint(20) unsigned;not null;comment:用户id"`
	CompanyID int64  `gorm:"column:company_id;type:bigint(20) unsigned;comment:用户企业ID"`
	Type      int    `gorm:"column:type;type:tinyint(4);not null;default:1;comment:风险类型：1漏洞风险 2数据泄漏 3资产风险 4资产信息"`
	Content   string `gorm:"column:content;type:text;comment:详细信息"`
}

type RisksModel interface {
	Create(risks *Risks) error
}

type defaultRisksModel struct {
	*gorm.DB
	table string
}

func (r *Risks) TableName() string {
	return TableName
}

func NewModel(conn ...*gorm.DB) RisksModel {
	return &defaultRisksModel{mysql.GetDbClient(conn...), TableName}
}

func (d *defaultRisksModel) Create(risks *Risks) error {
	return d.Table(d.table).Create(&risks).Error
}
