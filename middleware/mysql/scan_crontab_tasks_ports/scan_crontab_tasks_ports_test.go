package scan_crontab_tasks_ports

import (
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dbx"
)

func init() {
	cfg.InitLoadCfg()
	// 设置测试环境标志
	initmysql.SetTestEnv(true)

	// 初始化数据库连接
	initmysql.GetInstance(cfg.LoadMysql())
}

func TestNewModel(t *testing.T) {
	model := NewModel()
	assert.NotNil(t, model)
}

func TestDefaultScanCrontabTasksPortModel_FindByCronTaskId(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		cronTaskId := uint64(123)
		expectedPorts := []*ScanCrontabTasksPort{
			{
				Model:         dbx.Model{Id: 1},
				CronTaskId:    cronTaskId,
				CronportsType: "port",
				CronportsId:   80,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			},
			{
				Model:         dbx.Model{Id: 2},
				CronTaskId:    cronTaskId,
				CronportsType: "port_group",
				CronportsId:   443,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			},
		}

		rows := sqlmock.NewRows([]string{
			"id", "cron_task_id", "cronports_type", "cronports_id", "created_at", "updated_at",
		})
		for _, port := range expectedPorts {
			rows.AddRow(
				port.Id, port.CronTaskId, port.CronportsType, port.CronportsId,
				port.CreatedAt, port.UpdatedAt,
			)
		}

		mock.ExpectQuery("SELECT (.+) FROM `cron_task_ports` WHERE cron_task_id = (.+)").
			WithArgs(cronTaskId).
			WillReturnRows(rows)

		result, err := model.FindByCronTaskId(cronTaskId)
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, expectedPorts[0].CronportsType, result[0].CronportsType)
		assert.Equal(t, expectedPorts[0].CronportsId, result[0].CronportsId)
		assert.Equal(t, expectedPorts[1].CronportsType, result[1].CronportsType)
		assert.Equal(t, expectedPorts[1].CronportsId, result[1].CronportsId)
		assert.Equal(t, cronTaskId, result[0].CronTaskId)
		assert.Equal(t, cronTaskId, result[1].CronTaskId)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		cronTaskId := uint64(123)

		mock.ExpectQuery("SELECT (.+) FROM `cron_task_ports` WHERE cron_task_id = (.+)").
			WithArgs(cronTaskId).
			WillReturnError(sql.ErrConnDone)

		result, err := model.FindByCronTaskId(cronTaskId)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("empty_result", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		cronTaskId := uint64(999)

		rows := sqlmock.NewRows([]string{
			"id", "cron_task_id", "cronports_type", "cronports_id", "created_at", "updated_at",
		})

		mock.ExpectQuery("SELECT (.+) FROM `cron_task_ports` WHERE cron_task_id = (.+)").
			WithArgs(cronTaskId).
			WillReturnRows(rows)

		result, err := model.FindByCronTaskId(cronTaskId)
		assert.NoError(t, err)
		assert.Len(t, result, 0)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

// 测试常量
func TestConstants(t *testing.T) {
	assert.Equal(t, "cron_task_ports", TableName)
}

// 测试结构体字段
func TestScanCrontabTasksPort_Fields(t *testing.T) {
	now := time.Now()
	port := &ScanCrontabTasksPort{
		Model: dbx.Model{
			Id:        1,
			CreatedAt: now,
			UpdatedAt: now,
		},
		CronTaskId:    123,
		CronportsType: "port_group",
		CronportsId:   8080,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	assert.Equal(t, uint64(1), port.Id)
	assert.Equal(t, uint64(123), port.CronTaskId)
	assert.Equal(t, "port_group", port.CronportsType)
	assert.Equal(t, uint64(8080), port.CronportsId)
	assert.NotNil(t, port.CreatedAt)
	assert.NotNil(t, port.UpdatedAt)
}

// 测试边界情况
func TestScanCrontabTasksPort_EdgeCases(t *testing.T) {
	t.Run("zero_cron_task_id", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		cronTaskId := uint64(0)

		rows := sqlmock.NewRows([]string{
			"id", "cron_task_id", "cronports_type", "cronports_id", "created_at", "updated_at",
		})

		mock.ExpectQuery("SELECT (.+) FROM `cron_task_ports` WHERE cron_task_id = (.+)").
			WithArgs(cronTaskId).
			WillReturnRows(rows)

		result, err := model.FindByCronTaskId(cronTaskId)
		assert.NoError(t, err)
		assert.Len(t, result, 0)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("large_cron_task_id", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		cronTaskId := uint64(9223372036854775807) // 使用int64最大值，避免高位设置的uint64

		rows := sqlmock.NewRows([]string{
			"id", "cron_task_id", "cronports_type", "cronports_id", "created_at", "updated_at",
		}).AddRow(1, cronTaskId, "port", 80, time.Now(), time.Now())

		mock.ExpectQuery("SELECT (.+) FROM `cron_task_ports` WHERE cron_task_id = (.+)").
			WithArgs(cronTaskId).
			WillReturnRows(rows)

		result, err := model.FindByCronTaskId(cronTaskId)
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, cronTaskId, result[0].CronTaskId)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

// 测试不同端口类型
func TestScanCrontabTasksPort_DifferentPortTypes(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()

	testCases := []struct {
		name          string
		cronportsType string
		cronportsId   uint64
	}{
		{
			name:          "single_port",
			cronportsType: "port",
			cronportsId:   80,
		},
		{
			name:          "port_group",
			cronportsType: "port_group",
			cronportsId:   1,
		},
		{
			name:          "custom_port",
			cronportsType: "custom",
			cronportsId:   8080,
		},
		{
			name:          "empty_type",
			cronportsType: "",
			cronportsId:   443,
		},
		{
			name:          "zero_port_id",
			cronportsType: "port",
			cronportsId:   0,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cronTaskId := uint64(123)

			rows := sqlmock.NewRows([]string{
				"id", "cron_task_id", "cronports_type", "cronports_id", "created_at", "updated_at",
			}).AddRow(1, cronTaskId, tc.cronportsType, tc.cronportsId, time.Now(), time.Now())

			mock.ExpectQuery("SELECT (.+) FROM `cron_task_ports` WHERE cron_task_id = (.+)").
				WithArgs(cronTaskId).
				WillReturnRows(rows)

			result, err := model.FindByCronTaskId(cronTaskId)
			assert.NoError(t, err)
			assert.Len(t, result, 1)
			assert.Equal(t, tc.cronportsType, result[0].CronportsType)
			assert.Equal(t, tc.cronportsId, result[0].CronportsId)
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// 测试多种端口配置组合
func TestScanCrontabTasksPort_MultiplePortConfigurations(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()

	cronTaskId := uint64(123)

	// 模拟混合端口配置：单个端口 + 端口组
	expectedPorts := []*ScanCrontabTasksPort{
		{
			Model:         dbx.Model{Id: 1},
			CronTaskId:    cronTaskId,
			CronportsType: "port",
			CronportsId:   80,
		},
		{
			Model:         dbx.Model{Id: 2},
			CronTaskId:    cronTaskId,
			CronportsType: "port",
			CronportsId:   443,
		},
		{
			Model:         dbx.Model{Id: 3},
			CronTaskId:    cronTaskId,
			CronportsType: "port_group",
			CronportsId:   1,
		},
		{
			Model:         dbx.Model{Id: 4},
			CronTaskId:    cronTaskId,
			CronportsType: "port_group",
			CronportsId:   2,
		},
	}

	rows := sqlmock.NewRows([]string{
		"id", "cron_task_id", "cronports_type", "cronports_id", "created_at", "updated_at",
	})
	for _, port := range expectedPorts {
		rows.AddRow(
			port.Id, port.CronTaskId, port.CronportsType, port.CronportsId,
			time.Now(), time.Now(),
		)
	}

	mock.ExpectQuery("SELECT (.+) FROM `cron_task_ports` WHERE cron_task_id = (.+)").
		WithArgs(cronTaskId).
		WillReturnRows(rows)

	result, err := model.FindByCronTaskId(cronTaskId)
	assert.NoError(t, err)
	assert.Len(t, result, 4)

	// 验证端口类型分布
	portCount := 0
	portGroupCount := 0
	for _, port := range result {
		if port.CronportsType == "port" {
			portCount++
		} else if port.CronportsType == "port_group" {
			portGroupCount++
		}
	}
	assert.Equal(t, 2, portCount)
	assert.Equal(t, 2, portGroupCount)
	assert.NoError(t, mock.ExpectationsWereMet())
}

// 测试模型接口实现
func TestScanCrontabTasksPortModelInterface(t *testing.T) {
	model := NewModel()
	
	// 确保实现了ScanCrontabTasksPortModel接口
	var _ ScanCrontabTasksPortModel = model
	
	// 测试接口方法存在
	assert.NotNil(t, model)
}

// 测试默认模型结构
func TestDefaultScanCrontabTasksPortModel_Structure(t *testing.T) {
	model := NewModel()
	
	// 类型断言确保返回的是正确的类型
	defaultModel, ok := model.(*defaultScanCrontabTasksPortModel)
	assert.True(t, ok)
	assert.Equal(t, TableName, defaultModel.table)
	assert.NotNil(t, defaultModel.DB)
}
