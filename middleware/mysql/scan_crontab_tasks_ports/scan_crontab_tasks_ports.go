package scan_crontab_tasks_ports

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"time"

	"gorm.io/gorm"
)

const (
	TableName = "cron_task_ports"
)

type (
	ScanCrontabTasksPort struct {
		dbx.Model
		CronTaskId    uint64    `json:"cron_task_id" gorm:"column:cron_task_id"`     // 周期任务表id
		CronportsType string    `json:"cronports_type" gorm:"column:cronports_type"` // 对应哪个表的model
		CronportsId   uint64    `json:"cronports_id" gorm:"column:cronports_id"`     // 对应ports表的id或者要扫描的端口分组id
		CreatedAt     time.Time `json:"created_at" gorm:"column:created_at"`         // 创建时间
		UpdatedAt     time.Time `json:"updated_at" gorm:"column:updated_at"`         // 更新时间
	}

	defaultScanCrontabTasksPortModel struct {
		*gorm.DB
		table string
	}

	ScanCrontabTasksPortModel interface {
		FindByCronTaskId(cronTaskId uint64) ([]*ScanCrontabTasksPort, error)
	}
)

func NewModel() ScanCrontabTasksPortModel {
	return &defaultScanCrontabTasksPortModel{
		DB:    mysql.GetDbClient(),
		table: TableName,
	}
}

// FindByCronTaskId 根据周期任务ID查询端口列表
func (m *defaultScanCrontabTasksPortModel) FindByCronTaskId(cronTaskId uint64) ([]*ScanCrontabTasksPort, error) {
	var result []*ScanCrontabTasksPort
	err := m.Table(m.table).Where("cron_task_id = ?", cronTaskId).Find(&result).Error
	return result, err
}
