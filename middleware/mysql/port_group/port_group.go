package port_group

import (
	"errors"
	"fmt"
	"micro-service/middleware/mysql"
	pb "micro-service/webService/proto"
	"time"

	"gorm.io/gorm"
)

type (
	PortGroupModel interface {
		GetIndexData(userId uint64, req *pb.PortGroupIndexRequest, Page, Size int) ([]PortGroup, int64, error)
		PortGroupAdd(req *pb.PortGroupAddRequest) error
		GetIndexList(userId uint64) ([]PortGroup, error)
		PortGroupDel(req *pb.PortGroupDelRequest) error
		PortGroupEdit(req *pb.PortGroupEditRequest) error
		FindById(Id uint64) (*PortGroup, error)
		FindByIdAndUserId(Id uint64, UserId uint64) (*PortGroup, error)
	}

	defaultPortGroupModel struct {
		*gorm.DB
		table string
	}

	PortGroup struct {
		Id        uint64    `gorm:"column:id;type:bigint(20) unsigned;AUTO_INCREMENT;primary_key" json:"id"`
		UserId    uint64    `gorm:"column:user_id;type:bigint(20) unsigned;comment:用户id;NOT NULL" json:"user_id"`
		CompanyId int64     `gorm:"column:company_id;type:bigint(20) unsigned;comment:企业ID" json:"company_id"`
		Name      string    `gorm:"column:name;index;type:varchar(300);comment:分组名称 id为1:必须是现有端口组 2:全部常用端口 3:0-65535;NOT NULL" json:"name"`
		CanDel    int32     `gorm:"column:can_del;type:tinyint(4);comment:0:不可以删除; 1:可以删除;NOT NULL" json:"can_del"`
		CreatedAt time.Time `gorm:"column:created_at;type:timestamp" json:"created_at"`
		UpdatedAt time.Time `gorm:"column:updated_at;type:timestamp" json:"updated_at"`

		Ports []*Port `gorm:"many2many:port_port_group;"`
	}
)

type PortPortGroup struct {
	Id          uint64    `gorm:"column:id;type:bigint(20) unsigned;AUTO_INCREMENT;primary_key" json:"id"`
	PortId      uint64    `gorm:"column:port_id;type:bigint(20) unsigned;comment:端口id;NOT NULL" json:"port_id"`
	PortGroupId uint64    `gorm:"column:port_group_id;type:bigint(20) unsigned;comment:端口分组id;NOT NULL" json:"port_group_id"`
	CreatedAt   time.Time `gorm:"column:created_at;type:timestamp" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;type:timestamp" json:"updated_at"`
}

func (ppg *PortPortGroup) TableName() string {
	return "port_port_group"
}

const portGrouptable = "port_groups"

const (
	DefaultGroupId = 1
	NormalGroupId  = 2
	AllPortGroupId = 3
	CanDel         = 1
)

func (m *PortGroup) TableName() string {
	return portGrouptable
}

func NewPortGroupModel(conn ...*gorm.DB) PortGroupModel {
	return &defaultPortGroupModel{mysql.GetDbClient(conn...), portGrouptable}
}

func (d *defaultPortGroupModel) GetIndexData(userId uint64, req *pb.PortGroupIndexRequest, Page, Size int) ([]PortGroup, int64, error) {
	var total int64
	//查询语法
	query := d.DB.Table(d.table)

	if req.UserId != 0 {
		query = query.Where("user_id = ?", req.UserId)
	}
	if req.Keyword != "" {
		query = query.Where("name LIKE ?", fmt.Sprintf("%%%s%%", req.Keyword))
	}

	totalQuery := query

	//统计数量综合总和
	err := totalQuery.Count(&total).Error
	if err != nil {
		return []PortGroup{}, 0, err
	}

	var r []PortGroup
	err = query.Preload("Ports").Order("id desc").Scopes(PortGroupPaginate(Page, Size)).Find(&r).Error
	if err != nil {
		return []PortGroup{}, 0, err
	}

	data := make([]PortGroup, 0, len(r))
	data = append(data, r...)
	return data, total, nil
}

func (d *defaultPortGroupModel) PortGroupAdd(req *pb.PortGroupAddRequest) error {
	//查询语法
	portGroupObj := PortGroup{}
	d.DB.Table(d.table).Where("user_id = ? AND name = ?", req.UserId, req.Name).First(&portGroupObj)
	if portGroupObj.Id == 0 {
		portGroupObj.Name = req.Name
		portGroupObj.CanDel = 1
		portGroupObj.UserId = req.UserId
		portGroupObj.CompanyId = int64(req.OperateCompanyId)
		portGroupObj.CreatedAt = time.Now()
		portGroupObj.UpdatedAt = time.Now()
		err := d.DB.Table(d.table).Create(&portGroupObj).Error
		if err != nil {
			return err
		}
		if len(req.Ports) > 0 && req.Ports[0] != 0 {
			for _, port := range req.Ports {
				portPortGroupInfo := &PortPortGroup{
					PortId:      port,
					PortGroupId: portGroupObj.Id,
				}
				d.DB.Table("port_port_group").Create(portPortGroupInfo)
			}
		}
	} else {
		//此处主动抛异常
		err := errors.New("分组已存在")
		if err != nil {
			return err
		}
	}
	return nil
}

func (d *defaultPortGroupModel) PortGroupEdit(req *pb.PortGroupEditRequest) error {
	//查询语法
	portGroupObj := PortGroup{}
	d.DB.Table(d.table).Where("user_id = ? AND name = ? AND id != ?", req.UserId, req.Name, req.Id).First(&portGroupObj)
	if portGroupObj.Id == 0 {
		portGroupObj.Name = req.Name
		portGroupObj.UpdatedAt = time.Now()
		err := d.DB.Table(d.table).Where("user_id = ? AND id = ?", req.UserId, req.Id).Update("name", req.Name).Error
		if err != nil {
			return err
		}

		d.DB.Table("port_port_group").Where("port_group_id = ?", req.Id).Delete(&PortPortGroup{})

		if len(req.Ports) > 0 && req.Ports[0] != 0 {
			for _, port := range req.Ports {
				portPortGroupInfo := &PortPortGroup{
					PortId:      port,
					PortGroupId: req.Id,
				}
				d.DB.Table("port_port_group").Create(portPortGroupInfo)
			}
		}
	} else {
		//此处主动抛异常
		err := errors.New("分组已存在")
		if err != nil {
			return err
		}
	}
	return nil
}

func (d *defaultPortGroupModel) GetIndexList(userId uint64) ([]PortGroup, error) {
	query := d.DB.Table(d.table)
	if userId != 0 {
		query = query.Where("user_id = ?", userId)
	}
	var r []PortGroup
	err := query.Select("id,name").Order("id asc").Find(&r).Error
	if err != nil {
		return []PortGroup{}, err
	}
	data := make([]PortGroup, 0, len(r))
	data = append(data, r...)
	return data, nil
}

func (d *defaultPortGroupModel) PortGroupDel(req *pb.PortGroupDelRequest) error {
	var canDelIds []uint64
	if len(req.Id) > 0 && req.Id[0] != 0 {
		d.DB.Table(d.table).Where("id IN (?) and user_id = ? and can_del = ?", req.Id, req.UserId, CanDel).Pluck("id", &canDelIds)
	} else {
		if req.Keyword != "" {
			d.DB.Table(d.table).Where("name LIKE ? and user_id = ? and can_del = ?", fmt.Sprintf("%%%s%%", req.Keyword), req.UserId, CanDel).Pluck("id", &canDelIds)
		}
	}

	if len(canDelIds) > 0 {
		d.DB.Table(d.table).Where("id IN (?) and user_id = ? and can_del = ?", canDelIds, req.UserId, CanDel).Delete(&PortGroup{})
		d.DB.Table("port_port_group").Where("port_group_id IN (?)", canDelIds).Delete(&PortPortGroup{})
	}

	return nil
}

func (d *defaultPortGroupModel) FindByIdAndUserId(Id uint64, UserId uint64) (*PortGroup, error) {
	var v PortGroup
	query := d.DB.Table(d.table)
	err := query.Preload("Ports").Where("user_id = ? and id = ?", UserId, Id).Order("id desc").Find(&v).Error
	return &v, err
}

func (d *defaultPortGroupModel) FindById(Id uint64) (*PortGroup, error) {
	var v PortGroup
	query := d.DB.Table(d.table)
	err := query.Preload("Ports").Where("id = ?", Id).Order("id desc").Find(&v).Error
	return &v, err
}

func PortGroupPaginate(page, size int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		offset := (page - 1) * size
		if offset < 0 {
			offset = 0
		}
		return db.Offset(offset).Limit(size)
	}
}
