package port_group

import (
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	pb "micro-service/webService/proto"
)

func init() {
	cfg.InitLoadCfg()
	// 设置测试环境标志
	initmysql.SetTestEnv(true)

	// 初始化数据库连接
	initmysql.GetInstance(cfg.LoadMysql())
}

// setupPortTestModel 创建测试用的模型实例
func setupPortTestModel(t *testing.T) (PortModel, sqlmock.Sqlmock) {
	// 为每个测试创建新的Mock实例
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to create mock: %v", err)
	}

	// Mock GORM 初始化时的查询
	mock.ExpectQuery("SELECT VERSION\\(\\)").WillReturnRows(sqlmock.NewRows([]string{"VERSION()"}).AddRow("8.0.0"))

	// 创建GORM实例
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: false,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to create gorm instance: %v", err)
	}

	model := &defaultPortModel{
		DB:    gormDB,
		table: portTable,
	}
	return model, mock
}

func TestPort_TableName(t *testing.T) {
	port := &Port{}
	assert.Equal(t, portTable, port.TableName())
}

func TestPortProtocol_TableName(t *testing.T) {
	protocol := &PortProtocol{}
	assert.Equal(t, "port_protocols", protocol.TableName())
}

func TestNewPortModel(t *testing.T) {
	model := NewPortModel()
	assert.NotNil(t, model)
}

func TestDefaultPortModel_FindById(t *testing.T) {
	t.Run("success_without_detail", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		userId := uint64(123)
		portId := uint64(1)
		now := time.Now()

		rows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at",
		}).AddRow(portId, userId, 456, 80, 0, 1, now, now)

		mock.ExpectQuery("SELECT (.+) FROM `ports` WHERE user_id = (.+) and id = (.+) ORDER BY id desc").
			WithArgs(userId, portId).
			WillReturnRows(rows)

		result, err := model.FindById(portId, userId, false)
		assert.NoError(t, err)
		assert.Equal(t, portId, result.Id)
		assert.Equal(t, userId, result.UserId)
		assert.Equal(t, uint32(80), result.Port)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_with_detail", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		userId := uint64(123)
		portId := uint64(1)
		now := time.Now()

		// 主查询
		rows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at",
		}).AddRow(portId, userId, 456, 80, 0, 1, now, now)

		mock.ExpectQuery("SELECT (.+) FROM `ports` WHERE user_id = (.+) and id = (.+) ORDER BY id desc").
			WithArgs(userId, portId).
			WillReturnRows(rows)

		// Mock Groups preload
		groupJoinRows := sqlmock.NewRows([]string{
			"port_port_group.port_id", "port_port_group.port_group_id",
		})
		mock.ExpectQuery("SELECT \\* FROM `port_port_group` WHERE `port_port_group`.`port_id` = \\?").
			WithArgs(portId).
			WillReturnRows(groupJoinRows)

		// Mock Protocols preload
		protocolJoinRows := sqlmock.NewRows([]string{
			"port_port_protocol.port_id", "port_port_protocol.port_protocol_id",
		})
		mock.ExpectQuery("SELECT \\* FROM `port_port_protocol` WHERE `port_port_protocol`.`port_id` = \\?").
			WithArgs(portId).
			WillReturnRows(protocolJoinRows)

		result, err := model.FindById(portId, userId, true)
		assert.NoError(t, err)
		assert.Equal(t, portId, result.Id)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		userId := uint64(123)
		portId := uint64(1)

		mock.ExpectQuery("SELECT (.+) FROM `ports` WHERE user_id = (.+) and id = (.+) ORDER BY id desc").
			WithArgs(userId, portId).
			WillReturnError(sql.ErrConnDone)

		_, err := model.FindById(portId, userId, false)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultPortModel_GetIndexList(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		userId := uint64(123)

		rows := sqlmock.NewRows([]string{
			"id", "port",
		}).
			AddRow(1, 80).
			AddRow(2, 443)

		mock.ExpectQuery("SELECT id,port FROM `ports` WHERE user_id = (.+) ORDER BY updated_at desc").
			WithArgs(userId).
			WillReturnRows(rows)

		result, err := model.GetIndexList(userId)
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, uint64(1), result[0].Id)
		assert.Equal(t, uint32(80), result[0].Port)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		userId := uint64(123)

		mock.ExpectQuery("SELECT id,port FROM `ports` WHERE user_id = (.+) ORDER BY updated_at desc").
			WithArgs(userId).
			WillReturnError(sql.ErrConnDone)

		_, err := model.GetIndexList(userId)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultPortModel_CountByIds(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		ids := []uint64{1, 2, 3}
		status := int32(0)
		expectedCount := int64(2)

		countRows := sqlmock.NewRows([]string{"count(*)"}).AddRow(expectedCount)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `ports` WHERE id IN \\((.+)\\) and status = (.+)").
			WithArgs(ids[0], ids[1], ids[2], status).
			WillReturnRows(countRows)

		count := model.CountByIds(ids, status)
		assert.Equal(t, int(expectedCount), count)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		ids := []uint64{1, 2, 3}
		status := int32(0)

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `ports` WHERE id IN \\((.+)\\) and status = (.+)").
			WithArgs(ids[0], ids[1], ids[2], status).
			WillReturnError(sql.ErrConnDone)

		count := model.CountByIds(ids, status)
		assert.Equal(t, 0, count)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultPortModel_GetByIds(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		ids := []uint64{1, 2}
		status := int32(0)
		now := time.Now()

		rows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at",
		}).
			AddRow(1, 123, 456, 80, 0, 1, now, now).
			AddRow(2, 123, 456, 443, 0, 1, now, now)

		mock.ExpectQuery("SELECT (.+) FROM `ports` WHERE id IN \\((.+)\\) and status = (.+)").
			WithArgs(ids[0], ids[1], status).
			WillReturnRows(rows)

		result, err := model.GetByIds(ids, status)
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, uint64(1), result[0].Id)
		assert.Equal(t, uint64(2), result[1].Id)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		ids := []uint64{1, 2}
		status := int32(0)

		mock.ExpectQuery("SELECT (.+) FROM `ports` WHERE id IN \\((.+)\\) and status = (.+)").
			WithArgs(ids[0], ids[1], status).
			WillReturnError(sql.ErrConnDone)

		_, err := model.GetByIds(ids, status)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultPortModel_CountByUserIds(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		userId := uint64(123)
		status := int32(0)
		expectedCount := int64(5)

		countRows := sqlmock.NewRows([]string{"count(*)"}).AddRow(expectedCount)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `ports` WHERE user_id = (.+) and status = (.+)").
			WithArgs(userId, status).
			WillReturnRows(countRows)

		count := model.CountByUserIds(userId, status)
		assert.Equal(t, int(expectedCount), count)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		userId := uint64(123)
		status := int32(0)

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `ports` WHERE user_id = (.+) and status = (.+)").
			WithArgs(userId, status).
			WillReturnError(sql.ErrConnDone)

		count := model.CountByUserIds(userId, status)
		assert.Equal(t, 0, count)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultPortModel_FindWithProtocolsByIDs(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		ids := []uint64{1, 2}
		now := time.Now()

		// 主查询
		rows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at",
		}).
			AddRow(1, 123, 456, 80, 0, 1, now, now).
			AddRow(2, 123, 456, 443, 0, 1, now, now)

		mock.ExpectQuery("SELECT (.+) FROM `ports` WHERE id IN \\((.+)\\)").
			WithArgs(ids[0], ids[1]).
			WillReturnRows(rows)

		// Mock Protocols preload
		protocolJoinRows := sqlmock.NewRows([]string{
			"port_port_protocol.port_id", "port_port_protocol.port_protocol_id",
		})
		mock.ExpectQuery("SELECT (.+) FROM `port_port_protocol` WHERE `port_port_protocol`.`port_id` IN (.+)").
			WillReturnRows(protocolJoinRows)

		result, err := model.FindWithProtocolsByIDs(ids)
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, uint64(1), result[0].Id)
		assert.Equal(t, uint64(2), result[1].Id)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		ids := []uint64{1, 2}

		mock.ExpectQuery("SELECT (.+) FROM `ports` WHERE id IN \\((.+)\\)").
			WithArgs(ids[0], ids[1]).
			WillReturnError(sql.ErrConnDone)

		_, err := model.FindWithProtocolsByIDs(ids)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultPortModel_GetPortProtocolList(t *testing.T) {
	t.Run("success_with_keyword", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		req := &pb.PortProtocolIndexRequest{
			Keyword: "HTTP",
		}
		now := time.Now()

		rows := sqlmock.NewRows([]string{
			"id", "protocol", "type", "created_at", "updated_at",
		}).
			AddRow(1, "HTTP", 0, now, now).
			AddRow(2, "HTTPS", 0, now, now)

		mock.ExpectQuery("SELECT (.+) FROM `port_protocols` WHERE protocol LIKE (.+) ORDER BY updated_at desc").
			WithArgs("%" + req.Keyword + "%").
			WillReturnRows(rows)

		result, err := model.GetPortProtocolList(req)
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, "HTTP", result[0].Protocol)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_without_keyword", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		req := &pb.PortProtocolIndexRequest{}
		now := time.Now()

		rows := sqlmock.NewRows([]string{
			"id", "protocol", "type", "created_at", "updated_at",
		}).
			AddRow(1, "TCP", 0, now, now)

		mock.ExpectQuery("SELECT (.+) FROM `port_protocols` ORDER BY updated_at desc").
			WillReturnRows(rows)

		result, err := model.GetPortProtocolList(req)
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, "TCP", result[0].Protocol)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		req := &pb.PortProtocolIndexRequest{}

		mock.ExpectQuery("SELECT (.+) FROM `port_protocols` ORDER BY updated_at desc").
			WillReturnError(sql.ErrConnDone)

		_, err := model.GetPortProtocolList(req)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestArrayDiff(t *testing.T) {
	t.Run("normal_case", func(t *testing.T) {
		a := []uint64{1, 2, 3, 4, 5}
		b := []uint64{2, 4}
		expected := []uint64{1, 3, 5}

		result := arrayDiff(a, b)
		assert.ElementsMatch(t, expected, result)
	})

	t.Run("empty_b", func(t *testing.T) {
		a := []uint64{1, 2, 3}
		b := []uint64{}
		expected := []uint64{1, 2, 3}

		result := arrayDiff(a, b)
		assert.ElementsMatch(t, expected, result)
	})

	t.Run("empty_a", func(t *testing.T) {
		a := []uint64{}
		b := []uint64{1, 2, 3}
		expected := []uint64{}

		result := arrayDiff(a, b)
		assert.ElementsMatch(t, expected, result)
	})
}

func TestPortPaginate(t *testing.T) {
	t.Run("normal_pagination", func(t *testing.T) {
		page := 2
		size := 10

		// 创建一个模拟的 GORM DB
		db, mock, err := sqlmock.New()
		assert.NoError(t, err)
		defer db.Close()

		mock.ExpectQuery("SELECT VERSION\\(\\)").WillReturnRows(sqlmock.NewRows([]string{"VERSION()"}).AddRow("8.0.0"))

		gormDB, err := gorm.Open(mysql.New(mysql.Config{
			Conn:                      db,
			SkipInitializeWithVersion: false,
		}), &gorm.Config{})
		assert.NoError(t, err)

		// 应用分页函数
		result := PortPaginate(page, size)(gormDB)

		// 验证分页参数是否正确设置
		// 这里我们无法直接验证内部状态，但可以确保函数不会panic
		assert.NotNil(t, result)
	})

	t.Run("negative_page", func(t *testing.T) {
		page := -1
		size := 10

		db, mock, err := sqlmock.New()
		assert.NoError(t, err)
		defer db.Close()

		mock.ExpectQuery("SELECT VERSION\\(\\)").WillReturnRows(sqlmock.NewRows([]string{"VERSION()"}).AddRow("8.0.0"))

		gormDB, err := gorm.Open(mysql.New(mysql.Config{
			Conn:                      db,
			SkipInitializeWithVersion: false,
		}), &gorm.Config{})
		assert.NoError(t, err)

		// 应用分页函数，负数页码应该被处理为0
		result := PortPaginate(page, size)(gormDB)
		assert.NotNil(t, result)
	})
}

func TestDefaultPortModel_PortDel(t *testing.T) {
	t.Run("success_with_ids", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		req := &pb.PortDelRequest{
			Ids:    []uint64{1, 2},
			UserId: 123,
		}

		// Mock查询内置端口
		builtInRows := sqlmock.NewRows([]string{"id"}).AddRow(1)
		mock.ExpectQuery("SELECT (.+) FROM `ports` WHERE user_id = (.+) AND source = (.+)").
			WithArgs(req.UserId, StatusDefault).
			WillReturnRows(builtInRows)

		// Mock删除协议关联
		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `port_port_protocol` WHERE port_id IN \\(\\?\\)").
			WithArgs(2). // 只有ID为2的端口会被删除（1是内置的）
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		// Mock删除分组关联
		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `port_port_group` WHERE port_id IN \\(\\?\\)").
			WithArgs(2).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		// Mock删除端口
		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `ports` WHERE user_id = \\? AND id IN \\(\\?\\)").
			WithArgs(req.UserId, 2).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err := model.PortDel(req)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_with_keyword", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		req := &pb.PortDelRequest{
			Keyword: "80",
			UserId:  123,
		}

		// Mock查询符合条件的端口ID
		portIdRows := sqlmock.NewRows([]string{"id"}).AddRow(1).AddRow(2)
		mock.ExpectQuery("SELECT id FROM `ports`").
			WillReturnRows(portIdRows)

		// Mock查询内置端口
		builtInRows := sqlmock.NewRows([]string{"id"}).AddRow(1)
		mock.ExpectQuery("SELECT (.+) FROM `ports` WHERE user_id = (.+) AND source = (.+)").
			WithArgs(req.UserId, StatusDefault).
			WillReturnRows(builtInRows)

		// Mock删除操作
		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `port_port_protocol` WHERE port_id IN \\(\\?\\)").
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `port_port_group` WHERE port_id IN \\(\\?\\)").
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `ports` WHERE user_id = \\? AND id IN \\(\\?\\)").
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err := model.PortDel(req)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultPortModel_PortStore(t *testing.T) {
	t.Run("success_new_port", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		req := &PortChangeParam{
			UserId:       123,
			CompanyId:    456,
			Ports:        []uint64{80},
			ProtocolIds:  []uint64{1},
			PortGroupIds: []uint64{2},
		}

		// Mock查询现有端口（不存在）
		emptyRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at",
		})
		mock.ExpectQuery("SELECT (.+) FROM `ports` WHERE user_id = (.+) AND port = (.+) ORDER BY `ports`.`id` LIMIT (.+)").
			WithArgs(req.UserId, req.Ports[0], 1).
			WillReturnRows(emptyRows)

		// Mock创建端口
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `ports`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock创建协议关联 - FirstOrCreate会先查询再插入
		mock.ExpectQuery("SELECT (.+) FROM `port_port_protocol` WHERE `port_port_protocol`.`port_id` = (.+) AND `port_port_protocol`.`port_protocol_id` = (.+)").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_protocol_id", "created_at", "updated_at"}))
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `port_port_protocol`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock创建分组关联 - FirstOrCreate会先查询再插入
		mock.ExpectQuery("SELECT (.+) FROM `port_port_group` WHERE `port_port_group`.`port_id` = (.+) AND `port_port_group`.`port_group_id` = (.+)").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_group_id", "created_at", "updated_at"}))
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `port_port_group`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock创建默认分组关联 - FirstOrCreate会先查询再插入
		mock.ExpectQuery("SELECT (.+) FROM `port_port_group` WHERE `port_port_group`.`port_id` = (.+) AND `port_port_group`.`port_group_id` = (.+)").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_group_id", "created_at", "updated_at"}))
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `port_port_group`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		err := model.PortStore(req)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_existing_port", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		req := &PortChangeParam{
			UserId:       123,
			CompanyId:    456,
			Ports:        []uint64{80},
			ProtocolIds:  []uint64{1},
			PortGroupIds: []uint64{2},
		}

		now := time.Now()
		// Mock查询现有端口（存在）
		existingRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at",
		}).AddRow(1, req.UserId, req.CompanyId, 80, 0, 1, now, now)

		mock.ExpectQuery("SELECT (.+) FROM `ports` WHERE user_id = (.+) AND port = (.+) ORDER BY `ports`.`id` LIMIT (.+)").
			WithArgs(req.UserId, req.Ports[0], 1).
			WillReturnRows(existingRows)

		// Mock创建协议关联 - FirstOrCreate会先查询再插入
		mock.ExpectQuery("SELECT (.+) FROM `port_port_protocol` WHERE `port_port_protocol`.`port_id` = (.+) AND `port_port_protocol`.`port_protocol_id` = (.+)").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_protocol_id", "created_at", "updated_at"}))
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `port_port_protocol`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock创建分组关联 - FirstOrCreate会先查询再插入
		mock.ExpectQuery("SELECT (.+) FROM `port_port_group` WHERE `port_port_group`.`port_id` = (.+) AND `port_port_group`.`port_group_id` = (.+)").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_group_id", "created_at", "updated_at"}))
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `port_port_group`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock创建默认分组关联 - FirstOrCreate会先查询再插入
		mock.ExpectQuery("SELECT (.+) FROM `port_port_group` WHERE `port_port_group`.`port_id` = (.+) AND `port_port_group`.`port_group_id` = (.+)").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_group_id", "created_at", "updated_at"}))
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `port_port_group`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		err := model.PortStore(req)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultPortModel_FindProtocolById(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		protocolId := uint64(1)
		now := time.Now()

		rows := sqlmock.NewRows([]string{
			"id", "protocol", "type", "created_at", "updated_at",
		}).AddRow(protocolId, "HTTP", 0, now, now)

		mock.ExpectQuery("SELECT (.+) FROM `port_protocols` WHERE id = (.+) ORDER BY `port_protocols`.`id` LIMIT (.+)").
			WithArgs(protocolId, 1).
			WillReturnRows(rows)

		result, err := model.FindProtocolById(protocolId)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, protocolId, result.Id)
		assert.Equal(t, "HTTP", result.Protocol)
		assert.Equal(t, int32(0), result.Type)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not_found", func(t *testing.T) {
		model, mock := setupPortTestModel(t)

		protocolId := uint64(999)

		mock.ExpectQuery("SELECT (.+) FROM `port_protocols` WHERE id = (.+) ORDER BY `port_protocols`.`id` LIMIT (.+)").
			WithArgs(protocolId, 1).
			WillReturnError(gorm.ErrRecordNotFound)

		result, err := model.FindProtocolById(protocolId)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
