package cert_assets

import (
	"micro-service/middleware/mysql"
	"time"

	"gorm.io/gorm"
)

// 证书资产
type (
	CertAssetsModel interface {
		CountByUserID(userID uint64, start, end string, isValid ...int) (int64, error)
		List(page, size int64, keywordOpt []mysql.HandleFunc, opts ...mysql.HandleFunc) ([]*CertAssets, int64, error)
		ListAll(keywordOpt []mysql.HandleFunc, opts ...mysql.HandleFunc) ([]*CertAssets, error)
		ListDistinctCompany(userID uint64, opts ...mysql.HandleFunc) ([]string, error)
		Delete(keywordOpt []mysql.HandleFunc, opts ...mysql.HandleFunc) error
		FirstOrCreate(certAsset *CertAssets, opts ...mysql.HandleFunc) error
		Update(id uint64, m map[string]any) error
	}

	defaultCertAssetsModel struct {
		*gorm.DB
		table string
	}

	CertAssets struct {
		gorm.Model
		UserId           uint64    `gorm:"column:user_id;type:bigint(20) unsigned;comment:用户id;NOT NULL" json:"user_id"`
		CompanyId        uint64    `gorm:"column:company_id;type:bigint(20) unsigned;comment:用户企业ID" json:"company_id"`
		CompanyName      string    `gorm:"column:company_name;type:varchar(300);comment:企业名称" json:"company_name"`
		Sn               string    `gorm:"column:sn;type:varchar(300);comment:证书序列号;NOT NULL" json:"sn"`
		IssuerCn         string    `gorm:"column:issuer_cn;type:varchar(300);comment:颁发者cn" json:"issuer_cn"`
		IssuerCns        string    `gorm:"column:issuer_cns;type:text;comment:颁发者cns" json:"issuer_cns"`
		IssuerOrg        string    `gorm:"column:issuer_org;type:text;comment:颁发者org" json:"issuer_org"`
		IssuerOu         string    `gorm:"column:issuer_ou;type:varchar(300);comment:颁发者组织单位" json:"issuer_ou"`
		Cert             string    `gorm:"column:cert;type:text;comment:证书内容;NOT NULL" json:"cert"`
		CertNum          int       `gorm:"column:cert_num;type:tinyint(4);default:0;comment:证书数量;NOT NULL" json:"cert_num"`
		SigAlth          string    `gorm:"column:sig_alth;type:varchar(255);comment:签名算法" json:"sig_alth"`
		SubjectCn        string    `gorm:"column:subject_cn;type:varchar(255);comment:使用者cn" json:"subject_cn"`
		SubjectOrg       string    `gorm:"column:subject_org;type:varchar(255);comment:使用者org" json:"subject_org"`
		SubjectOu        string    `gorm:"column:subject_ou;type:varchar(300);comment:使用者单位" json:"subject_ou"`
		SubjectKey       string    `gorm:"column:subject_key;type:varchar(255);comment:使用者key" json:"subject_key"`
		ValidType        string    `gorm:"column:valid_type;type:varchar(255);comment:验证类型" json:"valid_type"`
		IsValid          int       `gorm:"column:is_valid;type:tinyint(1);default:0;comment:证书是否可信 false/true 不可信/可信;NOT NULL" json:"is_valid"`
		Version          string    `gorm:"column:version;type:varchar(255);comment:证书版本" json:"version"`
		Sha256           string    `gorm:"column:sha256;type:text;comment:SHA-256指纹" json:"sha256"`
		Sha1             string    `gorm:"column:sha1;type:text;comment:SHA-1指纹" json:"sha1"`
		CertDate         string    `gorm:"column:cert_date;type:varchar(255);comment:证书日期" json:"cert_date"`
		NotBefore        string    `gorm:"column:not_before;type:varchar(255);comment:颁发日期" json:"not_before"`
		NotAfter         string    `gorm:"column:not_after;type:varchar(255);comment:截止日期" json:"not_after"`
		CreatedAt        time.Time `gorm:"column:created_at;type:timestamp" json:"created_at"`
		DetectTaskId     uint64    `gorm:"column:detect_task_id;type:bigint(20) unsigned;comment:检测任务ID" json:"detect_task_id"`
		WebsiteMessageId uint64    `gorm:"column:website_message_id;type:bigint(20) unsigned;comment:网站消息ID" json:"website_message_id"`
		IsSelfSign       int       `gorm:"column:is_self_sign;type:tinyint(1);default:0;comment:是否自签名证书;NOT NULL" json:"is_self_sign"`
	}
)

const table = "cert_assets"

func (m *CertAssets) TableName() string {
	return table
}

func NewModel(conn ...*gorm.DB) CertAssetsModel {
	return &defaultCertAssetsModel{mysql.GetDbClient(conn...), table}
}

func (d *defaultCertAssetsModel) FirstOrCreate(certAsset *CertAssets, opts ...mysql.HandleFunc) error {
	q := d.DB.Model(&CertAssets{})
	for _, opt := range opts {
		opt(q)
	}
	return q.FirstOrCreate(certAsset).Error
}

func (d *defaultCertAssetsModel) CountByUserID(userID uint64, start, end string, isValid ...int) (int64, error) {
	var total int64
	q := d.DB.Model(&CertAssets{})
	q.Where("user_id = ?  ", userID)
	if len(isValid) > 0 {
		q.Where("is_valid = ?  ", isValid[0])
	}

	if start != "" && end != "" {
		q.Where("created_at between ? and ?", start, end)

	}
	e := q.Count(&total).Error
	return total, e
}

func (d *defaultCertAssetsModel) List(page, size int64, keywordOpt []mysql.HandleFunc, opts ...mysql.HandleFunc) ([]*CertAssets, int64, error) {
	q := d.DB.Model(&CertAssets{})
	q.Joins("LEFT JOIN cert_ip_domains ON cert_ip_domains.cert_assets_id = cert_assets.id")
	// 关键字筛选
	if len(keywordOpt) > 0 {
		k := d.DB.Model(&CertAssets{})
		for _, opt := range keywordOpt {
			opt(k)
		}
		q.Where(k)
	}
	// 剩余条件筛选
	for _, opt := range opts {
		opt(q)
	}
	q.Group("cert_assets.id")
	var total int64
	var list = make([]*CertAssets, 0, size)
	if !mysql.IsPageAll(int(page), int(size)) {
		q.Count(&total).Scopes(mysql.PageLimit(int(page), int(size)))
	}
	err := q.Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (d *defaultCertAssetsModel) ListAll(keywordOpt []mysql.HandleFunc, opts ...mysql.HandleFunc) ([]*CertAssets, error) {
	q := d.DB.Model(&CertAssets{})
	q.Joins("LEFT JOIN cert_ip_domains ON cert_ip_domains.cert_assets_id = cert_assets.id")
	// 关键字筛选
	if len(keywordOpt) > 0 {
		k := d.DB.Model(&CertAssets{})
		for _, opt := range keywordOpt {
			opt(k)
		}
		q.Where(k)
	}
	// 剩余条件筛选
	for _, opt := range opts {
		opt(q)
	}
	q.Group("cert_assets.id")
	var list = make([]*CertAssets, 0)
	err := q.Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (d *defaultCertAssetsModel) ListDistinctCompany(userID uint64, opts ...mysql.HandleFunc) ([]string, error) {
	q := d.DB.Model(&CertAssets{}).Select("DISTINCT company_name")
	q.Where("user_id = ?", userID)
	for _, opt := range opts {
		opt(q)
	}
	var list = make([]string, 0)
	err := q.Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (d *defaultCertAssetsModel) Delete(keywordOpt []mysql.HandleFunc, opts ...mysql.HandleFunc) error {
	q := d.DB.Model(&CertAssets{})
	q.Joins("LEFT JOIN cert_ip_domains ON cert_ip_domains.cert_assets_id = cert_assets.id")
	// 关键字筛选
	if len(keywordOpt) > 0 {
		k := d.DB.Model(&CertAssets{})
		for _, opt := range keywordOpt {
			opt(k)
		}
		q.Where(k)
	}
	// 剩余条件筛选
	for _, opt := range opts {
		opt(q)
	}
	q.Group("cert_assets.id")
	list := make([]uint64, 0)
	err := q.Select("cert_assets.id").Find(&list).Error
	if err != nil {
		return err
	}
	if len(list) > 0 {
		err = d.DB.Where("id IN (?)", list).Delete(&CertAssets{}).Error
		if err != nil {
			return err
		}
	}
	return nil
}

func (d *defaultCertAssetsModel) Update(id uint64, m map[string]any) error {
	return d.DB.Model(&CertAssets{}).Where("id = ?", id).Updates(m).Error
}
