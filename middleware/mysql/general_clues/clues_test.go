package general_clues

import (
	"fmt"
	"testing"

	mysqldb "micro-service/initialize/mysql"
	"micro-service/middleware/mysql"
	"micro-service/pkg/cfg"

	"github.com/stretchr/testify/assert"
)

func initCfg() {
	cfg.InitLoadCfg()
	_ = mysqldb.GetInstance(cfg.LoadMysql())
}

func TestList(t *testing.T) {
	NewGeneralCluesModel().ListByDiscoverGroupTask(0, 0, 1, 1)
}

func Test_ClueFilterGroup(t *testing.T) {
	initCfg()

	x, err := NewGeneralCluesModel().ClueFilterGroup("company_name", mysql.WithColumnValue("platform", "FOFA"))
	assert.Nil(t, err)
	fmt.Println(x)
}
