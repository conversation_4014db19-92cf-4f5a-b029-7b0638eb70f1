package general_clues

import (
	"gorm.io/gorm"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

type (
	GeneralCluesTaskModel interface {
	}

	defaultGeneralCluesTaskModel struct {
		*gorm.DB
		table string
	}

	GeneralCluesTask struct {
		dbx.Model
		TaskId uint64 `gorm:"column:task_id;type:varchar(128);comment:任务ID;NOT NULL;index;" json:"task_id"`
		ClueId uint64 `gorm:"column:clue_id;type:bigint(20);comment:父级ID;DEFAULT 0;index;" json:"clue_id"`
	}
)

const generalCluesTaskTable = "general_clues_task"

// TableName 表名
func (g *GeneralCluesTask) TableName() string {
	return generalCluesTaskTable
}

func NewGeneralCluesTaskModel(clients ...*gorm.DB) *defaultGeneralCluesTaskModel {
	return &defaultGeneralCluesTaskModel{mysql.GetDbClient(clients...), generalCluesTaskTable}
}

func (d *defaultGeneralCluesTaskModel) ClearByTask(taskId uint64) error {
	if taskId == 0 {
		return nil
	}
	return d.DB.Table(generalCluesTaskTable).Where("task_id = ?", taskId).Delete(&GeneralCluesTask{}).Error
}

func (d *defaultGeneralCluesTaskModel) AddTaskClue(taskId uint64, clue *GeneralClues) *GeneralCluesTask {
	if clue == nil {
		return nil
	}
	relation := &GeneralCluesTask{TaskId: taskId, ClueId: clue.Id}
	if err := d.DB.Table(generalCluesTaskTable).FirstOrCreate(relation, relation).Error; err != nil {
		return nil
	} else {
		return relation
	}
}
