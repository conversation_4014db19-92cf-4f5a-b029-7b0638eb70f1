package mysql

import (
	"time"

	"gorm.io/gorm"
)

type JoinType string

const (
	InnerJoin JoinType = "INNER"
	LeftJoin  JoinType = "LEFT"
	RightJoin JoinType = "RIGHT"
)

type JoinCondition struct {
	Type  JoinType    // JOIN 类型
	Table interface{} // 关联表名
	On    interface{} // ON 条件
	Alias string      // 表别名
	Args  []interface{}
}

// BetweenCondition 区间查询条件类型
type BetweenCondition struct {
	Field string
	Start interface{}
	End   interface{}
}

// InCondition IN查询条件
type InCondition struct {
	Field  string
	Values interface{} // 支持 []interface{} 或 *QueryBuilder (子查询)
}

// NotInCondition IN查询条件
type NotInCondition struct {
	Field  string
	Values interface{} // 支持 []interface{} 或 *QueryBuilder (子查询)
}

// ExistsCondition EXISTS查询条件
type ExistsCondition struct {
	SubQuery *QueryBuilder
	Not      bool
}

// SubQueryCondition 子查询条件
type SubQueryCondition struct {
	Field    string
	Operator string // =, >, IN 等
	Query    *QueryBuilder
}

// OrConditions OR查询结构体
type OrConditions struct {
	Conditions []Condition
}

// Condition 定义基础条件接口
type Condition interface{}

// CompareCond 定义比较条件结构体
type CompareCond struct {
	Field    string
	Operator string
	Value    interface{}
}

// RawCondition 直接字符串类型，如： id = ?
type RawCondition struct {
	Query string
	Args  []interface{}
}

// QueryBuilder 结构体
type QueryBuilder struct {
	Table   string
	Where   []Condition
	OrderBy []string
	GroupBy []string
	Select  []string
	Joins   []JoinCondition // JOIN 条件
	Limit   int
	Offset  int
}

type BaseDSL[T any] struct {
	Id uint64   `gorm:"primaryKey;autoIncrement;comment:id" json:"id"`
	DB *gorm.DB `gorm:"-" json:"-"`
}
type BaseTimestampDSL struct {
	CreatedAt time.Time `gorm:"autoCreateTime;comment:创建时间;column:created_at" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime;comment:更新时间;column:updated_at" json:"updated_at"`
}
type BaseSoftDeleteDSL struct {
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;comment:删除事件" json:"deleted_at"`
}

type DSL interface {
	Query(qb *QueryBuilder) ([]any, error)
	One(qb *QueryBuilder) (any, error)
	FindByID(id uint64) (any, error)
	Total(qb *QueryBuilder) (int64, error)
	Page(qb *QueryBuilder) ([]any, int64, error)
	UpdateByID(id uint64, data map[string]interface{}) (int64, error)
	Update(qb *QueryBuilder, data map[string]interface{}) (int64, error)
	SoftDelete(qb *QueryBuilder) (int64, error)
	DangerousDelete(qb *QueryBuilder) (int64, error)
	Create(data any) (any, error)
}
