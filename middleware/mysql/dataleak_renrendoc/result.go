package dataleak_renrendoc

import (
	"fmt"
	"time"

	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"micro-service/pkg/utils"

	"gorm.io/gorm"
)

type RenrendocResultModel interface {
	CompareAndUpdate(origin, target []*RenrendocResult) error
	Create([]*RenrendocResult) error
	Updates([]*RenrendocResult) error
	List(page, size int, opts ...mysql.HandleFunc) ([]*RenrendocResult, int64, error)
	ListAll(...mysql.HandleFunc) ([]*RenrendocResult, error)
	UpdateAny(id uint64, m map[string]any) error
	ListAllByTask(taskId uint64) ([]*RenrendocResult, error)
	RelationListAll(opts ...mysql.HandleFunc) ([]*Relation, error)
	Upsert(items []*RenrendocResult) ([]*RenrendocResult, error)
	RelationSave([]*Relation) error
	RelationFirst(opts ...mysql.HandleFunc) (Relation, error)
}

const ResultTableName = "dataleak_renrendoc_result"

type RenrendocResult struct {
	dbx.Model
	TaskId     uint   `gorm:"column:task_id"`
	Keyword    string `gorm:"column:keyword;<-:false"` // allow read, disable write permission
	DocTitle   string `gorm:"column:doc_title"`
	DocUrl     string `gorm:"column:doc_url"`
	Screenshot string `gorm:"column:screenshot"`
}

func (*RenrendocResult) TableName() string {
	return ResultTableName
}

type defaultResultImpl struct{ *gorm.DB }

func NewResultModel(clients ...*gorm.DB) RenrendocResultModel {
	return &defaultResultImpl{DB: mysql.GetDbClient(clients...)}
}

// CompareAndUpdate 对比更新
func (d *defaultResultImpl) CompareAndUpdate(old, target []*RenrendocResult) error {
	oldMap := make(map[string]*RenrendocResult, len(old))
	for i := range old {
		oldMap[old[i].DocUrl] = old[i]
	}

	insert := make([]*RenrendocResult, 0, len(target))
	update := make([]*RenrendocResult, 0, len(target))
	for i := range target {
		if item, ok := oldMap[target[i].DocUrl]; !ok {
			insert = append(insert, target[i])
		} else {
			target[i].Id = item.Id
			target[i].CreatedAt = item.CreatedAt
			update = append(update, target[i])
		}
	}

	err := d.Create(insert)
	if err != nil {
		return err
	}
	if len(update) > 0 {
		err = d.DB.Save(update).Error
	}
	return err
}

func (d *defaultResultImpl) Upsert(items []*RenrendocResult) ([]*RenrendocResult, error) {
	if len(items) == 0 {
		return nil, nil
	}

	var docUrls = make([]string, 0, len(items))
	var m = make(map[string]*RenrendocResult, len(items))
	for _, v := range items {
		if _, ok := m[v.DocUrl]; !ok {
			m[v.DocUrl] = v
			docUrls = append(docUrls, v.DocUrl)
		}
	}

	var got = make([]*RenrendocResult, 0, len(items))
	sl := utils.ListSplit[string](docUrls, 50)
	for i := range sl {
		l, err := d.ListAll(mysql.WithValuesIn("doc_url", sl[i]))
		if err != nil {
			continue
		}
		got = append(got, l...)
	}

	var gotMap = make(map[string]*RenrendocResult, len(got))
	for _, v := range got {
		gotMap[v.DocUrl] = v
	}

	upsertList := make([]*RenrendocResult, 0, len(got)+len(items))
	for _, v := range m {
		value, ok := gotMap[v.DocUrl]
		if ok {
			v.Id = value.Id
			v.CreatedAt = value.CreatedAt
		}
		upsertList = append(upsertList, v)
	}

	err := d.DB.Save(&upsertList).Error
	return upsertList, err
}

func (d *defaultResultImpl) Create(l []*RenrendocResult) (err error) {
	if len(l) > 0 {
		err = d.DB.Create(&l).Error
	}
	return err
}

func (d *defaultResultImpl) Updates(l []*RenrendocResult) error {
	if len(l) == 0 {
		return nil
	}

	err := d.DB.Transaction(func(tx *gorm.DB) error {
		for i := range l {
			q := tx.Updates(l[i])
			if err := q.Error; err != nil {
				return err
			}
		}
		return nil
	})
	return err
}

func WithKeyword(keyword string) mysql.HandleFunc {
	return func(query *gorm.DB) {
		keyword = "%" + keyword + "%"
		query.Where(fmt.Sprintf("%s.doc_url LIKE ? OR %s.doc_title LIKE ?", ResultTableName, ResultTableName), keyword, keyword)
	}
}

func WithBetween(field string, start, end time.Time) mysql.HandleFunc {
	return func(query *gorm.DB) {
		query.Where(fmt.Sprintf("%s.%s BETWEEN ? AND ?", ResultTableName, field), start, end)
	}
}

func WithResultOrder(field string, asc bool) mysql.HandleFunc {
	return func(db *gorm.DB) {
		asc := utils.If(asc, "ASC", "DESC")
		db.Order(fmt.Sprintf("%s.%s %s", ResultTableName, field, asc))
	}
}

// List with page and condition
func (d *defaultResultImpl) List(page, size int, opts ...mysql.HandleFunc) ([]*RenrendocResult, int64, error) {
	query := d.DB.Model(&RenrendocResult{})
	//joinCond := fmt.Sprintf("LEFT JOIN %s ON %s.task_id = %s.id", TaskTableName, ResultTableName, TaskTableName)
	//query.Joins(joinCond)
	for _, opt := range opts {
		opt(query)
	}
	//query.Select(fmt.Sprintf("%s.*", ResultTableName), fmt.Sprintf("%s.keyword", TaskTableName))

	var total int64
	list := make([]*RenrendocResult, 0, size)
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

// ListAll return all result with conditions
func (d *defaultResultImpl) ListAll(opts ...mysql.HandleFunc) ([]*RenrendocResult, error) {
	query := d.DB.Model(&RenrendocResult{})
	for _, opt := range opts {
		opt(query)
	}

	l := make([]*RenrendocResult, 0)
	err := query.Find(&l).Error
	if err != nil {
		return nil, err
	}
	return l, nil
}

func (d *defaultResultImpl) UpdateAny(id uint64, m map[string]any) error {
	if len(m) == 0 {
		return nil
	}
	return d.DB.Model(RenrendocResult{}).Where("`id` = ?", id).Updates(m).Error
}

func (d *defaultResultImpl) ListAllByTask(taskId uint64) ([]*RenrendocResult, error) {
	const et = RelationTableName
	const rt = ResultTableName

	q := d.DB.Model(&RenrendocResult{})
	q.Joins(fmt.Sprintf("INNER JOIN `%s` ON `%s`.`id` = `%s`.`result_id`", et, rt, et))
	q.Select(fmt.Sprintf("%s.`id`,%s.`created_at`,%s.`updated_at`", et, et, et), "doc_title", "doc_url", "screenshot")
	q.Where(fmt.Sprintf("`%s`.`task_id` = ?", et), taskId)

	var result []*RenrendocResult
	err := q.Find(&result).Error
	return result, err
}
