package clues_groups

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

const tableName = "clues_groups"

const (
	Show       = iota
	ShowHidden // 隐藏
)

type CluesGrouper interface {
	Create(*ClueGroup) error
	First(...mysql.HandleFunc) (ClueGroup, error)
	List(...mysql.HandleFunc) ([]ClueGroup, error)
	Update(ClueGroup) error
	UpdateAny(map[string]any, ...mysql.HandleFunc) error
}

type ClueGroup struct {
	dbx.Model
	Name      string `gorm:"column:name"`
	UserId    uint   `gorm:"column:user_id"`
	CompanyId uint   `gorm:"column:company_id"`
	IsShow    int    `gorm:"column:is_show"`
}

func (*ClueGroup) TableName() string {
	return tableName
}

type defaultCluesGroups struct{ *gorm.DB }

func NewCluesGrouper(db ...*gorm.DB) CluesGrouper {
	return &defaultCluesGroups{DB: mysql.GetDbClient(db...)}
}

func (d *defaultCluesGroups) Create(item *ClueGroup) error {
	return d.DB.Model(ClueGroup{}).Create(item).Error
}

func (d *defaultCluesGroups) First(opts ...mysql.HandleFunc) (ClueGroup, error) {
	query := d.DB.Model(ClueGroup{})
	for _, opt := range opts {
		opt(query)
	}

	var item ClueGroup
	err := query.First(&item).Error
	return item, err
}

func (d *defaultCluesGroups) List(opts ...mysql.HandleFunc) ([]ClueGroup, error) {
	query := d.DB.Model(ClueGroup{})
	for _, opt := range opts {
		opt(query)
	}

	var items = make([]ClueGroup, 0)
	err := query.Find(&items).Error
	return items, err
}

func (d *defaultCluesGroups) Update(item ClueGroup) error {
	return d.DB.Updates(item).Error
}

func (d *defaultCluesGroups) UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error {
	query := d.DB.Model(ClueGroup{})
	for _, opt := range opts {
		opt(query)
	}
	return query.Updates(m).Error
}
