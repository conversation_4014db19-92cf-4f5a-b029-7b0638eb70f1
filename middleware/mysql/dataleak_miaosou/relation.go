package dataleak_miaosou

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

const (
	RelationTableName = "dataleak_miaosou_relation"
	et                = RelationTableName
)

type Relation struct {
	dbx.Model
	TaskId   uint64 `gorm:"column:task_id"`
	ResultId uint64 `gorm:"column:result_id"`
}

func (*Relation) TableName() string {
	return RelationTableName
}

func (d *defaultResultImpl) RelationListAll(opts ...mysql.HandleFunc) ([]*Relation, error) {
	q := d.DB.Model(&Relation{})
	for _, f := range opts {
		f(q)
	}

	var result = make([]*Relation, 0)
	err := q.Find(&result).Error
	return result, err
}

func (d *defaultResultImpl) RelationSave(result []*Relation) error {
	if len(result) == 0 {
		return nil
	}
	return d.DB.Save(&result).Error
}

func (d *defaultResultImpl) RelationFirst(opts ...mysql.HandleFunc) (Relation, error) {
	q := d.DB.Model(&Relation{})
	for _, f := range opts {
		f(q)
	}

	var info Relation
	err := q.First(&info).Error
	return info, err
}
