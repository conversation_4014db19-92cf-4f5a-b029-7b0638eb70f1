package user_poc

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

const TableName = "users_pocs"

// 状态常量
const (
	STATUS_DISABLE = 0 // 禁用
	STATUS_ENABLE  = 1 // 启用
)

// users表和scan_pocs关系表
type UsersPocs struct {
	dbx.Model
	UserId    uint64 `gorm:"column:user_id;type:bigint(20) unsigned;not null;comment:'用户ID'"`
	CompanyId uint64 `gorm:"column:company_id;type:bigint(20) unsigned;default:null;comment:'企业ID'"`
	PocsId    uint64 `gorm:"column:pocs_id;type:int(11);not null;comment:'对应pocs_id表的id'"`
	Status    int    `gorm:"column:status;type:tinyint(4);not null;default:0;comment:'状态 默认 0启用 1禁用'"`
	RiskNum   int    `gorm:"column:risk_num;type:int(11);not null;default:0;comment:'该poc对应的风险资产数量'"`
}

type UsersPocsModel interface {
	FindByQuerys(opts ...mysql.HandleFunc) ([]*UsersPocs, error)
	Create(usersPocs *UsersPocs) error
	DeleteByPocId(pocId uint64) error
	Update(usersPocs interface{}, opts ...mysql.HandleFunc) error
}

type defaultUsersPocsModel struct {
	*gorm.DB
	table string
}

func (m *UsersPocs) TableName() string {
	return TableName
}

func NewUsersPocsModel(conn ...*gorm.DB) UsersPocsModel {
	return &defaultUsersPocsModel{mysql.GetDbClient(conn...), TableName}
}

func (d *defaultUsersPocsModel) FindByQuerys(opts ...mysql.HandleFunc) ([]*UsersPocs, error) {
	var resp []*UsersPocs
	query := d.DB.Table(d.table)
	for _, f := range opts {
		f(query)
	}
	err := query.Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (d *defaultUsersPocsModel) Create(usersPocs *UsersPocs) error {
	return d.DB.Model(&UsersPocs{}).Create(usersPocs).Error
}
func (d *defaultUsersPocsModel) DeleteByPocId(pocId uint64) error {
	return d.DB.Model(&UsersPocs{}).Where("pocs_id = ?", pocId).Delete(&UsersPocs{}).Error
}

func (d *defaultUsersPocsModel) Update(usersPocs interface{}, opts ...mysql.HandleFunc) error {
	query := d.DB.Model(&UsersPocs{})
	for _, opt := range opts {
		opt(query)
	}
	return query.Updates(usersPocs).Error
}
