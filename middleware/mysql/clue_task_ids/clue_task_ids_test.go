package clue_task_ids

import (
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
)

func init() {
	cfg.InitLoadCfg()
	// 设置测试环境标志
	initmysql.SetTestEnv(true)

	// 初始化数据库连接
	initmysql.GetInstance(cfg.LoadMysql())
}

// setupTestModel 创建测试用的模型实例
func setupTestModel(t *testing.T) (ClueTaskIdsModel, sqlmock.Sqlmock) {
	// 为每个测试创建新的Mock实例
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to create mock: %v", err)
	}

	// Mock GORM 初始化时的查询
	mock.ExpectQuery("SELECT VERSION\\(\\)").WillReturnRows(sqlmock.NewRows([]string{"VERSION()"}).AddRow("8.0.0"))

	// 创建GORM实例
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: false,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to create gorm instance: %v", err)
	}

	model := &clueTaskIdsModel{
		db: gormDB,
	}
	return model, mock
}

func TestClueTaskIds_TableName(t *testing.T) {
	taskIds := &ClueTaskIds{}
	assert.Equal(t, "clue_tasks_ids", taskIds.TableName())
}

func TestNewClueTaskIdsModel(t *testing.T) {
	model := NewClueTaskIdsModel()
	assert.NotNil(t, model)
}

func TestClueTaskIdsModel_Create(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewClueTaskIdsModel()
		taskIds := &ClueTaskIds{
			ClueTasksId: 123,
			ClueId:      456,
			Status:      StatusDefaultIds,
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `clue_tasks_ids`").
			WithArgs(utils.SqlMockArgs(5)...).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		err := model.Create(taskIds)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewClueTaskIdsModel()
		taskIds := &ClueTaskIds{
			ClueTasksId: 123,
			ClueId:      456,
			Status:      StatusDefaultIds,
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `clue_tasks_ids`").
			WithArgs(utils.SqlMockArgs(5)...).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Create(taskIds)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestClueTaskIdsModel_Update(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewClueTaskIdsModel()
		taskIds := &ClueTaskIds{
			Id:          1,
			ClueTasksId: 123,
			ClueId:      456,
			Status:      StatusRunningIds,
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `clue_tasks_ids`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		err := model.Update(taskIds)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewClueTaskIdsModel()
		taskIds := &ClueTaskIds{
			Id:          1,
			ClueTasksId: 123,
			ClueId:      456,
			Status:      StatusRunningIds,
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `clue_tasks_ids`").
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Update(taskIds)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestClueTaskIdsModel_First(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewClueTaskIdsModel()

	// 测试成功查询
	expectedTaskIds := &ClueTaskIds{
		Id:          1,
		ClueTasksId: 123,
		ClueId:      456,
		Status:      StatusRunningIds,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	rows := sqlmock.NewRows([]string{
		"id", "clue_tasks_id", "clue_id", "status", "created_at", "updated_at",
	}).AddRow(
		expectedTaskIds.Id, expectedTaskIds.ClueTasksId,
		expectedTaskIds.ClueId, expectedTaskIds.Status,
		expectedTaskIds.CreatedAt, expectedTaskIds.UpdatedAt,
	)

	mock.ExpectQuery("SELECT (.+) FROM `clue_tasks_ids`").
		WillReturnRows(rows)

	result, err := model.First()
	assert.NoError(t, err)
	assert.Equal(t, expectedTaskIds.Id, result.Id)
	assert.Equal(t, expectedTaskIds.ClueTasksId, result.ClueTasksId)
	assert.Equal(t, expectedTaskIds.ClueId, result.ClueId)
	assert.Equal(t, expectedTaskIds.Status, result.Status)
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试查询失败
	mock.ExpectQuery("SELECT (.+) FROM `clue_tasks_ids`").
		WillReturnError(sql.ErrNoRows)

	_, err = model.First()
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestClueTaskIdsModel_List(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewClueTaskIdsModel()

	// 测试成功查询多条记录
	expectedTaskIds := []*ClueTaskIds{
		{
			Id:          1,
			ClueTasksId: 123,
			ClueId:      456,
			Status:      StatusRunningIds,
		},
		{
			Id:          2,
			ClueTasksId: 123,
			ClueId:      789,
			Status:      StatusSuccessIds,
		},
	}

	countRows := sqlmock.NewRows([]string{"count"}).AddRow(2)
	mock.ExpectQuery("SELECT count").
		WillReturnRows(countRows)

	rows := sqlmock.NewRows([]string{
		"id", "clue_tasks_id", "clue_id", "status", "created_at", "updated_at",
	})
	for _, taskId := range expectedTaskIds {
		rows.AddRow(
			taskId.Id, taskId.ClueTasksId, taskId.ClueId,
			taskId.Status, time.Now(), time.Now(),
		)
	}

	mock.ExpectQuery("SELECT (.+) FROM `clue_tasks_ids`").
		WillReturnRows(rows)

	result, total, err := model.List(1, 10)
	assert.NoError(t, err)
	assert.Equal(t, int64(2), total)
	assert.Len(t, result, 2)
	assert.Equal(t, expectedTaskIds[0].Id, result[0].Id)
	assert.Equal(t, expectedTaskIds[1].Id, result[1].Id)
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试查询失败
	mock.ExpectQuery("SELECT count").
		WillReturnError(sql.ErrConnDone)

	_, _, err = model.List(1, 10)
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestClueTaskIdsModel_ListAll(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewClueTaskIdsModel()

	// 测试成功查询所有记录
	expectedTaskIds := []*ClueTaskIds{
		{
			Id:          1,
			ClueTasksId: 123,
			ClueId:      456,
			Status:      StatusRunningIds,
		},
		{
			Id:          2,
			ClueTasksId: 123,
			ClueId:      789,
			Status:      StatusSuccessIds,
		},
	}

	rows := sqlmock.NewRows([]string{
		"id", "clue_tasks_id", "clue_id", "status", "created_at", "updated_at",
	})
	for _, taskId := range expectedTaskIds {
		rows.AddRow(
			taskId.Id, taskId.ClueTasksId, taskId.ClueId,
			taskId.Status, time.Now(), time.Now(),
		)
	}

	mock.ExpectQuery("SELECT (.+) FROM `clue_tasks_ids`").
		WillReturnRows(rows)

	result, err := model.ListAll()
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.Equal(t, expectedTaskIds[0].Id, result[0].Id)
	assert.Equal(t, expectedTaskIds[1].Id, result[1].Id)
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试查询失败
	mock.ExpectQuery("SELECT (.+) FROM `clue_tasks_ids`").
		WillReturnError(sql.ErrConnDone)

	_, err = model.ListAll()
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestClueTaskIdsModel_DB(t *testing.T) {
	model := NewClueTaskIdsModel()
	db := model.DB()
	assert.NotNil(t, db)
}
