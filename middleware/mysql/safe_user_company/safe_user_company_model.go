package safe_user_company

import (
	"database/sql"
	"errors"
	"gorm.io/gorm"
	"micro-service/middleware/mysql"
)

type (
	SafeUserCompanyModel interface {
		Find(SafeUserId uint64) ([]SafeUserCompany, error)
		Has(SafeUserId, CompanyId uint64) error
	}

	defaultSafeUserCompanyModel struct {
		*gorm.DB
		table string
	}

	SafeUserCompany struct {
		Id         uint64       `gorm:"column:id;type:bigint(20) unsigned;AUTO_INCREMENT;primary_key" json:"id"`
		SafeUserId uint64       `gorm:"column:safe_user_id;type:bigint(20) unsigned;comment:安服人员id;NOT NULL" json:"safe_user_id"`
		CompanyId  uint64       `gorm:"column:company_id;type:bigint(20) unsigned;comment:企业ID;NOT NULL" json:"company_id"`
		CreatedAt  sql.NullTime `gorm:"column:created_at;type:timestamp" json:"created_at"`
		UpdatedAt  sql.NullTime `gorm:"column:updated_at;type:timestamp" json:"updated_at"`
		DeletedAt  sql.NullTime `gorm:"column:deleted_at;type:timestamp" json:"deleted_at"`
	}
)

func (m *SafeUserCompany) TableName() string {
	return "safe_user_company"
}

func NewModel(conn ...*gorm.DB) SafeUserCompanyModel {
	return &defaultSafeUserCompanyModel{
		mysql.GetDbClient(conn...), "safe_user_company",
	}
}

func (d *defaultSafeUserCompanyModel) Find(SafeUserId uint64) ([]SafeUserCompany, error) {
	var user []SafeUserCompany
	q := d.DB.Table(d.table)
	q.Where("safe_user_id = ?", SafeUserId)

	err := q.Find(&user).Error
	if err != nil {
		return []SafeUserCompany{}, err
	}
	return user, nil
}

func (d *defaultSafeUserCompanyModel) Has(SafeUserId, CompanyId uint64) error {
	var user SafeUserCompany
	err := d.DB.Table(d.table).Select("id").Where("safe_user_id = ? and company_id = ?", SafeUserId, CompanyId).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("无操作权限")
		}
		return err
	}
	return nil
}
