package report_template

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"micro-service/middleware/mysql"
	"time"

	"gorm.io/gorm"
)

// ReportTemplate 报告模板模型
type ReportTemplate struct {
	ID                  uint          `gorm:"primaryKey;column:id;type:bigint(20) unsigned;comment:主键ID"`
	CreatedAt           time.Time     `gorm:"column:created_at;type:datetime;comment:创建时间"`
	UpdatedAt           time.Time     `gorm:"column:updated_at;type:datetime;comment:更新时间"`
	Name                string        `gorm:"column:name;type:varchar(255);comment:模板名称"`
	UserId              uint64        `gorm:"column:user_id;type:bigint(20) unsigned;comment:用户ID;NOT NULL"`
	CompanyId           uint64        `gorm:"column:company_id;type:bigint(20) unsigned;comment:公司ID"`
	OperatorId          uint64        `gorm:"column:operator_id;type:bigint(20) unsigned;comment:上传报告的安服角色的用户id"`
	DataRangeType       int           `gorm:"column:data_range_type;type:tinyint(4);default:1;comment:数据源 1、数据维度 2、任务维度"`
	TimeType            int           `gorm:"column:time_type;type:tinyint(4);default:1;comment:数据时间范围维度 1、全部 2、近365天 3、近180天 4、近90天 5、30天"`
	Type                int           `gorm:"column:type;type:tinyint(4);default:2;comment:类型 1:系统生成 2:自定义"`
	Email               string        `gorm:"column:email;type:varchar(255);comment:email邮箱地址"`
	ReportModule        ReportModules `gorm:"column:report_module;type:json;comment:报告开启的模块"`
	KnowAssetsParam     string        `gorm:"column:know_assets_param;type:text;comment:台账模块的筛选参数"`
	UnknowAssetsParam   string        `gorm:"column:unknow_assets_param;type:text;comment:疑似资产的筛选参数"`
	AppAssetsParam      string        `gorm:"column:app_assets_param;type:text;comment:app/公众号和小程序筛选参数"`
	PocDataParam        string        `gorm:"column:poc_data_param;type:text;comment:漏洞数据的筛选参数"`
	SensitiveDataParam  string        `gorm:"column:sensitive_data_param;type:text;comment:敏感信息百度网盘等平台的筛选参数"`
	ThreatenAssetsParam string        `gorm:"column:threaten_assets_param;type:text;comment:威胁资产的筛选参数"`
	WarningEventParam   string        `gorm:"column:warning_event_param;type:text;comment:事件告警的筛选参数"`
	DetectAssetsTasksId uint64        `gorm:"column:detect_assets_tasks_id;type:bigint(20) unsigned;comment:资产测绘任务ID"`
	IsDel               int           `gorm:"column:is_del;type:tinyint(4);default:0;comment:是否删除 0:否 1:是"`
	IsShowLeakImage     int           `gorm:"column:is_show_leak_image;type:tinyint(4);default:1;comment:是否在报告里面显示 数据泄露的截图 1/是 0/不是"`
}

// ReportModules 报告模块JSON类型
type ReportModules []int

// 模块常量定义
const (
	// 模块类型
	SureAssetsModule     = 1 // 资产台账
	UnsureAssetsModule   = 2 // 疑似资产
	NumberAssetsModule   = 3 // 数字资产
	PocModule            = 4 // 漏洞检测
	DataLeakModule       = 5 // 数据泄露检测
	ThreatenAssetsModule = 6 // 威胁资产

	// 模板类型
	TemplateSystem = 1 // 系统生成
	TemplateAuto   = 2 // 自定义

	// 删除状态
	DeleteOK = 1 // 已删除
	DeleteNo = 0 // 未删除
)

// 模块名称映射
var ModuleNameMap = map[int]string{
	SureAssetsModule:     "资产台账",
	UnsureAssetsModule:   "疑似资产",
	NumberAssetsModule:   "数字资产",
	PocModule:            "漏洞检测",
	DataLeakModule:       "数据泄露检测",
	ThreatenAssetsModule: "威胁资产",
}

// Value 实现driver.Valuer接口，用于将ReportModules转换为数据库值
func (r ReportModules) Value() (driver.Value, error) {
	if len(r) == 0 {
		return "[]", nil
	}
	return json.Marshal(r)
}

// Scan 实现sql.Scanner接口，用于将数据库值转换为ReportModules
func (r *ReportModules) Scan(value interface{}) error {
	if value == nil {
		*r = ReportModules{}
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("failed to scan ReportModules")
	}

	return json.Unmarshal(bytes, r)
}

// TableName 返回表名
func (r *ReportTemplate) TableName() string {
	return "report_templates"
}

// ReportTemplateModel 定义模型接口
type ReportTemplateModel interface {
	First(...mysql.HandleFunc) (*ReportTemplate, error)
	FindAll(...mysql.HandleFunc) ([]*ReportTemplate, error)
	Create(*ReportTemplate) error
	Delete(...mysql.HandleFunc) error
	Update(*ReportTemplate, ...mysql.HandleFunc) error
	UpdateAny(map[string]interface{}, ...mysql.HandleFunc) error
}

// defaultReportTemplate 默认实现
type defaultReportTemplate struct {
	*gorm.DB
	tableName string
}

// NewModel 创建新的模型实例
func NewModel(conn ...*gorm.DB) ReportTemplateModel {
	return &defaultReportTemplate{
		DB:        mysql.GetDbClient(conn...),
		tableName: "report_templates",
	}
}

// First 查询单条记录
func (d *defaultReportTemplate) First(opts ...mysql.HandleFunc) (*ReportTemplate, error) {
	var template ReportTemplate
	query := d.DB.Model(&ReportTemplate{})
	for _, f := range opts {
		f(query)
	}
	err := query.First(&template).Error
	return &template, err
}

// FindAll 查询多条记录
func (d *defaultReportTemplate) FindAll(opts ...mysql.HandleFunc) ([]*ReportTemplate, error) {
	var templates []*ReportTemplate
	query := d.DB.Model(&ReportTemplate{})
	for _, f := range opts {
		f(query)
	}
	err := query.Find(&templates).Error
	return templates, err
}

// Create 创建记录
func (d *defaultReportTemplate) Create(template *ReportTemplate) error {
	return d.DB.Create(template).Error
}

// Delete 删除记录
func (d *defaultReportTemplate) Delete(opts ...mysql.HandleFunc) error {
	query := d.DB.Model(&ReportTemplate{})
	for _, f := range opts {
		f(query)
	}
	return query.Delete(&ReportTemplate{}).Error
}

// Update 更新记录
func (d *defaultReportTemplate) Update(template *ReportTemplate, opts ...mysql.HandleFunc) error {
	query := d.DB.Model(&ReportTemplate{})
	for _, f := range opts {
		f(query)
	}
	return query.Updates(template).Error
}

// UpdateAny 更新任意字段
func (d *defaultReportTemplate) UpdateAny(values map[string]interface{}, opts ...mysql.HandleFunc) error {
	query := d.DB.Model(&ReportTemplate{})
	for _, f := range opts {
		f(query)
	}
	return query.Updates(values).Error
}

// WithUserId 添加用户ID查询条件
func WithUserId(userId uint64) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("user_id = ?", userId)
	}
}

// WithDetectAssetsTasksId 添加资产测绘任务ID查询条件
func WithDetectAssetsTasksId(taskId uint64) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("detect_assets_tasks_id = ?", taskId)
	}
}

// WithDetectAssetsTasksIds 添加多个资产测绘任务ID查询条件
func WithDetectAssetsTasksIds(taskIds []uint64) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("detect_assets_tasks_id IN ?", taskIds)
	}
}

// WithCompanyId 添加公司ID查询条件
func WithCompanyId(companyId uint64) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("company_id = ?", companyId)
	}
}

// WithType 添加类型查询条件
func WithType(typeVal int) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("type = ?", typeVal)
	}
}

// WithKeyword 添加关键字查询条件
func WithKeyword(keyword string) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("name LIKE ?", "%"+keyword+"%")
	}
}

// WithIds 添加ID列表查询条件
func WithIds(ids []uint64) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("id IN ?", ids)
	}
}

// WithIsDel 添加是否删除查询条件
func WithIsDel(isDel int) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("is_del = ?", isDel)
	}
}
