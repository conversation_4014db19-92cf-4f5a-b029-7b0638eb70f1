package report_template

import (
	"database/sql"
	"database/sql/driver"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
)

func init() {
	cfg.InitLoadCfg()
	// 设置测试环境标志
	initmysql.SetTestEnv(true)

	// 初始化数据库连接
	initmysql.GetInstance(cfg.LoadMysql())
}

func TestReportTemplate_TableName(t *testing.T) {
	template := &ReportTemplate{}
	assert.Equal(t, "report_templates", template.TableName())
}

func TestNewModel(t *testing.T) {
	model := NewModel()
	assert.NotNil(t, model)
}

func TestReportModules_Value(t *testing.T) {
	t.Run("empty_modules", func(t *testing.T) {
		modules := ReportModules{}
		value, err := modules.Value()
		assert.NoError(t, err)
		assert.Equal(t, "[]", value)
	})

	t.Run("with_modules", func(t *testing.T) {
		modules := ReportModules{1, 2, 3}
		value, err := modules.Value()
		assert.NoError(t, err)
		assert.Equal(t, "[1,2,3]", string(value.([]byte)))
	})
}

func TestReportModules_Scan(t *testing.T) {
	t.Run("nil_value", func(t *testing.T) {
		var modules ReportModules
		err := modules.Scan(nil)
		assert.NoError(t, err)
		assert.Equal(t, ReportModules{}, modules)
	})

	t.Run("byte_slice", func(t *testing.T) {
		var modules ReportModules
		err := modules.Scan([]byte("[1,2,3]"))
		assert.NoError(t, err)
		assert.Equal(t, ReportModules{1, 2, 3}, modules)
	})

	t.Run("string_value", func(t *testing.T) {
		var modules ReportModules
		err := modules.Scan("[4,5,6]")
		assert.NoError(t, err)
		assert.Equal(t, ReportModules{4, 5, 6}, modules)
	})

	t.Run("invalid_type", func(t *testing.T) {
		var modules ReportModules
		err := modules.Scan(123)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to scan ReportModules")
	})

	t.Run("invalid_json", func(t *testing.T) {
		var modules ReportModules
		err := modules.Scan("invalid json")
		assert.Error(t, err)
	})
}

func TestDefaultReportTemplate_First(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		expectedTemplate := &ReportTemplate{
			ID:                  1,
			CreatedAt:           time.Now(),
			UpdatedAt:           time.Now(),
			Name:                "测试模板",
			UserId:              123,
			CompanyId:           456,
			Type:                TemplateAuto,
			ReportModule:        ReportModules{1, 2, 3},
			DetectAssetsTasksId: 789,
			IsDel:               DeleteNo,
		}

		rows := sqlmock.NewRows([]string{
			"id", "created_at", "updated_at", "name", "user_id", "company_id",
			"type", "report_module", "detect_assets_tasks_id", "is_del",
		}).AddRow(
			expectedTemplate.ID, expectedTemplate.CreatedAt, expectedTemplate.UpdatedAt,
			expectedTemplate.Name, expectedTemplate.UserId, expectedTemplate.CompanyId,
			expectedTemplate.Type, "[1,2,3]", expectedTemplate.DetectAssetsTasksId,
			expectedTemplate.IsDel,
		)

		mock.ExpectQuery("SELECT (.+) FROM `report_templates`").
			WillReturnRows(rows)

		result, err := model.First()
		assert.NoError(t, err)
		assert.Equal(t, expectedTemplate.ID, result.ID)
		assert.Equal(t, expectedTemplate.Name, result.Name)
		assert.Equal(t, expectedTemplate.UserId, result.UserId)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		mock.ExpectQuery("SELECT (.+) FROM `report_templates`").
			WillReturnError(sql.ErrNoRows)

		_, err := model.First()
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultReportTemplate_FindAll(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		expectedTemplates := []*ReportTemplate{
			{
				ID:           1,
				Name:         "模板1",
				UserId:       123,
				Type:         TemplateSystem,
				ReportModule: ReportModules{1, 2},
			},
			{
				ID:           2,
				Name:         "模板2",
				UserId:       123,
				Type:         TemplateAuto,
				ReportModule: ReportModules{3, 4},
			},
		}

		rows := sqlmock.NewRows([]string{
			"id", "created_at", "updated_at", "name", "user_id", "company_id",
			"type", "report_module", "detect_assets_tasks_id", "is_del",
		})
		for _, template := range expectedTemplates {
			rows.AddRow(
				template.ID, time.Now(), time.Now(), template.Name,
				template.UserId, template.CompanyId, template.Type,
				"[1,2]", template.DetectAssetsTasksId, template.IsDel,
			)
		}

		mock.ExpectQuery("SELECT (.+) FROM `report_templates`").
			WillReturnRows(rows)

		result, err := model.FindAll()
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, expectedTemplates[0].Name, result[0].Name)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		mock.ExpectQuery("SELECT (.+) FROM `report_templates`").
			WillReturnError(sql.ErrConnDone)

		_, err := model.FindAll()
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultReportTemplate_Create(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		template := &ReportTemplate{
			Name:                "新模板",
			UserId:              123,
			CompanyId:           456,
			OperatorId:          0,
			DataRangeType:       1,
			TimeType:            1,
			Type:                TemplateAuto,
			Email:               "",
			ReportModule:        ReportModules{1, 2, 3},
			KnowAssetsParam:     "",
			UnknowAssetsParam:   "",
			AppAssetsParam:      "",
			PocDataParam:        "",
			SensitiveDataParam:  "",
			ThreatenAssetsParam: "",
			WarningEventParam:   "",
			DetectAssetsTasksId: 789,
			IsDel:               DeleteNo,
			IsShowLeakImage:     1,
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `report_templates`").
			WithArgs(utils.SqlMockArgs(21)...).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		err := model.Create(template)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		template := &ReportTemplate{
			Name:                "新模板",
			UserId:              123,
			CompanyId:           0,
			OperatorId:          0,
			DataRangeType:       1,
			TimeType:            1,
			Type:                0,
			Email:               "",
			ReportModule:        nil,
			KnowAssetsParam:     "",
			UnknowAssetsParam:   "",
			AppAssetsParam:      "",
			PocDataParam:        "",
			SensitiveDataParam:  "",
			ThreatenAssetsParam: "",
			WarningEventParam:   "",
			DetectAssetsTasksId: 0,
			IsDel:               0,
			IsShowLeakImage:     1,
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `report_templates`").
			WithArgs(utils.SqlMockArgs(21)...).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Create(template)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultReportTemplate_Delete(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `report_templates` WHERE id IN (.+)").
			WithArgs(1).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err := model.Delete(WithIds([]uint64{1}))
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `report_templates` WHERE id IN (.+)").
			WithArgs(1).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Delete(WithIds([]uint64{1}))
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultReportTemplate_Update(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		template := &ReportTemplate{
			ID:   1,
			Name: "更新模板",
			Type: TemplateAuto,
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `report_templates` SET").
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err := model.Update(template, WithIds([]uint64{1}))
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		template := &ReportTemplate{
			ID:   1,
			Name: "更新模板",
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `report_templates` SET").
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Update(template, WithIds([]uint64{1}))
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultReportTemplate_UpdateAny(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		values := map[string]interface{}{
			"name": "更新名称",
			"type": TemplateSystem,
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `report_templates` SET").
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err := model.UpdateAny(values, WithIds([]uint64{1}))
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		values := map[string]interface{}{
			"name": "更新名称",
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `report_templates` SET").
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.UpdateAny(values, WithIds([]uint64{1}))
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

// 测试查询条件函数
func TestWithUserId(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	userId := uint64(123)

	rows := sqlmock.NewRows([]string{
		"id", "created_at", "updated_at", "name", "user_id", "company_id",
		"operator_id", "data_range_type", "time_type", "type", "email",
		"report_module", "know_assets_param", "unknow_assets_param",
		"app_assets_param", "poc_data_param", "sensitive_data_param",
		"threaten_assets_param", "warning_event_param", "detect_assets_tasks_id",
		"is_del", "is_show_leak_image",
	}).AddRow(1, time.Now(), time.Now(), "用户模板", userId, 456,
		0, 1, 1, TemplateAuto, "", "[]", "", "", "", "", "", "", "", 0, DeleteNo, 1)

	mock.ExpectQuery("SELECT (.+) FROM `report_templates` WHERE user_id = (.+)").
		WithArgs(userId).
		WillReturnRows(rows)

	result, err := model.FindAll(WithUserId(userId))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, userId, result[0].UserId)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithDetectAssetsTasksId(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	taskId := uint64(789)

	rows := sqlmock.NewRows([]string{
		"id", "created_at", "updated_at", "name", "user_id", "company_id",
		"operator_id", "data_range_type", "time_type", "type", "email",
		"report_module", "know_assets_param", "unknow_assets_param",
		"app_assets_param", "poc_data_param", "sensitive_data_param",
		"threaten_assets_param", "warning_event_param", "detect_assets_tasks_id",
		"is_del", "is_show_leak_image",
	}).AddRow(1, time.Now(), time.Now(), "任务模板", 123, 456,
		0, 1, 1, TemplateAuto, "", "[]", "", "", "", "", "", "", "", taskId, DeleteNo, 1)

	mock.ExpectQuery("SELECT (.+) FROM `report_templates` WHERE detect_assets_tasks_id = (.+)").
		WithArgs(taskId).
		WillReturnRows(rows)

	result, err := model.FindAll(WithDetectAssetsTasksId(taskId))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, taskId, result[0].DetectAssetsTasksId)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithDetectAssetsTasksIds(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	taskIds := []uint64{789, 790}

	rows := sqlmock.NewRows([]string{
		"id", "created_at", "updated_at", "name", "user_id", "company_id",
		"operator_id", "data_range_type", "time_type", "type", "email",
		"report_module", "know_assets_param", "unknow_assets_param",
		"app_assets_param", "poc_data_param", "sensitive_data_param",
		"threaten_assets_param", "warning_event_param", "detect_assets_tasks_id",
		"is_del", "is_show_leak_image",
	}).AddRow(1, time.Now(), time.Now(), "任务模板1", 123, 456,
		0, 1, 1, TemplateAuto, "", "[]", "", "", "", "", "", "", "", taskIds[0], DeleteNo, 1).
		AddRow(2, time.Now(), time.Now(), "任务模板2", 123, 456,
			0, 1, 1, TemplateAuto, "", "[]", "", "", "", "", "", "", "", taskIds[1], DeleteNo, 1)

	mock.ExpectQuery("SELECT (.+) FROM `report_templates` WHERE detect_assets_tasks_id IN (.+)").
		WithArgs(taskIds[0], taskIds[1]).
		WillReturnRows(rows)

	result, err := model.FindAll(WithDetectAssetsTasksIds(taskIds))
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithCompanyId(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	companyId := uint64(456)

	rows := sqlmock.NewRows([]string{
		"id", "created_at", "updated_at", "name", "user_id", "company_id",
		"operator_id", "data_range_type", "time_type", "type", "email",
		"report_module", "know_assets_param", "unknow_assets_param",
		"app_assets_param", "poc_data_param", "sensitive_data_param",
		"threaten_assets_param", "warning_event_param", "detect_assets_tasks_id",
		"is_del", "is_show_leak_image",
	}).AddRow(1, time.Now(), time.Now(), "公司模板", 123, companyId,
		0, 1, 1, TemplateAuto, "", "[]", "", "", "", "", "", "", "", 0, DeleteNo, 1)

	mock.ExpectQuery("SELECT (.+) FROM `report_templates` WHERE company_id = (.+)").
		WithArgs(companyId).
		WillReturnRows(rows)

	result, err := model.FindAll(WithCompanyId(companyId))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, companyId, result[0].CompanyId)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithType(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	typeVal := TemplateSystem

	rows := sqlmock.NewRows([]string{
		"id", "created_at", "updated_at", "name", "user_id", "company_id",
		"operator_id", "data_range_type", "time_type", "type", "email",
		"report_module", "know_assets_param", "unknow_assets_param",
		"app_assets_param", "poc_data_param", "sensitive_data_param",
		"threaten_assets_param", "warning_event_param", "detect_assets_tasks_id",
		"is_del", "is_show_leak_image",
	}).AddRow(1, time.Now(), time.Now(), "系统模板", 123, 456,
		0, 1, 1, typeVal, "", "[]", "", "", "", "", "", "", "", 0, DeleteNo, 1)

	mock.ExpectQuery("SELECT (.+) FROM `report_templates` WHERE type = (.+)").
		WithArgs(typeVal).
		WillReturnRows(rows)

	result, err := model.FindAll(WithType(typeVal))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, typeVal, result[0].Type)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithKeyword(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	keyword := "测试"

	rows := sqlmock.NewRows([]string{
		"id", "created_at", "updated_at", "name", "user_id", "company_id",
		"operator_id", "data_range_type", "time_type", "type", "email",
		"report_module", "know_assets_param", "unknow_assets_param",
		"app_assets_param", "poc_data_param", "sensitive_data_param",
		"threaten_assets_param", "warning_event_param", "detect_assets_tasks_id",
		"is_del", "is_show_leak_image",
	}).AddRow(1, time.Now(), time.Now(), "测试模板", 123, 456,
		0, 1, 1, TemplateAuto, "", "[]", "", "", "", "", "", "", "", 0, DeleteNo, 1)

	mock.ExpectQuery("SELECT (.+) FROM `report_templates` WHERE name LIKE (.+)").
		WithArgs("%" + keyword + "%").
		WillReturnRows(rows)

	result, err := model.FindAll(WithKeyword(keyword))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Contains(t, result[0].Name, keyword)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithIds(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	ids := []uint64{1, 2}

	rows := sqlmock.NewRows([]string{
		"id", "created_at", "updated_at", "name", "user_id", "company_id",
		"operator_id", "data_range_type", "time_type", "type", "email",
		"report_module", "know_assets_param", "unknow_assets_param",
		"app_assets_param", "poc_data_param", "sensitive_data_param",
		"threaten_assets_param", "warning_event_param", "detect_assets_tasks_id",
		"is_del", "is_show_leak_image",
	}).AddRow(ids[0], time.Now(), time.Now(), "模板1", 123, 456,
		0, 1, 1, TemplateAuto, "", "[]", "", "", "", "", "", "", "", 0, DeleteNo, 1).
		AddRow(ids[1], time.Now(), time.Now(), "模板2", 123, 456,
			0, 1, 1, TemplateAuto, "", "[]", "", "", "", "", "", "", "", 0, DeleteNo, 1)

	mock.ExpectQuery("SELECT (.+) FROM `report_templates` WHERE id IN (.+)").
		WithArgs(ids[0], ids[1]).
		WillReturnRows(rows)

	result, err := model.FindAll(WithIds(ids))
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithIsDel(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	isDel := DeleteNo

	rows := sqlmock.NewRows([]string{
		"id", "created_at", "updated_at", "name", "user_id", "company_id",
		"operator_id", "data_range_type", "time_type", "type", "email",
		"report_module", "know_assets_param", "unknow_assets_param",
		"app_assets_param", "poc_data_param", "sensitive_data_param",
		"threaten_assets_param", "warning_event_param", "detect_assets_tasks_id",
		"is_del", "is_show_leak_image",
	}).AddRow(1, time.Now(), time.Now(), "未删除模板", 123, 456,
		0, 1, 1, TemplateAuto, "", "[]", "", "", "", "", "", "", "", 0, isDel, 1)

	mock.ExpectQuery("SELECT (.+) FROM `report_templates` WHERE is_del = (.+)").
		WithArgs(isDel).
		WillReturnRows(rows)

	result, err := model.FindAll(WithIsDel(isDel))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, isDel, result[0].IsDel)
	assert.NoError(t, mock.ExpectationsWereMet())
}

// 测试常量
func TestConstants(t *testing.T) {
	// 测试模块常量
	assert.Equal(t, 1, SureAssetsModule)
	assert.Equal(t, 2, UnsureAssetsModule)
	assert.Equal(t, 3, NumberAssetsModule)
	assert.Equal(t, 4, PocModule)
	assert.Equal(t, 5, DataLeakModule)
	assert.Equal(t, 6, ThreatenAssetsModule)

	// 测试模板类型常量
	assert.Equal(t, 1, TemplateSystem)
	assert.Equal(t, 2, TemplateAuto)

	// 测试删除状态常量
	assert.Equal(t, 1, DeleteOK)
	assert.Equal(t, 0, DeleteNo)
}

func TestModuleNameMap(t *testing.T) {
	assert.Equal(t, "资产台账", ModuleNameMap[SureAssetsModule])
	assert.Equal(t, "疑似资产", ModuleNameMap[UnsureAssetsModule])
	assert.Equal(t, "数字资产", ModuleNameMap[NumberAssetsModule])
	assert.Equal(t, "漏洞检测", ModuleNameMap[PocModule])
	assert.Equal(t, "数据泄露检测", ModuleNameMap[DataLeakModule])
	assert.Equal(t, "威胁资产", ModuleNameMap[ThreatenAssetsModule])
}

// 测试边界情况
func TestReportTemplate_Fields(t *testing.T) {
	template := &ReportTemplate{
		ID:                  1,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
		Name:                "测试模板",
		UserId:              123,
		CompanyId:           456,
		OperatorId:          0,
		DataRangeType:       1,
		TimeType:            1,
		Type:                TemplateAuto,
		Email:               "",
		ReportModule:        ReportModules{1, 2, 3, 4, 5, 6},
		KnowAssetsParam:     "",
		UnknowAssetsParam:   "",
		AppAssetsParam:      "",
		PocDataParam:        "",
		SensitiveDataParam:  "",
		ThreatenAssetsParam: "",
		WarningEventParam:   "",
		DetectAssetsTasksId: 789,
		IsDel:               DeleteNo,
		IsShowLeakImage:     1,
	}

	assert.Equal(t, uint(1), template.ID)
	assert.Equal(t, "测试模板", template.Name)
	assert.Equal(t, uint64(123), template.UserId)
	assert.Equal(t, uint64(456), template.CompanyId)
	assert.Equal(t, TemplateAuto, template.Type)
	assert.Equal(t, ReportModules{1, 2, 3, 4, 5, 6}, template.ReportModule)
	assert.Equal(t, uint64(789), template.DetectAssetsTasksId)
	assert.Equal(t, DeleteNo, template.IsDel)
}

// 测试ReportModules的driver.Valuer接口
func TestReportModules_DriverValuer(t *testing.T) {
	modules := ReportModules{1, 2, 3}

	// 测试实现了driver.Valuer接口
	var _ driver.Valuer = modules

	value, err := modules.Value()
	assert.NoError(t, err)
	assert.NotNil(t, value)
}

// 测试ReportModules的sql.Scanner接口
func TestReportModules_SqlScanner(t *testing.T) {
	var modules ReportModules

	// 测试空字节切片
	err := modules.Scan([]byte("[]"))
	assert.NoError(t, err)
	assert.Equal(t, ReportModules{}, modules)

	// 测试空字符串
	err = modules.Scan("")
	assert.Error(t, err) // 空字符串不是有效的JSON
}

// 测试复杂的ReportModules操作
func TestReportModules_Complex(t *testing.T) {
	// 测试包含所有模块的情况
	allModules := ReportModules{
		SureAssetsModule,
		UnsureAssetsModule,
		NumberAssetsModule,
		PocModule,
		DataLeakModule,
		ThreatenAssetsModule,
	}

	value, err := allModules.Value()
	assert.NoError(t, err)

	var scannedModules ReportModules
	err = scannedModules.Scan(value)
	assert.NoError(t, err)
	assert.Equal(t, allModules, scannedModules)
}

// 测试查询条件组合使用
func TestCombinedConditions(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()

	rows := sqlmock.NewRows([]string{
		"id", "created_at", "updated_at", "name", "user_id", "company_id",
		"type", "report_module", "detect_assets_tasks_id", "is_del",
	}).AddRow(1, time.Now(), time.Now(), "测试模板", 123, 456, TemplateAuto, "[]", 789, DeleteNo)

	mock.ExpectQuery("SELECT (.+) FROM `report_templates` WHERE user_id = (.+) AND company_id = (.+) AND is_del = (.+)").
		WithArgs(uint64(123), uint64(456), DeleteNo).
		WillReturnRows(rows)

	result, err := model.FindAll(
		WithUserId(123),
		WithCompanyId(456),
		WithIsDel(DeleteNo),
	)
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestSqlFieldCount(t *testing.T) {
	t.Skip("跳过此测试，因为它尝试连接到真实的数据库")

	template := &ReportTemplate{
		Name:                "测试模板",
		UserId:              123,
		CompanyId:           456,
		OperatorId:          0,
		DataRangeType:       1,
		TimeType:            1,
		Type:                TemplateAuto,
		Email:               "",
		ReportModule:        ReportModules{1, 2, 3},
		KnowAssetsParam:     "",
		UnknowAssetsParam:   "",
		AppAssetsParam:      "",
		PocDataParam:        "",
		SensitiveDataParam:  "",
		ThreatenAssetsParam: "",
		WarningEventParam:   "",
		DetectAssetsTasksId: 789,
		IsDel:               DeleteNo,
		IsShowLeakImage:     1,
	}

	// 使用真实的数据库连接
	db, err := gorm.Open(mysql.Open("root:password@tcp(localhost:3306)/test?charset=utf8mb4&parseTime=True&loc=Local"), &gorm.Config{
		DryRun: true, // 只生成SQL语句，不执行
	})
	assert.NoError(t, err)

	// 创建操作
	stmt := db.ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Create(template)
	})
	t.Logf("Create SQL: %s", stmt)

	// 更新操作
	stmt = db.ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Model(&ReportTemplate{}).Where("id = ?", 1).Updates(template)
	})
	t.Logf("Update SQL: %s", stmt)

	// Map更新操作
	values := map[string]interface{}{
		"name": "更新名称",
		"type": TemplateSystem,
	}
	stmt = db.ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Model(&ReportTemplate{}).Where("id = ?", 1).Updates(values)
	})
	t.Logf("UpdateMap SQL: %s", stmt)
}
