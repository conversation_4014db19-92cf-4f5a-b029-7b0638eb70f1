package scan_task

import (
	"micro-service/middleware/mysql"

	"gorm.io/gorm"
)

// Model 扫描任务模型接口
type Model interface {
	// List 获取任务列表
	List(page, size int, opts ...mysql.HandleFunc) ([]ScanTasks, int64, error)
	// First 获取单个任务
	First(opts ...mysql.HandleFunc) (*ScanTasks, error)
	// Create 创建任务
	Create(task *ScanTasks) error
	// Update 更新任务
	Update(task *ScanTasks) error
	// Delete 删除任务
	Delete(id uint64) error
	// UpdateByMap 根据条件批量更新
	UpdateByMap(updates map[string]interface{}, opts ...mysql.HandleFunc) error
}

// defaultModel 默认模型实现
type defaultModel struct {
	*gorm.DB
}

// NewModel 创建模型实例
func NewModel(clients ...*gorm.DB) Model {
	return &defaultModel{
		DB: mysql.GetDbClient(clients...),
	}
}

// List 获取任务列表
func (m *defaultModel) List(page, size int, opts ...mysql.HandleFunc) ([]ScanTasks, int64, error) {
	query := m.DB.Model(&ScanTasks{})
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	var list []ScanTasks

	if !mysql.IsPageAll(page, size) {
		err := query.Count(&total).Error
		if err != nil {
			return nil, 0, err
		}

		err = query.Scopes(mysql.PageLimit(page, size)).Find(&list).Error
		if err != nil {
			return nil, 0, err
		}
	} else {
		err := query.Find(&list).Error
		if err != nil {
			return nil, 0, err
		}
		total = int64(len(list))
	}

	return list, total, nil
}

// First 获取单个任务
func (m *defaultModel) First(opts ...mysql.HandleFunc) (*ScanTasks, error) {
	query := m.DB.Model(&ScanTasks{})
	for _, opt := range opts {
		opt(query)
	}

	var task ScanTasks
	err := query.First(&task).Error
	if err != nil {
		return nil, err
	}

	return &task, nil
}

// Create 创建任务
func (m *defaultModel) Create(task *ScanTasks) error {
	return m.DB.Create(task).Error
}

// Update 更新任务
func (m *defaultModel) Update(task *ScanTasks) error {
	return m.DB.Updates(task).Error
}

// Delete 删除任务
func (m *defaultModel) Delete(id uint64) error {
	return m.DB.Delete(&ScanTasks{}, id).Error
}

// UpdateByMap 根据条件批量更新
func (m *defaultModel) UpdateByMap(updates map[string]interface{}, opts ...mysql.HandleFunc) error {
	query := m.DB.Model(&ScanTasks{})
	for _, opt := range opts {
		opt(query)
	}

	return query.Updates(updates).Error
}

// WithID ID条件
func WithID(id uint64) mysql.HandleFunc {
	return mysql.WithColumnValue("id", id)
}

// WithUserID 用户ID条件
func WithUserID(userID uint64) mysql.HandleFunc {
	return mysql.WithColumnValue("user_id", userID)
}

// WithCompanyID 企业ID条件
func WithCompanyID(companyID int64) mysql.HandleFunc {
	return mysql.WithColumnValue("company_id", companyID)
}

// WithTaskType 任务类型条件
func WithTaskType(taskType int) mysql.HandleFunc {
	return mysql.WithColumnValue("task_type", taskType)
}

// WithStatus 状态条件
func WithStatus(status int) mysql.HandleFunc {
	return mysql.WithColumnValue("status", status)
}

// WithName 名称模糊查询条件
func WithName(name string) mysql.HandleFunc {
	return mysql.WithLRLike("name", name)
}
