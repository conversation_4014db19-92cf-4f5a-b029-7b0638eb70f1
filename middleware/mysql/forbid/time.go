package forbid

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"micro-service/pkg/dbx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"strings"
	"time"

	"gorm.io/gorm"

	"micro-service/middleware/mysql"
)

const SwitchEnable = 1
const SwitchDisable = 2

type (
	ForbidTimesModel interface {
		First(opts ...mysql.HandleFunc) (ForbidTimes, error)
		List(page, size int, opts ...mysql.HandleFunc) ([]ForbidTimes, int64, error)
		Update(item *ForbidTimes) error
		Updates(items []ForbidTimes) error
		DeleteByIds(userId uint64, ids ...uint64) error
		GetWeekDayByInt() int
		InWeekDay(cycle string, weekDay int) bool
		IsForbidTime(userId uint64) bool
	}

	defaultForbidTimesModel struct {
		*gorm.DB
		table string
	}

	ForbidTimes struct {
		dbx.Model
		UserId    uint64 `json:"user_id"`
		CompanyId uint64 `json:"company_id"`
		Switch    int    `json:"switch"`
		Cycle     string `json:"cycle"`
		StartAt   string `json:"start_at"`
		EndAt     string `json:"end_at"`
	}
)

const forbidTimeTable = "forbid_times"

// TableName 表名
func (d *defaultForbidTimesModel) TableName() string {
	return forbidTimeTable
}

func NewForbidTimeModel(db ...*gorm.DB) *defaultForbidTimesModel {
	return &defaultForbidTimesModel{mysql.GetDbClient(db...), forbidTimeTable}
}

func (d *defaultForbidTimesModel) First(opts ...mysql.HandleFunc) (ForbidTimes, error) {
	query := d.DB.Model(ForbidTimes{})
	for _, opt := range opts {
		opt(query)
	}
	var item ForbidTimes
	if err := query.First(&item).Error; err != nil {
		return ForbidTimes{}, err
	}
	return item, nil
}

func (d *defaultForbidTimesModel) List(page, size int, opts ...mysql.HandleFunc) ([]ForbidTimes, int64, error) {
	query := d.DB.Model(&ForbidTimes{})
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	var items []ForbidTimes
	if !mysql.IsPageAll(page, size) {
		query.Count(&total).Scopes(mysql.PageLimit(page, size))
	}
	err := query.Find(&items).Error
	if err != nil {
		return nil, 0, err
	}
	return items, total, nil
}

func (d *defaultForbidTimesModel) Update(item *ForbidTimes) error {
	return d.DB.Updates(item).Error
}

func (d *defaultForbidTimesModel) Updates(items []ForbidTimes) error {
	if len(items) == 0 {
		return nil
	}
	return d.DB.Save(items).Error
}

func (d *defaultForbidTimesModel) DeleteByIds(userId uint64, ids ...uint64) error {
	if len(ids) == 0 {
		return nil
	}

	query := d.DB.Where("id IN (?)", ids)
	if userId != 0 {
		query = query.Where("user_id = ?", userId)
	}
	return query.Delete(&ForbidTimes{}).Error
}

// IsForbidTime 检查是否是封禁时间,是:true,否false
func (d *defaultForbidTimesModel) IsForbidTime(userId uint64) bool {
	cstSh, err := time.LoadLocation("Asia/Shanghai")
	// 无用户
	if userId == 0 {
		return false
	}
	// 获取用户禁扫设置
	forbidTime, err := d.First(mysql.WithWhere("user_id", userId), mysql.WithWhere("switch", SwitchEnable))
	if err != nil {
		log.Warnf(fmt.Sprintf("ForbidTime: user_id:%d,Error:%s", userId, err.Error()))
		return false
	}
	// 不在禁扫天里面
	if !d.InWeekDay(forbidTime.Cycle, d.GetWeekDayByInt()) {
		return false
	}
	// 判断禁扫时间
	nowTime := time.Now()
	startAt := strings.Split(forbidTime.StartAt, ":")
	endAt := strings.Split(forbidTime.EndAt, ":")
	startTime := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), cast.ToInt(startAt[0]), cast.ToInt(startAt[1]), 0, 0, cstSh)
	endTime := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), cast.ToInt(endAt[0]), cast.ToInt(endAt[1]), 59, 59, cstSh)
	return nowTime.Unix() >= startTime.Unix() && nowTime.Unix() <= endTime.Unix()
}

// InWeekDay 判断是否在封禁周内
func (d *defaultForbidTimesModel) InWeekDay(cycle string, weekDay int) bool {
	cycles := make([]int, 0)
	if err := json.Unmarshal([]byte(cycle), &cycles); err != nil {
		return false
	}
	if utils.ListContains(cycles, weekDay) {
		return true
	}
	return false
}

// GetWeekDayByInt 获取当前周几
func (d *defaultForbidTimesModel) GetWeekDayByInt() int {
	//周期: 1周一、2周二、3周三、4周四、5周五、6周六、7周日
	dty := time.Now().Weekday().String()
	var WeekDayMap = map[string]int{
		"Monday":    1,
		"Tuesday":   2,
		"Wednesday": 3,
		"Thursday":  4,
		"Friday":    5,
		"Saturday":  6,
		"Sunday":    7,
	}
	return WeekDayMap[dty]
}
