package black_forbidden_domains

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

type Model interface {
	Create(item *Domain) error
	Upsert(item ...*Domain) error
	First(opts ...mysql.HandleFunc) (Domain, error)
	List(page, size int, opts ...mysql.HandleFunc) ([]*Domain, int64, error)
	ListAll(opts ...mysql.HandleFunc) ([]string, error)
}

type Domain struct {
	dbx.Model
	Domain string `gorm:"column:domain"` // 域名
}

const TableName = "black_forbidden_domains"

func (*Domain) TableName() string {
	return TableName
}

func NewBlackDomainsModel(clients ...*gorm.DB) Model {
	return &defaultImpl{DB: mysql.GetDbClient(clients...)}
}

type defaultImpl struct{ *gorm.DB }

func (d *defaultImpl) Create(item *Domain) error {
	return d.DB.Create(item).Error
}

func (d *defaultImpl) Upsert(items ...*Domain) error {
	if len(items) == 0 {
		return nil
	}
	return d.DB.Save(items).Error
}

func (d *defaultImpl) First(opts ...mysql.HandleFunc) (Domain, error) {
	q := d.DB.Model(&Domain{})
	for _, opt := range opts {
		opt(q)
	}

	var info Domain
	err := q.First(&info).Error
	return info, err
}

func (d *defaultImpl) List(page, size int, opts ...mysql.HandleFunc) ([]*Domain, int64, error) {
	q := d.DB.Model(&Domain{})
	for _, opt := range opts {
		opt(q)
	}

	var total int64
	if !mysql.IsPageAll(page, size) {
		q.Count(&total).Scopes(mysql.PageLimit(page, size))
	}

	var list []*Domain
	err := q.Find(&list).Error
	return list, total, err
}

func (d *defaultImpl) DeleteByIds(ids ...uint64) error {
	if len(ids) == 0 {
		return nil
	}
	return d.DB.Where("`id` IN (?)", ids).Delete(&Domain{}).Error
}

func (d *defaultImpl) FirstOrCreate(domain string) (Domain, error) {
	var info Domain
	q := d.DB.Model(&Domain{}).Where("`domain`=?", domain)
	err := q.FirstOrCreate(&info).Error
	return info, err
}

func (d *defaultImpl) ListAll(opts ...mysql.HandleFunc) ([]string, error) {
	q := d.DB.Model(&Domain{})
	for _, opt := range opts {
		opt(q)
	}

	var list []string
	err := q.Select("domain").Find(&list).Error
	return list, err
}
