package company_clues

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

type (
	CompanyCluesModel interface {
		First(opts ...mysql.HandleFunc) (CompanyClues, error)
		ListAll(opts ...mysql.HandleFunc) ([]*CompanyClues, error)
		Save(record ...*CompanyClues) error
	}

	defaultCompanyCluesModel struct {
		*gorm.DB
		table string
	}

	CompanyClues struct {
		dbx.Model

		UserId          int64  `json:"user_id" gorm:"column:user_id"`                     // 用户id
		CompanyId       int64  `json:"company_id" gorm:"column:company_id"`               // 用户企业ID
		Content         string `json:"content" gorm:"column:content"`                     // 线索内容
		ClueCompanyName string `json:"clue_company_name" gorm:"column:clue_company_name"` // 线索的企业名称
		Hash            int64  `json:"hash" gorm:"column:hash"`                           // logo hash值
		PunycodeDomain  string `json:"punycode_domain" gorm:"column:punycode_domain"`     // punycode编码域名
		Type            int8   `json:"type" gorm:"column:type"`                           // 线索类型；0：根域；1：证书；2：ICP；3：LOGO；4：关键词 5：子域名
	}
)

const (
	CompanyCluesTypeDomain = iota
	CompanyCluesTypeCert
	CompanyCluesTypeICP
	CompanyCluesTypeLOGO
	CompanyCluesTypeKeyword
	CompanyCluesTypeSubdomain
	CompanyCluesTypeIP

	CompanyCluesTypeFID = 10
)

var CompanyCluesTypeMap = map[int8]string{
	CompanyCluesTypeDomain:    "根域",
	CompanyCluesTypeCert:      "证书",
	CompanyCluesTypeICP:       "ICP",
	CompanyCluesTypeLOGO:      "ICON",
	CompanyCluesTypeKeyword:   "关键词",
	CompanyCluesTypeSubdomain: "子域名",
	CompanyCluesTypeIP:        "ip",
	CompanyCluesTypeFID:       "FID",
}

const CompanyCluesTable = "company_clues"

// TableName 表名
func (c *CompanyClues) TableName() string {
	return CompanyCluesTable
}

func NewCompanyCluesModel(conn ...*gorm.DB) CompanyCluesModel {
	return &defaultCompanyCluesModel{mysql.GetDbClient(conn...), CompanyCluesTable}
}

func (d *defaultCompanyCluesModel) First(opts ...mysql.HandleFunc) (CompanyClues, error) {
	q := d.DB.Model(&CompanyClues{})
	for _, f := range opts {
		f(q)
	}

	var info CompanyClues
	err := q.First(&info).Error
	return info, err
}

func (d *defaultCompanyCluesModel) ListAll(opts ...mysql.HandleFunc) ([]*CompanyClues, error) {
	query := d.DB.Model(&CompanyClues{})
	for _, opt := range opts {
		opt(query)
	}

	var records []*CompanyClues
	err := query.Find(&records).Error
	return records, err
}

// Save will update record all fields
func (d *defaultCompanyCluesModel) Save(records ...*CompanyClues) error {
	if len(records) == 0 {
		return nil
	}
	return d.DB.Save(records).Error
}
