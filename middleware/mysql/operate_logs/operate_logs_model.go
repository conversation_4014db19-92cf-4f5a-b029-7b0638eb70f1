package operate_logs

import (
	"micro-service/middleware/mysql"
	"time"

	"gorm.io/gorm"
)

const table = "operate_logs"

type (
	OperateLogsModel interface {
		CountByUserIDAndTime(userID uint64, day string) (int64, error)
		GetLastLoginTime(userId uint64) (time.Time, error)
	}

	defaultOperateLogsModel struct {
		*gorm.DB
		table string
	}

	OperateLogs struct {
		Id        uint64    `gorm:"column:id;type:bigint(20) unsigned;AUTO_INCREMENT;primary_key" json:"id"`
		CreatedAt time.Time `gorm:"column:created_at;type:timestamp" json:"created_at"`
		UpdatedAt time.Time `gorm:"column:updated_at;type:timestamp" json:"updated_at"`

		UserId    uint64 `gorm:"column:user_id;type:bigint(20) unsigned;comment:用户id;NOT NULL" json:"user_id"`
		CompanyId uint64 `gorm:"column:company_id;type:bigint(20) unsigned;comment:企业ID" json:"company_id"`
		Username  string `gorm:"column:username;type:varchar(255);comment:用户名" json:"username"`
		Ip        string `gorm:"column:ip;type:varchar(255);comment:登录IP" json:"ip"`
		Role      int    `gorm:"column:role;type:int(11);comment:用户类型: 1/2/3 超级管理员/安服人员/企业租户;NOT NULL" json:"role"`
		Type      int    `gorm:"column:type;type:tinyint(4);default:1;comment:日志类型 默认 0/登录日志 1/操作日志 2/系统日志;NOT NULL" json:"type"`
		Content   string `gorm:"column:content;type:text;comment:操作内容;NOT NULL" json:"content"`
		Model     int    `gorm:"column:model;type:tinyint(4);comment:操作模块 1/资产发现  2/资产认领 3/已知资产 4/漏洞管理 5/报告管理 6/系统管理;NOT NULL" json:"model"`
	}
)

const (
	LOGIN_MODEL = iota
	FIND_ASSETS
	SIGN_ASSETS
	KNOWN_ASSETS
	POC_MANAGE
	REPORT_MANAGE
	SYSTEM_MANAGE
	PERSON_CENTER
)
const (
	TYPE_LOGIN = iota
	TYPE_OPERATE
	TYPE_SYSTEM
)

func NewModel(conn ...*gorm.DB) OperateLogsModel {
	return &defaultOperateLogsModel{mysql.GetDbClient(conn...), table}
}

func (h *OperateLogs) TableName() string {
	return table
}

func (d *defaultOperateLogsModel) GetLastLoginTime(userId uint64) (time.Time, error) {
	var operateLogs OperateLogs
	err := d.DB.Table(d.table).Where("user_id = ? and type = 0 and content= '登录'", userId).Order("created_at desc").First(&operateLogs).Error
	if err != nil {
		return time.Time{}, err
	}
	return operateLogs.CreatedAt, err
}

func (d *defaultOperateLogsModel) CountByUserIDAndTime(userID uint64, day string) (int64, error) {
	// select count(*) from operate_logs where user_id = 559 and type = 0 and created_at >= '2023-02-02 00:00:00';
	var r int64
	err := d.DB.Table(d.table).Where("type = 0 and user_id = ? and  created_at >= ?", userID, day).Count(&r).Error
	return r, err
}
