package domain_task_golang

import (
	"database/sql"
	"regexp"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"micro-service/pkg/dbx"
)

func setupTestDB(t *testing.T) (*gorm.DB, sqlmock.Sqlmock, error) {
	var err error
	var db *sql.DB
	db, mock, err := sqlmock.New()
	if err != nil {
		return nil, nil, err
	}

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})

	return gormDB, mock, err
}

func TestTask_Create(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := &defaultTask{DB: gormDB}

	testCases := []struct {
		name    string
		input   *Task
		mockSQL func()
		wantErr bool
	}{
		{
			name: "successful creation",
			input: &Task{
				Model:    dbx.Model{Id: 1},
				Name:     "Test Task",
				Status:   TaskStatusDefault,
				Level:    1,
				Progress: 0.0,
				DomainList: &sql.NullString{
					String: "test.com,example.com",
					Valid:  true,
				},
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec(regexp.QuoteMeta("INSERT INTO `golang_domain_tasks`")).
					WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name: "creation failure",
			input: &Task{
				Name: "Failed Task",
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec(regexp.QuoteMeta("INSERT INTO `golang_domain_tasks`")).
					WillReturnError(gorm.ErrInvalidTransaction)
				mock.ExpectRollback()
			},
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSQL()
			err := model.Create(tc.input)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestTask_FindById(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := &defaultTask{DB: gormDB}

	testCases := []struct {
		name    string
		id      uint64
		mockSQL func()
		wantErr bool
	}{
		{
			name: "successful find",
			id:   1,
			mockSQL: func() {
				rows := sqlmock.NewRows([]string{"id", "name", "status", "level", "progress"}).
					AddRow(1, "Test Task", TaskStatusDefault, 1, 0.0)
				mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `golang_domain_tasks`")).
					WithArgs(1).
					WillReturnRows(rows)
			},
			wantErr: false,
		},
		{
			name: "record not found",
			id:   999,
			mockSQL: func() {
				mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `golang_domain_tasks`")).
					WithArgs(999).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "status"}))
			},
			wantErr: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSQL()
			result, err := model.FindById(tc.id)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if tc.id == 1 {
					assert.Equal(t, uint64(1), result.Id)
					assert.Equal(t, "Test Task", result.Name)
					assert.Equal(t, uint8(TaskStatusDefault), result.Status)
				} else {
					assert.Empty(t, result.Name)
				}
			}
		})
	}
}

func TestTask_Updates(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := &defaultTask{DB: gormDB}

	testCases := []struct {
		name    string
		input   *Task
		mockSQL func()
		wantErr bool
	}{
		{
			name: "successful update",
			input: &Task{
				Model:    dbx.Model{Id: 1},
				Status:   TaskStatusDoing,
				Progress: 50.0,
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("UPDATE `golang_domain_tasks` SET").
					WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name: "update failure",
			input: &Task{
				Model:  dbx.Model{Id: 999},
				Status: TaskStatusFail,
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("UPDATE `golang_domain_tasks` SET").
					WillReturnError(gorm.ErrRecordNotFound)
				mock.ExpectRollback()
			},
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSQL()
			err := model.Updates(tc.input)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestTask_DeleteByIds(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := &defaultTask{DB: gormDB}

	testCases := []struct {
		name    string
		ids     []uint64
		mockSQL func()
		wantErr bool
	}{
		{
			name: "successful delete multiple",
			ids:  []uint64{1, 2, 3},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec(regexp.QuoteMeta("DELETE FROM `golang_domain_tasks` WHERE `id` IN (?,?,?)")).
					WithArgs(1, 2, 3).
					WillReturnResult(sqlmock.NewResult(0, 3))
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name: "successful delete single",
			ids:  []uint64{1},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec(regexp.QuoteMeta("DELETE FROM `golang_domain_tasks` WHERE `id` IN (?)")).
					WithArgs(1).
					WillReturnResult(sqlmock.NewResult(0, 1))
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name:    "empty ids",
			ids:     []uint64{},
			mockSQL: func() {},
			wantErr: false,
		},
		{
			name: "delete failure",
			ids:  []uint64{999},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec(regexp.QuoteMeta("DELETE FROM `golang_domain_tasks` WHERE `id` IN (?)")).
					WithArgs(999).
					WillReturnError(gorm.ErrInvalidTransaction)
				mock.ExpectRollback()
			},
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSQL()
			err := model.DeleteByIds(tc.ids...)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestTask_TableName(t *testing.T) {
	task := &Task{}
	assert.Equal(t, DomainTaskGoTableName, task.TableName())
}
