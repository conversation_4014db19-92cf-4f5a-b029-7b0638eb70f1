package phishing_fake_task

import (
	"testing"

	initmysql "micro-service/initialize/mysql"
	"micro-service/middleware/mysql"
	"micro-service/pkg/cfg"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func initCfg() {
	cfg.InitLoadCfg()
	_ = initmysql.GetInstance(cfg.LoadMysql())
}

func TestList(t *testing.T) {
	initCfg()

	// list with page
	_, total, err := NewTasker().List(1, 5,
		mysql.WithColumnValue("user_id", -1),
		WithCreated<PERSON>t(1, 2),
		WithOperator("foradar"),
	)
	assert.Nil(t, err)
	assert.Equal(t, total, int64(0))

	// list all
	_, err = NewTasker().ListAll(mysql.WithColumnValue("user_id", 1))
	assert.Nil(t, err)
}

func Test_CRUD(t *testing.T) {
	initCfg()

	// create
	task := &Task{
		CompanyId:    10,
		UserId:       100,
		GroupId:      50,
		Step:         4,
		CluesCount:   "100",
		DomainOnline: 100,
	}
	err := NewTasker().Create(task)
	assert.Nil(t, err)

	// update
	task.Step = 3
	task.DomainOffline = 100
	task.DomainOnline = 1
	task.ExpendFlags = GetExpendFlagId(task.UserId, "钓鱼仿冒任务")
	err = NewTasker().Update(*task)
	assert.Nil(t, err)

	// read
	item, err := NewTasker().First(mysql.WithColumnValue("id", task.ID))
	assert.Nil(t, err)
	assert.Equal(t, task.DomainOnline, item.DomainOnline)

	// delete
	err = NewTasker().Delete(task.ID)
	assert.Nil(t, err)
}

func TestFirst(t *testing.T) {
	initCfg()

	_, err := NewTasker().First(mysql.WithColumnValue("id", -1))
	assert.Equal(t, gorm.ErrRecordNotFound, err)
}
