package website_message

import (
	"micro-service/pkg/dbx"
)

// NotificationPolicy 通知策略表
type NotificationPolicy struct {
	dbx.Model
	UserId          uint64 `gorm:"column:user_id"`
	CompanyId       uint64 `gorm:"column:company_id"`
	AssetChange<PERSON>son string `gorm:"column:asset_change_json"` // 资产变化策略配置
	TaskJson        string `gorm:"column:task_json"`         // 任务变化策略配置
	RiskFoundJson   string `gorm:"column:risk_found_json"`   // 风险变化策略配置
	IsWebsiteMsg    uint64 `gorm:"column:is_website_msg"`    // 是否开启站内信通知 1是 2否
	IsEmail         uint64 `gorm:"column:is_email"`          // 是否邮箱通知 1是 2否
	Email           string `gorm:"column:email"`             // 邮箱
}

func (*NotificationPolicy) TableName() string {
	return NotificationPolicyTableName
}

func NewNotificationPolicyModel() WebsiteMessageFetcher {
	return NewWebsiteMessageModel()
}

// 站内信通知策略常量
const (
	// 是否开启站内信 1、开启 2、关闭
	YES_WEBSITE_MSG = 1
	NO_WEBSITE_MSG  = 2

	// 是否开启邮箱通知
	YES_EMAIL_MSG = 1
	NO_EMAIL_MSG  = 2

	// 模块 1:资产变化 2:任务状态 3:风险发现
	ASSETS_MODEL = 1
	TABLE_MODEL  = 2
	RISK_MODEL   = 3

	// 资产变化模块常量定义  // 1:IP资产 2:登录入口 3:域名资产 4:证书资产 5:业务系统 6:URL(API)资产 7:疑似资产 8:数字资产
	TABLE_IP      = 1
	LOGIN_PAGE    = 2
	DOMAIN_ASSETS = 3
	CERT_ASSETS   = 4
	BUSSINESS     = 5
	URL_API       = 6
	UNSURE_IP     = 7
	APP_ASSETS    = 8

	// 风险变化模块 18:漏洞数据 22：热点漏洞 23：风险情报
	POC_DATA = 18
	HOT_POC  = 22
	RISK_POC = 23

	// 消息类型 1/新增 2/删除
	ADD_TYPE    = 1
	DELETE_TYPE = 2
)
