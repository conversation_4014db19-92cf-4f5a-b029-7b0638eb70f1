package scan_crontab_tasks_ips

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"time"

	"gorm.io/gorm"
)

const (
	TableName = "cron_task_ips"
)

type (
	ScanCrontabTasksIp struct {
		dbx.Model
		CronTaskId uint64    `json:"cron_task_id" gorm:"column:cron_task_id"` // 周期任务表id
		Ip         string    `json:"ip" gorm:"column:ip"`                     // ip
		CreatedAt  time.Time `json:"created_at" gorm:"column:created_at"`     // 创建时间
		UpdatedAt  time.Time `json:"updated_at" gorm:"column:updated_at"`     // 更新时间
	}

	defaultScanCrontabTasksIpModel struct {
		*gorm.DB
		table string
	}

	ScanCrontabTasksIpModel interface {
		FindByCronTaskId(cronTaskId uint64) ([]*ScanCrontabTasksIp, error)
	}
)

func NewModel() ScanCrontabTasksIpModel {
	return &defaultScanCrontabTasksIpModel{
		DB:    mysql.GetDbClient(),
		table: TableName,
	}
}

// FindByCronTaskId 根据周期任务ID查询IP列表
func (m *defaultScanCrontabTasksIpModel) FindByCronTaskId(cronTaskId uint64) ([]*ScanCrontabTasksIp, error) {
	var result []*ScanCrontabTasksIp
	err := m.Table(m.table).Where("cron_task_id = ?", cronTaskId).Find(&result).Error
	return result, err
}
