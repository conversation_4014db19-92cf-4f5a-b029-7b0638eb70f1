package detect_task_report_result

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"time"

	"gorm.io/gorm"
)

// 测绘任务简报结果

type Resulter interface {
	Create(item *Result) error
	First(opts ...mysql.HandleFunc) (Result, error)
	UpdateAny(id uint64, m map[string]any) error
	Delete(ids ...uint64) error
}

type Result struct {
	dbx.Model
	UserID               uint64    `gorm:"column:user_id;not null" json:"user_id"`                                    // 用户ID
	CompanyID            uint64    `gorm:"column:company_id;default:NULL" json:"company_id"`                          // 用户企业ID
	DetectTaskID         uint64    `gorm:"column:detect_task_id;not null" json:"detect_task_id"`                      // 测绘任务的ID
	CompanyJSON          string    `gorm:"column:company_json" json:"company_json"`                                   // 所有测绘的企业名称的 JSON 字符串
	AllIPNum             string    `gorm:"column:all_ip_num;default:'0'" json:"all_ip_num"`                           // 发现的IP资产
	TableIPNum           string    `gorm:"column:table_ip_num;default:'0'" json:"table_ip_num"`                       // 资产台账的IP数量
	DomainNum            string    `gorm:"column:domain_num;default:'0'" json:"domain_num"`                           // 该测绘任务产生的域名数量
	CertNum              string    `gorm:"column:cert_num;default:'0'" json:"cert_num"`                               // 该测绘任务产生的证书数量
	LoginPageNum         string    `gorm:"column:login_page_num;default:'0'" json:"login_page_num"`                   // 该测绘任务产生的登录入口数量
	ShadowAssetsNum      string    `gorm:"column:shadow_assets_num;default:'0'" json:"shadow_assets_num"`             // 该测绘任务产生的影子资产数量
	DataAssetsNum        string    `gorm:"column:data_assets_num;default:'0'" json:"data_assets_num"`                 // 该测绘任务产生的数字资产数量
	AppNum               string    `gorm:"column:app_num;default:'0'" json:"app_num"`                                 // 该测绘任务产生的app的数量
	WechatNum            string    `gorm:"column:wechat_num;default:'0'" json:"wechat_num"`                           // 该测绘任务产生的公众号的数量
	RiskEventNum         string    `gorm:"column:risk_event_num;default:'0'" json:"risk_event_num"`                   // 该测绘任务产生的风险事件的个数
	InputClueJSON        string    `gorm:"column:input_clue_json" json:"input_clue_json"`                             // 初始线索数据的 JSON 字符串
	ICPClueJSON          string    `gorm:"column:icp_clue_json" json:"icp_clue_json"`                                 // ICP查到的线索的 JSON 字符串
	ExpendClueJSON       string    `gorm:"column:expend_clue_json" json:"expend_clue_json"`                           // 扩展过程中确认的线索的 JSON 字符串
	ConfirmAllClueJSON   string    `gorm:"column:confirm_all_clue_json" json:"confirm_all_clue_json"`                 // 单位测绘最终生成线索总表的时候的线索数据的 JSON 字符串
	RecommendIPNum       string    `gorm:"column:recommend_ip_num;default:'0'" json:"recommend_ip_num"`               // 单位测绘推荐流程跑出来的IP数量-自动发现
	RecommendInputIPNum  string    `gorm:"column:recommend_input_ip_num;default:'0'" json:"recommend_input_ip_num"`   // 单位测绘推荐流程导入的第三方数据的IP数量-第三方导入
	RecommendIPHighNum   string    `gorm:"column:recommend_ip_high_num;default:'0'" json:"recommend_ip_high_num"`     // 单位测绘的资产结果进到高可信度的资产的数量
	RecommendIPMiddleNum string    `gorm:"column:recommend_ip_middle_num;default:'0'" json:"recommend_ip_middle_num"` // 单位测绘的资产结果进到中可信度的资产的数量
	RecommendIPLowNum    string    `gorm:"column:recommend_ip_low_num;default:'0'" json:"recommend_ip_low_num"`       // 单位测绘的资产结果进到低可信度的资产的数量
	UnsureIPNum          string    `gorm:"column:unsure_ip_num;default:'0'" json:"unsure_ip_num"`                     // 单位测绘的资产结果进到疑似资产的数量
	ThreatenIPNum        string    `gorm:"column:threaten_ip_num;default:'0'" json:"threaten_ip_num"`                 // 单位测绘的资产结果进到威胁资产的数量
	DataLeakNum          string    `gorm:"column:data_leak_num;default:'0'" json:"data_leak_num"`                     // 该测绘任务产生的数据泄露的数量
	NetDiskNum           string    `gorm:"column:net_disk_num;default:'0'" json:"net_disk_num"`                       // 该测绘任务产生的网盘的数据泄露的数量
	LibraryNum           string    `gorm:"column:library_num;default:'0'" json:"library_num"`                         // 该测绘任务产生的文库的数据泄露的数量
	UrlApiNum            string    `gorm:"column:url_api_num;default:'0'" json:"url_api_num"`                         // 该测绘任务产生的URL-API资产的数量
	CodeNum              string    `gorm:"column:code_num;default:'0'" json:"code_num"`                               // 该测绘任务产生的文库的代码仓库的数量
	DataAssetsKeyword    string    `gorm:"column:data_assets_keyword" json:"data_assets_keyword"`                     // 该测绘任务的数字资产监控的关键词的 JSON 字符串
	DataLeakKeyword      string    `gorm:"column:data_leak_keyword" json:"data_leak_keyword"`                         // 该测绘任务的数据泄露监控的关键词的 JSON 字符串
	BurstDomainKeyword   string    `gorm:"column:brust_domain_keyword" json:"brust_domain_keyword"`                   // 该测绘任务的域名爆破任务的目标域名数据的 JSON 字符串
	UrlApiKeyword        string    `gorm:"column:url_api_keyword" json:"url_api_keyword"`                             // 该测绘任务的URL-API的目标域名数据的 JSON 字符串
	BurstDomainResultNum string    `gorm:"column:brust_domain_result_num"`                                            // 域名爆破结果数量
	MatchIPShortData     string    `gorm:"column:match_ip_short_data;default:NULL" json:"match_ip_short_data"`        // 该测绘任务的风险事件要匹配的IP数据，就取3条数据作为页面展示
	MatchIPData          string    `gorm:"column:match_ip_data" json:"match_ip_data"`                                 // 该测绘任务的风险事件要匹配的IP数据，总量数据的 JSON 字符串
	CreatedAt            time.Time `gorm:"column:created_at" json:"created_at"`                                       // 创建时间
	UpdatedAt            time.Time `gorm:"column:updated_at" json:"updated_at"`                                       // 更新时间
}

const TableName = "detect_tasks_results_report"

func (*Result) TableName() string {
	return TableName
}

type defaultResult struct{ *gorm.DB }

func NewCall(clients ...*gorm.DB) Resulter {
	return &defaultResult{DB: mysql.GetDbClient(clients...)}
}

func NewResult(id uint64) *Result {
	item := &Result{Model: dbx.Model{Id: id}}
	return item
}

func (d *defaultResult) Create(item *Result) error {
	return d.DB.Model(&Result{}).Create(item).Error
}

func (d *defaultResult) First(opts ...mysql.HandleFunc) (Result, error) {
	q := d.DB.Model(&Result{})
	for _, opt := range opts {
		opt(q)
	}

	var info Result
	err := q.First(&info).Error
	return info, err
}

func (d *defaultResult) UpdateAny(id uint64, m map[string]any) error {
	return d.DB.Model(&Result{}).Where("`id`=?", id).Updates(m).Error
}

func (d *defaultResult) Delete(ids ...uint64) error {
	if len(ids) == 0 {
		return nil
	}
	return d.DB.Delete(&Result{}, "`id` IN (?)", ids).Error
}
