package dataleak_github

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSortAndHash(t *testing.T) {
	cases := []struct {
		in, exp []string
	}{
		{in: []string{"2", "", "2", "1", "1"}, exp: []string{"1", "2"}},
		{in: []string{}},
		{in: []string{"d", "", "a", "", "d", "a"}, exp: []string{"a", "d"}},
	}

	for _, v := range cases {
		_, l := SortAndHash(v.in)
		assert.Equal(t, v.exp, l)
	}
}
