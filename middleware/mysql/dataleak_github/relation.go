package dataleak_github

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

const RelationTableName = "dataleak_github_relation"

type Relation struct {
	dbx.Model
	TaskId   uint64 `gorm:"column:task_id"`
	ResultId uint64 `gorm:"column:result_id"`
}

func (*Relation) TableName() string {
	return RelationTableName
}

func (d *defaultResultImpl) RelationListAll(opts ...mysql.HandleFunc) ([]*Relation, error) {
	q := d.DB.Model(&Relation{})
	for _, f := range opts {
		f(q)
	}

	var result []*Relation
	err := q.Find(&result).Error
	return result, err
}

func (d *defaultResultImpl) RelationSave(result []*Relation) error {
	if len(result) == 0 {
		return nil
	}
	return d.Save(&result).Error
}

func (d *defaultResultImpl) RelationFirst(opts ...mysql.HandleFunc) (Relation, error) {
	q := d.DB.Model(&Relation{})
	for _, f := range opts {
		f(q)
	}

	var info Relation
	err := q.First(&info).Error
	return info, err
}
