package cdn

import (
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	initmysql "micro-service/initialize/mysql"
	mysqlutil "micro-service/middleware/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dbx"
)

func init() {
	cfg.InitLoadCfg()
	// 确保测试环境标志被设置
	initmysql.SetTestEnv(true)
}

func TestCdn_TableName(t *testing.T) {
	cdn := &Cdn{}
	assert.Equal(t, TableName, cdn.TableName())
}

func TestNewCdnModel(t *testing.T) {
	// 确保mock实例被初始化
	mock := initmysql.GetMockInstance()
	assert.NotNil(t, mock)

	model := NewCdnModel()
	assert.NotNil(t, model)
}

func TestDefaultCdn_GetByQuery(t *testing.T) {
	// 确保mock实例被初始化
	mock := initmysql.GetMockInstance()
	assert.NotNil(t, mock)

	model := NewCdnModel()

	// 测试成功查询
	expectedCdns := []*Cdn{
		{
			Model:   dbx.Model{Id: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Id:      "cdn1",
			Type:    TypeDomain,
			Content: "cdn.example.com",
		},
		{
			Model:   dbx.Model{Id: 2, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			Id:      "cdn2",
			Type:    TypeIp,
			Content: "***********",
		},
	}

	rows := sqlmock.NewRows([]string{"id", "type", "content", "created_at", "updated_at"})
	for _, cdn := range expectedCdns {
		rows.AddRow(cdn.Id, cdn.Type, cdn.Content, cdn.CreatedAt, cdn.UpdatedAt)
	}

	mock.ExpectQuery("SELECT (.+) FROM `cdns`").
		WillReturnRows(rows)

	result, err := model.GetByQuery()
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.Equal(t, expectedCdns[0].Content, result[0].Content)
	assert.Equal(t, expectedCdns[1].Content, result[1].Content)
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试查询失败
	mock.ExpectQuery("SELECT (.+) FROM `cdns`").
		WillReturnError(sql.ErrConnDone)

	_, err = model.GetByQuery()
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultCdn_CountByQuery(t *testing.T) {
	mock := initmysql.GetMockInstance()
	assert.NotNil(t, mock)

	model := NewCdnModel()

	// 测试成功计数
	expectedCount := int64(5)
	countRows := sqlmock.NewRows([]string{"count"}).AddRow(expectedCount)
	mock.ExpectQuery("SELECT count(.+) FROM `cdns`").
		WillReturnRows(countRows)

	count, err := model.CountByQuery()
	assert.NoError(t, err)
	assert.Equal(t, expectedCount, count)
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试计数失败
	mock.ExpectQuery("SELECT count(.+) FROM `cdns`").
		WillReturnError(sql.ErrConnDone)

	_, err = model.CountByQuery()
	assert.Error(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultCdn_GetCdnIpCards(t *testing.T) {
	mock := initmysql.GetMockInstance()
	assert.NotNil(t, mock)

	model := NewCdnModel()

	// 测试成功获取CDN IP段
	expectedIpCards := []string{"***********/24", "10.0.0.0/8", "**********/12"}

	rows := sqlmock.NewRows([]string{"id", "type", "content", "created_at", "updated_at"})
	for _, ipCard := range expectedIpCards {
		rows.AddRow("cdn_id", TypeIp, ipCard, time.Now(), time.Now())
	}

	mock.ExpectQuery("SELECT (.+) FROM `cdns` WHERE `type` = ?").
		WithArgs(TypeIp).
		WillReturnRows(rows)

	result := model.GetCdnIpCards()
	assert.Len(t, result, 3)
	assert.Contains(t, result, "***********/24")
	assert.Contains(t, result, "10.0.0.0/8")
	assert.Contains(t, result, "**********/12")
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试查询失败
	mock.ExpectQuery("SELECT (.+) FROM `cdns` WHERE `type` = ?").
		WithArgs(TypeIp).
		WillReturnError(sql.ErrConnDone)

	result = model.GetCdnIpCards()
	assert.Nil(t, result)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultCdn_GetCdnDomains(t *testing.T) {
	mock := initmysql.GetMockInstance()
	assert.NotNil(t, mock)

	model := NewCdnModel()

	// 测试成功获取CDN域名
	expectedDomains := []string{"cdn1.example.com", "cdn2.example.com", "cdn3.example.com"}

	rows := sqlmock.NewRows([]string{"id", "type", "content", "created_at", "updated_at"})
	for _, domain := range expectedDomains {
		rows.AddRow("cdn_id", TypeDomain, domain, time.Now(), time.Now())
	}

	mock.ExpectQuery("SELECT (.+) FROM `cdns` WHERE `type` = ?").
		WithArgs(TypeDomain).
		WillReturnRows(rows)

	result := model.GetCdnDomains()
	assert.Len(t, result, 3)
	assert.Contains(t, result, "cdn1.example.com")
	assert.Contains(t, result, "cdn2.example.com")
	assert.Contains(t, result, "cdn3.example.com")
	assert.NoError(t, mock.ExpectationsWereMet())

	// 测试查询失败
	mock.ExpectQuery("SELECT (.+) FROM `cdns` WHERE `type` = ?").
		WithArgs(TypeDomain).
		WillReturnError(sql.ErrConnDone)

	result = model.GetCdnDomains()
	assert.Nil(t, result)
	assert.NoError(t, mock.ExpectationsWereMet())
}

// 测试边界情况
func TestDefaultCdn_GetCdnIpCards_EmptyResult(t *testing.T) {
	mock := initmysql.GetMockInstance()
	assert.NotNil(t, mock)

	model := NewCdnModel()

	// 测试空结果
	rows := sqlmock.NewRows([]string{"id", "type", "content", "created_at", "updated_at"})

	mock.ExpectQuery("SELECT (.+) FROM `cdns` WHERE `type` = ?").
		WithArgs(TypeIp).
		WillReturnRows(rows)

	result := model.GetCdnIpCards()
	assert.Len(t, result, 0)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultCdn_GetCdnDomains_EmptyResult(t *testing.T) {
	mock := initmysql.GetMockInstance()
	assert.NotNil(t, mock)

	model := NewCdnModel()

	// 测试空结果
	rows := sqlmock.NewRows([]string{"id", "type", "content", "created_at", "updated_at"})

	mock.ExpectQuery("SELECT (.+) FROM `cdns` WHERE `type` = ?").
		WithArgs(TypeDomain).
		WillReturnRows(rows)

	result := model.GetCdnDomains()
	assert.Len(t, result, 0)
	assert.NoError(t, mock.ExpectationsWereMet())
}

// 测试常量
func TestCdnConstants(t *testing.T) {
	// TypeIp和TypeDomain在cdn.go中定义为int类型常量
	assert.Equal(t, int(1), TypeIp)
	assert.Equal(t, int(0), TypeDomain)
}

// 测试Cdn结构体字段
func TestCdnStruct(t *testing.T) {
	cdn := &Cdn{
		Model:   dbx.Model{Id: 1},
		Id:      "test_cdn",
		Type:    TypeDomain,
		Content: "test.example.com",
	}

	assert.Equal(t, uint64(1), cdn.Model.Id)
	assert.Equal(t, "test_cdn", cdn.Id)
	assert.Equal(t, uint(0), cdn.Type)
	assert.Equal(t, "test.example.com", cdn.Content)
}

// 测试查询条件
func TestDefaultCdn_GetByQuery_WithConditions(t *testing.T) {
	mock := initmysql.GetMockInstance()
	assert.NotNil(t, mock)

	model := NewCdnModel()

	// 测试带条件的查询
	expectedCdn := &Cdn{
		Model:   dbx.Model{Id: 1, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		Id:      "filtered_cdn",
		Type:    TypeIp,
		Content: "***********",
	}

	rows := sqlmock.NewRows([]string{"id", "type", "content", "created_at", "updated_at"}).
		AddRow(expectedCdn.Id, expectedCdn.Type, expectedCdn.Content, expectedCdn.CreatedAt, expectedCdn.UpdatedAt)

	mock.ExpectQuery("SELECT (.+) FROM `cdns` WHERE `type` = ?").
		WithArgs(TypeIp).
		WillReturnRows(rows)

	result, err := model.GetByQuery(mysqlutil.WithColumnValue("type", TypeIp))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	if len(result) > 0 {
		assert.Equal(t, expectedCdn.Content, result[0].Content)
		assert.Equal(t, expectedCdn.Type, result[0].Type)
	}
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultCdn_CountByQuery_WithConditions(t *testing.T) {
	mock := initmysql.GetMockInstance()
	assert.NotNil(t, mock)

	model := NewCdnModel()

	// 测试带条件的计数
	expectedCount := int64(3)
	countRows := sqlmock.NewRows([]string{"count"}).AddRow(expectedCount)
	mock.ExpectQuery("SELECT count\\(\\*\\) FROM `cdns` WHERE `type` = ?").
		WithArgs(TypeDomain).
		WillReturnRows(countRows)

	count, err := model.CountByQuery(mysqlutil.WithColumnValue("type", TypeDomain))
	assert.NoError(t, err)
	assert.Equal(t, expectedCount, count)
	assert.NoError(t, mock.ExpectationsWereMet())
}
