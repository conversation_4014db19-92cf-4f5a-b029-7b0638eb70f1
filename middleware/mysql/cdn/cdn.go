package cdn

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

const TableName = "cdns" // 业务系统

const (
	TypeIp     = 1
	TypeDomain = 0
)

type CdnModel interface {
	GetCdnDomains() []string
	GetCdnIpCards() []string
	GetByQuery(opts ...mysql.HandleFunc) ([]*Cdn, error)
	CountByQuery(opts ...mysql.HandleFunc) (int64, error)
}

type Cdn struct {
	dbx.Model
	Id      string `gorm:"column:id" json:"id"`
	Type    uint   `gorm:"column:type" json:"type"`       // CDN类型
	Content string `gorm:"column:content" json:"content"` // CDN数据
}

func (*Cdn) TableName() string {
	return TableName
}

type defaultCdn struct {
	*gorm.DB
}

func NewCdnModel(db ...*gorm.DB) CdnModel {
	return &defaultCdn{
		DB: mysql.GetDbClient(db...),
	}
}

func (d *defaultCdn) GetByQuery(opts ...mysql.HandleFunc) ([]*Cdn, error) {
	query := d.DB.Model(&Cdn{})
	for _, opt := range opts {
		opt(query)
	}
	list := make([]*Cdn, 0)
	if err := query.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (d *defaultCdn) CountByQuery(opts ...mysql.HandleFunc) (int64, error) {
	query := d.DB.Model(&Cdn{})
	for _, opt := range opts {
		opt(query)
	}
	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// GetCdnIpCards 获取CDN IP段
func (d *defaultCdn) GetCdnIpCards() []string {
	list := make([]Cdn, 0)
	ipCards := make([]string, 0)
	if err := d.DB.Model(&Cdn{}).Unscoped().Where("type", 1).Find(&list).Error; err != nil {
		return nil
	}
	for _, ipCard := range list {
		ipCards = append(ipCards, ipCard.Content)
	}
	return ipCards
}

// GetCdnDomains 获取CDN 域名
func (d *defaultCdn) GetCdnDomains() []string {
	list := make([]Cdn, 0)
	ipCards := make([]string, 0)
	if err := d.DB.Model(&Cdn{}).Unscoped().Where("type", 0).Find(&list).Error; err != nil {
		return nil
	}
	for _, ipCard := range list {
		ipCards = append(ipCards, ipCard.Content)
	}
	return ipCards
}
