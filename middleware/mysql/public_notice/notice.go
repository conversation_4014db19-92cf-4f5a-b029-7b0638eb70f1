package public_notice

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"micro-service/middleware/mysql"
	"time"
)

// 公告表
type (
	PublicNoticeModel interface {
		First() (PublicNotice, error)
		FindByID(uint64) (PublicNotice, error)
		List(Page, Size int) ([]PublicNotice, int64, error)
		Add(PublicNotice) (uint, error)
		Update(PublicNotice) error
		Del(uint64) error
	}

	defaultPublicNoticeModel struct {
		*gorm.DB
		table string
	}

	PublicNotice struct {
		gorm.Model
		Notice    string    `gorm:"column:notice;type:longtext;comment:公告" json:"notice"`
		UpAtStart time.Time `gorm:"column:up_at_start;type:datetime;comment:开始时间;Index:idx_up_at" json:"up_at_start"`
		UpAtEnd   time.Time `gorm:"column:up_at_end;type:datetime;comment:结束时间;Index:idx_up_at" json:"up_at_end"`
	}
)

const table = "public_notice"

func (m *PublicNotice) TableName() string {
	return table
}

func NewModel(conn ...*gorm.DB) PublicNoticeModel {
	return &defaultPublicNoticeModel{mysql.GetDbClient(conn...), table}
}

func (d *defaultPublicNoticeModel) First() (PublicNotice, error) {
	var notice PublicNotice
	err := d.DB.Table(d.table).Order("created_at desc").First(&notice).Error
	return notice, err
}

func (d *defaultPublicNoticeModel) FindByID(id uint64) (PublicNotice, error) {
	var notice PublicNotice
	err := d.DB.Table(d.table).Where("id = ?", id).Order("created_at desc").First(&notice).Error
	return notice, err
}

func (d *defaultPublicNoticeModel) List(Page, Size int) ([]PublicNotice, int64, error) {
	q := d.DB.Model(&PublicNotice{})
	var total int64
	var resp []PublicNotice
	err := q.Count(&total).Order("created_at desc").Scopes(Paginate(Page, Size)).Find(&resp).Error
	return resp, total, err

}

func (d *defaultPublicNoticeModel) Update(data PublicNotice) error {
	r := d.DB.Table(d.table).Where("id = ?", data.ID).Updates(&data)
	if r.Error != nil {
		return r.Error
	}

	if r.RowsAffected == 0 {
		return fmt.Errorf("记录不存在, id: %d", data.ID)
	}
	return nil
}

func Paginate(page, size int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		offset := (page - 1) * size
		if offset < 0 {
			offset = 0
		}
		return db.Offset(offset).Limit(size)
	}
}

func (d *defaultPublicNoticeModel) Add(req PublicNotice) (uint, error) {
	e := d.DB.Table(d.table).Clauses(clause.Insert{Modifier: "IGNORE"}).Create(&req).Error
	return req.ID, e
}

func (d *defaultPublicNoticeModel) Del(id uint64) error {
	r := d.DB.Table(d.table).Where("id = ?", id).Unscoped().Delete(&PublicNotice{})
	if r.Error != nil {
		return r.Error
	}

	if r.RowsAffected == 0 {
		return errors.New("不存在记录")
	}
	return nil
}
