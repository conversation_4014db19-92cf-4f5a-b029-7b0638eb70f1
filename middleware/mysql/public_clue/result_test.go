package public_clue

import (
	"fmt"
	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"testing"
	"time"
)

func TestDefaultPublicClueResultModel_FindByID(t *testing.T) {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())

	d, err := NewPublicClueResultModel(mysql.GetInstance()).FindByID([]string{"1111111.com", "2222222.com"})
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Println(d)
}

func TestDefaultPublicClueResultModel_Update(t *testing.T) {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())

	var demo []*PublicClueResult
	demo1 := &PublicClueResult{Id: 1, CreatedAt: time.Now(), UpdatedAt: time.Now(), ClueContent: "1111111.com", ClueType: 4}
	demo2 := &PublicClueResult{Id: 2, CreatedAt: time.Now(), UpdatedAt: time.Now(), ClueContent: "2222222.com", ClueType: 4}
	demo = append(demo, demo1, demo2)

	err := NewPublicClueResultModel(mysql.GetInstance()).BatchUpdate(demo)
	if err != nil {
		t.Error(err)
		return
	}
}
