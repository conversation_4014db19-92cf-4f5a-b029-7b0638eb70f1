package clue_black_keyword

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"time"

	"gorm.io/gorm"
)

const TableName = "clue_black_keyword"

const (
	STATS_ENABLE = 1
)

// ClueBlackKeyword 黑名单关键词表
type ClueBlackKeyword struct {
	dbx.Model
	UserId     uint64     `gorm:"column:user_id;comment:用户id" json:"user_id"`
	CompanyId  uint64     `gorm:"column:company_id;comment:企业ID" json:"company_id"`
	OperatorId uint64     `gorm:"column:operator_id;comment:安服角色的用户id" json:"operator_id"`
	Name       string     `gorm:"column:name;type:varchar(300);comment:关键词" json:"name"`
	Status     int8       `gorm:"column:status;type:tinyint(4);not null;default:1;comment:状态 1/2 启用/2禁用" json:"status"`
	Hash       int        `gorm:"column:hash;comment:logo hash值" json:"hash"`
	Type       int8       `gorm:"column:type;type:tinyint(4);not null;comment:线索类型；0：根域；1：证书；2：ICP；3：LOGO；4：关键词 5：子域名" json:"type"`
	DeletedAt  *time.Time `gorm:"column:deleted_at" json:"deleted_at"`
}

func (*ClueBlackKeyword) TableName() string {
	return TableName
}

type ClueBlackKeyworder interface {
	Create(*ClueBlackKeyword) error
	First(...mysql.HandleFunc) (ClueBlackKeyword, error)
	List(page, size int, opts ...mysql.HandleFunc) ([]ClueBlackKeyword, int64, error)
	ListAll(fields []string, opts ...mysql.HandleFunc) ([]*ClueBlackKeyword, error)
	Update(ClueBlackKeyword) error
	UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error
	DeleteById(uint64) error
	Search(params map[string]interface{}) *gorm.DB
	Count(opts ...mysql.HandleFunc) (int64, error)
}

func NewClueBlackKeyworder(db ...*gorm.DB) ClueBlackKeyworder {
	return &defaultClueBlackKeyword{DB: mysql.GetDbClient(db...)}
}

type defaultClueBlackKeyword struct{ *gorm.DB }

func (d *defaultClueBlackKeyword) Create(clue *ClueBlackKeyword) error {
	return d.DB.Create(clue).Error
}

func (d *defaultClueBlackKeyword) First(opts ...mysql.HandleFunc) (ClueBlackKeyword, error) {
	query := d.DB.Model(&ClueBlackKeyword{})
	for _, opt := range opts {
		opt(query)
	}

	var clue ClueBlackKeyword
	err := query.First(&clue).Error
	return clue, err
}

func (d *defaultClueBlackKeyword) List(page, size int, opts ...mysql.HandleFunc) ([]ClueBlackKeyword, int64, error) {
	query := d.DB.Model(&ClueBlackKeyword{})
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	var clues = make([]ClueBlackKeyword, 0)
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Find(&clues).Error
	return clues, total, err
}

func (d *defaultClueBlackKeyword) ListAll(fields []string, opts ...mysql.HandleFunc) ([]*ClueBlackKeyword, error) {
	query := d.DB.Model(&ClueBlackKeyword{})
	for _, opt := range opts {
		opt(query)
	}
	dataList := make([]*ClueBlackKeyword, 0)
	if len(fields) > 0 {
		err := query.Select(fields).Find(&dataList).Error
		return dataList, err
	} else {
		err := query.Find(&dataList).Error
		return dataList, err
	}
}

func (d *defaultClueBlackKeyword) Update(clue ClueBlackKeyword) error {
	return d.DB.Updates(&clue).Error
}

func (d *defaultClueBlackKeyword) UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error {
	q := d.DB.Model(&ClueBlackKeyword{})
	for _, opt := range opts {
		opt(q)
	}
	return q.Updates(m).Error
}

func (d *defaultClueBlackKeyword) DeleteById(id uint64) error {
	return d.DB.Delete(&ClueBlackKeyword{}, id).Error
}

// Search 搜索黑名单关键词
func (d *defaultClueBlackKeyword) Search(params map[string]interface{}) *gorm.DB {
	query := d.DB.Model(&ClueBlackKeyword{})

	// 关键词搜索
	if keyword, ok := params["keyword"].(string); ok && keyword != "" {
		query = query.Where("name LIKE ? OR hash LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 名称搜索
	if name, ok := params["name"].(string); ok && name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	// 创建时间范围
	if createdRange, ok := params["created_at_range"].([]time.Time); ok && len(createdRange) == 2 {
		endTime := createdRange[1].Add(24 * time.Hour).Add(-time.Second) // 设置为当天结束时间
		query = query.Where("created_at BETWEEN ? AND ?", createdRange[0], endTime)
	}

	// 类型筛选
	if typeStr, ok := params["type"].(string); ok && typeStr != "" {
		query = query.Where("type = ?", typeStr)
	}

	// 状态筛选
	if status, ok := params["status"].(string); ok && status != "" {
		if status == "2" {
			query = query.Where("status = ?", 2)
		} else {
			query = query.Where("status = ?", 1)
		}
	}

	// 用户ID筛选
	if userId, ok := params["user_id"].(uint64); ok && userId > 0 {
		query = query.Where("user_id = ?", userId)
	}

	// ID筛选
	if ids, ok := params["id"].([]uint64); ok && len(ids) > 0 {
		query = query.Where("id IN ?", ids)
	}

	// 按ID降序排序
	return query.Order("id DESC")
}

func (d *defaultClueBlackKeyword) Count(opts ...mysql.HandleFunc) (int64, error) {
	query := d.DB.Model(&ClueBlackKeyword{})
	for _, opt := range opts {
		opt(query)
	}
	var count int64
	err := query.Count(&count).Error
	return count, err
}

// GetName 获取名称（已废弃）
// TODO: 实现URL转换逻辑
func (c *ClueBlackKeyword) GetName() string {
	// TODO: 实现类似PHP中的URL转换逻辑
	// 需要实现Extract的域名验证和genDownloadUrl的功能
	return c.Name
}
