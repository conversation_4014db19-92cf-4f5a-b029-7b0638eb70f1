package apicounter

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

const TableName = "api_counter"

type Counter interface {
	List(page, size int, opts ...mysql.HandleFunc) ([]ApiCount, int64, error)
	ListAll(opts ...mysql.HandleFunc) ([]*ApiCount, error)
	Upsert([]*ApiCount) error
}

type ApiCount struct {
	dbx.Model
	ClientId string `gorm:"column:client_id" json:"client_id"`
	UserId   uint64 `gorm:"column:user_id" json:"user_id"`
	Year     uint64 `gorm:"column:year" json:"year"`
	Month    uint64 `gorm:"column:month" json:"month"`
	Total    uint64 `gorm:"column:total" json:"total"`
	Path     string `gorm:"column:path" json:"path"`
}

func (*ApiCount) TableName() string {
	return TableName
}

type defaultCounter struct{ *gorm.DB }

func NewCounter(clients ...*gorm.DB) Counter {
	return &defaultCounter{
		DB: mysql.GetDbClient(clients...),
	}
}

func WithSearch(search string) mysql.HandleFunc {
	return func(db *gorm.DB) {
		db.Where("`path` LIKE ? OR `client_id` LIKE ?", "%"+search+"%", "%"+search+"%")
	}
}

func (d *defaultCounter) List(page, size int, opts ...mysql.HandleFunc) ([]ApiCount, int64, error) {
	query := d.DB.Model(&ApiCount{})
	for _, opt := range opts {
		opt(query)
	}

	var list = make([]ApiCount, 0)
	var total int64
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Find(&list).Error
	return list, total, err
}

func (d *defaultCounter) ListAll(opts ...mysql.HandleFunc) ([]*ApiCount, error) {
	query := d.DB.Model(&ApiCount{})
	for _, opt := range opts {
		opt(query)
	}
	var list = make([]*ApiCount, 0)
	err := query.Find(&list).Error
	return list, err
}

func (d *defaultCounter) Upsert(item []*ApiCount) error {
	if len(item) > 0 {
		return d.DB.Save(item).Error
	}
	return nil
}
