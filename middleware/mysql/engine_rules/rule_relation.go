package engine_rules

import (
	"fmt"
	"time"

	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

const RuleRelationTableName = "builtin_rules_relation"

// 记录内置规则与用户的关系，用户未启用的规则，默认用户启用

type RuleRelationModel interface {
	First(ruleId, userId uint64) (RuleRelation, error)
	List(opts ...mysql.HandleFunc) ([]RuleRelation, error)
	Create(...RuleRelation) error
	DeleteBy(opts ...mysql.HandleFunc) error
	IsExist(ruleId, userId uint64) bool
	Update(RuleRelation) error
}

func WithUser(userId uint64) mysql.HandleFunc {
	return mysql.WithColumnValue(fmt.Sprintf("%s.user_id", RuleRelationTableName), userId)
}

func WithKeyword(keyword string) mysql.HandleFunc {
	return mysql.WithLRLike(fmt.Sprintf("%s.name", RuleTableName), keyword)
}

type RuleRelation struct {
	dbx.Model
	RuleId           uint64     `gorm:"column:rule_id" json:"rule_id"`                       // 规则ID
	UserId           uint64     `gorm:"column:user_id" json:"user_id"`                       // 用户ID
	Status           int        `gorm:"column:status" json:"status"`                         // 显示状态
	CompanyId        uint64     `gorm:"column:company_id" json:"company_id"`                 // 企业id
	Node             string     `gorm:"column:node" json:"node"`                             // 执行这个数据更新的job的node节点，方便排查代码
	LastestFoundTime *time.Time `gorm:"column:lastest_found_time" json:"lastest_found_time"` // 最近发现时间
	Enable           int        `gorm:"column:enable" json:"enable"`                         // 规则启用禁用状态
}

func (*RuleRelation) TableName() string {
	return RuleRelationTableName
}

type ruleRelationModelImpl struct{ *gorm.DB }

func NewRuleRelation() RuleRelationModel {
	return &ruleRelationModelImpl{DB: mysql.GetDbClient()}
}

func (r *ruleRelationModelImpl) First(ruleId, userId uint64) (RuleRelation, error) {
	var relation RuleRelation
	err := r.DB.Model(&RuleRelation{}).Where("rule_id = ? AND user_id = ?", ruleId, userId).First(&relation).Error
	return relation, err
}

func (r *ruleRelationModelImpl) List(opts ...mysql.HandleFunc) ([]RuleRelation, error) {
	query := r.DB.Model(&RuleRelation{})
	for _, opt := range opts {
		opt(query)
	}

	var items []RuleRelation
	err := query.Find(&items).Error
	if err != nil {
		return nil, err
	}
	return items, nil
}

func (r *ruleRelationModelImpl) Create(items ...RuleRelation) error {
	if len(items) == 0 {
		return nil
	}
	for i := range items {
		items[i].Status = StatusDisable
	}
	return r.DB.Create(items).Error
}

func (r *ruleRelationModelImpl) DeleteBy(opts ...mysql.HandleFunc) error {
	query := r.DB.Model(&RuleRelation{})
	for _, opt := range opts {
		opt(query)
	}
	return query.Delete(&RuleRelation{}).Error
}

func (r *ruleRelationModelImpl) IsExist(ruleId, userId uint64) bool {
	var relation RuleRelation
	if r.DB.Model(&RuleRelation{}).Where("rule_id = ? AND user_id = ?", ruleId, userId).First(&relation).RowsAffected != 0 {
		return true
	}
	return false
}

func (r *ruleRelationModelImpl) Update(item RuleRelation) error {
	return r.DB.Select("enable").Where("rule_id = ? AND user_id = ?", item.RuleId, item.UserId).Updates(item).Error
}
