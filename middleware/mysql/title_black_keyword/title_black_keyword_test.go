package title_black_keyword

import (
	"micro-service/initialize/mysql"
	mysql2 "micro-service/middleware/mysql"
	"micro-service/pkg/cfg"
	"testing"

	"github.com/stretchr/testify/assert"
)

func initCfg() {
	cfg.InitLoadCfg()
	mysql.GetInstance(cfg.LoadMysql())
}

func Test_CRUD(t *testing.T) {
	initCfg()

	db := NewModel()
	item := &BlackKeyword{
		UserId:    10,
		CompanyId: 5,
		TypeId:    1,
		Keyword:   "abcd",
		Status:    0,
	}
	// 新建
	err := db.Create(item)
	assert.Nil(t, err)

	// 更新
	item.TypeId = 100
	item.Status = StatusRefused
	err = db.Update(item)
	assert.Nil(t, err)

	// 读取
	info, err := db.First(mysql2.WithId(item.Id))
	assert.Nil(t, err)
	assert.Equal(t, item.Status, info.Status)
}
