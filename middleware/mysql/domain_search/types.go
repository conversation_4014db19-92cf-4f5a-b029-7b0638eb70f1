package domain_search

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

const (
	TaskTable   = "domain_search_tasks"
	ResultTable = "domain_search_results"
)

const (
	SourceGoogle = "Google"
	SourceBing   = "Bing"
	SourceBaidu  = "Baidu"
)

const (
	StatusProcessing = 1 // 进行中
	StatusFinished   = 2 // 已完成
	HasChildren      = 1 // 有子任务

	TypeDomain    = 1
	TypeSubdomain = 2
	TypeMixed     = 3
)

type Task struct {
	dbx.Model
	UserId      uint64 `gorm:"column:user_id;comment:用户ID"`
	SafeUserId  uint64 `gorm:"column:safe_user_id;comment:安服用户ID"`
	CompanyId   uint64 `gorm:"column:company_id;comment:企业ID"`
	ParentId    uint64 `gorm:"column:parent_id;comment:父任务ID"`
	HasChildren int    `gorm:"column:has_children;comment:是否有子任务 0/1 否/是"`
	DomainType  int    `gorm:"column:domain_type;comment:域名类型"` // 主域名 子域名
	Status      int    `gorm:"column:status;comment:任务状态 1/2 进行中/完成"`
	Search      string `gorm:"column:search;comment:搜索内容"`
	Progress    string `gorm:"column:progress;comment:进度"`
}

func (*Task) TableName() string {
	return TaskTable
}

type Result struct {
	dbx.Model
	TaskId       uint64 `gorm:"column:task_id;comment:任务ID"`
	Url          string `gorm:"column:url;comment:URL"`
	Domain       string `gorm:"column:domain;comment:域名"`
	Title        string `gorm:"column:title;comment:标题"`
	Source       string `gorm:"column:source;comment:来源"` // Google, Bing, Baidu
	SearchSyntax string `gorm:"column:search_syntax;comment:查询语法"`
}

func (*Result) TableName() string {
	return ResultTable
}

type defaultDomainSearchModel struct{ *gorm.DB }

func (d *defaultDomainSearchModel) TaskCreate(info *Task) error {
	return d.DB.Create(info).Error
}

func (d *defaultDomainSearchModel) TaskFirst(opts ...mysql.HandleFunc) (Task, error) {
	query := d.DB.Model(&Task{})
	for _, opt := range opts {
		opt(query)
	}

	var info Task
	err := query.First(&info).Error
	return info, err
}

func (d *defaultDomainSearchModel) TaskList(page, size int, opts ...mysql.HandleFunc) ([]*Task, int64, error) {
	query := d.DB.Model(&Task{})
	for _, opt := range opts {
		opt(query)
	}
	var list = make([]*Task, 0, size)
	err := query.Find(&list).Error
	return list, 0, err
}

func (d *defaultDomainSearchModel) TasksSave(tasks ...*Task) error {
	if len(tasks) > 0 {
		return d.DB.Save(tasks).Error
	}
	return nil
}

func (d *defaultDomainSearchModel) TaskUpdateAny(m map[string]any, opts ...mysql.HandleFunc) error {
	query := d.DB.Model(&Task{})
	for _, opt := range opts {
		opt(query)
	}

	return query.Updates(m).Error
}

func (d *defaultDomainSearchModel) ResultFind(page, size int, opts ...mysql.HandleFunc) ([]*Result, int64, error) {
	query := d.DB.Model(&Result{})
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if !mysql.IsPageAll(page, size) {
		query.Count(&total).Scopes(mysql.PageLimit(page, size))
	}

	var list = make([]*Result, 0, size)
	err := query.Find(&list).Error
	return list, total, err
}

func (d *defaultDomainSearchModel) ResultSave(results ...*Result) error {
	if len(results) > 0 {
		return d.DB.Save(results).Error
	}
	return nil
}

func (d *defaultDomainSearchModel) ResultCreateInBatch(batch int, result ...*Result) error {
	if len(result) == 0 {
		return nil
	}
	if batch == 0 {
		batch = 500
	}
	return d.DB.CreateInBatches(result, batch).Error
}

func (d *defaultDomainSearchModel) ResultDeleteBy(opts ...mysql.HandleFunc) error {
	query := d.DB.Model(&Result{})
	for _, opt := range opts {
		opt(query)
	}

	return query.Delete(&Result{}).Error
}
