package domain_search

import (
	"micro-service/middleware/mysql"

	"gorm.io/gorm"
)

var _ DomainSearchModel = (*defaultDomainSearchModel)(nil)

type DomainSearchModel interface {
	TaskCreate(info *Task) error
	TaskFirst(opts ...mysql.HandleFunc) (Task, error)
	TaskList(page, size int, opts ...mysql.HandleFunc) ([]*Task, int64, error)
	TasksSave(tasks ...*Task) error
	TaskUpdateAny(m map[string]any, opts ...mysql.HandleFunc) error
	ResultFind(page, size int, opts ...mysql.HandleFunc) ([]*Result, int64, error)
	ResultSave(results ...*Result) error
	ResultCreateInBatch(batch int, result ...*Result) error
	ResultDeleteBy(opts ...mysql.HandleFunc) error
}

func NewDomainSearchModel(dbs ...*gorm.DB) DomainSearchModel {
	return &defaultDomainSearchModel{DB: mysql.GetDbClient(dbs...)}
}
