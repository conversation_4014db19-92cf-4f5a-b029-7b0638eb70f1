package api_analyze

import (
	"testing"
	"time"

	"micro-service/pkg/cfg"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func init() {
	cfg.InitLoadCfg()
}

// setupTest 创建一个新的 mock 对象和模型
func setupTest(t *testing.T) (UserTaskModel, sqlmock.Sqlmock) {
	db, mock, err := sqlmock.New()
	assert.NoError(t, err)

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}))
	assert.NoError(t, err)

	model := &defaultUserTaskImpl{DB: gormDB}
	return model, mock
}

func TestDefaultUserTaskImpl_UpdateProcess(t *testing.T) {
	model, mock := setupTest(t)

	var id uint64 = 13

	// 预期 SQL 执行
	mock.ExpectExec("UPDATE api_analyze_user_tasks SET progress").
		WithArgs(id, id, id).
		WillReturnResult(sqlmock.NewResult(0, 1))

	// 预期查询用户任务
	rows := sqlmock.NewRows([]string{"id", "user_id", "operator_name", "name", "sub_task_num", "total_risks", "status", "progress", "start_time", "end_time", "created_at", "updated_at", "deleted_at"}).
		AddRow(1, 100, "operator", "task", 5, 10, 1, 80.0, time.Now(), nil, time.Now(), time.Now(), nil)

	mock.ExpectQuery("SELECT (.+) FROM `api_analyze_user_tasks` WHERE").
		WithArgs(id).
		WillReturnRows(rows)

	err := model.UpdateProcess(id)
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultUserTaskImpl_AddRisksCount(t *testing.T) {
	model, mock := setupTest(t)

	var id uint64 = 13
	var num int64 = 1

	mock.ExpectBegin()
	mock.ExpectExec("UPDATE `api_analyze_user_tasks` SET").
		WithArgs(num, sqlmock.AnyArg(), id).
		WillReturnResult(sqlmock.NewResult(0, 1))
	mock.ExpectCommit()

	err := model.AddRisksCount(id, num)
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestDefaultUserTaskImpl_Delete(t *testing.T) {
	model, mock := setupTest(t)

	var id uint64 = 1

	mock.ExpectBegin()
	mock.ExpectExec("UPDATE `api_analyze_user_tasks` SET").
		WithArgs(sqlmock.AnyArg(), id).
		WillReturnResult(sqlmock.NewResult(0, 1))
	mock.ExpectCommit()

	err := model.Delete(id)
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}
