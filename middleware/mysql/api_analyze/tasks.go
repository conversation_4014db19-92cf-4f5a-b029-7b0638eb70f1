package api_analyze

import (
	"fmt"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

type TaskModel interface {
	List(page, size int64, opts ...mysql.HandleFunc) ([]Task, int64, error)
	First(opts ...mysql.HandleFunc) (Task, error)
	Create(*Task) error
	Update(Task) error
	UpdateAny(id uint64, m map[string]any) error
	UpdateProcess(id uint64, process float32) error
	Delete(id uint64) error
	DeleteList(userTaskId uint64, targetUrlPattern string) error

	ListByUserTaskId(userTaskId uint64, userId uint64, opts ...mysql.HandleFunc) ([]Task, error)
}

const TaskTableName = "api_analyze_tasks"

type defaultTaskImpl struct {
	*gorm.DB
}

func NewTaskModel(clients ...*gorm.DB) TaskModel {
	return &defaultTaskImpl{
		DB: mysql.GetDbClient(clients...),
	}
}

type Task struct {
	dbx.Model
	gorm.DeletedAt
	UserId    uint64  `gorm:"column:user_id;comment:用户ID"`         // 用户ID
	TargetURL string  `gorm:"column:target_url;comment:目标URL"`     // 目标URL
	Domain    string  `gorm:"column:domain;comment:域名"`            // 域名
	UserAgent string  `gorm:"column:user_agent;comment:UserAgent"` // UserAgent
	Fuzz      int     `gorm:"column:fuzz;comment:模糊测试"`            // 模糊测试
	Progress  float32 `gorm:"column:progress;comment:进度"`          // 进度
	Status    int     `gorm:"column:status;comment:状态"`            // 状态
	JsCount   int     `gorm:"column:js_count;comment:JS数量"`        // JS数量
	UrlCount  int     `gorm:"column:url_count;comment:URL数量"`      // URL数量
	ApiCount  int     `gorm:"column:api_count;comment:API数量"`      // API数量
}

func (*Task) TableName() string {
	return TaskTableName
}

const (
	StatusProcessing = iota + 1 // 任务进行中
	StatusFinished              // 任务已完成
)

func (d *defaultTaskImpl) First(opts ...mysql.HandleFunc) (Task, error) {
	query := d.DB.Model(&Task{})
	for _, opt := range opts {
		opt(query)
	}

	var item Task
	err := query.First(&item).Error
	if err != nil {
		return Task{}, err
	}
	return item, nil
}

func (d *defaultTaskImpl) Create(item *Task) error {
	return d.DB.Create(item).Error
}

func (d *defaultTaskImpl) Update(item Task) error {
	return d.DB.Model(&Task{}).Where("id = ?", item.Id).Updates(&item).Error
}

func (d *defaultTaskImpl) UpdateAny(id uint64, m map[string]any) error {
	return d.DB.Model(Task{}).Where("id = ?", id).Updates(m).Error
}

func (d *defaultTaskImpl) List(page, size int64, opts ...mysql.HandleFunc) ([]Task, int64, error) {
	query := d.DB.Model(&Task{})
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	var list = make([]Task, 0, size)
	if !mysql.IsPageAll(int(page), int(size)) {
		query.Count(&total).Scopes(mysql.PageLimit(int(page), int(size)))
	}
	err := query.Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (d *defaultTaskImpl) UpdateProcess(id uint64, process float32) error {
	return d.DB.Model(Task{}).Where("id = ?", id).Update("progress", process).Error
}

func (d *defaultTaskImpl) ListByUserTaskId(userTaskId uint64, userId uint64, opts ...mysql.HandleFunc) ([]Task, error) {
	q := d.DB.Model(&Relation{})
	q.Joins(fmt.Sprintf("INNER JOIN `%s` ON `%s`.id = `%s`.task_id", TaskTableName, TaskTableName, RelationTableName))
	q.Select(fmt.Sprintf("`%s`.*", TaskTableName))
	q.Where(fmt.Sprintf("`%s`.user_task_id = ?", RelationTableName), userTaskId)
	q.Where(fmt.Sprintf("`%s`.user_id = ?", TaskTableName), userId)
	q.Where(fmt.Sprintf("`%s`.deleted_at IS NULL", TaskTableName))
	for _, opt := range opts {
		opt(q)
	}
	var items []Task
	if err := q.Find(&items).Error; err != nil {
		return nil, fmt.Errorf("%s - Failed to get list: %w", RelationTableName, err)
	}
	return items, nil
}

func (d *defaultTaskImpl) Delete(id uint64) error {
	return d.DB.Model(&Task{}).Where("id = ?", id).Delete(&Task{}).Error
}

func (d *defaultTaskImpl) DeleteList(userTaskId uint64, targetUrlPattern string) error {
	sql := fmt.Sprintf("UPDATE `%s` t INNER JOIN `%s` r ON r.task_id = t.id SET t.deleted_at = NOW() WHERE r.user_task_id = ? ", TaskTableName, RelationTableName)
	if targetUrlPattern != "" {
		targetUrlPattern = "%" + targetUrlPattern + "%"
		sql += "AND t.target_url LIKE ?"
		return d.DB.Exec(sql, userTaskId, targetUrlPattern).Error
	}
	return d.DB.Exec(sql, userTaskId).Error
}
