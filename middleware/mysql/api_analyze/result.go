package api_analyze

import (
	"fmt"
	"strconv"
	"time"

	"gorm.io/gorm"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

type ResultModel interface {
	Upsert(result []*ApiAnalyzerResult) ([]*ApiAnalyzerResult, error)
	CompareAndUpdate(old, target []*ApiAnalyzerResult) error
	Create([]*ApiAnalyzerResult) error
	Updates([]*ApiAnalyzerResult) error
	List(page, size int, opts ...mysql.HandleFunc) ([]*ApiAnalyzerResult, int64, error)
	ListAll(...mysql.HandleFunc) ([]*ApiAnalyzerResult, error)
	UpdateAny(id uint64, m map[string]any) error
	ListAllByTask(taskId uint64, opts ...mysql.HandleFunc) ([]*ApiAnalyzerResult, error)
	SelectStatusCodeList(taskId uint64, opts ...mysql.HandleFunc) ([]int64, error)
	Count(taskId uint64, opts ...mysql.HandleFunc) (int64, error)
	DeleteByTask(taskId uint64) error

	RelationListAll(opts ...mysql.HandleFunc) ([]*Relation, error)
	RelationSave(result []*Relation) error
	RelationFirst(opts ...mysql.HandleFunc) (Relation, error)
}

const (
	ResultTableName = "api_analyze_result"
)

type ApiAnalyzerResult struct {
	dbx.Model
	TaskId       int    `gorm:"column:task_id;comment:任务ID"`        // 任务ID
	RiskType     int    `gorm:"column:risk_type;comment:风险类型"`      // 风险类型
	Url          string `gorm:"column:url;comment:完整URL"`           // 完整URL
	Api          string `gorm:"column:api;comment:API"`             // API
	Tag          string `gorm:"column:tag;comment:标签"`              // 标签
	Describe     string `gorm:"column:describe;comment:描述"`         // 描述
	Code         int    `gorm:"column:code;comment:状态码"`            // 状态码
	Size         int    `gorm:"column:size;comment:响应体大小"`          // 响应体大小
	ResponseType string `gorm:"column:response_type;comment:响应体类型"` // 响应体类型
	Count        int    `gorm:"column:count;comment:匹配次数"`          // 匹配次数
	Matches      string `gorm:"column:matches;comment:匹配字段"`        // 匹配字段
}

func (*ApiAnalyzerResult) TableName() string {
	return ResultTableName
}

type defaultResultImpl struct {
	*gorm.DB
}

func NewResultModel(clients ...*gorm.DB) ResultModel {
	return &defaultResultImpl{
		DB: mysql.GetDbClient(clients...),
	}
}

func (d *defaultResultImpl) Upsert(result []*ApiAnalyzerResult) ([]*ApiAnalyzerResult, error) {
	if len(result) == 0 {
		return nil, nil
	}
	all, err := d.ListAll(mysql.WithWhere("task_id = ?", result[0].TaskId))
	if err != nil {
		return nil, err
	}
	allMap := make(map[string]*ApiAnalyzerResult, len(all))
	for i := range all {
		allMap[strconv.Itoa(all[i].TaskId)+"|+|"+all[i].Url+"|+|"+strconv.Itoa(all[i].RiskType)] = all[i]
	}
	insertList := make([]*ApiAnalyzerResult, 0, len(result))
	updateList := make([]*ApiAnalyzerResult, 0, len(result))
	for i := range result {
		if v, ok := allMap[strconv.Itoa(result[i].TaskId)+"|+|"+result[i].Url+"|+|"+strconv.Itoa(result[i].RiskType)]; !ok {
			insertList = append(insertList, result[i])
		} else {
			result[i].Id = v.Id
			updateList = append(updateList, result[i])
		}
	}
	if err = d.Create(insertList); err != nil {
		return nil, err
	}
	if err = d.Updates(updateList); err != nil {
		return nil, err
	}
	return result, err
}

// CompareAndUpdate compare and update
func (d *defaultResultImpl) CompareAndUpdate(old, target []*ApiAnalyzerResult) error {
	oldMap := make(map[string]*ApiAnalyzerResult, len(old))
	for i := range old {
		oldMap[strconv.Itoa(old[i].TaskId)+"|+|"+old[i].Url] = old[i]
	}
	insertList := make([]*ApiAnalyzerResult, 0, len(target))
	updateList := make([]*ApiAnalyzerResult, 0, len(old))
	for i := range target {
		if n, ok := oldMap[strconv.Itoa(target[i].TaskId)+"|+|"+target[i].Url]; !ok {
			insertList = append(insertList, target[i])
		} else {
			target[i].Id = n.Id
			updateList = append(updateList, target[i])
		}
	}
	if err := d.Create(insertList); err != nil {
		return err
	}
	return d.Save(updateList)
}

func (d *defaultResultImpl) Create(l []*ApiAnalyzerResult) (err error) {
	if len(l) > 0 {
		err = d.DB.Create(&l).Error
	}
	return err
}

func (d *defaultResultImpl) Save(list []*ApiAnalyzerResult) error {
	if len(list) == 0 {
		return nil
	}
	return d.DB.Save(list).Error
}

func (d *defaultResultImpl) Updates(l []*ApiAnalyzerResult) error {
	if len(l) == 0 {
		return nil
	}

	err := d.DB.Transaction(func(tx *gorm.DB) error {
		for i := range l {
			q := tx.Where("id = ?", l[i].Id).Updates(l[i])
			if err := q.Error; err != nil {
				return err
			}
		}
		return nil
	})
	return err
}

func WithKeyword(keyword string) mysql.HandleFunc {
	return func(query *gorm.DB) {
		keyword = "%" + keyword + "%"
		query.Where(fmt.Sprintf("%s.`name` LIKE ? ", UserTaskTableName), keyword)
	}
}

func WithResultKeyword(keyword string) mysql.HandleFunc {
	return func(query *gorm.DB) {
		keyword = "%" + keyword + "%"
		query.Where(fmt.Sprintf("%s.`url` LIKE ? ", ResultTableName), keyword)
	}
}

func WithTaskKeyword(keyword string) mysql.HandleFunc {
	return func(query *gorm.DB) {
		keyword = "%" + keyword + "%"
		query.Where(fmt.Sprintf("%s.`target_url` LIKE ? ", TaskTableName), keyword)
	}
}

func WithBetween(field string, start, end time.Time) mysql.HandleFunc {
	return func(query *gorm.DB) {
		query.Where(fmt.Sprintf("%s.%s BETWEEN ? AND ?", UserTaskTableName, field), start, end)
	}
}

// List with page and condition
func (d *defaultResultImpl) List(page, size int, opts ...mysql.HandleFunc) ([]*ApiAnalyzerResult, int64, error) {
	query := d.DB.Model(&ApiAnalyzerResult{})
	joinCond := fmt.Sprintf("LEFT JOIN %s ON %s.task_id = %s.id", TaskTableName, ResultTableName, TaskTableName)
	query.Joins(joinCond)
	for _, opt := range opts {
		opt(query)
	}
	//query.Select(fmt.Sprintf("%s.*", ResultTableName), fmt.Sprintf("%s.keyword", TaskTableName))

	var total int64
	list := make([]*ApiAnalyzerResult, 0, size)
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

// ListAll return all result with conditions
func (d *defaultResultImpl) ListAll(opts ...mysql.HandleFunc) ([]*ApiAnalyzerResult, error) {
	query := d.DB.Model(&ApiAnalyzerResult{})
	for _, opt := range opts {
		opt(query)
	}

	l := make([]*ApiAnalyzerResult, 0)
	err := query.Find(&l).Error
	if err != nil {
		return nil, err
	}
	return l, nil
}

func (d *defaultResultImpl) UpdateAny(id uint64, m map[string]any) error {
	if len(m) == 0 {
		return nil
	}
	return d.DB.Model(ApiAnalyzerResult{}).Where("id = ?", id).Updates(m).Error
}

func (d *defaultResultImpl) ListAllByTask(taskId uint64, opts ...mysql.HandleFunc) ([]*ApiAnalyzerResult, error) {
	q := d.DB.Model(&ApiAnalyzerResult{})
	for _, opt := range opts {
		opt(q)
	}
	var items []*ApiAnalyzerResult
	err := q.Where(fmt.Sprintf("`%s`.`task_id` = ?", ResultTableName), taskId).Find(&items).Error
	return items, err
}

func (d *defaultResultImpl) SelectStatusCodeList(taskId uint64, opts ...mysql.HandleFunc) ([]int64, error) {
	q := d.DB.Model(&ApiAnalyzerResult{})
	for _, opt := range opts {
		opt(q)
	}
	var res []int64
	q.Select("DISTINCT code")
	q.Where(fmt.Sprintf("`%s`.`task_id` = ?", ResultTableName), taskId).Scan(&res)
	if err := q.Error; err != nil {
		return nil, err
	}
	return res, nil
}

func (d *defaultResultImpl) Count(taskId uint64, opts ...mysql.HandleFunc) (int64, error) {
	q := mysql.GetDbClient().Model(&ApiAnalyzerResult{})
	for _, opt := range opts {
		opt(q)
	}
	var res int64
	q.Where(fmt.Sprintf("`%s`.`task_id` = ?", ResultTableName), taskId).Count(&res)
	if err := q.Error; err != nil {
		return 0, err
	}
	return res, nil
}

func (d *defaultResultImpl) DeleteByTask(taskId uint64) error {
	return d.DB.Where("task_id = ?", taskId).Delete(&ApiAnalyzerResult{}).Error
}
