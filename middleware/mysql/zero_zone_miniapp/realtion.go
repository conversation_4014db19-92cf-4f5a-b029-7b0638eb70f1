package zero_zone_miniapp

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

const RelationTableName = "mini_app_relation"

type Relation struct {
	dbx.Model
	OfficialAccountHistoryId uint64 `gorm:"column:mini_app_history_id"` // keyword id
	OfficialAccountId        uint64 `gorm:"column:mini_app_id"`         // WeChat account info id
}

func (*Relation) TableName() string {
	return RelationTableName
}

func (d *defaultOfficialAccountModel) FirstRelation(opts ...mysql.HandleFunc) (Relation, error) {
	query := d.DB.Model(&Relation{})
	for _, opt := range opts {
		opt(query)
	}

	var info Relation
	err := query.First(&info).Error
	return info, err
}

func (d *defaultOfficialAccountModel) SaveRelation(list ...Relation) error {
	if len(list) == 0 {
		return nil
	}
	return d.DB.Save(list).Error
}

func (d *defaultOfficialAccountModel) ListRelation(page, size int, opts ...mysql.HandleFunc) ([]*Relation, int64, error) {
	query := d.DB.Model(&Relation{})
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if !mysql.IsPageAll(page, size) {
		query.Count(&total).Scopes(mysql.PageLimit(page, size))
	}
	var list []*Relation
	err := query.Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (d *defaultOfficialAccountModel) DeleteRelation(opts ...mysql.HandleFunc) error {
	query := d.DB.Model(&Relation{})
	for _, opt := range opts {
		opt(query)
	}
	return query.Delete(&Relation{}).Error
}

func (d *defaultOfficialAccountModel) ListAccounts(page, size int, opts ...mysql.HandleFunc) ([]OfficialAccount, int64, error) {
	q := d.DB.Model(&Relation{})
	q.Joins("LEFT JOIN `zero_zone_mini_app` ON `mini_app_relation`.`mini_app_id` = `zero_zone_mini_app`.id")
	q.Select("mini_app_relation.*", "`name`,`wechat_id`,`app_id`,`company_name`,`introduction`,`icp`,`platform`,`icon_url`,`update_time`")
	for _, opt := range opts {
		opt(q)
	}

	var total int64
	var accounts []OfficialAccount
	if !mysql.IsPageAll(page, size) {
		q.Count(&total).Scopes(mysql.PageLimit(page, size))
	}
	err := q.Find(&accounts).Error
	return accounts, total, err
}
