package zero_zone_miniapp

import (
	"time"

	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"micro-service/pkg/utils"

	"gorm.io/gorm"
)

type (
	HistoryModel interface {
		Create(data *History) error
		First(opts ...mysql.HandleFunc) (History, error)
		List(page, size int, opts ...mysql.HandleFunc) ([]History, int64, error)
		FindByKeyword(keyword string, updatedAt string) (*History, error)
		UpdateHistory(keyword string)
		CreateOrFindByKeywords(keywords []string) ([]*History, error)
		UpdateAny(id uint64, data map[string]any) error
		DeleteByIds(ids []uint64) error
	}

	defaultHistoryModel struct {
		*gorm.DB
		table string
	}

	History struct {
		dbx.Model
		Keyword  string  `gorm:"column:keyword;type:varchar(300);comment:搜索关键字;DEFAULT NULL" json:"keyword"`
		Progress float32 `gorm:"column:progress"` // 任务进度
	}
)

const HistoryTable = "mini_app_history"

// TableName 表名
func (h *History) TableName() string {
	return HistoryTable
}

func NewHistoryModel(conn ...*gorm.DB) HistoryModel {
	return &defaultHistoryModel{mysql.GetDbClient(conn...), HistoryTable}
}

func (d *defaultHistoryModel) Create(data *History) error {
	return d.DB.Model(&History{}).Create(data).Error
}

func (d *defaultHistoryModel) First(opts ...mysql.HandleFunc) (History, error) {
	query := d.DB.Model(&History{})
	for _, opt := range opts {
		opt(query)
	}

	var record History
	err := query.First(&record).Error
	return record, err
}

func (d *defaultHistoryModel) List(page, size int, opts ...mysql.HandleFunc) ([]History, int64, error) {
	q := d.DB.Model(&History{})
	for _, opt := range opts {
		opt(q)
	}

	var total int64
	var list = make([]History, 0)
	if !mysql.IsPageAll(page, size) {
		q.Count(&total).Scopes(mysql.PageLimit(page, size))
	}

	err := q.Find(&list).Error
	return list, total, err
}

func (d *defaultHistoryModel) FindByKeyword(keyword string, updatedAt string) (*History, error) {
	record := &History{}
	err := d.DB.Table(d.table).Where("keyword = ? and updated_at >= ?", keyword, updatedAt).First(&record).Error
	return record, err
}

func (d *defaultHistoryModel) UpdateHistory(keyword string) {
	record := &History{}
	if err := d.DB.Table(d.table).Where("keyword = ?", keyword).Find(&record).Error; err != nil {
		return
	}
	if record.Id != 0 {
		d.DB.Table(d.table).Where("id", record.Id).Update("updated_at", time.Now())
	} else {
		d.DB.Table(d.table).Create(&History{
			Keyword: keyword,
		})
	}
	return
}

func (d *defaultHistoryModel) UpdateAny(id uint64, data map[string]any) error {
	return d.DB.Model(&History{}).Where("`id` = ?", id).Updates(data).Error
}

func (d *defaultHistoryModel) CreateOrFindByKeywords(keywords []string) ([]*History, error) {
	keywords = utils.ListDistinctNonZero(keywords)
	if len(keywords) == 0 {
		return nil, nil
	}

	var list = make([]*History, 0, len(keywords))
	err := d.DB.Model(&History{}).Where("`keyword` IN (?)", keywords).Find(&list).Error
	if err != nil {
		return nil, err
	}

	create := make([]*History, 0, len(list))
	m := utils.ListToSetFunc[string, *History](list, func(h *History) (key string, ok bool) { return h.Keyword, true })
	for _, keyword := range keywords {
		if _, ok := m[keyword]; !ok {
			create = append(create, &History{Keyword: keyword})
		}
	}
	if len(create) > 0 {
		if errCreate := d.DB.Create(&create).Error; errCreate != nil {
			return nil, errCreate
		}
	}

	list = append(list, create...)
	return list, nil
}

func (d *defaultHistoryModel) DeleteByIds(ids []uint64) error {
	if len(ids) == 0 {
		return nil
	}
	return d.DB.Where("id IN (?)", ids).Delete(&History{}).Error
}
