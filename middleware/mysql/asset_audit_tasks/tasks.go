package asset_audit_tasks

import (
	"gorm.io/gorm"

	"micro-service/middleware/mysql"
)

const AuditTableName = "asset_audit_tasks" // 数据表名

const (
	AuditStatusDoing  = iota + 1 // 任务进行中
	AuditStatusDone              // 任务已完成
	AuditStatusFailed            // 任务处理失败
)

const (
	AuditTypeAccountOfAssets   = iota + 1 // 资产台账核查
	AuditTypeRecommendedAssets            // 推荐资产核查
)

const (
	SyncAssetNo  = iota + 1 // 不同步导入资产至台账
	SyncAssetYes            // 同步导入资产至台账
)

type AssetAuditor interface {
	First(opts ...mysql.HandleFunc) (AssetAuditTask, error)
	Create(*AssetAuditTask) error
	List(page, size int, opts ...mysql.HandleFunc) ([]AssetAuditTask, int64, error)
	Delete(opts ...mysql.HandleFunc) (rowsAffected int64, err error)
	Update(AssetAuditTask) error
	Count(userId, status int) (int64, error)
	GroupStatus(opts ...mysql.HandleFunc) ([]GroupCount, error)
}

type AssetAuditTask struct {
	gorm.Model
	UserID            uint64 `gorm:"column:user_id;type:bigint(20);comment:用户ID;NOT NULL" json:"user_id"`
	CompanyId         uint64 `gorm:"column:commany_id;type:bigint(20);comment:企业ID;NOT NULL" json:"company_id"`
	OperatorId        uint64 `gorm:"column:operator_id;type:bigint(20);comment:操作人ID;NOT NULL" json:"operator_id"`
	Operator          string `gorm:"column:operator;type:varchar(255);comment:操作人;default:''" json:"operator"`
	AuditType         int    `gorm:"column:audit_type;type:tinyint;comment:核查模式(1资产台账，2推荐资产);NOT NULL" json:"audit_type"`
	Status            int    `gorm:"column:status;type:tinyint;comment:状态进度;NOT NULL" json:"status"`
	ImportAssetsTotal int    `gorm:"column:import_assets_total;type:bigint(20);comment:用户导入总数据量;default:0"`
	AssetsNotIncluded int    `gorm:"column:assets_not_included;type:bigint(20);comment:未纳入管理资产;default:0"`
	AssetsIncluded    int    `gorm:"column:assets_included;type:bigint(20);comment:已纳入管理资产;default:0"`
	AssetsPort        int    `gorm:"column:assets_port;type:bigint(20);comment:新增端口资产;default:0"`
	AssetSystemFind   int    `gorm:"column:asset_system_find;type:int;comment:系统发现数据;default:0"`
	AssetTotal        int    `gorm:"column:asset_total;type:int;comment:数据总表;default:0"`
	SyncAsset         int    `gorm:"column:sync_asset;type:tinyint;comment:是否同步资产到台账(1否, 2是);default:1"`
	ImportPath        string `gorm:"column:import_path;type:varchar(255);comment:导入路径;default:''"`
	ExportPath        string `gorm:"column:export_path;type:varchar(255);comment:导出文件路径;default:''"`
	Description       string `gorm:"column:description;type:varchar(255);comment:说明;default:''"`
}

type defaultAssetAuditTask struct {
	client    *gorm.DB
	tableName string
}

var _ AssetAuditor = (*defaultAssetAuditTask)(nil)

func (*AssetAuditTask) TableName() string {
	return AuditTableName
}

func NewAssetAuditor(clients ...*gorm.DB) AssetAuditor {
	return &defaultAssetAuditTask{
		client:    mysql.GetDbClient(clients...),
		tableName: AuditTableName,
	}
}

func (at *defaultAssetAuditTask) First(opts ...mysql.HandleFunc) (info AssetAuditTask, err error) {
	query := at.client.Table(at.tableName)

	for _, f := range opts {
		f(query)
	}

	err = query.First(&info).Error
	if err != nil {
		return AssetAuditTask{}, err
	}

	return info, err
}

func (at *defaultAssetAuditTask) Create(info *AssetAuditTask) error {
	err := at.client.Table(at.tableName).Create(info).Error

	return err
}

func (at *defaultAssetAuditTask) Info(userId uint, taskId uint) (AssetAuditTask, error) {
	var info AssetAuditTask
	err := at.client.Table(at.tableName).
		Where("id = ? AND user_id = ?", userId, taskId).First(&info).Error
	if err != nil {
		return AssetAuditTask{}, err
	}

	return info, nil
}

func (at *defaultAssetAuditTask) List(page, size int, opts ...mysql.HandleFunc) ([]AssetAuditTask, int64, error) {
	query := at.client.Table(at.tableName)
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if !mysql.IsPageAll(page, size) {
		query.Scopes(mysql.PageLimit(mysql.PageProcess(page, size)))
	}

	var list = make([]AssetAuditTask, 0)
	if err := query.Find(&list).Error; err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

func (at *defaultAssetAuditTask) Delete(opts ...mysql.HandleFunc) (rowsAffected int64, err error) {
	query := at.client.Table(at.tableName)
	for _, opt := range opts {
		opt(query)
	}

	query.Unscoped().Delete(&AssetAuditTask{})
	rowsAffected = query.RowsAffected
	err = query.Error
	if err != nil {
		return 0, err
	}

	return rowsAffected, nil
}

func (at *defaultAssetAuditTask) Update(info AssetAuditTask) error {
	update := at.client.Table(at.tableName).Where("id = ?", info.ID).Updates(info)
	err := update.Error

	return err
}

func (at *defaultAssetAuditTask) Count(userId int, status int) (int64, error) {
	query := at.client.Model(AssetAuditTask{})
	if userId > 0 {
		query.Where("user_id = ?", userId)
	}
	if status > 0 {
		query.Where("status = ?", status)
	}

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return 0, err
	}

	return total, nil
}

type GroupCount struct {
	Status int   `gorm:"column:val"`
	Count  int64 `gorm:"column:cnt"`
}

func (at *defaultAssetAuditTask) GroupStatus(opts ...mysql.HandleFunc) ([]GroupCount, error) {
	query := at.client.Model(AssetAuditTask{})
	for _, f := range opts {
		f(query)
	}

	list := make([]GroupCount, 0)
	err := query.Select("`status` val, count(*) cnt").Group("`status`").Find(&list).Error
	if err != nil {
		return nil, err
	}

	return list, nil
}
