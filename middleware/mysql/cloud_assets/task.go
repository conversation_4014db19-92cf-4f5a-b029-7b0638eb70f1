package cloud_assets

import (
	"github.com/spf13/cast"
	"gorm.io/gorm"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/general_clues"
	"micro-service/pkg/dbx"
	"micro-service/pkg/utils"
	"sort"
	"strings"
	"time"
)

type (
	TaskModel interface {
		FindByHash(hash string, updatedAt string) (*Task, error)
		UpdateByHash(hash string) *Task
		UpdateProcess(id uint64) error
		FindById(id uint64) (*Task, error)
		GetTaskClues(id uint64) ([]*Clue, error)
		UpSet(id uint64, column string, val any) error
		InitProcess(id uint64) error
	}

	defaultTaskModel struct {
		*gorm.DB
		table string
	}

	Task struct {
		dbx.Model
		Process float32 `gorm:"comment:任务进度" json:"process"`
		Hash    string  `gorm:"comment:任务hash" json:"hash"`
	}
)

var _ TaskModel = (*defaultTaskModel)(nil)

const taskTable = "cloud_assets_task"

// TableName 表名
func (g *Task) TableName() string {
	return taskTable
}

func NewTaskModel(clients ...*gorm.DB) TaskModel {
	return &defaultTaskModel{
		DB:    mysql.GetDbClient(clients...),
		table: taskTable,
	}
}

// FindByHash 根据Hash查找任务
func (d *defaultTaskModel) FindByHash(hash string, updatedAt string) (*Task, error) {
	record := &Task{}
	err := d.DB.Table(d.table).Where("hash = ? and updated_at >= ?", hash, updatedAt).First(&record).Error
	return record, err
}

// FindById 根据ID查找任务
func (d *defaultTaskModel) FindById(id uint64) (*Task, error) {
	record := &Task{}
	err := d.DB.Where("(id = ?)", id).Find(&record).Error
	if err != nil {
		return nil, err
	}
	return record, nil
}

// UpdateByHash 根据Hash值更新任务信息
func (d *defaultTaskModel) UpdateByHash(hash string) *Task {
	record := &Task{}
	if err := d.DB.Table(d.table).Where("hash = ?", hash).Find(record).Error; err != nil {
		return nil
	}
	if record.Id != 0 {
		record.UpdatedAt = time.Now()
		record.Process = 0
		d.DB.Table(d.table).Select([]string{"process"}).Where("id", record.Id).Updates(record)
	} else {
		record = &Task{Hash: hash, Process: 0}
		d.DB.Table(d.table).Create(record)
	}
	return record
}

// InitProcess 更新任务进度
func (d *defaultTaskModel) InitProcess(id uint64) error {
	return d.DB.Table(d.table).Where("id", id).Update("process", 1).Error
}

// UpdateProcess 更新任务进度
func (d *defaultTaskModel) UpdateProcess(id uint64) error {
	clues, err := NewClueModel().GetCluesByTaskId(id)
	if err != nil {
		return err
	}
	total := len(clues) * 100
	current := cast.ToFloat32(0)
	for _, clue := range clues {
		// 线索进度为0.1(到达数量限制)时,默认为完成
		if clue.Process == 0.1 {
			clue.Process = 100
		}
		current += clue.Process
	}
	if total == 0 {
		total = 1
	}
	process := (cast.ToFloat32(current) / cast.ToFloat32(total)) * 100
	if process > 100 {
		process = 100
	}
	return d.DB.Table(d.table).Where("id", id).Where("process != 100").Where("process <= ?", process).Update("process", process).Error
}

func (d *defaultTaskModel) UpSet(id uint64, column string, val any) error {
	return d.DB.Table(d.table).Select("*").Where("id = ?", id).Update(column, val).Error
}

// GetTaskClues 获取任务线索
func (d *defaultTaskModel) GetTaskClues(id uint64) ([]*Clue, error) {
	ids := make([]string, 0)
	clueIds, err := NewTaskClueModel().GetTaskClueIds(id)
	if err != nil {
		return nil, err
	}
	for i := range clueIds {
		ids = append(ids, cast.ToString(clueIds[i].ClueId))
	}
	return NewClueModel().GetClueByIds(ids)
}

// TaskHash 计算任务hash
func TaskHash(clues []*pb.CloudClue) string {
	keys := make([]string, 0)
	if len(clues) == 0 {
		return ""
	}
	for _, clue := range clues {
		if clue.Type != general_clues.ClueTypeIcon {
			keys = append(keys, cast.ToString(clue.Type)+clue.Content)
		} else {
			keys = append(keys, cast.ToString(clue.Hash))
		}
	}
	sort.Strings(keys)
	return utils.Md5bHash([]byte(strings.Join(keys, "")), false)
}
