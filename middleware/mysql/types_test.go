package mysql

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
)

func init() {
	cfg.InitLoadCfg()
	// 设置测试环境标志
	initmysql.SetTestEnv(true)

	// 初始化数据库连接
	initmysql.GetInstance(cfg.LoadMysql())
}

// 测试JoinType常量
func TestJoinTypeConstants(t *testing.T) {
	assert.Equal(t, JoinType("INNER"), InnerJoin)
	assert.Equal(t, JoinType("LEFT"), LeftJoin)
	assert.Equal(t, JoinType("RIGHT"), RightJoin)
}

// 测试JoinCondition结构体
func TestJoinCondition(t *testing.T) {
	join := JoinCondition{
		Type:  InnerJoin,
		Table: "users",
		On:    "users.id = orders.user_id",
		Alias: "u",
		Args:  []interface{}{1, "test"},
	}

	assert.Equal(t, InnerJoin, join.Type)
	assert.Equal(t, "users", join.Table)
	assert.Equal(t, "users.id = orders.user_id", join.On)
	assert.Equal(t, "u", join.Alias)
	assert.Len(t, join.Args, 2)
	assert.Equal(t, 1, join.Args[0])
	assert.Equal(t, "test", join.Args[1])
}

// 测试BetweenCondition结构体
func TestBetweenCondition(t *testing.T) {
	between := BetweenCondition{
		Field: "created_at",
		Start: "2024-01-01",
		End:   "2024-12-31",
	}

	assert.Equal(t, "created_at", between.Field)
	assert.Equal(t, "2024-01-01", between.Start)
	assert.Equal(t, "2024-12-31", between.End)
}

// 测试InCondition结构体
func TestInCondition(t *testing.T) {
	t.Run("with_slice_values", func(t *testing.T) {
		in := InCondition{
			Field:  "status",
			Values: []interface{}{1, 2, 3},
		}

		assert.Equal(t, "status", in.Field)
		assert.Equal(t, []interface{}{1, 2, 3}, in.Values)
	})

	t.Run("with_query_builder", func(t *testing.T) {
		subQuery := &QueryBuilder{
			Table:  "sub_table",
			Select: []string{"id"},
		}

		in := InCondition{
			Field:  "id",
			Values: subQuery,
		}

		assert.Equal(t, "id", in.Field)
		assert.Equal(t, subQuery, in.Values)
	})
}

// 测试NotInCondition结构体
func TestNotInCondition(t *testing.T) {
	t.Run("with_slice_values", func(t *testing.T) {
		notIn := NotInCondition{
			Field:  "status",
			Values: []interface{}{0, -1},
		}

		assert.Equal(t, "status", notIn.Field)
		assert.Equal(t, []interface{}{0, -1}, notIn.Values)
	})

	t.Run("with_query_builder", func(t *testing.T) {
		subQuery := &QueryBuilder{
			Table:  "excluded_table",
			Select: []string{"id"},
		}

		notIn := NotInCondition{
			Field:  "id",
			Values: subQuery,
		}

		assert.Equal(t, "id", notIn.Field)
		assert.Equal(t, subQuery, notIn.Values)
	})
}

// 测试ExistsCondition结构体
func TestExistsCondition(t *testing.T) {
	t.Run("exists_condition", func(t *testing.T) {
		subQuery := &QueryBuilder{
			Table: "related_table",
			Where: []Condition{
				CompareCond{Field: "parent_id", Operator: "=", Value: 1},
			},
		}

		exists := ExistsCondition{
			SubQuery: subQuery,
			Not:      false,
		}

		assert.Equal(t, subQuery, exists.SubQuery)
		assert.False(t, exists.Not)
	})

	t.Run("not_exists_condition", func(t *testing.T) {
		subQuery := &QueryBuilder{
			Table: "related_table",
			Where: []Condition{
				CompareCond{Field: "parent_id", Operator: "=", Value: 1},
			},
		}

		notExists := ExistsCondition{
			SubQuery: subQuery,
			Not:      true,
		}

		assert.Equal(t, subQuery, notExists.SubQuery)
		assert.True(t, notExists.Not)
	})
}

// 测试SubQueryCondition结构体
func TestSubQueryCondition(t *testing.T) {
	subQuery := &QueryBuilder{
		Table:  "sub_table",
		Select: []string{"MAX(score)"},
	}

	subQueryCond := SubQueryCondition{
		Field:    "score",
		Operator: ">",
		Query:    subQuery,
	}

	assert.Equal(t, "score", subQueryCond.Field)
	assert.Equal(t, ">", subQueryCond.Operator)
	assert.Equal(t, subQuery, subQueryCond.Query)
}

// 测试OrConditions结构体
func TestOrConditions(t *testing.T) {
	conditions := []Condition{
		CompareCond{Field: "status", Operator: "=", Value: 1},
		CompareCond{Field: "priority", Operator: ">", Value: 5},
	}

	orCond := OrConditions{
		Conditions: conditions,
	}

	assert.Len(t, orCond.Conditions, 2)
	assert.Equal(t, conditions, orCond.Conditions)
}

// 测试CompareCond结构体
func TestCompareCond(t *testing.T) {
	testCases := []struct {
		name     string
		field    string
		operator string
		value    interface{}
	}{
		{
			name:     "equal_condition",
			field:    "id",
			operator: "=",
			value:    1,
		},
		{
			name:     "greater_than_condition",
			field:    "age",
			operator: ">",
			value:    18,
		},
		{
			name:     "like_condition",
			field:    "name",
			operator: "LIKE",
			value:    "%test%",
		},
		{
			name:     "null_condition",
			field:    "deleted_at",
			operator: "IS",
			value:    nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cond := CompareCond{
				Field:    tc.field,
				Operator: tc.operator,
				Value:    tc.value,
			}

			assert.Equal(t, tc.field, cond.Field)
			assert.Equal(t, tc.operator, cond.Operator)
			assert.Equal(t, tc.value, cond.Value)
		})
	}
}

// 测试RawCondition结构体
func TestRawCondition(t *testing.T) {
	raw := RawCondition{
		Query: "DATE(created_at) = ?",
		Args:  []interface{}{"2024-01-01"},
	}

	assert.Equal(t, "DATE(created_at) = ?", raw.Query)
	assert.Len(t, raw.Args, 1)
	assert.Equal(t, "2024-01-01", raw.Args[0])
}

// 测试QueryBuilder结构体
func TestQueryBuilder(t *testing.T) {
	qb := QueryBuilder{
		Table: "users",
		Where: []Condition{
			CompareCond{Field: "status", Operator: "=", Value: 1},
			BetweenCondition{Field: "age", Start: 18, End: 65},
		},
		OrderBy: []string{"created_at DESC", "id ASC"},
		GroupBy: []string{"department", "role"},
		Select:  []string{"id", "name", "email"},
		Joins: []JoinCondition{
			{Type: LeftJoin, Table: "profiles", On: "users.id = profiles.user_id"},
		},
		Limit:  10,
		Offset: 20,
	}

	assert.Equal(t, "users", qb.Table)
	assert.Len(t, qb.Where, 2)
	assert.Len(t, qb.OrderBy, 2)
	assert.Len(t, qb.GroupBy, 2)
	assert.Len(t, qb.Select, 3)
	assert.Len(t, qb.Joins, 1)
	assert.Equal(t, 10, qb.Limit)
	assert.Equal(t, 20, qb.Offset)
}

// 测试BaseDSL结构体
func TestBaseDSL(t *testing.T) {
	type TestModel struct {
		BaseDSL[TestModel]
		Name string
	}

	model := TestModel{
		BaseDSL: BaseDSL[TestModel]{
			Id: 1,
			DB: &gorm.DB{},
		},
		Name: "test",
	}

	assert.Equal(t, uint64(1), model.Id)
	assert.NotNil(t, model.DB)
	assert.Equal(t, "test", model.Name)
}

// 测试BaseTimestampDSL结构体
func TestBaseTimestampDSL(t *testing.T) {
	now := time.Now()
	timestamp := BaseTimestampDSL{
		CreatedAt: now,
		UpdatedAt: now,
	}

	assert.Equal(t, now, timestamp.CreatedAt)
	assert.Equal(t, now, timestamp.UpdatedAt)
}

// 测试BaseSoftDeleteDSL结构体
func TestBaseSoftDeleteDSL(t *testing.T) {
	softDelete := BaseSoftDeleteDSL{
		DeletedAt: gorm.DeletedAt{},
	}

	assert.NotNil(t, softDelete.DeletedAt)
	assert.False(t, softDelete.DeletedAt.Valid)
}

// 测试复杂查询构建
func TestComplexQueryBuilder(t *testing.T) {
	qb := QueryBuilder{
		Table: "orders",
		Where: []Condition{
			CompareCond{Field: "status", Operator: "=", Value: "active"},
			BetweenCondition{Field: "created_at", Start: "2024-01-01", End: "2024-12-31"},
			InCondition{Field: "category_id", Values: []interface{}{1, 2, 3}},
			OrConditions{
				Conditions: []Condition{
					CompareCond{Field: "priority", Operator: "=", Value: "high"},
					CompareCond{Field: "urgent", Operator: "=", Value: true},
				},
			},
			RawCondition{Query: "amount > ?", Args: []interface{}{100}},
		},
		Select: []string{"id", "customer_name", "amount", "status"},
		Joins: []JoinCondition{
			{Type: LeftJoin, Table: "customers", On: "orders.customer_id = customers.id", Alias: "c"},
			{Type: InnerJoin, Table: "categories", On: "orders.category_id = categories.id", Alias: "cat"},
		},
		OrderBy: []string{"created_at DESC", "amount ASC"},
		GroupBy: []string{"status", "category_id"},
		Limit:   50,
		Offset:  100,
	}

	assert.Equal(t, "orders", qb.Table)
	assert.Len(t, qb.Where, 5)
	assert.Len(t, qb.Select, 4)
	assert.Len(t, qb.Joins, 2)
	assert.Len(t, qb.OrderBy, 2)
	assert.Len(t, qb.GroupBy, 2)
	assert.Equal(t, 50, qb.Limit)
	assert.Equal(t, 100, qb.Offset)

	// 验证JOIN条件
	assert.Equal(t, LeftJoin, qb.Joins[0].Type)
	assert.Equal(t, "customers", qb.Joins[0].Table)
	assert.Equal(t, "c", qb.Joins[0].Alias)

	assert.Equal(t, InnerJoin, qb.Joins[1].Type)
	assert.Equal(t, "categories", qb.Joins[1].Table)
	assert.Equal(t, "cat", qb.Joins[1].Alias)
}

// 测试DSL接口（这里只测试接口定义，实际实现会在具体的模型中测试）
func TestDSLInterface(t *testing.T) {
	// 这里只是确保DSL接口定义正确
	var dsl DSL
	assert.Nil(t, dsl) // 接口零值为nil

	// 验证接口方法签名（通过编译即可验证）
	if dsl != nil {
		_, _ = dsl.Query(nil)
		_, _ = dsl.One(nil)
		_, _ = dsl.FindByID(1)
		_, _ = dsl.Total(nil)
		_, _, _ = dsl.Page(nil)
		_, _ = dsl.UpdateByID(1, nil)
		_, _ = dsl.Update(nil, nil)
		_, _ = dsl.SoftDelete(nil)
		_, _ = dsl.DangerousDelete(nil)
		_, _ = dsl.Create(nil)
	}
}
