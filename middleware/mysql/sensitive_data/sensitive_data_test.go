package sensitive_data

import (
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
)

func init() {
	cfg.InitLoadCfg()
	// 设置测试环境标志
	initmysql.SetTestEnv(true)

	// 初始化数据库连接
	initmysql.GetInstance(cfg.LoadMysql())
}

func TestSensitiveData_TableName(t *testing.T) {
	data := &SensitiveData{}
	assert.Equal(t, table, data.TableName())
}

func TestNewModel(t *testing.T) {
	model := NewModel()
	assert.NotNil(t, model)
}

func TestDefaultSensitiveDataModel_FindByUserID(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		userId := uint64(123)
		typeModel := 1
		page := 1
		size := 10

		expectedData := []SensitiveData{
			{
				Id:               1,
				UserId:           userId,
				CompanyId:        456,
				Type:             typeModel,
				Name:             "敏感数据1",
				Account:          "account1",
				Url:              "https://example.com/1",
				Keyword:          "关键词1",
				Owner:            "所有者1",
				FirstFoundTime:   time.Now(),
				LastestFoundTime: time.Now(),
				Status:           0,
				CreatedAt:        time.Now(),
				UpdatedAt:        time.Now(),
			},
			{
				Id:               2,
				UserId:           userId,
				CompanyId:        456,
				Type:             typeModel,
				Name:             "敏感数据2",
				Account:          "account2",
				Url:              "https://example.com/2",
				Keyword:          "关键词2",
				Owner:            "所有者2",
				FirstFoundTime:   time.Now(),
				LastestFoundTime: time.Now(),
				Status:           1,
				CreatedAt:        time.Now(),
				UpdatedAt:        time.Now(),
			},
		}

		// 计数查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(2)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_data` WHERE user_id = (.+) and type = (.+)").
			WithArgs(userId, typeModel).
			WillReturnRows(countRows)

		// 数据查询
		dataRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "operator_id", "type", "name", "account", "url",
			"img", "keyword", "owner", "first_found_time", "lastest_found_time", "status",
			"created_at", "updated_at", "sha", "img_url", "plat_name", "logo", "download_url",
			"description", "app_type", "company_name", "origin_id", "erweima", "is_danger",
		})
		for _, data := range expectedData {
			dataRows.AddRow(
				data.Id, data.UserId, data.CompanyId, data.OperatorId, data.Type, data.Name,
				data.Account, data.Url, data.Img, data.Keyword, data.Owner, data.FirstFoundTime,
				data.LastestFoundTime, data.Status, data.CreatedAt, data.UpdatedAt, data.Sha,
				data.ImgUrl, data.PlatName, data.Logo, data.DownloadUrl, data.Description,
				data.AppType, data.CompanyName, data.OriginId, data.Erweima, data.IsDanger,
			)
		}

		mock.ExpectQuery("SELECT (.+) FROM `sensitive_data` WHERE user_id = (.+) and type = (.+) ORDER BY created_at desc LIMIT (.+)").
			WithArgs(userId, typeModel, size).
			WillReturnRows(dataRows)

		result, total, err := model.FindByUserID(userId, typeModel, page, size)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, result, 2)
		assert.Equal(t, expectedData[0].Name, result[0].Name)
		assert.Equal(t, expectedData[1].Name, result[1].Name)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("empty_result", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		userId := uint64(999)
		typeModel := 1
		page := 1
		size := 10

		// 计数查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(0)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_data` WHERE user_id = (.+) and type = (.+)").
			WithArgs(userId, typeModel).
			WillReturnRows(countRows)

		// 数据查询
		dataRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "operator_id", "type", "name", "account", "url",
			"img", "keyword", "owner", "first_found_time", "lastest_found_time", "status",
			"created_at", "updated_at", "sha", "img_url", "plat_name", "logo", "download_url",
			"description", "app_type", "company_name", "origin_id", "erweima", "is_danger",
		})

		mock.ExpectQuery("SELECT (.+) FROM `sensitive_data` WHERE user_id = (.+) and type = (.+) ORDER BY created_at desc LIMIT (.+)").
			WithArgs(userId, typeModel, size).
			WillReturnRows(dataRows)

		result, total, err := model.FindByUserID(userId, typeModel, page, size)
		assert.NoError(t, err)
		assert.Equal(t, int64(0), total)
		assert.Len(t, result, 0)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		userId := uint64(123)
		typeModel := 1
		page := 1
		size := 10

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_data` WHERE user_id = (.+) and type = (.+)").
			WithArgs(userId, typeModel).
			WillReturnError(sql.ErrConnDone)

		_, _, err := model.FindByUserID(userId, typeModel, page, size)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultSensitiveDataModel_CountByRange(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		userId := uint64(123)
		typeModel := []int{1, 2, 3}
		start := "2024-01-01"
		end := "2024-12-31"

		expectedCount := int64(5)
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(expectedCount)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_data` WHERE user_id = (.+) and `type` IN \\((.+)\\) AND created_at between (.+) AND (.+)").
			WithArgs(userId, typeModel[0], typeModel[1], typeModel[2], start, end).
			WillReturnRows(countRows)

		count, err := model.CountByRange(userId, typeModel, start, end)
		assert.NoError(t, err)
		assert.Equal(t, expectedCount, count)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("zero_count", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		userId := uint64(999)
		typeModel := []int{1}
		start := "2024-01-01"
		end := "2024-01-02"

		countRows := sqlmock.NewRows([]string{"count"}).AddRow(0)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_data` WHERE user_id = (.+) and `type` IN \\((.+)\\) AND created_at between (.+) AND (.+)").
			WithArgs(userId, typeModel[0], start, end).
			WillReturnRows(countRows)

		count, err := model.CountByRange(userId, typeModel, start, end)
		assert.NoError(t, err)
		assert.Equal(t, int64(0), count)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		userId := uint64(123)
		typeModel := []int{1}
		start := "2024-01-01"
		end := "2024-12-31"

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_data` WHERE user_id = (.+) and `type` IN \\((.+)\\) AND created_at between (.+) AND (.+)").
			WithArgs(userId, typeModel[0], start, end).
			WillReturnError(sql.ErrConnDone)

		_, err := model.CountByRange(userId, typeModel, start, end)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultSensitiveDataModel_Update(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		id := uint64(1)
		updates := map[string]interface{}{
			"status": 1,
			"name":   "更新后的名称",
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `sensitive_data` SET (.+) WHERE id = (.+)").
			WithArgs(utils.SqlMockArgs(4)...).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err := model.Update(id, updates)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		id := uint64(1)
		updates := map[string]interface{}{
			"status": 2,
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `sensitive_data` SET (.+) WHERE id = (.+)").
			WithArgs(utils.SqlMockArgs(3)...).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Update(id, updates)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultSensitiveDataModel_Create(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		data := &SensitiveData{
			UserId:           123,
			CompanyId:        456,
			Type:             1,
			Name:             "新敏感数据",
			Account:          "new_account",
			Url:              "https://example.com/new",
			Keyword:          "新关键词",
			Owner:            "新所有者",
			FirstFoundTime:   time.Now(),
			LastestFoundTime: time.Now(),
			Status:           0,
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `sensitive_data`").
			WithArgs(utils.SqlMockArgs(32)...).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		err := model.Create(data)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		data := &SensitiveData{
			UserId:  123,
			Type:    1,
			Name:    "失败数据",
			Account: "fail_account",
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `sensitive_data`").
			WithArgs(utils.SqlMockArgs(32)...).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Create(data)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

// 测试Paginate函数
func TestPaginate(t *testing.T) {
	t.Run("normal_pagination", func(t *testing.T) {
		page := 2
		size := 10
		paginateFunc := Paginate(page, size)
		assert.NotNil(t, paginateFunc)
	})

	t.Run("first_page", func(t *testing.T) {
		page := 1
		size := 5
		paginateFunc := Paginate(page, size)
		assert.NotNil(t, paginateFunc)
	})

	t.Run("zero_page", func(t *testing.T) {
		page := 0
		size := 10
		paginateFunc := Paginate(page, size)
		assert.NotNil(t, paginateFunc)
	})

	t.Run("negative_page", func(t *testing.T) {
		page := -1
		size := 10
		paginateFunc := Paginate(page, size)
		assert.NotNil(t, paginateFunc)
	})
}

// 测试常量
func TestConstants(t *testing.T) {
	assert.Equal(t, "sensitive_data", table)
}

// 测试结构体字段
func TestSensitiveData_Fields(t *testing.T) {
	now := time.Now()
	data := &SensitiveData{
		Id:               1,
		UserId:           123,
		CompanyId:        456,
		OperatorId:       789,
		Type:             1,
		Name:             "测试敏感数据",
		Account:          "test_account",
		Url:              "https://example.com/test",
		Img:              "base64_image_data",
		Keyword:          "测试关键词",
		Owner:            "测试所有者",
		FirstFoundTime:   now,
		LastestFoundTime: now,
		Status:           0,
		CreatedAt:        now,
		UpdatedAt:        now,
		Sha:              "test_sha_hash",
		ImgUrl:           "https://example.com/img.jpg",
		PlatName:         "测试平台",
		Logo:             "https://example.com/logo.png",
		DownloadUrl:      "https://example.com/download",
		Description:      "测试描述",
		AppType:          1,
		CompanyName:      "测试公司",
		OriginId:         "test_origin_id",
		Erweima:          "https://example.com/qr.png",
		IsDanger:         0,
	}

	assert.Equal(t, uint64(1), data.Id)
	assert.Equal(t, uint64(123), data.UserId)
	assert.Equal(t, uint64(456), data.CompanyId)
	assert.Equal(t, uint64(789), data.OperatorId)
	assert.Equal(t, 1, data.Type)
	assert.Equal(t, "测试敏感数据", data.Name)
	assert.Equal(t, "test_account", data.Account)
	assert.Equal(t, "https://example.com/test", data.Url)
	assert.Equal(t, "base64_image_data", data.Img)
	assert.Equal(t, "测试关键词", data.Keyword)
	assert.Equal(t, "测试所有者", data.Owner)
	assert.Equal(t, now, data.FirstFoundTime)
	assert.Equal(t, now, data.LastestFoundTime)
	assert.Equal(t, 0, data.Status)
	assert.Equal(t, now, data.CreatedAt)
	assert.Equal(t, now, data.UpdatedAt)
	assert.Equal(t, "test_sha_hash", data.Sha)
	assert.Equal(t, "https://example.com/img.jpg", data.ImgUrl)
	assert.Equal(t, "测试平台", data.PlatName)
	assert.Equal(t, "https://example.com/logo.png", data.Logo)
	assert.Equal(t, "https://example.com/download", data.DownloadUrl)
	assert.Equal(t, "测试描述", data.Description)
	assert.Equal(t, 1, data.AppType)
	assert.Equal(t, "测试公司", data.CompanyName)
	assert.Equal(t, "test_origin_id", data.OriginId)
	assert.Equal(t, "https://example.com/qr.png", data.Erweima)
	assert.Equal(t, 0, data.IsDanger)
}

// 测试不同类型的敏感数据
func TestSensitiveData_DifferentTypes(t *testing.T) {
	testCases := []struct {
		name        string
		dataType    int
		description string
	}{
		{
			name:        "platform_type",
			dataType:    1,
			description: "平台类型",
		},
		{
			name:        "netdisk_type",
			dataType:    2,
			description: "网盘类型",
		},
		{
			name:        "library_type",
			dataType:    3,
			description: "文库类型",
		},
		{
			name:        "code_repo_type",
			dataType:    4,
			description: "代码仓库类型",
		},
		{
			name:        "wechat_public_type",
			dataType:    5,
			description: "公众号类型",
		},
		{
			name:        "miniprogram_type",
			dataType:    6,
			description: "小程序类型",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			data := &SensitiveData{
				Type: tc.dataType,
				Name: tc.description,
			}
			assert.Equal(t, tc.dataType, data.Type)
			assert.Equal(t, tc.description, data.Name)
		})
	}
}

// 测试模型接口实现
func TestSensitiveDataModelInterface(t *testing.T) {
	model := NewModel()

	// 确保实现了SensitiveDataModel接口
	var _ SensitiveDataModel = model

	// 测试接口方法存在
	assert.NotNil(t, model)
}

// 测试默认模型结构
func TestDefaultSensitiveDataModel_Structure(t *testing.T) {
	model := NewModel()

	// 类型断言确保返回的是正确的类型
	defaultModel, ok := model.(*defaultSensitiveDataModel)
	assert.True(t, ok)
	assert.Equal(t, table, defaultModel.table)
	assert.NotNil(t, defaultModel.DB)
}
