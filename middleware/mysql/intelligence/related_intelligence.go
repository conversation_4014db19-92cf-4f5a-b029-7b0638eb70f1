package intelligence

//go:generate mockgen -destination=./mock/related_intelligence_mock.go -package=intelligence micro-service/middleware/mysql/intelligence RelatedIntelligenceInterface

import (
	"micro-service/middleware/mysql"

	"gorm.io/gorm"
)

// RelatedIntelligence 关联情报
type RelatedIntelligence struct {
	// 主键ID
	ID uint64 `json:"id"`
	// 情报ID
	IntelligenceID uint64 `json:"intelligence_id"`
	// 企业ID
	EnterpriseID uint64 `json:"enterprise_id"`
	// 企业名称
	EnterpriseName string `json:"enterprise_name"`
	// 用户ID
	UserID uint64 `json:"user_id"`
	// 资产ID
	AssetID string `json:"asset_id"`
	// ip 地址
	AssetIP string `json:"asset_ip"`
	// 端口
	AssetPort string `json:"asset_port"`
	// 协议
	AssetProtocol string `json:"asset_protocol"`
	// URL
	AssetURL string `json:"asset_url"`
	// domain
	AssetDomain string `json:"asset_domain"`
	// 网站标题
	AssetTitle string `json:"asset_title"`
	// 状态
	AssetStatus string `json:"asset_status"`
	// 情报类型
	IntelligenceType string `json:"intelligence_type"`
	// 情报标签
	IntelligenceTags string `json:"intelligence_tags"`
	// 情报国家
	IntelligenceCountry string `json:"intelligence_country"`
	// 风险类型，1: 热点漏洞，2: 威胁情报，3: 专项事件 4: 数据泄露
	RiskType string `json:"risk_type"`
	// 风险名称/URL
	RiskName string `json:"risk_name"`
	// 专项名称
	SpecialProjectName string `json:"special_project_name"`
	// 受影响的服务或组件
	ServiceComponent string `json:"service_component"`
	// 第一次检测发现，发现时间
	FoundTime string `json:"found_time"`
	// 最近一次检测发现，更新时间
	UpdateTime string `json:"update_time"`
}

func (*RelatedIntelligence) TableName() string {
	return "intelligence_related_intelligence"
}

type RelatedIntelligenceInterface interface {
	Create(*RelatedIntelligence) error
	BatchCreate([]*RelatedIntelligence) error
	FindBy(opts ...mysql.HandleFunc) (*RelatedIntelligence, error)
	List(page, size int, opts ...mysql.HandleFunc) ([]RelatedIntelligence, int64, error)
	ListAll(opts ...mysql.HandleFunc) ([]RelatedIntelligence, error)
	Count(opts ...mysql.HandleFunc) (int64, error)
	DistinctColumn(column string, opts ...mysql.HandleFunc) ([]string, error)
	DeleteByIds(ids []uint) error
	DeleteBy(opts ...mysql.HandleFunc) error
}

func NewRelatedIntelligence(db ...*gorm.DB) RelatedIntelligenceInterface {
	return &DefaultRelatedIntelligence{DB: mysql.GetDbClient(db...)}
}

type DefaultRelatedIntelligence struct{ *gorm.DB }

func (d *DefaultRelatedIntelligence) Create(ri *RelatedIntelligence) error {
	return d.DB.Create(ri).Error
}

func (d *DefaultRelatedIntelligence) BatchCreate(riList []*RelatedIntelligence) error {
	if len(riList) == 0 {
		return nil
	}
	return d.DB.CreateInBatches(riList, 500).Error
}

func (d *DefaultRelatedIntelligence) FindBy(opts ...mysql.HandleFunc) (*RelatedIntelligence, error) {
	query := d.DB.Model(&RelatedIntelligence{})
	for _, opt := range opts {
		opt(query)
	}
	var ri RelatedIntelligence
	err := query.First(&ri).Error
	return &ri, err
}

func (d *DefaultRelatedIntelligence) List(page, size int, opts ...mysql.HandleFunc) ([]RelatedIntelligence, int64, error) {
	query := d.DB.Model(&RelatedIntelligence{})
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	var list = make([]RelatedIntelligence, 0)
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Find(&list).Error
	return list, total, err
}

func (d *DefaultRelatedIntelligence) ListAll(opts ...mysql.HandleFunc) ([]RelatedIntelligence, error) {
	query := d.DB.Model(&RelatedIntelligence{})
	for _, opt := range opts {
		opt(query)
	}
	var list = make([]RelatedIntelligence, 0)
	err := query.Find(&list).Error
	return list, err
}

func (d *DefaultRelatedIntelligence) Count(opts ...mysql.HandleFunc) (int64, error) {
	query := d.DB.Model(&RelatedIntelligence{})
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	err := query.Count(&total).Error
	return total, err
}

func (d *DefaultRelatedIntelligence) DistinctColumn(column string, opts ...mysql.HandleFunc) ([]string, error) {
	query := d.DB.Model(&RelatedIntelligence{})
	for _, opt := range opts {
		opt(query)
	}
	var list = make([]string, 0)
	err := query.Distinct(column).Pluck(column, &list).Error
	return list, err
}

func (d *DefaultRelatedIntelligence) DeleteByIds(ids []uint) error {
	return d.DB.Where("id in (?)", ids).Delete(&RelatedIntelligence{}).Error
}

func (d *DefaultRelatedIntelligence) DeleteBy(opts ...mysql.HandleFunc) error {
	query := d.DB.Model(&RelatedIntelligence{})
	for _, opt := range opts {
		opt(query)
	}
	return query.Delete(&RelatedIntelligence{}).Error
}
