package intelligence

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"time"

	"gorm.io/gorm"
)

const (
	StatusOnline  = 1
	StatusOffline = 2
)

type Faker interface {
	Create(*Fake) error
	First(...mysql.HandleFunc) (Fake, error)
	Count(opts ...mysql.HandleFunc) (int64, error)
	List(page, size int, opts ...mysql.HandleFunc) ([]Fake, int64, error)
	ListAll(opts ...mysql.HandleFunc) ([]*Fake, error)
	Update(Fake) error
	UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error
	Save([]*Fake) error
	DeleteById(uint64) error
	DeleteByIds(id []uint64) error
	GetLastFoundDate() (time.Time, error)
}

func NewFake(db ...*gorm.DB) Faker {
	return &defaultFake{DB: mysql.GetDbClient(db...)}
}

// Fake 线索表
type Fake struct {
	dbx.Model
	Url       string    `gorm:"column:url;comment:钓鱼URL" json:"url"`
	Ip        string    `gorm:"column:ip;comment:IP地址" json:"ip"`
	Title     string    `gorm:"column:title;comment:标题" json:"title"`
	Country   string    `gorm:"column:country;comment:IP所在国家" json:"country"`
	Target    string    `gorm:"column:target;comment:仿冒目标" json:"target"`
	CloudName string    `gorm:"column:cloud_name;comment:云厂商" json:"cloud_name"`
	Status    int       `gorm:"column:status;comment:是否在线: 1/在线 2/离线" json:"status"`
	Source    string    `gorm:"column:source;comment:数据来源" json:"source"`
	FoundAt   time.Time `gorm:"column:found_at;comment:发现时间" json:"found_at"`
}

func (*Fake) TableName() string {
	return "intelligence_fake"
}

type defaultFake struct{ *gorm.DB }

func (d *defaultFake) Create(clue *Fake) (err error) {
	return d.DB.Create(clue).Error
}

func (d *defaultFake) First(opts ...mysql.HandleFunc) (Fake, error) {
	query := d.DB.Model(&Fake{})
	for _, opt := range opts {
		opt(query)
	}
	var clue Fake
	err := query.First(&clue).Error
	return clue, err
}

func (d *defaultFake) Count(opts ...mysql.HandleFunc) (int64, error) {
	query := d.DB.Model(&Fake{})
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	err := query.Count(&total).Error
	return total, err
}

func (d *defaultFake) List(page, size int, opts ...mysql.HandleFunc) ([]Fake, int64, error) {
	query := d.DB.Model(&Fake{})
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	var fakes = make([]Fake, 0)
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Find(&fakes).Error
	return fakes, total, err
}

func (d *defaultFake) ListAll(opts ...mysql.HandleFunc) ([]*Fake, error) {
	query := d.DB.Model(&Fake{})
	for _, opt := range opts {
		opt(query)
	}
	var clues = make([]*Fake, 0)
	err := query.Find(&clues).Error
	return clues, err
}

func (d *defaultFake) Update(clue Fake) error {
	return d.DB.Updates(&clue).Error
}

func (d *defaultFake) UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error {
	q := d.DB.Model(&Fake{})
	for _, opt := range opts {
		opt(q)
	}
	return q.Updates(m).Error
}

func (d *defaultFake) Save(clues []*Fake) error {
	if len(clues) == 0 {
		return nil
	}
	return d.DB.Save(clues).Error
}

func (d *defaultFake) DeleteById(id uint64) error {
	return d.DB.Model(&Fake{}).Where("id=?", id).Delete(&Fake{}).Error
}

func (d *defaultFake) DeleteByIds(id []uint64) error {
	return d.DB.Model(&Fake{}).Where("id in (?)", id).Delete(&Fake{}).Error
}

func (d *defaultFake) GetLastFoundDate() (time.Time, error) {
	var fake Fake
	err := d.DB.Model(&Fake{}).Order("found_at desc").First(&fake).Error
	if err != nil {
		return time.Time{}, err
	}
	return fake.FoundAt, nil
}
