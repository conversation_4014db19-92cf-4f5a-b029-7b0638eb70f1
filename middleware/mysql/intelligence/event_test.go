package intelligence

import (
	mysql2 "micro-service/middleware/mysql"
	"testing"
	"time"
)

func TestIsExist(t *testing.T) {
	Init()
	e := Event{
		CreationTime: time.Now(),
		DownloadLink: "test",
		Filename:     "test",
		Hash:         "test",
		Tags:         "test",
		IpCount:      10,
		LocalLink:    "test",
	}
	err := NewEvent().Create(&e)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("创建测试数据失败: %v", err)
	}
	exist, err := NewEvent().IsExist(mysql2.WithColumnValue("hash", e.Hash))
	if err != nil {
		t.<PERSON><PERSON><PERSON>("查询测试数据失败: %v", err)
	}
	if !exist {
		t.<PERSON><PERSON><PERSON>("查询测试数据失败: %v", err)
	}

	//清理测试数据
	event, err := NewEvent().First(mysql2.WithColumnValue("hash", e.Hash))
	if err != nil {
		t.<PERSON><PERSON><PERSON>("查询测试数据失败: %v", err)
	}

	err = NewEvent().DeleteById(uint(event.ID))
	if err != nil {
		t.<PERSON><PERSON><PERSON>("删除测试数据失败: %v", err)
	}
}
