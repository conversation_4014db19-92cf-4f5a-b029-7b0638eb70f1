package intelligence

import (
	"time"

	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

type IntelligenceHotPoc interface {
	Create(*HotPoc) error
	First(...mysql.HandleFunc) (HotPoc, error)
	GetById(uint) (HotPoc, error)
	Count(opts ...mysql.HandleFunc) (int64, error)
	CountByLevel() (map[string]int64, error)
	List(page, size int, opts ...mysql.HandleFunc) ([]HotPoc, int64, error)
	ListAll(opts ...mysql.HandleFunc) ([]*HotPoc, error)
	DistinctColumn(column string, opts ...mysql.HandleFunc) ([]string, error)
	Update(*HotPoc) error
	UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error
	Save([]*HotPoc) error
	DeleteById(uint64) error
	DeleteByIds(id []uint64) error
}

func NewHotPoc(db ...*gorm.DB) IntelligenceHotPoc {
	return &DefaultHotPoc{DB: mysql.GetDbClient(db...)}
}

// HotPoc 热点漏洞
type HotPoc struct {
	dbx.Model
	Report
	Name          string    `gorm:"column:name;comment:漏洞名称" json:"name"`
	Cve           string    `gorm:"column:cve;comment:CVE编号" json:"cve"`
	Cnnvd         string    `gorm:"column:cnnvd;comment:CNNVD编号" json:"cnnvd"`
	ImpactRange   string    `gorm:"column:impact_range;comment:影响范围" json:"impact_range"`
	ImpactProduct string    `gorm:"column:impact_product;comment:影响产品" json:"impact_product"`
	ImpactVersion string    `gorm:"column:impact_version;comment:影响版本" json:"impact_version"`
	Solution      string    `gorm:"column:solution;comment:解决方案" json:"solution"`
	Introduce     string    `gorm:"column:introduce;comment:漏洞介绍" json:"introduce"`
	RiskLevel     string    `gorm:"column:risk_level;comment:风险级别" json:"risk_level"`
	FofaQuery     string    `gorm:"column:fofa_query;comment:FOFA查询语句" json:"fofa_query"`
	FofaCount     uint64    `gorm:"column:fofa_count;comment:影响资产数量" json:"fofa_count"`
	FoundAt       time.Time `gorm:"column:found_at;comment:发现时间" json:"found_at"`
	ESQuery       string    `gorm:"column:es_query;comment:ES查询语句" json:"es_query"`
	ESIndex       string    `gorm:"column:es_index;comment:ES查询索引" json:"es_index"`
	ESKeywords    string    `gorm:"column:es_keywords;comment:ES查询关键字" json:"es_keywords"`
	Tag           string    `gorm:"column:tag;comment:标签" json:"tag"` // 多个标签用逗号分隔
}

func (*HotPoc) TableName() string {
	return "intelligence_hot_poc"
}

type DefaultHotPoc struct{ *gorm.DB }

func (d *DefaultHotPoc) Create(clue *HotPoc) (err error) {
	return d.DB.Create(clue).Error
}

func (d *DefaultHotPoc) GetById(id uint) (HotPoc, error) {
	var poc HotPoc
	err := d.DB.First(&poc, id).Error
	return poc, err
}

func (d *DefaultHotPoc) First(opts ...mysql.HandleFunc) (HotPoc, error) {
	query := d.DB.Model(&HotPoc{})
	for _, opt := range opts {
		opt(query)
	}
	var clue HotPoc
	err := query.First(&clue).Error
	return clue, err
}

func (d *DefaultHotPoc) Count(opts ...mysql.HandleFunc) (int64, error) {
	query := d.DB.Model(&HotPoc{})
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	err := query.Count(&total).Error
	return total, err
}

func (d *DefaultHotPoc) CountByLevel() (map[string]int64, error) {
	data := make([]map[string]interface{}, 0)
	err := d.DB.Table("intelligence_hot_poc").Select("risk_level, COUNT(1) as count").Group("risk_level").Scan(&data).Error
	if err != nil {
		return nil, err
	}
	result := make(map[string]int64, len(data))
	for _, r := range data {
		result[r["risk_level"].(string)] = r["count"].(int64)
	}
	return result, nil
}

func (d *DefaultHotPoc) List(page, size int, opts ...mysql.HandleFunc) ([]HotPoc, int64, error) {
	query := d.DB.Model(&HotPoc{})
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	var clues = make([]HotPoc, 0)
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Find(&clues).Error
	return clues, total, err
}

func (d *DefaultHotPoc) ListAll(opts ...mysql.HandleFunc) ([]*HotPoc, error) {
	query := d.DB.Model(&HotPoc{})
	for _, opt := range opts {
		opt(query)
	}
	var clues = make([]*HotPoc, 0)
	err := query.Find(&clues).Error
	return clues, err
}

func (d *DefaultHotPoc) DistinctColumn(column string, opts ...mysql.HandleFunc) ([]string, error) {
	query := d.DB.Model(&HotPoc{})
	for _, opt := range opts {
		opt(query)
	}
	var list []string
	err := query.Distinct(column).Pluck(column, &list).Error
	return list, err
}

func (d *DefaultHotPoc) Update(clue *HotPoc) error {
	return d.DB.Updates(&clue).Error
}

func (d *DefaultHotPoc) UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error {
	q := d.DB.Model(&HotPoc{})
	for _, opt := range opts {
		opt(q)
	}
	return q.Updates(m).Error
}

func (d *DefaultHotPoc) Save(clues []*HotPoc) error {
	if len(clues) == 0 {
		return nil
	}
	return d.DB.Save(clues).Error
}

func (d *DefaultHotPoc) DeleteById(id uint64) error {
	return d.DB.Model(&HotPoc{}).Where("id=?", id).Delete(&HotPoc{}).Error
}

func (d *DefaultHotPoc) DeleteByIds(id []uint64) error {
	return d.DB.Model(&HotPoc{}).Where("id in (?)", id).Delete(&HotPoc{}).Error
}
