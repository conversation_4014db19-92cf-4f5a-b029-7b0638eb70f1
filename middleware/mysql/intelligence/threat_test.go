package intelligence

import (
	"micro-service/initialize/es"
	mysqllib "micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
)

func Init() {
	cfg.InitLoadCfg()
	log.Init()
	_ = mysqllib.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	es.GetInstance(cfg.LoadElastic())
}

func TestThreatUserList(t *testing.T) {
	Init()
	_, _, err := NewUserThreat().List(1, 10, 2)
	if err != nil {
		panic(err)
	}
}
