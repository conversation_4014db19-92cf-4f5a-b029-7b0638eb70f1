package intelligence

import (
	"micro-service/middleware/mysql"
	"time"

	"gorm.io/gorm"
)

// DataSummary 数据汇总
type DataSummary struct {
	// 主键ID
	ID uint `json:"id"`
	// 专项名称
	SpecialProjectName string `json:"special_project_name"`
	// 数据所属实体，全部涉及企业
	Entities string `json:"entities"`
	// 数据量，互联网影响面
	DataVolume int `json:"data_volume"`
	// 涉及企业数量，关联资产得到
	CompanyNum int `json:"company_num"`
	// 涉及企业名单
	CompanyList []string `gorm:"-"`
	// 涉及资产数量，关联资产得到
	AssetNum int `json:"asset_num"`
	// 最新更新时间
	LastUpdateTime *time.Time `json:"last_update_time"`
}

func (*DataSummary) TableName() string {
	return "intelligence_data_summary"
}

type IntelligenceDataSummary interface {
	GetById(id uint) (*DataSummary, error)
	GetByName(name string) (*DataSummary, error)
	Save(*DataSummary) error
	BatchSave([]*DataSummary) error
	List(page, size int, opts ...mysql.HandleFunc) ([]DataSummary, int64, error)
	DistinctColumn(column string, opts ...mysql.HandleFunc) ([]string, error)
	Count(...mysql.HandleFunc) (int64, error)
	DeleteAll() error
	DeleteByNames(name []string) error
	Update(*DataSummary) error
	UpdateRelatedAssetCount(name string, assetCount, companyCount int) error
}

// DefaultDataSummary实现IntelligenceDataSummary接口
type DefaultDataSummary struct{ *gorm.DB }

func NewDataSummary(db ...*gorm.DB) IntelligenceDataSummary {
	return &DefaultDataSummary{DB: mysql.GetDbClient(db...)}
}

func (ds *DefaultDataSummary) GetById(id uint) (*DataSummary, error) {
	var data DataSummary
	err := ds.DB.Where("id = ?", id).First(&data).Error
	return &data, err
}

func (ds *DefaultDataSummary) GetByName(name string) (*DataSummary, error) {
	var data DataSummary
	err := ds.DB.Where("special_project_name = ?", name).First(&data).Error
	return &data, err
}

func (ds *DefaultDataSummary) Save(data *DataSummary) error {
	return ds.DB.Save(data).Error
}

func (ds *DefaultDataSummary) BatchSave(dataList []*DataSummary) error {
	return ds.DB.CreateInBatches(&dataList, 500).Error
}

func (ds *DefaultDataSummary) List(page, size int, opts ...mysql.HandleFunc) ([]DataSummary, int64, error) {
	query := ds.DB.Model(&DataSummary{})
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	var dataList = make([]DataSummary, 0)
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Order("id asc").Find(&dataList).Error
	return dataList, total, err
}

func (ds *DefaultDataSummary) DistinctColumn(column string, opts ...mysql.HandleFunc) ([]string, error) {
	query := ds.DB.Model(&DataSummary{})
	for _, opt := range opts {
		opt(query)
	}
	var list []string
	err := query.Distinct(column).Pluck(column, &list).Error
	return list, err
}

func (d *DefaultDataSummary) Count(opts ...mysql.HandleFunc) (int64, error) {
	query := d.DB.Model(&DataSummary{})
	for _, opt := range opts {
		opt(query)
	}
	var count int64
	err := query.Count(&count).Error
	return count, err
}

func (ds *DefaultDataSummary) DeleteAll() error {
	return ds.DB.Unscoped().Delete(&DataSummary{}).Error
}

func (ds *DefaultDataSummary) DeleteByNames(name []string) error {
	return ds.DB.Where("special_project_name in ?", name).Unscoped().Delete(&DataSummary{}).Error
}

func (ds *DefaultDataSummary) Update(data *DataSummary) error {
	return ds.DB.Model(&DataSummary{}).Omit("id").Where("id = ?", data.ID).Updates(data).Error
}

func (ds *DefaultDataSummary) UpdateRelatedAssetCount(name string, assetCount, companyCount int) error {
	return ds.DB.Model(&DataSummary{}).Omit("id").Where("special_project_name = ?", name).Updates(map[string]interface{}{
		"asset_num":   assetCount,
		"company_num": companyCount,
	}).Error
}
