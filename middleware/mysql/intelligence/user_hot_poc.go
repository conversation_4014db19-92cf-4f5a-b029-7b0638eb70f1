package intelligence

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

type IntelligenceUserHotPoc interface {
	Create(*UserHotPoc) error
	CreateInBatches(list []*UserHotPoc) error
	First(...mysql.HandleFunc) (UserHotPoc, error)
	List(page, size int, userId uint64, opts ...mysql.HandleFunc) ([]FullHotPoc, int64, error)
	ListAll(opts ...mysql.HandleFunc) ([]*UserHotPoc, error)
	DistinctColumn(column string, opts ...mysql.HandleFunc) ([]string, error)
	Update(UserHotPoc) error
	UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error
	Save([]*UserHotPoc) error
	DeleteById(uint64) error
	DeleteByIds(id []uint64) error
	DeleteBy(userId uint64, pocIds []uint64) error
}

func NewUserHotPoc(db ...*gorm.DB) IntelligenceUserHotPoc {
	return &defaultUserHotPoc{DB: mysql.GetDbClient(db...)}
}

type FullHotPoc struct {
	HotPoc
	UserHotPoc
}

// UserHotPoc 热度漏洞-用户
type UserHotPoc struct {
	dbx.Model
	PocId     uint64 `gorm:"column:poc_id;comment:漏洞ID" json:"poc_id"`
	UserId    uint64 `gorm:"column:user_id;comment:用户ID" json:"user_id"`
	RiskCount int    `gorm:"column:risk_count;comment:风险资产数量" json:"risk_count"`
}

func (*UserHotPoc) TableName() string {
	return "intelligence_user_hot_poc"
}

type defaultUserHotPoc struct{ *gorm.DB }

func (d *defaultUserHotPoc) Create(clue *UserHotPoc) (err error) {
	return d.DB.Create(clue).Error
}

func (g *defaultUserHotPoc) CreateInBatches(list []*UserHotPoc) error {
	if len(list) == 0 {
		return nil
	}
	return g.DB.Model(&UserHotPoc{}).CreateInBatches(list, 500).Error
}

func (d *defaultUserHotPoc) First(opts ...mysql.HandleFunc) (UserHotPoc, error) {
	query := d.DB.Model(&UserHotPoc{})
	for _, opt := range opts {
		opt(query)
	}
	var clue UserHotPoc
	err := query.First(&clue).Error
	return clue, err
}

func (d *defaultUserHotPoc) List(page, size int, userId uint64, opts ...mysql.HandleFunc) ([]FullHotPoc, int64, error) {
	query := d.DB.Select("*").Model(&HotPoc{}).Joins("left join intelligence_user_hot_poc on intelligence_user_hot_poc.poc_id=intelligence_hot_poc.id and intelligence_user_hot_poc.user_id = ?", userId)
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	var fakes = make([]FullHotPoc, 0)
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Find(&fakes).Error
	return fakes, total, err
}

func (d *defaultUserHotPoc) ListAll(opts ...mysql.HandleFunc) ([]*UserHotPoc, error) {
	query := d.DB.Model(&UserHotPoc{})
	for _, opt := range opts {
		opt(query)
	}
	var clues = make([]*UserHotPoc, 0)
	err := query.Find(&clues).Error
	return clues, err
}

func (d *defaultUserHotPoc) DistinctColumn(column string, opts ...mysql.HandleFunc) ([]string, error) {
	query := d.DB.Model(&UserHotPoc{})
	for _, opt := range opts {
		opt(query)
	}
	var clues = make([]string, 0)
	err := query.Distinct(column).Pluck(column, &clues).Error
	return clues, err
}

func (d *defaultUserHotPoc) Update(clue UserHotPoc) error {
	return d.DB.Updates(&clue).Error
}

func (d *defaultUserHotPoc) UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error {
	q := d.DB.Model(&UserHotPoc{})
	for _, opt := range opts {
		opt(q)
	}
	return q.Updates(m).Error
}

func (d *defaultUserHotPoc) Save(clues []*UserHotPoc) error {
	if len(clues) == 0 {
		return nil
	}
	return d.DB.Save(clues).Error
}

func (d *defaultUserHotPoc) DeleteById(id uint64) error {
	return d.DB.Model(&UserHotPoc{}).Where("id=?", id).Delete(&UserHotPoc{}).Error
}

func (d *defaultUserHotPoc) DeleteBy(userId uint64, pocIds []uint64) error {
	return d.DB.Model(&UserHotPoc{}).Where("user_id=?", userId).Where("poc_id in (?)", pocIds).Delete(&UserHotPoc{}).Error
}

func (d *defaultUserHotPoc) DeleteByIds(id []uint64) error {
	return d.DB.Model(&UserHotPoc{}).Where("id in (?)", id).Delete(&UserHotPoc{}).Error
}
