package category

import (
	"gorm.io/gorm"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

type (
	RuleModel interface {
		FindOne(opts ...mysql.HandleFunc) (Rule, error)
	}

	defaultRuleModel struct {
		*gorm.DB
		table string
	}

	Rule struct {
		dbx.Model
		CompanyName  string `gorm:"column:company" json:"company"`
		Product      string `gorm:"column:product" json:"product"`
		ProductUrl   string `gorm:"column:producturl" json:"producturl"`
		Rule         string `gorm:"column:rule" json:"rule"`
		SoftHardCode string `gorm:"column:soft_hard_code" json:"soft_hard_code"`
	}
)

var _ RuleModel = (*defaultRuleModel)(nil)

const ruleTable = "rules"

// TableName 表名
func (r *Rule) TableName() string {
	return ruleTable
}

func NewRuleModel(clients ...*gorm.DB) RuleModel {
	return &defaultRuleModel{
		DB:    mysql.GetDbClient(clients...),
		table: ruleTable,
	}
}

func (d *defaultRuleModel) FindOne(opts ...mysql.HandleFunc) (Rule, error) {
	query := d.DB.Model(&Rule{})
	for _, f := range opts {
		f(query)
	}

	var info Rule
	err := query.Limit(1).Find(&info).Error
	if err != nil {
		return Rule{}, err
	}

	return info, nil
}
