package organization_discover_task

import (
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
)

func init() {
	cfg.InitLoadCfg()
	// 设置测试环境标志
	initmysql.SetTestEnv(true)

	// 初始化数据库连接
	initmysql.GetInstance(cfg.LoadMysql())
}

// setupTestModel 创建测试用的模型实例
func setupTestModel(t *testing.T) (Model, sqlmock.Sqlmock) {
	// 为每个测试创建新的Mock实例
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to create mock: %v", err)
	}

	// Mock GORM 初始化时的查询
	mock.ExpectQuery("SELECT VERSION\\(\\)").WillReturnRows(sqlmock.NewRows([]string{"VERSION()"}).AddRow("8.0.0"))

	// 创建GORM实例
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: false,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to create gorm instance: %v", err)
	}

	model := &defaultModel{
		DB:    gormDB,
		table: TableName,
	}
	return model, mock
}

func TestOrganizationDiscoverTask_TableName(t *testing.T) {
	task := &OrganizationDiscoverTask{}
	assert.Equal(t, TableName, task.TableName())
}

func TestNewModel(t *testing.T) {
	model := NewModel()
	assert.NotNil(t, model)
}

func TestDefaultModel_Create(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		task := &OrganizationDiscoverTask{
			UserId:    123,
			CompanyId: 456,
			GroupId:   789,
			Name:      "测试任务",
			Status:    StatusDefault,
			Step:      StepDefault,
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `organization_discover_task`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		err := model.Create(task)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		task := &OrganizationDiscoverTask{
			UserId:    123,
			CompanyId: 456,
			GroupId:   789,
			Name:      "测试任务",
			Status:    StatusDefault,
			Step:      StepDefault,
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `organization_discover_task`").
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Create(task)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultModel_Update(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		task := &OrganizationDiscoverTask{
			ID:        1,
			UserId:    123,
			CompanyId: 456,
			GroupId:   789,
			Name:      "更新的任务名称",
			Status:    StatusDoing,
			Step:      StepClueOne,
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `organization_discover_task`").
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err := model.Update(task)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		task := &OrganizationDiscoverTask{
			ID:        1,
			UserId:    123,
			CompanyId: 456,
			GroupId:   789,
			Name:      "更新的任务名称",
			Status:    StatusDoing,
			Step:      StepClueOne,
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `organization_discover_task`").
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Update(task)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultModel_First(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		now := time.Now()
		expectedTask := &OrganizationDiscoverTask{
			ID:           1,
			UserId:       123,
			CompanyId:    456,
			GroupId:      789,
			Name:         "测试任务",
			Status:       StatusDoing,
			Step:         StepClueOne,
			StepStatus:   StepStatusNotFinish,
			ScanProgress: 50.00,
			CreatedAt:    now,
			UpdatedAt:    now,
		}

		rows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "group_id", "name", "status", "step",
			"step_status", "step_detail", "scan_progress", "created_at", "updated_at",
		}).AddRow(
			expectedTask.ID, expectedTask.UserId, expectedTask.CompanyId,
			expectedTask.GroupId, expectedTask.Name, expectedTask.Status,
			expectedTask.Step, expectedTask.StepStatus, "", expectedTask.ScanProgress,
			expectedTask.CreatedAt, expectedTask.UpdatedAt,
		)

		mock.ExpectQuery("SELECT (.+) FROM `organization_discover_task`").
			WillReturnRows(rows)

		result, err := model.First()
		assert.NoError(t, err)
		assert.Equal(t, expectedTask.ID, result.ID)
		assert.Equal(t, expectedTask.Name, result.Name)
		assert.Equal(t, expectedTask.Status, result.Status)
		assert.Equal(t, expectedTask.Step, result.Step)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		model, mock := setupTestModel(t)

		mock.ExpectQuery("SELECT (.+) FROM `organization_discover_task`").
			WillReturnError(gorm.ErrRecordNotFound)

		result, err := model.First()
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultModel_Find(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		now := time.Now()
		expectedTasks := []*OrganizationDiscoverTask{
			{
				ID:           1,
				UserId:       123,
				CompanyId:    456,
				GroupId:      789,
				Name:         "测试任务1",
				Status:       StatusDoing,
				Step:         StepClueOne,
				StepStatus:   StepStatusNotFinish,
				ScanProgress: 50.00,
				CreatedAt:    now,
				UpdatedAt:    now,
			},
			{
				ID:           2,
				UserId:       123,
				CompanyId:    456,
				GroupId:      789,
				Name:         "测试任务2",
				Status:       StatusSuccess,
				Step:         StepFinish,
				StepStatus:   StepStatusFinish,
				ScanProgress: 100.00,
				CreatedAt:    now,
				UpdatedAt:    now,
			},
		}

		rows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "group_id", "name", "status", "step",
			"step_status", "step_detail", "scan_progress", "created_at", "updated_at",
		})
		for _, task := range expectedTasks {
			rows.AddRow(
				task.ID, task.UserId, task.CompanyId,
				task.GroupId, task.Name, task.Status,
				task.Step, task.StepStatus, "", task.ScanProgress,
				task.CreatedAt, task.UpdatedAt,
			)
		}

		mock.ExpectQuery("SELECT (.+) FROM `organization_discover_task`").
			WillReturnRows(rows)

		results, err := model.Find()
		assert.NoError(t, err)
		assert.Len(t, results, 2)
		assert.Equal(t, expectedTasks[0].ID, results[0].ID)
		assert.Equal(t, expectedTasks[1].ID, results[1].ID)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("empty result", func(t *testing.T) {
		model, mock := setupTestModel(t)

		rows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "group_id", "name", "status", "step",
			"step_status", "step_detail", "scan_progress", "created_at", "updated_at",
		})

		mock.ExpectQuery("SELECT (.+) FROM `organization_discover_task`").
			WillReturnRows(rows)

		results, err := model.Find()
		assert.NoError(t, err)
		assert.Len(t, results, 0)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		mock.ExpectQuery("SELECT (.+) FROM `organization_discover_task`").
			WillReturnError(sql.ErrConnDone)

		results, err := model.Find()
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultModel_Count(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		expectedCount := int64(10)
		rows := sqlmock.NewRows([]string{"count(*)"}).AddRow(expectedCount)

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `organization_discover_task`").
			WillReturnRows(rows)

		count, err := model.Count()
		assert.NoError(t, err)
		assert.Equal(t, expectedCount, count)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `organization_discover_task`").
			WillReturnError(sql.ErrConnDone)

		count, err := model.Count()
		assert.Error(t, err)
		assert.Equal(t, int64(0), count)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultModel_Page(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		page := 1
		size := 10
		expectedCount := int64(15)
		now := time.Now()
		expectedTasks := []*OrganizationDiscoverTask{
			{
				ID:           1,
				UserId:       123,
				CompanyId:    456,
				GroupId:      789,
				Name:         "测试任务1",
				Status:       StatusDoing,
				Step:         StepClueOne,
				StepStatus:   StepStatusNotFinish,
				ScanProgress: 50.00,
				CreatedAt:    now,
				UpdatedAt:    now,
			},
			{
				ID:           2,
				UserId:       123,
				CompanyId:    456,
				GroupId:      789,
				Name:         "测试任务2",
				Status:       StatusSuccess,
				Step:         StepFinish,
				StepStatus:   StepStatusFinish,
				ScanProgress: 100.00,
				CreatedAt:    now,
				UpdatedAt:    now,
			},
		}

		countRows := sqlmock.NewRows([]string{"count(*)"}).AddRow(expectedCount)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `organization_discover_task`").
			WillReturnRows(countRows)

		rows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "group_id", "name", "status", "step",
			"step_status", "step_detail", "scan_progress", "created_at", "updated_at",
		})
		for _, task := range expectedTasks {
			rows.AddRow(
				task.ID, task.UserId, task.CompanyId,
				task.GroupId, task.Name, task.Status,
				task.Step, task.StepStatus, "", task.ScanProgress,
				task.CreatedAt, task.UpdatedAt,
			)
		}

		mock.ExpectQuery("SELECT \\* FROM `organization_discover_task` WHERE `organization_discover_task`.`deleted_at` IS NULL LIMIT \\?").
			WithArgs(10).
			WillReturnRows(rows)

		results, count, err := model.Page(page, size)
		assert.NoError(t, err)
		assert.Equal(t, expectedCount, count)
		assert.Len(t, results, 2)
		assert.Equal(t, expectedTasks[0].ID, results[0].ID)
		assert.Equal(t, expectedTasks[1].ID, results[1].ID)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("count error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		page := 1
		size := 10

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `organization_discover_task`").
			WillReturnError(sql.ErrConnDone)

		results, count, err := model.Page(page, size)
		assert.Error(t, err)
		assert.Equal(t, int64(0), count)
		assert.Nil(t, results)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("query error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		page := 1
		size := 10
		expectedCount := int64(15)

		countRows := sqlmock.NewRows([]string{"count(*)"}).AddRow(expectedCount)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `organization_discover_task`").
			WillReturnRows(countRows)

		mock.ExpectQuery("SELECT \\* FROM `organization_discover_task` WHERE `organization_discover_task`.`deleted_at` IS NULL LIMIT \\?").
			WithArgs(10).
			WillReturnError(sql.ErrConnDone)

		results, count, err := model.Page(page, size)
		assert.Error(t, err)
		assert.Equal(t, int64(0), count)
		assert.Nil(t, results)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestWithID(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	id := uint64(1)

	mock.ExpectQuery("SELECT (.+) FROM `organization_discover_task` WHERE id = \\? AND `organization_discover_task`.`deleted_at` IS NULL ORDER BY `organization_discover_task`.`id` LIMIT \\?").
		WithArgs(id, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithID(id))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithUserID(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	userID := uint64(123)

	mock.ExpectQuery("SELECT (.+) FROM `organization_discover_task` WHERE user_id = \\? AND `organization_discover_task`.`deleted_at` IS NULL ORDER BY `organization_discover_task`.`id` LIMIT \\?").
		WithArgs(userID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithUserID(userID))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithCompanyID(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	companyID := uint64(456)

	mock.ExpectQuery("SELECT (.+) FROM `organization_discover_task` WHERE company_id = \\? AND `organization_discover_task`.`deleted_at` IS NULL ORDER BY `organization_discover_task`.`id` LIMIT \\?").
		WithArgs(companyID, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithCompanyID(companyID))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithGroupId(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	groupId := uint64(789)

	mock.ExpectQuery("SELECT (.+) FROM `organization_discover_task` WHERE group_id = \\? AND `organization_discover_task`.`deleted_at` IS NULL ORDER BY `organization_discover_task`.`id` LIMIT \\?").
		WithArgs(groupId, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithGroupId(groupId))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithStatus(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	status := StatusDoing

	mock.ExpectQuery("SELECT (.+) FROM `organization_discover_task` WHERE status = \\? AND `organization_discover_task`.`deleted_at` IS NULL ORDER BY `organization_discover_task`.`id` LIMIT \\?").
		WithArgs(status, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithStatus(status))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithStep(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	step := StepClueOne

	mock.ExpectQuery("SELECT (.+) FROM `organization_discover_task` WHERE step = \\? AND `organization_discover_task`.`deleted_at` IS NULL ORDER BY `organization_discover_task`.`id` LIMIT \\?").
		WithArgs(step, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithStep(step))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithStepStatus(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	stepStatus := StepStatusNotFinish

	mock.ExpectQuery("SELECT (.+) FROM `organization_discover_task` WHERE step_status = \\? AND `organization_discover_task`.`deleted_at` IS NULL ORDER BY `organization_discover_task`.`id` LIMIT \\?").
		WithArgs(stepStatus, 1).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithStepStatus(stepStatus))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}
