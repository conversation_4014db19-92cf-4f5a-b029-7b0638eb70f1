package scan_crontab_tasks

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/log"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

const (
	// 表名
	TableName = "scan_crontab_tasks"

	// 扫描范围
	CronInputIP       = 0
	CronFileIP        = 1
	CronSureIP        = 2
	CronFilterRangeIP = 3

	// 任务状态
	CronStatusDefault  = 0 // 默认0
	CronStatusRunning  = 1 // 正在运行
	CronStatusFinished = 2 // 资产扫描状态 已完成
	CronStatusFailed   = 3 // 资产扫描状态 失败
	CronStatusPause    = 4 // 资产扫描状态 停止

	// 任务进行到哪一步了 默认0 没开始或者是资产扫描未完成 1:正在同步数据 2:正在打标签 3:正在漏洞扫描 4:扫描完成
	CronStepScanning = 0
	CronStepSyncData = 1
	CronStepTagging  = 2
	CronStepLoophole = 3
	CronStepFinished = 4

	// 是否为周期任务 默认0 否 1 是周期任务
	CronIsLopTask = 1

	// 类型 默认0 全量扫 1:单位资产上传后扫描 2:周期任务实例化
	CronTypeDefault          = 0
	CronTypeCompany          = 1
	CronTypeScheduleInstance = 2
	CronTypeScheduleMonth    = 3
	CronTypeScheduleWeek     = 4
	CronTypeScheduleDay      = 5
	CronTypeScheduleOnce     = 6

	// 任务类型 默认0 资产及漏洞扫描 1:资产扫描 2:漏洞扫描
	CronTaskTypeDefault = 0
	CronTaskTypeAsset   = 1 // 资产扫描类型
	CronTaskTypePoc     = 2 // 漏洞扫描类型

	// 扫描模式 0为精准 1为极速
	CronScanTypeAccurate = 0
	CronScanTypeSpeed    = 1

	// 审核状态 默认0 1通过 2拒绝
	AuditPass   = 1
	AuditReject = 2
)

// CronTaskTypeMap 任务类型映射
var CronTaskTypeMap = map[int]string{
	CronTaskTypeDefault: "资产及漏洞扫描",
	CronTaskTypeAsset:   "资产扫描",
	CronTaskTypePoc:     "漏洞扫描",
}

// CronScanType 扫描模式映射
var CronScanType = map[int]string{
	CronScanTypeAccurate: "common",
	CronScanTypeSpeed:    "quick",
}

// Poc POC结构体，用于GetAllPoc方法返回
type Poc struct {
	ID          uint64 `gorm:"column:id" json:"id"`
	Name        string `gorm:"column:name" json:"name"`
	Description string `gorm:"column:description" json:"description"`
}

// Port 端口结构体，用于GetAllPort方法返回
type Port struct {
	ID       uint64 `gorm:"column:id" json:"id"`
	Port     int    `gorm:"column:port" json:"port"`
	Protocol string `gorm:"column:protocol" json:"protocol"`
}

// ScanCrontabTask 周期任务-扫描任务信息表
type ScanCrontabTask struct {
	ID                  uint64         `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserId              uint64         `gorm:"column:user_id;type:bigint(20) unsigned;comment:用户id;NOT NULL" json:"user_id"`
	CompanyId           uint64         `gorm:"column:company_id;type:bigint(20) unsigned;comment:企业ID" json:"company_id"`
	Name                string         `gorm:"column:name;type:varchar(255);comment:任务名称;NOT NULL" json:"name"`
	Bandwidth           string         `gorm:"column:bandwidth;type:varchar(12);comment:扫描带宽;NOT NULL" json:"bandwidth"`
	PocScanType         int            `gorm:"column:poc_scan_type;type:tinyint(4);default:1;comment:poc扫描的时候，poc范围 0全部poc  1指定poc;NOT NULL" json:"poc_scan_type"`
	ProtocolConcurrency string         `gorm:"column:protocol_concurrency;type:varchar(16);default:2;comment:并发数;NOT NULL" json:"protocol_concurrency"`
	TaskType            int            `gorm:"column:task_type;type:tinyint(4);default:0;comment:任务分类 默认1 1 资产扫描 2 漏洞扫描;NOT NULL" json:"task_type"`
	IpType              int            `gorm:"column:ip_type;type:tinyint(4);default:1;comment:IP类型: 1.IPv4 2.IPv6;NOT NULL" json:"ip_type"`
	ScanRange           int            `gorm:"column:scan_range;type:tinyint(4);default:0;comment:扫描范围,0手动输入的ip 1上传的ip 2已经认证的资产ip 3根据ip段筛选,给前端回显用的字段 4 全部poc 5 指定poc;NOT NULL" json:"scan_range"`
	PingSwitch          int            `gorm:"column:ping_switch;type:tinyint(4);default:0;comment:开启Ping识别资产 0不开启 1开启;NOT NULL" json:"ping_switch"`
	WebLogoSwitch       int            `gorm:"column:web_logo_switch;type:tinyint(4);default:0;comment:网站首页截图是否开启 0不开启 1开启;NOT NULL" json:"web_logo_switch"`
	Type                int            `gorm:"column:type;type:tinyint(4);default:6;comment:扫描周期  3月 4周 5天 6一次;NOT NULL" json:"type"`
	ScheduleTime        string         `gorm:"column:schedule_time;type:varchar(255);comment:周期任务，任务开始时间" json:"schedule_time"`
	DayOfX              string         `gorm:"column:day_of_x;type:varchar(255);comment:周期任务，值如果为0开头，代表周，非0的代表日 28代表28日 02代表周二" json:"day_of_x"`
	ScanType            int            `gorm:"column:scan_type;type:tinyint(4);default:1;comment:扫描模式 0为精准 1为极速;NOT NULL" json:"scan_type"`
	Switch              int            `gorm:"column:switch;type:tinyint(4);default:0;comment:是否开启周期任务 0开启 1关闭;NOT NULL" json:"switch"`
	CreatedAt           time.Time      `gorm:"column:created_at;type:timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt           time.Time      `gorm:"column:updated_at;type:timestamp;comment:更新时间" json:"updated_at"`
	DeletedAt           gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp;comment:删除时间" json:"deleted_at"`
	FileName            string         `gorm:"column:file_name;type:varchar(250);default:'';comment:上传ip扫描的时候，记录的文件名" json:"file_name"`
	TaskFrom            int            `gorm:"column:task_from;type:tinyint(4);default:0;comment:0/用户自己下发的扫描任务 1/安服下发的扫描任务;NOT NULL" json:"task_from"`
	OpId                int64          `gorm:"column:op_id;type:bigint(20);default:0;comment:操作人" json:"op_id"`
	IsAudit             int            `gorm:"column:is_audit;type:tinyint(4);default:0;comment:0/1/2 待审核/审核通过/审核失败" json:"is_audit"`
	Reason              string         `gorm:"column:reason;type:varchar(255);default:'';comment:审核驳回原因" json:"reason"`
	IsDefinePort        int            `gorm:"column:is_define_port;type:tinyint(4);default:0;comment:是否是自定义端口资产扫描0/1 否/是" json:"is_define_port"`
	ScanEngine          string         `gorm:"column:scan_engine;type:varchar(20);default:'';comment:漏洞扫描的扫描引擎 空值/goscanner  1/天融信扫描器 2/goscanner和天融信都选择了" json:"scan_engine"`
	EngineRole          int            `gorm:"column:engine_role;type:tinyint(4);default:0;comment:用到的扫描器  0/只有goscanner 1/只有天融信 2/都有" json:"engine_role"`
	TableAssetsType     int            `gorm:"column:table_assets_type;type:tinyint(4);default:0;comment:周期任务设置扫描台账ip的时候，下次执行的时候是 实时获取台账ip/1  下发的时候的台账ip/0" json:"table_assets_type"`
}

// TableName 表名
func (m *ScanCrontabTask) TableName() string {
	return TableName
}

// Model 周期任务模型接口
type Model interface {
	// 创建记录
	Create(data *ScanCrontabTask) error
	// 更新记录
	Update(data *ScanCrontabTask) error
	// 根据ID查询
	First(opts ...mysql.HandleFunc) (*ScanCrontabTask, error)
	// 查询多条记录
	Find(opts ...mysql.HandleFunc) ([]*ScanCrontabTask, error)
	// 统计记录数
	Count(opts ...mysql.HandleFunc) (int64, error)
	// 分页查询
	Page(page, size int, opts ...mysql.HandleFunc) ([]*ScanCrontabTask, int64, error)
	// 获取所有POC包含分组
	GetAllPoc(taskID uint64) ([]interface{}, error)
	// 获取所有PORT包含分组
	GetAllPort(taskID uint64) ([]interface{}, error)
	// 判断是否到了周期任务的时间
	IsCanInstanceScheduleTask(task *ScanCrontabTask) bool
	// 搜索
	Search(params map[string]interface{}) (query *gorm.DB)
	// 删除记录
	Delete(opts ...mysql.HandleFunc) error
}

// 默认模型实现
type defaultModel struct {
	*gorm.DB
	table string
}

// NewModel 创建模型实例
func NewModel(conn ...*gorm.DB) Model {
	return &defaultModel{
		DB:    mysql.GetDbClient(conn...),
		table: TableName,
	}
}

// Create 创建记录
func (m *defaultModel) Create(data *ScanCrontabTask) error {
	return m.DB.Create(data).Error
}

// Update 更新记录
func (m *defaultModel) Update(data *ScanCrontabTask) error {
	return m.DB.Save(data).Error
}

// First 根据条件查询单条记录
func (m *defaultModel) First(opts ...mysql.HandleFunc) (*ScanCrontabTask, error) {
	var result ScanCrontabTask
	query := m.DB.Table(m.table)
	for _, f := range opts {
		f(query)
	}
	err := query.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// Find 查询多条记录
func (m *defaultModel) Find(opts ...mysql.HandleFunc) ([]*ScanCrontabTask, error) {
	var result []*ScanCrontabTask
	query := m.DB.Table(m.table)
	for _, f := range opts {
		f(query)
	}
	err := query.Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, nil
}

// Count 统计记录数
func (m *defaultModel) Count(opts ...mysql.HandleFunc) (int64, error) {
	var count int64
	query := m.DB.Table(m.table)
	for _, f := range opts {
		f(query)
	}
	err := query.Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// Page 分页查询
func (m *defaultModel) Page(page, size int, opts ...mysql.HandleFunc) ([]*ScanCrontabTask, int64, error) {
	var result []*ScanCrontabTask
	var count int64

	query := m.DB.Table(m.table)
	for _, f := range opts {
		f(query)
	}

	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	if page > 0 && size > 0 {
		offset := (page - 1) * size
		query = query.Offset(offset).Limit(size)
	}

	err = query.Find(&result).Error
	if err != nil {
		return nil, 0, err
	}

	return result, count, nil
}

// GetAllPoc 获取所有POC包含分组
func (m *defaultModel) GetAllPoc(taskID uint64) ([]interface{}, error) {
	// 这里需要根据具体的表结构和关联关系实现
	// 由于PHP版本使用了Laravel的ORM关系，Go版本需要手动实现
	log.Infof("GetAllPoc: 获取任务ID %d 的所有POC", taskID)

	// 查询直接关联的POC
	var directPocs []Poc
	err := m.DB.Table("cron_task_pocs").
		Select("scan_pocs.*").
		Joins("JOIN scan_pocs ON cron_task_pocs.cronpocs_id = scan_pocs.id").
		Where("cron_task_pocs.cron_task_id = ? AND cron_task_pocs.cronpocs_type = ?", taskID, "App\\Models\\MySql\\Poc").
		Find(&directPocs).Error
	if err != nil {
		return nil, err
	}

	// 查询通过分组关联的POC
	var groupPocs []Poc
	err = m.DB.Table("cron_task_pocs").
		Select("scan_pocs.*").
		Joins("JOIN poc_groups ON cron_task_pocs.cronpocs_id = poc_groups.id").
		Joins("JOIN poc_poc_group ON poc_groups.id = poc_poc_group.poc_group_id").
		Joins("JOIN scan_pocs ON poc_poc_group.poc_id = scan_pocs.id").
		Where("cron_task_pocs.cron_task_id = ? AND cron_task_pocs.cronpocs_type = ?", taskID, "App\\Models\\MySql\\PocGroup").
		Find(&groupPocs).Error
	if err != nil {
		return nil, err
	}

	// 合并结果并转换为[]interface{}
	var result []interface{}
	for _, poc := range directPocs {
		result = append(result, poc)
	}
	for _, poc := range groupPocs {
		result = append(result, poc)
	}
	return result, nil
}

// GetAllPort 获取所有PORT包含分组
func (m *defaultModel) GetAllPort(taskID uint64) ([]interface{}, error) {
	// 这里需要根据具体的表结构和关联关系实现
	// 由于PHP版本使用了Laravel的ORM关系，Go版本需要手动实现
	log.Infof("GetAllPort: 获取任务ID %d 的所有PORT", taskID)

	// 查询直接关联的PORT
	var directPorts []Port
	err := m.DB.Table("cron_task_ports").
		Select("ports.*").
		Joins("JOIN ports ON cron_task_ports.cronports_id = ports.id").
		Where("cron_task_ports.cron_task_id = ? AND cron_task_ports.cronports_type = ?", taskID, "App\\Models\\MySql\\Port").
		Find(&directPorts).Error
	if err != nil {
		return nil, err
	}

	// 查询通过分组关联的PORT
	var groupPorts []Port
	err = m.DB.Table("cron_task_ports").
		Select("ports.*").
		Joins("JOIN port_port_group ON cron_task_ports.cronports_id = port_port_group.port_group_id").
		Joins("JOIN ports ON port_port_group.port_id = ports.id").
		Where("cron_task_ports.cron_task_id = ? AND cron_task_ports.cronports_type = ?", taskID, "App\\Models\\MySql\\PortGroup").
		Find(&groupPorts).Error
	if err != nil {
		return nil, err
	}

	// 合并结果并转换为[]interface{}
	var result []interface{}
	for _, port := range directPorts {
		result = append(result, port)
	}
	for _, port := range groupPorts {
		result = append(result, port)
	}
	return result, nil
}

// IsCanInstanceScheduleTask 判断是否到了周期任务的时间
func (m *defaultModel) IsCanInstanceScheduleTask(task *ScanCrontabTask) bool {
	now := time.Now()

	switch task.Type {
	case CronTypeScheduleMonth:
		// 判断是否是当月的指定日期
		if now.Format("02") != task.DayOfX {
			return false
		}
		// 判断是否是指定时间
		if now.Format("15:04") != task.ScheduleTime {
			return false
		}
		return true

	case CronTypeScheduleWeek:
		// 获取当前是周几
		weekday := int(now.Weekday())
		// 周日的时候，weekday的值是0，周一的时候weekday的值是1
		// 但是前端页面保存的时候，数据库里面周一存的是1，周二存的是2，以此类推。周日存的是0（周日特殊）
		// 所以这里不需要转换

		// 转换为字符串进行比较
		dayOfXInt := task.DayOfX
		// 如果dayOfX是以0开头的，需要去掉前导0
		if strings.HasPrefix(dayOfXInt, "0") {
			dayOfXInt = dayOfXInt[1:]
		}

		// 比较周几
		if strconv.Itoa(weekday) != dayOfXInt {
			return false
		}

		// 判断是否是指定时间
		if now.Format("15:04") != task.ScheduleTime {
			return false
		}
		return true

	case CronTypeScheduleDay:
		// 判断是否是指定时间
		if now.Format("15:04") != task.ScheduleTime {
			return false
		}
		return true

	case CronTypeScheduleOnce:
		// 判断是否是指定日期和时间
		if (now.Format("2006-01-02") != task.DayOfX) || (now.Format("15:04") != task.ScheduleTime) {
			return false
		}
		return true

	default:
		return false
	}
}

// Search 搜索
func (m *defaultModel) Search(params map[string]interface{}) *gorm.DB {
	query := m.DB.Table(m.table)

	// 用户ID
	if userID, exists := params["user_id"]; exists && userID != nil {
		query = query.Where("user_id = ?", userID)
	}

	// 任务类型
	if taskType, exists := params["task_type"]; exists && taskType != nil {
		query = query.Where("task_type = ?", taskType)
	}

	// 操作人ID
	if opID, exists := params["op_id"]; exists && opID != nil {
		query = query.Where("op_id = ?", opID)
	}

	// 创建时间范围
	if createdAtRange, exists := params["created_at_range"].([]string); exists && len(createdAtRange) == 2 {
		// 将结束日期设置为当天的结束时间
		endDate, _ := time.Parse("2006-01-02", createdAtRange[1])
		endDate = endDate.Add(24*time.Hour - time.Second)

		query = query.Where("created_at BETWEEN ? AND ?", createdAtRange[0], endDate.Format("2006-01-02 15:04:05"))
	}

	// 名称模糊查询
	if name, exists := params["name"].(string); exists && name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	// 排序
	sortField, hasSortField := params["sort_field"].(string)
	sortOrder, hasSortOrder := params["sort_order"].(string)

	if hasSortField && hasSortOrder && sortField != "" && sortOrder != "" {
		query = query.Order(sortField + " " + sortOrder)
	} else {
		query = query.Order("updated_at DESC")
	}

	return query
}

// WithID 根据ID查询
func WithID(id uint64) mysql.HandleFunc {
	return func(db *gorm.DB) {
		db.Where("id = ?", id)
	}
}

// WithUserID 根据用户ID查询
func WithUserID(userID uint64) mysql.HandleFunc {
	return func(db *gorm.DB) {
		db.Where("user_id = ?", userID)
	}
}

// WithCompanyID 根据企业ID查询
func WithCompanyID(companyID uint64) mysql.HandleFunc {
	return func(db *gorm.DB) {
		db.Where("company_id = ?", companyID)
	}
}

// WithTaskType 根据任务类型查询
func WithTaskType(taskType int) mysql.HandleFunc {
	return func(db *gorm.DB) {
		db.Where("task_type = ?", taskType)
	}
}

// WithType 根据周期类型查询
func WithType(taskType int) mysql.HandleFunc {
	return func(db *gorm.DB) {
		db.Where("type = ?", taskType)
	}
}

// WithSwitch 根据开关状态查询
func WithSwitch(switchStatus int) mysql.HandleFunc {
	return func(db *gorm.DB) {
		db.Where("switch = ?", switchStatus)
	}
}

// Delete 删除记录
func (m *defaultModel) Delete(opts ...mysql.HandleFunc) error {
	query := m.DB.Table(m.table)
	for _, f := range opts {
		f(query)
	}
	// 使用Unscoped()方法绕过GORM的软删除机制，实现硬删除
	return query.Unscoped().Delete(&ScanCrontabTask{}).Error
}

// WithIDs 根据ID列表查询
func WithIDs(ids []uint64) mysql.HandleFunc {
	return func(db *gorm.DB) {
		db.Where("id IN ?", ids)
	}
}
