package scan_crontab_tasks

import (
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

func init() {
	cfg.InitLoadCfg()
	// 设置测试环境标志
	initmysql.SetTestEnv(true)

	// 初始化数据库连接
	initmysql.GetInstance(cfg.LoadMysql())
}

// setupTestModel 创建测试用的模型实例
func setupTestModel(t *testing.T) (Model, sqlmock.Sqlmock) {
	// 初始化日志（如果还没有初始化）
	if !isLoggerInitialized() {
		cfg.InitLoadCfg()
		log.Init()
	}

	// 为每个测试创建新的Mock实例
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to create mock: %v", err)
	}

	// 创建GORM实例
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to create gorm instance: %v", err)
	}

	model := &defaultModel{
		DB:    gormDB,
		table: TableName,
	}
	return model, mock
}

// isLoggerInitialized 检查日志是否已经初始化
func isLoggerInitialized() bool {
	// 简单检查，如果日志包的GetLogger返回非nil，则认为已初始化
	return log.GetLogger() != nil
}

func TestScanCrontabTask_TableName(t *testing.T) {
	task := &ScanCrontabTask{}
	assert.Equal(t, TableName, task.TableName())
}

func TestNewModel(t *testing.T) {
	model := NewModel()
	assert.NotNil(t, model)
}

func TestDefaultModel_Create(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		task := &ScanCrontabTask{
			ID:                  1,
			UserId:              123,
			CompanyId:           456,
			Name:                "测试周期任务",
			Bandwidth:           "100M",
			PocScanType:         1,
			ProtocolConcurrency: "2",
			TaskType:            CronTaskTypeDefault,
			IpType:              1,
			ScanRange:           CronInputIP,
			PingSwitch:          0,
			WebLogoSwitch:       0,
			Type:                CronTypeScheduleOnce,
			ScheduleTime:        "10:00",
			DayOfX:              "2024-01-01",
			ScanType:            CronScanTypeAccurate,
			Switch:              0,
			CreatedAt:           time.Now(),
			UpdatedAt:           time.Now(),
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `scan_crontab_tasks`").
			WithArgs(utils.SqlMockArgs(29)...).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		err := model.Create(task)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		model, mock := setupTestModel(t)

		task := &ScanCrontabTask{
			ID:     1,
			UserId: 123,
			Name:   "测试任务",
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `scan_crontab_tasks`").
			WithArgs(utils.SqlMockArgs(29)...).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Create(task)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultModel_Update(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		task := &ScanCrontabTask{
			ID:     1,
			UserId: 123,
			Name:   "更新任务",
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `scan_crontab_tasks`").
			WithArgs(utils.SqlMockArgs(29)...).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		err := model.Update(task)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		model, mock := setupTestModel(t)

		task := &ScanCrontabTask{
			ID:     1,
			UserId: 123,
			Name:   "更新任务",
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `scan_crontab_tasks`").
			WithArgs(utils.SqlMockArgs(29)...).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Update(task)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultModel_First(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		expectedTask := &ScanCrontabTask{
			ID:        1,
			UserId:    123,
			CompanyId: 456,
			Name:      "测试任务",
			TaskType:  CronTaskTypeDefault,
		}

		rows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
			"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
			"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
			"switch", "created_at", "updated_at", "deleted_at", "file_name",
			"task_from", "op_id", "is_audit", "reason", "is_define_port",
			"scan_engine", "engine_role", "table_assets_type",
		}).AddRow(
			expectedTask.ID, expectedTask.UserId, expectedTask.CompanyId,
			expectedTask.Name, "100M", 1, "2", expectedTask.TaskType, 1, 0, 0, 0,
			CronTypeScheduleOnce, "10:00", "2024-01-01", 0, 0,
			time.Now(), time.Now(), nil, "", 0, 0, 0, "", 0, "", 0, 0,
		)

		mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks`").
			WillReturnRows(rows)

		result, err := model.First()
		assert.NoError(t, err)
		assert.Equal(t, expectedTask.ID, result.ID)
		assert.Equal(t, expectedTask.Name, result.Name)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		model, mock := setupTestModel(t)

		mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks`").
			WillReturnError(sql.ErrNoRows)

		_, err := model.First()
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultModel_Find(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		expectedTasks := []*ScanCrontabTask{
			{ID: 1, UserId: 123, Name: "任务1", TaskType: CronTaskTypeAsset},
			{ID: 2, UserId: 123, Name: "任务2", TaskType: CronTaskTypePoc},
		}

		rows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
			"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
			"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
			"switch", "created_at", "updated_at", "deleted_at", "file_name",
			"task_from", "op_id", "is_audit", "reason", "is_define_port",
			"scan_engine", "engine_role", "table_assets_type",
		})
		for _, task := range expectedTasks {
			rows.AddRow(
				task.ID, task.UserId, task.CompanyId, task.Name, "100M", 1, "2",
				task.TaskType, 1, 0, 0, 0, CronTypeScheduleOnce, "10:00", "2024-01-01",
				0, 0, time.Now(), time.Now(), nil, "", 0, 0, 0, "", 0, "", 0, 0,
			)
		}

		mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks`").
			WillReturnRows(rows)

		result, err := model.Find()
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, expectedTasks[0].Name, result[0].Name)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks`").
			WillReturnError(sql.ErrConnDone)

		_, err := model.Find()
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultModel_Count(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		expectedCount := int64(5)
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(expectedCount)
		mock.ExpectQuery("SELECT count(.+) FROM `scan_crontab_tasks`").
			WillReturnRows(countRows)

		count, err := model.Count()
		assert.NoError(t, err)
		assert.Equal(t, expectedCount, count)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		mock.ExpectQuery("SELECT count(.+) FROM `scan_crontab_tasks`").
			WillReturnError(sql.ErrConnDone)

		_, err := model.Count()
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultModel_Page(t *testing.T) {
	t.Run("success_with_pagination", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		expectedTasks := []*ScanCrontabTask{
			{ID: 1, UserId: 123, Name: "任务1"},
			{ID: 2, UserId: 123, Name: "任务2"},
		}

		// 计数查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(2)
		mock.ExpectQuery("SELECT count(.+) FROM `scan_crontab_tasks`").
			WillReturnRows(countRows)

		// 数据查询
		dataRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
			"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
			"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
			"switch", "created_at", "updated_at", "deleted_at", "file_name",
			"task_from", "op_id", "is_audit", "reason", "is_define_port",
			"scan_engine", "engine_role", "table_assets_type",
		})
		for _, task := range expectedTasks {
			dataRows.AddRow(
				task.ID, task.UserId, task.CompanyId, task.Name, "100M", 1, "2",
				CronTaskTypeDefault, 1, 0, 0, 0, CronTypeScheduleOnce, "10:00",
				"2024-01-01", 0, 0, time.Now(), time.Now(), nil, "", 0, 0, 0, "", 0, "", 0, 0,
			)
		}

		mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks`").
			WillReturnRows(dataRows)

		result, total, err := model.Page(1, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, result, 2)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_without_pagination", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		// 计数查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(1)
		mock.ExpectQuery("SELECT count(.+) FROM `scan_crontab_tasks`").
			WillReturnRows(countRows)

		// 数据查询（不分页）
		dataRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
			"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
			"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
			"switch", "created_at", "updated_at", "deleted_at", "file_name",
			"task_from", "op_id", "is_audit", "reason", "is_define_port",
			"scan_engine", "engine_role", "table_assets_type",
		}).AddRow(1, 123, 456, "任务1", "100M", 1, "2", CronTaskTypeDefault, 1, 0, 0, 0,
			CronTypeScheduleOnce, "10:00", "2024-01-01", 0, 0, time.Now(), time.Now(),
			nil, "", 0, 0, 0, "", 0, "", 0, 0)

		mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks`").
			WillReturnRows(dataRows)

		result, total, err := model.Page(0, 0) // 不分页
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, result, 1)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure_count", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		mock.ExpectQuery("SELECT count(.+) FROM `scan_crontab_tasks`").
			WillReturnError(sql.ErrConnDone)

		_, _, err := model.Page(1, 10)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure_find", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		// 计数查询成功
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(1)
		mock.ExpectQuery("SELECT count(.+) FROM `scan_crontab_tasks`").
			WillReturnRows(countRows)

		// 数据查询失败
		mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks`").
			WillReturnError(sql.ErrConnDone)

		_, _, err := model.Page(1, 10)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultModel_Delete(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `scan_crontab_tasks` WHERE id = (.+)").
			WithArgs(uint64(1)).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err := model.Delete(WithID(1))
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		model, mock := setupTestModel(t)

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `scan_crontab_tasks` WHERE id = (.+)").
			WithArgs(uint64(1)).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Delete(WithID(1))
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

// 测试查询条件函数
func TestWithID(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	id := uint64(1)

	rows := sqlmock.NewRows([]string{
		"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
		"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
		"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
		"switch", "created_at", "updated_at", "deleted_at", "file_name",
		"task_from", "op_id", "is_audit", "reason", "is_define_port",
		"scan_engine", "engine_role", "table_assets_type",
	}).AddRow(id, 123, 456, "ID任务", "100M", 1, "2", CronTaskTypeDefault, 1, 0, 0, 0,
		CronTypeScheduleOnce, "10:00", "2024-01-01", 0, 0, time.Now(), time.Now(),
		nil, "", 0, 0, 0, "", 0, "", 0, 0)

	mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks` WHERE id = (.+)").
		WithArgs(id).
		WillReturnRows(rows)

	result, err := model.Find(WithID(id))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, id, result[0].ID)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithUserID(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	userID := uint64(123)

	rows := sqlmock.NewRows([]string{
		"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
		"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
		"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
		"switch", "created_at", "updated_at", "deleted_at", "file_name",
		"task_from", "op_id", "is_audit", "reason", "is_define_port",
		"scan_engine", "engine_role", "table_assets_type",
	}).AddRow(1, userID, 456, "用户任务", "100M", 1, "2", CronTaskTypeDefault, 1, 0, 0, 0,
		CronTypeScheduleOnce, "10:00", "2024-01-01", 0, 0, time.Now(), time.Now(),
		nil, "", 0, 0, 0, "", 0, "", 0, 0)

	mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks` WHERE user_id = (.+)").
		WithArgs(userID).
		WillReturnRows(rows)

	result, err := model.Find(WithUserID(userID))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, userID, result[0].UserId)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithCompanyID(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	companyID := uint64(456)

	rows := sqlmock.NewRows([]string{
		"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
		"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
		"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
		"switch", "created_at", "updated_at", "deleted_at", "file_name",
		"task_from", "op_id", "is_audit", "reason", "is_define_port",
		"scan_engine", "engine_role", "table_assets_type",
	}).AddRow(1, 123, companyID, "公司任务", "100M", 1, "2", CronTaskTypeDefault, 1, 0, 0, 0,
		CronTypeScheduleOnce, "10:00", "2024-01-01", 0, 0, time.Now(), time.Now(),
		nil, "", 0, 0, 0, "", 0, "", 0, 0)

	mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks` WHERE company_id = (.+)").
		WithArgs(companyID).
		WillReturnRows(rows)

	result, err := model.Find(WithCompanyID(companyID))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, companyID, result[0].CompanyId)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithTaskType(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	taskType := CronTaskTypeAsset

	rows := sqlmock.NewRows([]string{
		"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
		"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
		"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
		"switch", "created_at", "updated_at", "deleted_at", "file_name",
		"task_from", "op_id", "is_audit", "reason", "is_define_port",
		"scan_engine", "engine_role", "table_assets_type",
	}).AddRow(1, 123, 456, "资产任务", "100M", 1, "2", taskType, 1, 0, 0, 0,
		CronTypeScheduleOnce, "10:00", "2024-01-01", 0, 0, time.Now(), time.Now(),
		nil, "", 0, 0, 0, "", 0, "", 0, 0)

	mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks` WHERE task_type = (.+)").
		WithArgs(taskType).
		WillReturnRows(rows)

	result, err := model.Find(WithTaskType(taskType))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, taskType, result[0].TaskType)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithType(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	cronType := CronTypeScheduleMonth

	rows := sqlmock.NewRows([]string{
		"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
		"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
		"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
		"switch", "created_at", "updated_at", "deleted_at", "file_name",
		"task_from", "op_id", "is_audit", "reason", "is_define_port",
		"scan_engine", "engine_role", "table_assets_type",
	}).AddRow(1, 123, 456, "月度任务", "100M", 1, "2", CronTaskTypeDefault, 1, 0, 0, 0,
		cronType, "10:00", "15", 0, 0, time.Now(), time.Now(),
		nil, "", 0, 0, 0, "", 0, "", 0, 0)

	mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks` WHERE type = (.+)").
		WithArgs(cronType).
		WillReturnRows(rows)

	result, err := model.Find(WithType(cronType))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, cronType, result[0].Type)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithSwitch(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	switchStatus := 1

	rows := sqlmock.NewRows([]string{
		"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
		"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
		"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
		"switch", "created_at", "updated_at", "deleted_at", "file_name",
		"task_from", "op_id", "is_audit", "reason", "is_define_port",
		"scan_engine", "engine_role", "table_assets_type",
	}).AddRow(1, 123, 456, "关闭任务", "100M", 1, "2", CronTaskTypeDefault, 1, 0, 0, 0,
		CronTypeScheduleOnce, "10:00", "2024-01-01", 0, switchStatus, time.Now(), time.Now(),
		nil, "", 0, 0, 0, "", 0, "", 0, 0)

	mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks` WHERE switch = (.+)").
		WithArgs(switchStatus).
		WillReturnRows(rows)

	result, err := model.Find(WithSwitch(switchStatus))
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, switchStatus, result[0].Switch)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithIDs(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	ids := []uint64{1, 2}

	rows := sqlmock.NewRows([]string{
		"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
		"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
		"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
		"switch", "created_at", "updated_at", "deleted_at", "file_name",
		"task_from", "op_id", "is_audit", "reason", "is_define_port",
		"scan_engine", "engine_role", "table_assets_type",
	}).AddRow(ids[0], 123, 456, "任务1", "100M", 1, "2", CronTaskTypeDefault, 1, 0, 0, 0,
		CronTypeScheduleOnce, "10:00", "2024-01-01", 0, 0, time.Now(), time.Now(),
		nil, "", 0, 0, 0, "", 0, "", 0, 0).
		AddRow(ids[1], 123, 456, "任务2", "100M", 1, "2", CronTaskTypeDefault, 1, 0, 0, 0,
			CronTypeScheduleOnce, "10:00", "2024-01-01", 0, 0, time.Now(), time.Now(),
			nil, "", 0, 0, 0, "", 0, "", 0, 0)

	mock.ExpectQuery("SELECT (.+) FROM `scan_crontab_tasks` WHERE id IN (.+)").
		WithArgs(ids[0], ids[1]).
		WillReturnRows(rows)

	result, err := model.Find(WithIDs(ids))
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.NoError(t, mock.ExpectationsWereMet())
}

// 测试常量
func TestConstants(t *testing.T) {
	// 测试表名
	assert.Equal(t, "scan_crontab_tasks", TableName)

	// 测试扫描范围常量
	assert.Equal(t, 0, CronInputIP)
	assert.Equal(t, 1, CronFileIP)
	assert.Equal(t, 2, CronSureIP)
	assert.Equal(t, 3, CronFilterRangeIP)

	// 测试任务状态常量
	assert.Equal(t, 0, CronStatusDefault)
	assert.Equal(t, 1, CronStatusRunning)
	assert.Equal(t, 2, CronStatusFinished)
	assert.Equal(t, 3, CronStatusFailed)
	assert.Equal(t, 4, CronStatusPause)

	// 测试任务步骤常量
	assert.Equal(t, 0, CronStepScanning)
	assert.Equal(t, 1, CronStepSyncData)
	assert.Equal(t, 2, CronStepTagging)
	assert.Equal(t, 3, CronStepLoophole)
	assert.Equal(t, 4, CronStepFinished)

	// 测试周期任务标志
	assert.Equal(t, 1, CronIsLopTask)

	// 测试任务类型常量
	assert.Equal(t, 0, CronTypeDefault)
	assert.Equal(t, 1, CronTypeCompany)
	assert.Equal(t, 2, CronTypeScheduleInstance)
	assert.Equal(t, 3, CronTypeScheduleMonth)
	assert.Equal(t, 4, CronTypeScheduleWeek)
	assert.Equal(t, 5, CronTypeScheduleDay)
	assert.Equal(t, 6, CronTypeScheduleOnce)

	// 测试任务分类常量
	assert.Equal(t, 0, CronTaskTypeDefault)
	assert.Equal(t, 1, CronTaskTypeAsset)
	assert.Equal(t, 2, CronTaskTypePoc)

	// 测试扫描模式常量
	assert.Equal(t, 0, CronScanTypeAccurate)
	assert.Equal(t, 1, CronScanTypeSpeed)

	// 测试审核状态常量
	assert.Equal(t, 1, AuditPass)
	assert.Equal(t, 2, AuditReject)
}

func TestCronTaskTypeMap(t *testing.T) {
	assert.Equal(t, "资产及漏洞扫描", CronTaskTypeMap[CronTaskTypeDefault])
	assert.Equal(t, "资产扫描", CronTaskTypeMap[CronTaskTypeAsset])
	assert.Equal(t, "漏洞扫描", CronTaskTypeMap[CronTaskTypePoc])
}

func TestCronScanType(t *testing.T) {
	assert.Equal(t, "common", CronScanType[CronScanTypeAccurate])
	assert.Equal(t, "quick", CronScanType[CronScanTypeSpeed])
}

// 测试IsCanInstanceScheduleTask方法
func TestDefaultModel_IsCanInstanceScheduleTask(t *testing.T) {
	model := NewModel()

	t.Run("monthly_task_correct_time", func(t *testing.T) {
		// 假设当前时间是2024年1月15日10:00
		task := &ScanCrontabTask{
			Type:         CronTypeScheduleMonth,
			DayOfX:       "15", // 每月15日
			ScheduleTime: "10:00",
		}

		// 由于时间判断依赖当前时间，这里只测试逻辑结构
		result := model.IsCanInstanceScheduleTask(task)
		// 结果取决于当前实际时间，这里主要测试方法不会panic
		assert.IsType(t, false, result)
	})

	t.Run("weekly_task", func(t *testing.T) {
		task := &ScanCrontabTask{
			Type:         CronTypeScheduleWeek,
			DayOfX:       "1", // 周一
			ScheduleTime: "10:00",
		}

		result := model.IsCanInstanceScheduleTask(task)
		assert.IsType(t, false, result)
	})

	t.Run("daily_task", func(t *testing.T) {
		task := &ScanCrontabTask{
			Type:         CronTypeScheduleDay,
			ScheduleTime: "10:00",
		}

		result := model.IsCanInstanceScheduleTask(task)
		assert.IsType(t, false, result)
	})

	t.Run("once_task", func(t *testing.T) {
		task := &ScanCrontabTask{
			Type:         CronTypeScheduleOnce,
			DayOfX:       "2024-01-15",
			ScheduleTime: "10:00",
		}

		result := model.IsCanInstanceScheduleTask(task)
		assert.IsType(t, false, result)
	})

	t.Run("unknown_type", func(t *testing.T) {
		task := &ScanCrontabTask{
			Type:         999, // 未知类型
			ScheduleTime: "10:00",
		}

		result := model.IsCanInstanceScheduleTask(task)
		assert.False(t, result)
	})
}

// 测试Search方法
func TestDefaultModel_Search(t *testing.T) {
	model := NewModel()

	t.Run("search_with_user_id", func(t *testing.T) {
		params := map[string]interface{}{
			"user_id": uint64(123),
		}

		query := model.Search(params)
		assert.NotNil(t, query)
	})

	t.Run("search_with_task_type", func(t *testing.T) {
		params := map[string]interface{}{
			"task_type": CronTaskTypeAsset,
		}

		query := model.Search(params)
		assert.NotNil(t, query)
	})

	t.Run("search_with_op_id", func(t *testing.T) {
		params := map[string]interface{}{
			"op_id": int64(456),
		}

		query := model.Search(params)
		assert.NotNil(t, query)
	})

	t.Run("search_with_created_at_range", func(t *testing.T) {
		params := map[string]interface{}{
			"created_at_range": []string{"2024-01-01", "2024-01-31"},
		}

		query := model.Search(params)
		assert.NotNil(t, query)
	})

	t.Run("search_with_name", func(t *testing.T) {
		params := map[string]interface{}{
			"name": "测试任务",
		}

		query := model.Search(params)
		assert.NotNil(t, query)
	})

	t.Run("search_with_sort", func(t *testing.T) {
		params := map[string]interface{}{
			"sort_field": "created_at",
			"sort_order": "ASC",
		}

		query := model.Search(params)
		assert.NotNil(t, query)
	})

	t.Run("search_empty_params", func(t *testing.T) {
		params := map[string]interface{}{}

		query := model.Search(params)
		assert.NotNil(t, query)
	})
}

// 测试GetAllPoc方法
func TestDefaultModel_GetAllPoc(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		taskID := uint64(123)

		// Mock直接关联的POC查询
		directPocRows := sqlmock.NewRows([]string{"id", "name", "description"}).
			AddRow(1, "POC1", "直接关联POC1").
			AddRow(2, "POC2", "直接关联POC2")

		mock.ExpectQuery("SELECT scan_pocs.\\* FROM `cron_task_pocs` JOIN scan_pocs ON cron_task_pocs.cronpocs_id = scan_pocs.id WHERE cron_task_pocs.cron_task_id = \\? AND cron_task_pocs.cronpocs_type = \\?").
			WithArgs(taskID, "App\\Models\\MySql\\Poc").
			WillReturnRows(directPocRows)

		// Mock通过分组关联的POC查询
		groupPocRows := sqlmock.NewRows([]string{"id", "name", "description"}).
			AddRow(3, "POC3", "分组关联POC3")

		mock.ExpectQuery("SELECT scan_pocs.\\* FROM `cron_task_pocs` JOIN poc_groups ON cron_task_pocs.cronpocs_id = poc_groups.id JOIN poc_poc_group ON poc_groups.id = poc_poc_group.poc_group_id JOIN scan_pocs ON poc_poc_group.poc_id = scan_pocs.id WHERE cron_task_pocs.cron_task_id = \\? AND cron_task_pocs.cronpocs_type = \\?").
			WithArgs(taskID, "App\\Models\\MySql\\PocGroup").
			WillReturnRows(groupPocRows)

		result, err := model.GetAllPoc(taskID)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure_direct_poc", func(t *testing.T) {
		model, mock := setupTestModel(t)

		taskID := uint64(123)

		mock.ExpectQuery("SELECT scan_pocs.\\* FROM `cron_task_pocs` JOIN scan_pocs ON cron_task_pocs.cronpocs_id = scan_pocs.id WHERE cron_task_pocs.cron_task_id = \\? AND cron_task_pocs.cronpocs_type = \\?").
			WithArgs(taskID, "App\\Models\\MySql\\Poc").
			WillReturnError(sql.ErrConnDone)

		result, err := model.GetAllPoc(taskID)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure_group_poc", func(t *testing.T) {
		model, mock := setupTestModel(t)

		taskID := uint64(123)

		// 直接POC查询成功
		directPocRows := sqlmock.NewRows([]string{"id", "name", "description"})
		mock.ExpectQuery("SELECT scan_pocs.\\* FROM `cron_task_pocs` JOIN scan_pocs ON cron_task_pocs.cronpocs_id = scan_pocs.id WHERE cron_task_pocs.cron_task_id = \\? AND cron_task_pocs.cronpocs_type = \\?").
			WithArgs(taskID, "App\\Models\\MySql\\Poc").
			WillReturnRows(directPocRows)

		// 分组POC查询失败
		mock.ExpectQuery("SELECT scan_pocs.\\* FROM `cron_task_pocs` JOIN poc_groups ON cron_task_pocs.cronpocs_id = poc_groups.id JOIN poc_poc_group ON poc_groups.id = poc_poc_group.poc_group_id JOIN scan_pocs ON poc_poc_group.poc_id = scan_pocs.id WHERE cron_task_pocs.cron_task_id = \\? AND cron_task_pocs.cronpocs_type = \\?").
			WithArgs(taskID, "App\\Models\\MySql\\PocGroup").
			WillReturnError(sql.ErrConnDone)

		result, err := model.GetAllPoc(taskID)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

// 测试GetAllPort方法
func TestDefaultModel_GetAllPort(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		taskID := uint64(123)

		// Mock直接关联的PORT查询
		directPortRows := sqlmock.NewRows([]string{"id", "port", "protocol"}).
			AddRow(1, 80, "HTTP").
			AddRow(2, 443, "HTTPS")

		mock.ExpectQuery("SELECT ports.\\* FROM `cron_task_ports` JOIN ports ON cron_task_ports.cronports_id = ports.id WHERE cron_task_ports.cron_task_id = \\? AND cron_task_ports.cronports_type = \\?").
			WithArgs(taskID, "App\\Models\\MySql\\Port").
			WillReturnRows(directPortRows)

		// Mock通过分组关联的PORT查询
		groupPortRows := sqlmock.NewRows([]string{"id", "port", "protocol"}).
			AddRow(3, 22, "SSH")

		mock.ExpectQuery("SELECT ports.\\* FROM `cron_task_ports` JOIN port_port_group ON cron_task_ports.cronports_id = port_port_group.port_group_id JOIN ports ON port_port_group.port_id = ports.id WHERE cron_task_ports.cron_task_id = \\? AND cron_task_ports.cronports_type = \\?").
			WithArgs(taskID, "App\\Models\\MySql\\PortGroup").
			WillReturnRows(groupPortRows)

		result, err := model.GetAllPort(taskID)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure_direct_port", func(t *testing.T) {
		model, mock := setupTestModel(t)

		taskID := uint64(123)

		mock.ExpectQuery("SELECT ports.\\* FROM `cron_task_ports` JOIN ports ON cron_task_ports.cronports_id = ports.id WHERE cron_task_ports.cron_task_id = \\? AND cron_task_ports.cronports_type = \\?").
			WithArgs(taskID, "App\\Models\\MySql\\Port").
			WillReturnError(sql.ErrConnDone)

		result, err := model.GetAllPort(taskID)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure_group_port", func(t *testing.T) {
		model, mock := setupTestModel(t)

		taskID := uint64(123)

		// 直接PORT查询成功
		directPortRows := sqlmock.NewRows([]string{"id", "port", "protocol"})
		mock.ExpectQuery("SELECT ports.\\* FROM `cron_task_ports` JOIN ports ON cron_task_ports.cronports_id = ports.id WHERE cron_task_ports.cron_task_id = \\? AND cron_task_ports.cronports_type = \\?").
			WithArgs(taskID, "App\\Models\\MySql\\Port").
			WillReturnRows(directPortRows)

		// 分组PORT查询失败
		mock.ExpectQuery("SELECT ports.\\* FROM `cron_task_ports` JOIN port_port_group ON cron_task_ports.cronports_id = port_port_group.port_group_id JOIN ports ON port_port_group.port_id = ports.id WHERE cron_task_ports.cron_task_id = \\? AND cron_task_ports.cronports_type = \\?").
			WithArgs(taskID, "App\\Models\\MySql\\PortGroup").
			WillReturnError(sql.ErrConnDone)

		result, err := model.GetAllPort(taskID)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

// 测试结构体字段
func TestScanCrontabTask_Fields(t *testing.T) {
	now := time.Now()
	task := &ScanCrontabTask{
		ID:                  1,
		UserId:              123,
		CompanyId:           456,
		Name:                "测试周期任务",
		Bandwidth:           "100M",
		PocScanType:         1,
		ProtocolConcurrency: "2",
		TaskType:            CronTaskTypeAsset,
		IpType:              1,
		ScanRange:           CronInputIP,
		PingSwitch:          1,
		WebLogoSwitch:       1,
		Type:                CronTypeScheduleMonth,
		ScheduleTime:        "10:00",
		DayOfX:              "15",
		ScanType:            CronScanTypeAccurate,
		Switch:              0,
		CreatedAt:           now,
		UpdatedAt:           now,
		FileName:            "test.txt",
		TaskFrom:            0,
		OpId:                789,
		IsAudit:             AuditPass,
		Reason:              "审核通过",
		IsDefinePort:        1,
		ScanEngine:          "goscanner",
		EngineRole:          0,
		TableAssetsType:     1,
	}

	assert.Equal(t, uint64(1), task.ID)
	assert.Equal(t, uint64(123), task.UserId)
	assert.Equal(t, uint64(456), task.CompanyId)
	assert.Equal(t, "测试周期任务", task.Name)
	assert.Equal(t, "100M", task.Bandwidth)
	assert.Equal(t, 1, task.PocScanType)
	assert.Equal(t, "2", task.ProtocolConcurrency)
	assert.Equal(t, CronTaskTypeAsset, task.TaskType)
	assert.Equal(t, 1, task.IpType)
	assert.Equal(t, CronInputIP, task.ScanRange)
	assert.Equal(t, 1, task.PingSwitch)
	assert.Equal(t, 1, task.WebLogoSwitch)
	assert.Equal(t, CronTypeScheduleMonth, task.Type)
	assert.Equal(t, "10:00", task.ScheduleTime)
	assert.Equal(t, "15", task.DayOfX)
	assert.Equal(t, CronScanTypeAccurate, task.ScanType)
	assert.Equal(t, 0, task.Switch)
	assert.Equal(t, "test.txt", task.FileName)
	assert.Equal(t, 0, task.TaskFrom)
	assert.Equal(t, int64(789), task.OpId)
	assert.Equal(t, AuditPass, task.IsAudit)
	assert.Equal(t, "审核通过", task.Reason)
	assert.Equal(t, 1, task.IsDefinePort)
	assert.Equal(t, "goscanner", task.ScanEngine)
	assert.Equal(t, 0, task.EngineRole)
	assert.Equal(t, 1, task.TableAssetsType)
}
