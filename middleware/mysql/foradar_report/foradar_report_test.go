package foradar_report

import (
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
)

func init() {
	cfg.InitLoadCfg()
	// 设置测试环境标志
	initmysql.SetTestEnv(true)

	// 初始化数据库连接
	initmysql.GetInstance(cfg.LoadMysql())
}

// setupTestModel 创建测试用的模型实例
func setupTestModel(t *testing.T) (ForadarReportModel, sqlmock.Sqlmock) {
	// 为每个测试创建新的Mock实例
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to create mock: %v", err)
	}

	// Mock GORM 初始化时的查询
	mock.ExpectQuery("SELECT VERSION\\(\\)").WillReturnRows(sqlmock.NewRows([]string{"VERSION()"}).AddRow("8.0.0"))

	// 创建GORM实例
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: false,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to create gorm instance: %v", err)
	}

	model := &defaultForadarReport{
		DB:        gormDB,
		tableName: "foradar_reports",
	}
	return model, mock
}

func TestForadarReport_TableName(t *testing.T) {
	report := &ForadarReport{}
	assert.Equal(t, "foradar_reports", report.TableName())
}

func TestNewModel(t *testing.T) {
	model := NewModel()
	assert.NotNil(t, model)
}

func TestDefaultForadarReport_First(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		expectedReport := &ForadarReport{
			ID:               1,
			Name:             "测试报告",
			UserId:           123,
			CompanyId:        456,
			OperatorId:       789,
			ReportTemplateId: 10,
			Path:             "/path/to/report.pdf",
			Size:             "1024",
			Status:           StatusSuccess,
			CreateStatus:     StatusCreateSuccess,
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		}

		rows := sqlmock.NewRows([]string{
			"id", "name", "user_id", "company_id", "operator_id", "report_template_id",
			"path", "size", "safe_path", "safe_size", "status", "create_status",
			"created_at", "updated_at", "upload_at",
		}).AddRow(
			expectedReport.ID, expectedReport.Name, expectedReport.UserId,
			expectedReport.CompanyId, expectedReport.OperatorId, expectedReport.ReportTemplateId,
			expectedReport.Path, expectedReport.Size, "", "", expectedReport.Status,
			expectedReport.CreateStatus, expectedReport.CreatedAt, expectedReport.UpdatedAt, nil,
		)

		mock.ExpectQuery("SELECT (.+) FROM `foradar_reports`").
			WillReturnRows(rows)

		result, err := model.First()
		assert.NoError(t, err)
		assert.Equal(t, expectedReport.ID, result.ID)
		assert.Equal(t, expectedReport.Name, result.Name)
		assert.Equal(t, expectedReport.UserId, result.UserId)
		assert.Equal(t, expectedReport.Status, result.Status)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("not found", func(t *testing.T) {
		model, mock := setupTestModel(t)

		mock.ExpectQuery("SELECT (.+) FROM `foradar_reports`").
			WillReturnError(gorm.ErrRecordNotFound)

		_, err := model.First()
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultForadarReport_FindAll(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		expectedReports := []*ForadarReport{
			{
				ID:           1,
				Name:         "测试报告1",
				UserId:       123,
				CompanyId:    456,
				Status:       StatusSuccess,
				CreateStatus: StatusCreateSuccess,
				CreatedAt:    time.Now(),
				UpdatedAt:    time.Now(),
			},
			{
				ID:           2,
				Name:         "测试报告2",
				UserId:       123,
				CompanyId:    456,
				Status:       StatusDefault,
				CreateStatus: StatusDefault,
				CreatedAt:    time.Now(),
				UpdatedAt:    time.Now(),
			},
		}

		rows := sqlmock.NewRows([]string{
			"id", "name", "user_id", "company_id", "status", "create_status",
			"created_at", "updated_at",
		})
		for _, report := range expectedReports {
			rows.AddRow(
				report.ID, report.Name, report.UserId, report.CompanyId,
				report.Status, report.CreateStatus, report.CreatedAt, report.UpdatedAt,
			)
		}

		mock.ExpectQuery("SELECT (.+) FROM `foradar_reports`").
			WillReturnRows(rows)

		results, err := model.FindAll()
		assert.NoError(t, err)
		assert.Len(t, results, 2)
		assert.Equal(t, expectedReports[0].ID, results[0].ID)
		assert.Equal(t, expectedReports[1].ID, results[1].ID)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("empty result", func(t *testing.T) {
		model, mock := setupTestModel(t)

		rows := sqlmock.NewRows([]string{
			"id", "name", "user_id", "company_id", "status", "create_status",
			"created_at", "updated_at",
		})

		mock.ExpectQuery("SELECT (.+) FROM `foradar_reports`").
			WillReturnRows(rows)

		results, err := model.FindAll()
		assert.NoError(t, err)
		assert.Len(t, results, 0)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		mock.ExpectQuery("SELECT (.+) FROM `foradar_reports`").
			WillReturnError(sql.ErrConnDone)

		results, err := model.FindAll()
		assert.Error(t, err)
		assert.Nil(t, results)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultForadarReport_Create(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		report := &ForadarReport{
			Name:             "新报告",
			UserId:           123,
			CompanyId:        456,
			OperatorId:       789,
			ReportTemplateId: 10,
			Status:           StatusDefault,
			CreateStatus:     StatusDefault,
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `foradar_reports`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		err := model.Create(report)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		report := &ForadarReport{
			Name:             "新报告",
			UserId:           123,
			CompanyId:        456,
			OperatorId:       789,
			ReportTemplateId: 10,
			Status:           StatusDefault,
			CreateStatus:     StatusDefault,
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `foradar_reports`").
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Create(report)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultForadarReport_Delete(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `foradar_reports`").
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err := model.Delete(WithIds([]uint64{1}))
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `foradar_reports`").
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Delete(WithIds([]uint64{1}))
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestDefaultForadarReport_Update(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		model, mock := setupTestModel(t)

		report := &ForadarReport{
			ID:           1,
			Name:         "更新的报告",
			Status:       StatusSuccess,
			CreateStatus: StatusCreateSuccess,
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `foradar_reports`").
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err := model.Update(report, WithIds([]uint64{1}))
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error", func(t *testing.T) {
		model, mock := setupTestModel(t)

		report := &ForadarReport{
			ID:           1,
			Name:         "更新的报告",
			Status:       StatusSuccess,
			CreateStatus: StatusCreateSuccess,
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `foradar_reports`").
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Update(report, WithIds([]uint64{1}))
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestWithUserId(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	userId := uint64(123)

	mock.ExpectQuery("SELECT (.+) FROM `foradar_reports` WHERE user_id = ?").
		WithArgs(userId).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithUserId(userId))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithReportTemplateId(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	templateId := uint64(10)

	mock.ExpectQuery("SELECT (.+) FROM `foradar_reports` WHERE report_template_id = ?").
		WithArgs(templateId).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithReportTemplateId(templateId))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithReportTemplateIds(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	templateIds := []uint64{10, 20, 30}

	mock.ExpectQuery("SELECT (.+) FROM `foradar_reports` WHERE report_template_id IN \\(\\?,\\?,\\?\\)").
		WithArgs(templateIds[0], templateIds[1], templateIds[2]).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithReportTemplateIds(templateIds))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithCompanyId(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	companyId := uint64(456)

	mock.ExpectQuery("SELECT (.+) FROM `foradar_reports` WHERE company_id = ?").
		WithArgs(companyId).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithCompanyId(companyId))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithStatus(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	status := StatusSuccess

	mock.ExpectQuery("SELECT (.+) FROM `foradar_reports` WHERE status = ?").
		WithArgs(status).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithStatus(status))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithCreateStatus(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	createStatus := StatusCreateSuccess

	mock.ExpectQuery("SELECT (.+) FROM `foradar_reports` WHERE create_status = ?").
		WithArgs(createStatus).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithCreateStatus(createStatus))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithKeyword(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	keyword := "测试"

	mock.ExpectQuery("SELECT (.+) FROM `foradar_reports` WHERE name LIKE ?").
		WithArgs("%" + keyword + "%").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithKeyword(keyword))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestWithIds(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()
	ids := []uint64{1, 2, 3}

	mock.ExpectQuery("SELECT (.+) FROM `foradar_reports` WHERE id IN \\(\\?,\\?,\\?\\)").
		WithArgs(ids[0], ids[1], ids[2]).
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(WithIds(ids))
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

func TestOrderByIdDesc(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()

	mock.ExpectQuery("SELECT (.+) FROM `foradar_reports` ORDER BY id DESC").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	_, err := model.First(OrderByIdDesc())
	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}
