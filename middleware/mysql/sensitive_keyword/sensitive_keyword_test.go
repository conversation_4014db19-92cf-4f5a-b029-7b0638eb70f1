package sensitive_keyword

import (
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
)

func init() {
	cfg.InitLoadCfg()
	// 设置测试环境标志
	initmysql.SetTestEnv(true)

	// 初始化数据库连接
	initmysql.GetInstance(cfg.LoadMysql())
}

func TestSensitiveKeyword_TableName(t *testing.T) {
	keyword := &SensitiveKeyword{}
	assert.Equal(t, "sensitive_keyword", keyword.TableName())
}

func TestNewModel(t *testing.T) {
	model := NewModel()
	assert.NotNil(t, model)
}

func TestModel_Create(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		keyword := &SensitiveKeyword{
			UserId:       123,
			CompanyId:    456,
			OperatorId:   789,
			Name:         "测试关键词",
			Status:       STATS_ENABLE,
			Type:         NEW_DATA_TYPE,
			DetectTaskId: 999,
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `sensitive_keyword`").
			WithArgs(utils.SqlMockArgs(9)...).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		err := model.Create(keyword)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		keyword := &SensitiveKeyword{
			UserId: 123,
			Name:   "失败关键词",
		}

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `sensitive_keyword`").
			WithArgs(utils.SqlMockArgs(9)...).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Create(keyword)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestModel_Update(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		id := uint64(1)
		updates := map[string]interface{}{
			"status": STATS_ENABLE,
			"name":   "更新后的关键词",
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `sensitive_keyword` SET (.+) WHERE id = (.+)").
			WithArgs(utils.SqlMockArgs(4)...).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		err := model.Update(id, updates)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		id := uint64(1)
		updates := map[string]interface{}{
			"status": 0,
		}

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `sensitive_keyword` SET (.+) WHERE id = (.+)").
			WithArgs(utils.SqlMockArgs(3)...).
			WillReturnError(sql.ErrConnDone)
		mock.ExpectRollback()

		err := model.Update(id, updates)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestModel_Search(t *testing.T) {
	t.Run("success_with_pagination", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		params := map[string]interface{}{
			"user_id": uint64(123),
			"type":    NEW_DATA_TYPE,
		}
		page := 1
		size := 10

		expectedKeywords := []SensitiveKeyword{
			{
				Id:           1,
				UserId:       123,
				CompanyId:    456,
				Name:         "关键词1",
				Status:       STATS_ENABLE,
				Type:         NEW_DATA_TYPE,
				DetectTaskId: 999,
				CreatedAt:    time.Now(),
				UpdatedAt:    time.Now(),
			},
			{
				Id:           2,
				UserId:       123,
				CompanyId:    456,
				Name:         "关键词2",
				Status:       STATS_ENABLE,
				Type:         NEW_DATA_TYPE,
				DetectTaskId: 999,
				CreatedAt:    time.Now(),
				UpdatedAt:    time.Now(),
			},
		}

		// 计数查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(2)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_keyword` WHERE user_id = (.+) AND type = (.+)").
			WithArgs(uint64(123), NEW_DATA_TYPE).
			WillReturnRows(countRows)

		// 数据查询
		dataRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "operator_id", "name", "status",
			"created_at", "updated_at", "type", "detect_task_id",
		})
		for _, keyword := range expectedKeywords {
			dataRows.AddRow(
				keyword.Id, keyword.UserId, keyword.CompanyId, keyword.OperatorId,
				keyword.Name, keyword.Status, keyword.CreatedAt, keyword.UpdatedAt,
				keyword.Type, keyword.DetectTaskId,
			)
		}

		mock.ExpectQuery("SELECT (.+) FROM `sensitive_keyword` WHERE user_id = (.+) AND type = (.+) ORDER BY id desc LIMIT (.+)").
			WithArgs(uint64(123), NEW_DATA_TYPE, size).
			WillReturnRows(dataRows)

		result, total, err := model.Search(params, page, size)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, result, 2)
		assert.Equal(t, expectedKeywords[0].Name, result[0].Name)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_with_keyword_search", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		params := map[string]interface{}{
			"keyword": "测试",
		}
		page := 1
		size := 10

		// 计数查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(1)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_keyword` WHERE name LIKE (.+)").
			WithArgs("%测试%").
			WillReturnRows(countRows)

		// 数据查询
		dataRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "operator_id", "name", "status",
			"created_at", "updated_at", "type", "detect_task_id",
		}).AddRow(1, 123, 456, 789, "测试关键词", STATS_ENABLE, time.Now(), time.Now(), NEW_DATA_TYPE, 999)

		mock.ExpectQuery("SELECT (.+) FROM `sensitive_keyword` WHERE name LIKE (.+) ORDER BY id desc LIMIT (.+)").
			WithArgs("%测试%", size).
			WillReturnRows(dataRows)

		result, total, err := model.Search(params, page, size)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, result, 1)
		assert.Contains(t, result[0].Name, "测试")
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_with_exact_name", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		params := map[string]interface{}{
			"name": "精确关键词",
		}
		page := 1
		size := 10

		// 计数查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(1)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_keyword` WHERE name = (.+)").
			WithArgs("精确关键词").
			WillReturnRows(countRows)

		// 数据查询
		dataRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "operator_id", "name", "status",
			"created_at", "updated_at", "type", "detect_task_id",
		}).AddRow(1, 123, 456, 789, "精确关键词", STATS_ENABLE, time.Now(), time.Now(), NEW_DATA_TYPE, 999)

		mock.ExpectQuery("SELECT (.+) FROM `sensitive_keyword` WHERE name = (.+) ORDER BY id desc LIMIT (.+)").
			WithArgs("精确关键词", size).
			WillReturnRows(dataRows)

		result, total, err := model.Search(params, page, size)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, result, 1)
		assert.Equal(t, "精确关键词", result[0].Name)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_with_id_list", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		params := map[string]interface{}{
			"id": []uint64{1, 2, 3},
		}
		page := 1
		size := 10

		// 计数查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(3)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_keyword` WHERE id IN (.+)").
			WithArgs(uint64(1), uint64(2), uint64(3)).
			WillReturnRows(countRows)

		// 数据查询
		dataRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "operator_id", "name", "status",
			"created_at", "updated_at", "type", "detect_task_id",
		}).AddRow(1, 123, 456, 789, "ID关键词1", STATS_ENABLE, time.Now(), time.Now(), NEW_DATA_TYPE, 999).
			AddRow(2, 123, 456, 789, "ID关键词2", STATS_ENABLE, time.Now(), time.Now(), NEW_DATA_TYPE, 999).
			AddRow(3, 123, 456, 789, "ID关键词3", STATS_ENABLE, time.Now(), time.Now(), NEW_DATA_TYPE, 999)

		mock.ExpectQuery("SELECT (.+) FROM `sensitive_keyword` WHERE id IN (.+) ORDER BY id desc LIMIT (.+)").
			WithArgs(uint64(1), uint64(2), uint64(3), size).
			WillReturnRows(dataRows)

		result, total, err := model.Search(params, page, size)
		assert.NoError(t, err)
		assert.Equal(t, int64(3), total)
		assert.Len(t, result, 3)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_with_date_range", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		params := map[string]interface{}{
			"created_at_range": []string{"2024-01-01", "2024-01-31"},
		}
		page := 1
		size := 10

		// 计数查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(1)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_keyword` WHERE created_at BETWEEN (.+) AND (.+)").
			WithArgs("2024-01-01", sqlmock.AnyArg()).
			WillReturnRows(countRows)

		// 数据查询
		dataRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "operator_id", "name", "status",
			"created_at", "updated_at", "type", "detect_task_id",
		}).AddRow(1, 123, 456, 789, "日期范围关键词", STATS_ENABLE, time.Now(), time.Now(), NEW_DATA_TYPE, 999)

		mock.ExpectQuery("SELECT (.+) FROM `sensitive_keyword` WHERE created_at BETWEEN (.+) AND (.+) ORDER BY id desc LIMIT (.+)").
			WithArgs("2024-01-01", sqlmock.AnyArg(), size).
			WillReturnRows(dataRows)

		result, total, err := model.Search(params, page, size)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, result, 1)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_without_pagination", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		params := map[string]interface{}{
			"status": STATS_ENABLE,
		}
		page := 0
		size := 0

		// 计数查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(1)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_keyword` WHERE status = (.+)").
			WithArgs(STATS_ENABLE).
			WillReturnRows(countRows)

		// 数据查询（不分页）
		dataRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "operator_id", "name", "status",
			"created_at", "updated_at", "type", "detect_task_id",
		}).AddRow(1, 123, 456, 789, "无分页关键词", STATS_ENABLE, time.Now(), time.Now(), NEW_DATA_TYPE, 999)

		mock.ExpectQuery("SELECT (.+) FROM `sensitive_keyword` WHERE status = (.+) ORDER BY id desc").
			WithArgs(STATS_ENABLE).
			WillReturnRows(dataRows)

		result, total, err := model.Search(params, page, size)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, result, 1)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("empty_result", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		params := map[string]interface{}{
			"user_id": uint64(999),
		}
		page := 1
		size := 10

		// 计数查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(0)
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_keyword` WHERE user_id = (.+)").
			WithArgs(uint64(999)).
			WillReturnRows(countRows)

		// 数据查询
		dataRows := sqlmock.NewRows([]string{
			"id", "user_id", "company_id", "operator_id", "name", "status",
			"created_at", "updated_at", "type", "detect_task_id",
		})

		mock.ExpectQuery("SELECT (.+) FROM `sensitive_keyword` WHERE user_id = (.+) ORDER BY id desc LIMIT (.+)").
			WithArgs(uint64(999), size).
			WillReturnRows(dataRows)

		result, total, err := model.Search(params, page, size)
		assert.NoError(t, err)
		assert.Equal(t, int64(0), total)
		assert.Len(t, result, 0)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		params := map[string]interface{}{
			"user_id": uint64(123),
		}
		page := 1
		size := 10

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `sensitive_keyword` WHERE user_id = (.+)").
			WithArgs(uint64(123)).
			WillReturnError(sql.ErrConnDone)

		_, _, err := model.Search(params, page, size)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

// 测试常量
func TestConstants(t *testing.T) {
	assert.Equal(t, 1, STATS_ENABLE)
	assert.Equal(t, 0, ALL_TYPE)
	assert.Equal(t, 1, NEW_DATA_TYPE)
	assert.Equal(t, 2, LEAK_DATA_TYPE)
}

// 测试结构体字段
func TestSensitiveKeyword_Fields(t *testing.T) {
	now := time.Now()
	keyword := &SensitiveKeyword{
		Id:           1,
		UserId:       123,
		CompanyId:    456,
		OperatorId:   789,
		Name:         "测试敏感关键词",
		Status:       STATS_ENABLE,
		CreatedAt:    now,
		UpdatedAt:    now,
		Type:         NEW_DATA_TYPE,
		DetectTaskId: 999,
	}

	assert.Equal(t, uint64(1), keyword.Id)
	assert.Equal(t, uint64(123), keyword.UserId)
	assert.Equal(t, uint64(456), keyword.CompanyId)
	assert.Equal(t, uint64(789), keyword.OperatorId)
	assert.Equal(t, "测试敏感关键词", keyword.Name)
	assert.Equal(t, int8(STATS_ENABLE), keyword.Status)
	assert.Equal(t, now, keyword.CreatedAt)
	assert.Equal(t, now, keyword.UpdatedAt)
	assert.Equal(t, int8(NEW_DATA_TYPE), keyword.Type)
	assert.Equal(t, uint64(999), keyword.DetectTaskId)
}

// 测试不同类型的关键词
func TestSensitiveKeyword_DifferentTypes(t *testing.T) {
	testCases := []struct {
		name        string
		keywordType int8
		description string
	}{
		{
			name:        "all_type",
			keywordType: ALL_TYPE,
			description: "全部类型",
		},
		{
			name:        "new_data_type",
			keywordType: NEW_DATA_TYPE,
			description: "新数据类型",
		},
		{
			name:        "leak_data_type",
			keywordType: LEAK_DATA_TYPE,
			description: "泄露数据类型",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			keyword := &SensitiveKeyword{
				Type: tc.keywordType,
				Name: tc.description,
			}
			assert.Equal(t, tc.keywordType, keyword.Type)
			assert.Equal(t, tc.description, keyword.Name)
		})
	}
}

// 测试不同状态的关键词
func TestSensitiveKeyword_DifferentStatus(t *testing.T) {
	testCases := []struct {
		name   string
		status int8
	}{
		{
			name:   "disabled",
			status: 0,
		},
		{
			name:   "enabled",
			status: STATS_ENABLE,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			keyword := &SensitiveKeyword{
				Status: tc.status,
				Name:   "状态测试关键词",
			}
			assert.Equal(t, tc.status, keyword.Status)
		})
	}
}

// 测试边界情况
func TestSensitiveKeyword_EdgeCases(t *testing.T) {
	t.Run("zero_values", func(t *testing.T) {
		keyword := &SensitiveKeyword{
			UserId:       0,
			CompanyId:    0,
			OperatorId:   0,
			Name:         "",
			Status:       0,
			Type:         0,
			DetectTaskId: 0,
		}

		assert.Equal(t, uint64(0), keyword.UserId)
		assert.Equal(t, uint64(0), keyword.CompanyId)
		assert.Equal(t, uint64(0), keyword.OperatorId)
		assert.Equal(t, "", keyword.Name)
		assert.Equal(t, int8(0), keyword.Status)
		assert.Equal(t, int8(0), keyword.Type)
		assert.Equal(t, uint64(0), keyword.DetectTaskId)
	})

	t.Run("max_values", func(t *testing.T) {
		keyword := &SensitiveKeyword{
			UserId:       ^uint64(0), // 最大uint64值
			CompanyId:    ^uint64(0),
			OperatorId:   ^uint64(0),
			Name:         "非常长的关键词名称，用于测试字段长度限制和边界情况处理",
			Status:       127, // int8最大值
			Type:         127,
			DetectTaskId: ^uint64(0),
		}

		assert.Equal(t, ^uint64(0), keyword.UserId)
		assert.Equal(t, ^uint64(0), keyword.CompanyId)
		assert.Equal(t, ^uint64(0), keyword.OperatorId)
		assert.Contains(t, keyword.Name, "非常长的关键词名称")
		assert.Equal(t, int8(127), keyword.Status)
		assert.Equal(t, int8(127), keyword.Type)
		assert.Equal(t, ^uint64(0), keyword.DetectTaskId)
	})
}

// 测试模型结构
func TestModel_Structure(t *testing.T) {
	model := NewModel()

	// 类型断言确保返回的是正确的类型
	assert.NotNil(t, model)
	assert.NotNil(t, model.db)
}
