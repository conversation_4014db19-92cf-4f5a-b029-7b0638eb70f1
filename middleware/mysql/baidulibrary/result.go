package baidulibrary

import (
	"fmt"
	"time"

	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"micro-service/pkg/utils"

	"gorm.io/gorm"
)

type BaiduLibraryResultModel interface {
	Upsert([]*BaiduLibraryResult) ([]*BaiduLibraryResult, error)
	CompareAndUpdate(origin, target []*BaiduLibraryResult) error
	Create([]*BaiduLibraryResult) error
	List(page, size int, opts ...mysql.HandleFunc) ([]*BaiduLibraryResult, int64, error)
	ListAll(...mysql.HandleFunc) ([]*BaiduLibraryResult, error)
	UpdateAny(id uint64, m map[string]any) error
	ListAllByTask(taskId uint64) ([]*BaiduLibraryResult, error)
	RelationListAll(opts ...mysql.HandleFunc) ([]*Relation, error)
	RelationSave([]*Relation) error
	RelationFirst(opts ...mysql.HandleFunc) (Relation, error)
}

const ResultTableName = "dataleak_baidulibrary_result"

type BaiduLibraryResult struct {
	dbx.Model
	TaskId     uint   `gorm:"column:task_id"`
	Keyword    string `gorm:"column:keyword;<-:false"` // allow read, disable write permission
	Title      string `gorm:"column:title"`
	Address    string `gorm:"column:address"`
	Url        string `gorm:"column:url"`
	Screenshot string `gorm:"column:screenshot"`
}

func (*BaiduLibraryResult) TableName() string {
	return ResultTableName
}

type defaultResultImpl struct{ *gorm.DB }

func NewResultModel(clients ...*gorm.DB) BaiduLibraryResultModel {
	return &defaultResultImpl{DB: mysql.GetDbClient(clients...)}
}

func (d *defaultResultImpl) Upsert(items []*BaiduLibraryResult) ([]*BaiduLibraryResult, error) {
	addresses := make([]string, 0, len(items))
	resultMap := make(map[string]*BaiduLibraryResult, len(items))
	for _, v := range items {
		if _, ok := resultMap[v.Address]; !ok {
			addresses = append(addresses, v.Address)
			resultMap[v.Address] = v
		}
	}

	var got = make([]*BaiduLibraryResult, 0, len(items))
	for _, v := range utils.ListSplit[string](addresses, 50) {
		la, err := d.ListAll(mysql.WithValuesIn("address", v))
		if err != nil {
			continue
		}
		got = append(got, la...)
	}

	var saveList = make([]*BaiduLibraryResult, 0, len(got)+len(items))
	for _, v := range resultMap {
		exist := false
		for _, x := range got {
			if v.Address == x.Address {
				v.Id = x.Id
				v.CreatedAt = x.CreatedAt
				saveList = append(saveList, v)
				exist = true
				break
			}
		}
		if !exist {
			saveList = append(saveList, v)
		}
	}

	err := d.DB.Save(&saveList).Error
	return saveList, err
}

// CompareAndUpdate 对比更新
// compare fields: address
func (d *defaultResultImpl) CompareAndUpdate(origin, target []*BaiduLibraryResult) error {
	var compareMap = make(map[string]*BaiduLibraryResult, len(origin))
	for _, v := range origin {
		compareMap[v.Address] = v
	}

	var insert = make([]*BaiduLibraryResult, 0, len(target))
	var update = make([]*BaiduLibraryResult, 0, len(target))
	for _, v := range target {
		if item, ok := compareMap[v.Address]; !ok {
			insert = append(insert, v)
		} else {
			v.Id = item.Id
			v.CreatedAt = item.CreatedAt
			update = append(update, v)
		}
	}

	if err := d.Create(insert); err != nil {
		return err
	}
	return d.Save(update)
}

func (d *defaultResultImpl) Create(l []*BaiduLibraryResult) (err error) {
	if len(l) > 0 {
		err = d.DB.Create(&l).Error
	}
	return err
}

func (d *defaultResultImpl) Save(list []*BaiduLibraryResult) error {
	if len(list) == 0 {
		return nil
	}
	return d.DB.Save(list).Error
}

func WithKeyword(keyword string) mysql.HandleFunc {
	return func(query *gorm.DB) {
		keyword = "%" + keyword + "%"
		query.Where(fmt.Sprintf("%s.title LIKE ? OR %s.url LIKE ?", ResultTableName, ResultTableName), keyword, keyword)
	}
	//return func(query *gorm.DB) {
	//	if isEq {
	//		query.Where(fmt.Sprintf("%s.keyword = ?", TaskTableName), keyword)
	//	} else {
	//		keyword = "%" + keyword + "%"
	//		query.Where(fmt.Sprintf("%s.keyword LIKE ? OR %s.title LIKE ?", TaskTableName, ResultTableName), keyword, keyword)
	//	}
	//}
}

func WithBetween(field string, start, end time.Time) mysql.HandleFunc {
	return func(query *gorm.DB) {
		query.Where(fmt.Sprintf("%s.%s BETWEEN ? AND ?", ResultTableName, field), start, end)
	}
}

func WithResultOrder(field string, asc bool) mysql.HandleFunc {
	return func(db *gorm.DB) {
		asc := utils.If(asc, "ASC", "DESC")
		db.Order(fmt.Sprintf("%s.%s %s", ResultTableName, field, asc))
	}
}

// List with page and condition
func (d *defaultResultImpl) List(page, size int, opts ...mysql.HandleFunc) ([]*BaiduLibraryResult, int64, error) {
	query := d.DB.Model(&BaiduLibraryResult{})
	//joinCond := fmt.Sprintf("LEFT JOIN %s ON %s.task_id = %s.id", TaskTableName, ResultTableName, TaskTableName)
	//query.Joins(joinCond)
	for _, opt := range opts {
		opt(query)
	}
	//query.Select(fmt.Sprintf("%s.*", ResultTableName), fmt.Sprintf("%s.keyword", TaskTableName))

	var total int64
	list := make([]*BaiduLibraryResult, 0, size)
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

// ListAll return all result with conditions
func (d *defaultResultImpl) ListAll(opts ...mysql.HandleFunc) ([]*BaiduLibraryResult, error) {
	query := d.DB.Model(&BaiduLibraryResult{})
	for _, opt := range opts {
		opt(query)
	}

	l := make([]*BaiduLibraryResult, 0)
	err := query.Find(&l).Error
	if err != nil {
		return nil, err
	}
	return l, nil
}

func (d *defaultResultImpl) UpdateAny(id uint64, m map[string]any) error {
	return d.DB.Model(BaiduLibraryResult{}).Where("id = ?", id).Updates(m).Error
}
