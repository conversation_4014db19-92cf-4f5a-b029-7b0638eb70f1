package baidulibrary

import (
	"fmt"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

const RelationTableName = "dataleak_baidulibrary_relation"

type Relation struct {
	dbx.Model
	TaskId   uint64 `gorm:"column:task_id"`
	ResultId uint64 `gorm:"column:result_id"`
}

func (*Relation) TableName() string {
	return RelationTableName
}

func (d *defaultResultImpl) ListAllByTask(taskId uint64) ([]*BaiduLibraryResult, error) {
	const et = RelationTableName
	const rt = ResultTableName

	q := d.DB.Model(&Relation{})
	q.Joins(fmt.Sprintf("INNER JOIN %s ON `%s`.`id` = `%s`.`result_id`", rt, rt, et))
	q.Select(fmt.Sprintf("%s.id, %s.created_at, %s.updated_at", et, et, et), "title", "address", "url", "screenshot")
	q.Where(fmt.Sprintf("`%s`.`task_id` = ?", et), taskId)

	var result []*BaiduLibraryResult
	err := q.Find(&result).Error
	return result, err
}

func (d *defaultResultImpl) RelationListAll(opts ...mysql.HandleFunc) ([]*Relation, error) {
	q := d.DB.Model(&Relation{})
	for _, opt := range opts {
		opt(q)
	}

	var items = make([]*Relation, 0)
	err := q.Find(&items).Error
	return items, err
}

func (d *defaultResultImpl) RelationSave(result []*Relation) error {
	if len(result) == 0 {
		return nil
	}
	return d.DB.Save(&result).Error
}

func (d *defaultResultImpl) RelationFirst(opts ...mysql.HandleFunc) (Relation, error) {
	q := d.DB.Model(&Relation{})
	for _, f := range opts {
		f(q)
	}

	var info Relation
	err := q.First(&info).Error
	return info, err
}
