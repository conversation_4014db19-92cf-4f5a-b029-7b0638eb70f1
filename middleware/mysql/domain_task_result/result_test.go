package domain_task_result

import (
	"database/sql"
	"regexp"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"micro-service/pkg/dbx"
)

func setupTestDB(t *testing.T) (*gorm.DB, sqlmock.Sqlmock, error) {
	var err error
	var db *sql.DB
	db, mock, err := sqlmock.New()
	if err != nil {
		return nil, nil, err
	}

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})

	return gormDB, mock, err
}

func TestDomainResult_Create(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := &defaultDomainResult{DB: gormDB}

	testCases := []struct {
		name    string
		input   []*DomainResult
		mockSQL func()
		wantErr bool
	}{
		{
			name: "successful creation",
			input: []*DomainResult{
				{
					Model:       dbx.Model{Id: 1},
					UserID:      1,
					CompanyID:   1,
					TaskID:      1,
					Domain:      "test.com",
					FDomain:     "test.com",
					TopDomain:   "test.com",
					ParseStatus: DomainParseValid,
					DnsA:        "*******",
					Level:       "1",
				},
				{
					Model:       dbx.Model{Id: 2},
					UserID:      1,
					CompanyID:   1,
					TaskID:      1,
					Domain:      "sub.test.com",
					FDomain:     "test.com",
					TopDomain:   "test.com",
					ParseStatus: DomainParseValid,
					DnsA:        "*******",
					Level:       "2",
				},
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec(regexp.QuoteMeta("INSERT INTO `domain_task_results`")).
					WillReturnResult(sqlmock.NewResult(1, 2))
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name:  "empty input",
			input: []*DomainResult{},
			mockSQL: func() {
			},
			wantErr: false,
		},
		{
			name: "creation failure",
			input: []*DomainResult{
				{
					Model:  dbx.Model{Id: 1},
					UserID: 1,
					Domain: "test.com",
				},
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec(regexp.QuoteMeta("INSERT INTO `domain_task_results`")).
					WillReturnError(gorm.ErrInvalidTransaction)
				mock.ExpectRollback()
			},
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSQL()
			err := model.Create(tc.input)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestDomainResult_ListAll(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := &defaultDomainResult{DB: gormDB}

	testCases := []struct {
		name      string
		fields    []string
		mockSQL   func()
		wantCount int
		wantErr   bool
	}{
		{
			name:   "list all with all fields",
			fields: []string{},
			mockSQL: func() {
				rows := sqlmock.NewRows([]string{
					"id", "user_id", "company_id", "task_id", "domain",
					"f_domain", "top_domain", "parse_status", "dns_a", "level",
				}).
					AddRow(1, 1, 1, 1, "test.com", "test.com", "test.com", DomainParseValid, "*******", "1").
					AddRow(2, 1, 1, 1, "sub.test.com", "test.com", "test.com", DomainParseValid, "*******", "2")
				mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `domain_task_results`")).
					WillReturnRows(rows)
			},
			wantCount: 2,
			wantErr:   false,
		},
		{
			name:   "list all with selected fields",
			fields: []string{"id", "domain", "parse_status"},
			mockSQL: func() {
				rows := sqlmock.NewRows([]string{"id", "domain", "parse_status"}).
					AddRow(1, "test.com", DomainParseValid).
					AddRow(2, "sub.test.com", DomainParseValid)
				mock.ExpectQuery(regexp.QuoteMeta("SELECT `id`,`domain`,`parse_status` FROM `domain_task_results`")).
					WillReturnRows(rows)
			},
			wantCount: 2,
			wantErr:   false,
		},
		{
			name:   "empty result",
			fields: []string{},
			mockSQL: func() {
				mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `domain_task_results`")).
					WillReturnRows(sqlmock.NewRows([]string{"id", "domain", "parse_status"}))
			},
			wantCount: 0,
			wantErr:   false,
		},
		{
			name:   "query error",
			fields: []string{},
			mockSQL: func() {
				mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `domain_task_results`")).
					WillReturnError(gorm.ErrInvalidDB)
			},
			wantCount: 0,
			wantErr:   true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockSQL()
			results, err := model.ListAll(tc.fields)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Len(t, results, tc.wantCount)
				if tc.wantCount > 0 {
					if len(tc.fields) > 0 {
						// 验证只返回了请求的字段
						for _, field := range tc.fields {
							assert.NotNil(t, results[0].Id) // ID 总是会返回
							switch field {
							case "domain":
								assert.NotEmpty(t, results[0].Domain)
							case "parse_status":
								assert.NotZero(t, results[0].ParseStatus)
							}
						}
					} else {
						// 验证返回了所有字段
						assert.NotEmpty(t, results[0].Domain)
						assert.NotEmpty(t, results[0].FDomain)
						assert.NotEmpty(t, results[0].TopDomain)
						assert.NotZero(t, results[0].ParseStatus)
						assert.NotEmpty(t, results[0].Level)
					}
				}
			}
		})
	}
}

func TestDomainResult_TableName(t *testing.T) {
	result := &DomainResult{}
	assert.Equal(t, DomainTaskResultTableName, result.TableName())
}
