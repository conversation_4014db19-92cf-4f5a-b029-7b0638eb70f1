package domain_task_result

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

const DomainTaskResultTableName = "domain_task_results"

const (
	DomainParseValid   = iota + 1 // 域名可解析
	DomainParseInvalid            // 域名不可解析
)

type Result interface {
	Create([]*DomainResult) error
	ListAll(fields []string, opts ...mysql.HandleFunc) ([]*DomainResult, error)
}

type DomainResult struct {
	dbx.Model
	UserID      uint64 `gorm:"column:user_id;NOT NULL"`       // 用户id
	CompanyID   uint64 `gorm:"column:company_id"`             // 用户企业ID
	TaskID      uint64 `gorm:"column:task_id;NOT NULL"`       // domain_task表任务ID
	Domain      string `gorm:"column:domain;NOT NULL"`        // 域名
	FDomain     string `gorm:"column:f_domain;NOT NULL"`      // 被爆破域名
	TopDomain   string `gorm:"column:top_domain;NOT NULL"`    // 主域名
	ParseStatus int    `gorm:"column:parse_status;default:1"` // 域名解析结果 1/可以解析 2/不可以解析
	DnsA        string `gorm:"column:dns_a"`                  // ipv4_A记录
	DnsAaaa     string `gorm:"column:dns_aaaa"`               // ipv6_AAAA
	Cname       string `gorm:"column:cname"`                  // CNAME
	Level       string `gorm:"column:level;default:1"`        // 爆破层级: 1/2/3 一二三层
}

func (*DomainResult) TableName() string {
	return DomainTaskResultTableName
}

type defaultDomainResult struct{ *gorm.DB }

func NewResulter(db ...*gorm.DB) Result {
	return &defaultDomainResult{DB: mysql.GetDbClient(db...)}
}

func (d *defaultDomainResult) Create(items []*DomainResult) error {
	if len(items) > 0 {
		return d.DB.CreateInBatches(items, 1000).Error
	}
	return nil
}

func (d *defaultDomainResult) ListAll(fields []string, opts ...mysql.HandleFunc) ([]*DomainResult, error) {
	var results []*DomainResult
	query := d.DB.Model(&DomainResult{})
	for _, opt := range opts {
		opt(query)
	}
	if len(fields) > 0 {
		query.Select(fields)
	}
	err := query.Find(&results).Error
	return results, err
}
