package ipdomain

import (
	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"

	"testing"

	"github.com/stretchr/testify/assert"
)

func initCfg() {
	cfg.InitLoadCfg()

	_ = mysql.GetInstance(cfg.LoadMysql())
}

func Test_Record_domain(t *testing.T) {
	initCfg()

	var domain = "test.com"
	t.Run("record domain", func(t *testing.T) {
		err := NewRecorder("domain").Upsert(domain)
		assert.Nil(t, err)
	})

	t.Run("test for exist domain", func(t *testing.T) {
		err := NewRecorder("domain").Upsert(domain)
		assert.Nil(t, err)
	})
}

func Test_Record_ip(t *testing.T) {
	initCfg()

	var ip = "127.0.0.1"
	t.Run("record ip", func(t *testing.T) {
		err := NewRecorder("ip").Upsert(ip)
		assert.Nil(t, err)
	})

	t.Run("test for exist ip", func(t *testing.T) {
		err := NewRecorder("ip").Upsert(ip)
		assert.Nil(t, err)
	})
}
