package discover

import (
	"gorm.io/gorm"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

type (
	TaskClueModel interface {
		First(opts ...mysql.HandleFunc) (*TaskClue, error)
		List(page, size int, opts ...mysql.HandleFunc) ([]TaskClue, int64, error)
		Exists(taskId, clueId uint64) bool
		Create(item *TaskClue) error
		DeleteByTaskId(taskId uint64) error
	}

	defaultTaskClueModel struct {
		*gorm.DB
		table string
	}

	TaskClue struct {
		dbx.Model
		TaskId   uint64 `json:"task_id"`
		ClueId   uint64 `json:"clue_id"`
		ParentId uint64 `json:"parent_id"`
		Path     string `json:"path"`
		Status   int    `json:"status"`
	}
)

const (
	taskClueTableName = "discover_clues" // 表名
	NoConfirm         = 2
	OkConfirm         = 1
)

// TableName 表名
func (t *TaskClue) TableName() string {
	return taskClueTableName
}

func NewTaskClueModel(clients ...*gorm.DB) TaskClueModel {
	return &defaultTaskClueModel{mysql.GetDbClient(clients...), taskClueTableName}
}

func (d *defaultTaskClueModel) Create(item *TaskClue) error {
	return d.DB.Model(&TaskClue{}).Create(item).Error
}

func (g *defaultTaskClueModel) List(page, size int, opts ...mysql.HandleFunc) ([]TaskClue, int64, error) {
	query := g.DB.Model(&TaskClue{})
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if !mysql.IsPageAll(page, size) {
		query.Scopes(mysql.PageLimit(mysql.PageProcess(page, size)))
	}
	var list = make([]TaskClue, 0)
	if err := query.Find(&list).Error; err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (d *defaultTaskClueModel) Exists(taskId, clueId uint64) bool {
	query := d.DB.Model(&TaskClue{})
	var item TaskClue
	err := query.Where("task_id = ? and clue_id = ?", taskId, clueId).First(&item).Error
	if err != nil {
		return false
	}
	return true
}

func (d *defaultTaskClueModel) First(opts ...mysql.HandleFunc) (*TaskClue, error) {
	query := d.DB.Model(&TaskClue{})
	for _, opt := range opts {
		opt(query)
	}
	var item TaskClue
	err := query.First(&item).Error
	if err != nil {
		return nil, err
	}
	return &item, nil
}

func (d *defaultTaskClueModel) DeleteByTaskId(taskId uint64) error {
	if taskId == 0 {
		return nil
	}
	return d.DB.Model(&TaskClue{}).Where("`task_id`  = ? ", taskId).Delete(&TaskClue{}).Error
}
