package dataleak_gitee

import (
	"fmt"

	"micro-service/middleware/mysql"

	"micro-service/pkg/dbx"
)

type Relation struct {
	dbx.Model
	TaskId   uint64 `gorm:"column:task_id"`
	ResultId uint64 `gorm:"column:result_id"`
}

const RelationTableName = "dataleak_gitee_relation"

func (*Relation) TableName() string {
	return RelationTableName
}

func (d *defaultResultImpl) RelationSave(batch []*Relation) error {
	if len(batch) == 0 {
		return nil
	}
	return d.DB.Save(batch).Error
}

func (d *defaultResultImpl) ListAllByTask(taskId uint64) ([]*GiteeResult, error) {
	const et = RelationTableName
	const rt = ResultTableName

	q := d.DB.Model(&Relation{})
	q.Joins(fmt.Sprintf("INNER JOIN %s ON `%s`.`id` = `%s`.`result_id`", rt, rt, et))
	q.Select(fmt.Sprintf("%s.id,%s.created_at,%s.updated_at", et, et, et), "`repo_name`,`repo_url`,`repo_desc`,`code_url`,`code_snippet`,`sha`,`language`")
	q.Where(fmt.Sprintf("`%s`.`task_id` = ?", et), taskId)

	var list = make([]*GiteeResult, 0)
	err := q.Find(&list).Error
	return list, err
}

func (d *defaultResultImpl) RelationList(opts ...mysql.HandleFunc) ([]*Relation, error) {
	q := d.DB.Model(&Relation{})
	for _, f := range opts {
		f(q)
	}

	var list []*Relation
	err := q.Find(&list).Error
	return list, err
}

func (d *defaultResultImpl) RelationFirst(opts ...mysql.HandleFunc) (Relation, error) {
	q := d.DB.Model(&Relation{})
	for _, f := range opts {
		f(q)
	}

	var info Relation
	err := q.First(&info).Error
	return info, err
}
