package dataleak_gitee

import (
	"gorm.io/gorm"

	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

type TaskModel interface {
	List(page, size int64, opt ...mysql.HandleFunc) ([]Task, int64, error)
	First(opts ...mysql.HandleFunc) (Task, error)
	Create(*Task) error
	Update(Task) error
	UpdateAny(id uint, m map[string]any) error
}

const TaskTableName = "dataleak_gitee_tasks"

type defaultTaskImpl struct {
	*gorm.DB
}

func NewTaskModel(clients ...*gorm.DB) TaskModel {
	return &defaultTaskImpl{
		DB: mysql.GetDbClient(clients...),
	}
}

type Task struct {
	dbx.Model
	Keyword     string  `gorm:"column:keyword"`
	KeywordHash string  `gorm:"column:keyword_hash"` // md5: keyword sort
	Status      int     `gorm:"column:status"`
	Progress    float32 `gorm:"column:progress"`
}

func (*Task) TableName() string {
	return TaskTableName
}

const (
	StatusDoing    = iota + 1 // 任务进行中
	StatusFinished            // 任务已完成
)

func (d *defaultTaskImpl) First(opts ...mysql.HandleFunc) (Task, error) {
	query := d.DB.Model(&Task{})
	for _, opt := range opts {
		opt(query)
	}

	var item Task
	err := query.First(&item).Error
	if err != nil {
		return Task{}, err
	}
	return item, nil
}

func (d *defaultTaskImpl) Create(item *Task) error {
	return d.DB.Create(item).Error
}

func (d *defaultTaskImpl) Update(item Task) error {
	return d.DB.Model(&Task{}).Updates(&item).Error
}

func (d *defaultTaskImpl) UpdateAny(id uint, m map[string]any) error {
	return d.DB.Model(Task{}).Where("id = ?", id).Updates(m).Error
}

func (d *defaultTaskImpl) List(page, size int64, opts ...mysql.HandleFunc) ([]Task, int64, error) {
	query := d.DB.Model(&Task{})
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	var list = make([]Task, 0, size)
	if !mysql.IsPageAll(int(page), int(size)) {
		query.Count(&total).Scopes(mysql.PageLimit(int(page), int(size)))
	}
	err := query.Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}
