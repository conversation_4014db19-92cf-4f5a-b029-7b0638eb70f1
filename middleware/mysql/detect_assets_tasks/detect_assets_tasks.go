package detect_assets_tasks

import (
	"errors"
	"micro-service/middleware/mysql"

	"gorm.io/gorm"
)

type DetectAssetsTaskModel interface {
	Create(item *DetectAssetsTask) error
	FindAll(...mysql.HandleFunc) ([]DetectAssetsTask, error)
	First(...mysql.HandleFunc) (*DetectAssetsTask, error)
	Count(...mysql.HandleFunc) (int64, error)
	UpdateAny(m map[string]interface{}, opts ...mysql.HandleFunc) error
}

// 单位资产测绘任务表名称
const detectAssetsTaskTableName = "detect_assets_tasks"

const (
	StatusDefault           = iota // 默认
	StatusDoing                    // 进行中
	StatusFinished                 // 成功
	StatusExceptionFinished        // 异常成功
	StatusFailed                   // 失败
)

const (
	ExpandSourceDetect    = 0 // 扩展来源: 单位测绘
	ExpandSourceRecommend = 1 // 扩展来源: 云端推荐
	ModeSmart             = 1 // 智能模式
	ModeSmartPause        = 1 // 智能模式暂停
	ModeDefault           = 2 // 标准模式
	ModeExpert            = 3 // 专家模式
	ModeAuto              = 4 // 自动模式
	False0                = 0 // 否-0
	True                  = 1 // 是
	False2                = 2 // 否-2
	DetectTypeSpeed       = 1 // 极速模式
	DetectTypeDepth       = 2 // 深度模式
	ToDefault             = 1 // 离线资产去疑似
	ToTable               = 2 // 离线资产去台账
)

const (
	StepOne               = 1
	StepOneCompanyList    = 100
	StepTwo               = 2
	StepTwoConfirm        = 200
	StepTwoExpandDomain   = 201
	StepTwoExpandICP      = 202
	StepTwoExpandCert     = 203
	StepTwoExpandIP       = 206
	StepTwoExpandAll      = 204
	StepTwoFormAll        = 205
	StepThree             = 3
	StepThreeRecommendAll = 300
	StepThreeImport       = 301
	StepFour              = 4
	StepFourPredict       = 400
	StepFourScan          = 401
	StepFive              = 5
	StepFiveSync          = 500
	StepStatusDefault     = 0
	StepStatusDone        = 1 // 已完成
)

// 扩展步骤来源
const (
	ExpandFromDetect = 0
	ExpandFromCloud  = 1
)

type DetectAssetsTask struct {
	gorm.Model
	UserId                    uint64   `gorm:"column:user_id;type:bigint(20) unsigned;comment:用户id;NOT NULL" json:"user_id"`
	CompanyId                 uint64   `gorm:"column:company_id;type:bigint(20) unsigned;comment:企业ID" json:"company_id"`
	SafeUserId                uint64   `gorm:"column:safe_user_id;type:bigint(20) unsigned;comment:如果是安服给企业操作的任务，那么记录安服的用户id" json:"safe_user_id"`
	Name                      string   `gorm:"column:name;type:varchar(255);comment:任务名称/企业名称;NOT NULL" json:"name"`
	Step                      int      `gorm:"column:step;type:tinyint(4);default:1;comment:资产测绘步骤 1/2/3/4 第一步/二步/三步/四步;NOT NULL" json:"step"`
	StepDetail                int      `gorm:"column:step_detail;type:int(11);default:100;comment:资产测绘详细步骤 100/200/300/400 第一步/二步/三步/四步;NOT NULL" json:"step_detail"`
	DetectType                int      `gorm:"column:detect_type;type:tinyint(4);default:1;comment:线索模式 1/2 极速/深度;NOT NULL" json:"detect_type"`
	GroupId                   uint64   `gorm:"column:group_id;type:bigint(20) unsigned;comment:线索分组ID;NOT NULL" json:"group_id"`
	StepStatus                int      `gorm:"column:step_status;type:int(11);default:0;comment:状态值 0/1 未完成/已完成;NOT NULL" json:"step_status"`
	Status                    int      `gorm:"column:status;type:tinyint(4);default:0;comment:状态 0/1/2/3/4 默认/执行中/成功/异常成功/失败;NOT NULL" json:"status"`
	CompanyJSON               string   `gorm:"column:company_json;comment:企业JSON" json:"company_json"` // 测绘企业和控股比例
	ExpendFlags               string   `gorm:"column:expend_flags;type:varchar(255);comment:资产推荐flag,数组格式，存在4个任务，里面存的是foradar_recommend_record表的flag字段" json:"expend_flags"`
	ExpendProgress            float64  `gorm:"column:expend_progress;type:decimal(11,2);default:0.00;comment:资产推荐进度;NOT NULL" json:"expend_progress"`
	ClueProgress              float64  `gorm:"column:clue_progress;type:decimal(11,2);default:0.00;comment:极速线索扩展任务进度;NOT NULL" json:"clue_progress"`
	Progress                  float64  `gorm:"column:progress;type:decimal(11,2);default:0.00;comment:任务进度;NOT NULL" json:"progress"`
	UpdateAssetsLevelProgress float64  `gorm:"column:update_assets_level_progress;type:decimal(11,2);default:0.00;comment:资产定级进度;NOT NULL" json:"update_assets_level_progress"`
	ReturnJson                string   `gorm:"column:return_json;type:text;comment:资产测绘第二步线索返回ids" json:"return_json"`
	SureIpNum                 string   `gorm:"column:sure_ip_num;type:varchar(10);comment:台账的数量" json:"sure_ip_num"`
	UnsureIpNum               string   `gorm:"column:unsure_ip_num;type:varchar(10);comment:疑似资产的数量" json:"unsure_ip_num"`
	ThreatenIpNum             string   `gorm:"column:threaten_ip_num;type:varchar(10);comment:威胁资产的数量" json:"threaten_ip_num"`
	ExpendFirstStepClueNode   string   `gorm:"column:expend_first_step_clue_node;type:varchar(255);comment:单位测绘初始线索获取执行的节点名称，方便日志排查" json:"expend_first_step_clue_node"`
	IsExtractClue             int      `gorm:"column:is_extract_clue;type:tinyint(4);default:0;comment:是否自动提取线索 0/1  不自动提取/自动提取;NOT NULL" json:"is_extract_clue"`
	IsCheckRisk               int      `gorm:"column:is_check_risk;type:tinyint(4);default:0;comment:是否自动监测风险事件 0/1  不自动/自动;NOT NULL" json:"is_check_risk"`
	ExpandSource              int      `gorm:"column:expand_source;type:tinyint(4);default:0;comment:资产测绘扩展来源 0/1 资产测绘/云端推荐" json:"expand_source"`
	IsIntellectMode           int      `gorm:"column:is_intellect_mode;type:tinyint(4);default:0;comment:是否是智能模式 0/1 否/是" json:"is_intellect_mode"`
	IsIntellectFailed         int      `gorm:"column:is_intellect_failed;type:tinyint(4);default:0;comment:智能模式是否失败 0/1 否/是" json:"is_intellect_failed"`
	CluesCount                string   `gorm:"column:clues_count;type:varchar(10);comment:该场景的扩展资产用的线索数量" json:"clues_count"`
	UpdateLevelNode           string   `gorm:"column:update_level_node"`                                // 更新资产ABCD级别的node节点
	ConfirmCompanyList        string   `gorm:"column:confirm_company_list"`                             // 单位测绘所有做的测绘的企业名称集合，包含手动输入的初始线索对应的企业名称，数组格式，json字符串
	Bandwidth                 string   `gorm:"column:bandwidth"`                                        // 扫描带宽
	Percent                   string   `gorm:"column:percent"`                                          // 其他公司的控股比例，可以为空，如果有值，就测绘其他所有企业下的控股单位
	ImportSureIps             string   `gorm:"column:import_sure_ips"`                                  // 对比影子资产的ip源，json格式
	OffAssetsTo               int      `gorm:"column:off_assets_to"`                                    // 离线资产去向 1/2  疑似/台账
	UseFakeClue               int      `gorm:"column:use_fake_clue"`                                    // 是否使用icp盗用的线索推荐资产 1/是 0/不是
	ScanType                  int      `gorm:"column:scan_type"`                                        // 资产扫描的端口范围 0/1/2  列表端口/常用端口/全部端口
	IsAutoDomainBurst         int      `gorm:"column:is_auto_domain_brust"`                             // 是否自动域名爆破 0/1  不自动/自动
	IsAutoLeakAsset           int      `gorm:"column:is_auto_leak_assets"`                              // 是否自动获取数据泄露 0/1  不自动/自动
	IsAutoDataAsset           int      `gorm:"column:is_auto_data_assets"`                              // 是否自动获取数据资产 0/1  不自动/自动
	IsAutoURLApi              int      `gorm:"column:is_auto_url_api"`                                  // 是否自动去搜索引擎获取url-api资产 0/1  不自动/自动
	FofaRange                 int      `gorm:"column:fofa_range"`                                       // fofa资产的获取范围 0/1/2  全部/一年/半年
	IsShow                    int      `gorm:"column:is_show"`                                          // 是否展示任务简报 1/2  是/不是
	IsFinishCheckRisk         int      `gorm:"column:is_finish_check_risk"`                             // 是否完成风险事件检测 1/2  是/不是
	IsFinishShadowAssets      int      `gorm:"column:is_finish_shadow_assets"`                          // 是否完成影子资产检测 1/2  是/不是
	IsAutoBusinessApi         int      `gorm:"column:is_auto_business_api"`                             // 是否自动同步url资产到业务系统 0/1  不自动/自动
	IsNeedHunter              int      `gorm:"column:is_need_hunter"`                                   // 是否自动对接hunter的api 0/1  否/是
	IsNeedDnschecker          int      `gorm:"column:is_need_dnschecker"`                               // 这个测绘任务是否需要对接hunter的api 0/1  否/是
	IsAutoExpendIp            int      `gorm:"column:is_auto_expend_ip"`                                // 是否对接自动根据发现的推荐ip进行关联 已经发现的推荐资产ip里面的其他ip线索 0/1  否/是
	ScanTaskIDs               []uint64 `gorm:"-" json:"scan_task_ids"`                                  // 关联的资产扫描任务ID列表
	DomainTaskIDs             []uint64 `gorm:"-" json:"domain_task_ids"`                                // 关联的域名爆破任务ID列表
	IsFinishURLApi            int      `gorm:"column:is_finish_url_api"`                                // 这个测绘任务是否完成了搜索引擎获取url-api资产  1/是 2/否
	DetectMode                int      `gorm:"column:detect_mode;comment:测绘模式 0/1/2/3/4 无/智能/标准/专家/自动"` // 测绘模式 0/1/2/3/4 无/智能/标准/专家/自动
}

func (*DetectAssetsTask) TableName() string {
	return detectAssetsTaskTableName
}

type defaultDetectAssetsTask struct {
	*gorm.DB
	tableName string
}

func NewModel(conn ...*gorm.DB) DetectAssetsTaskModel {
	return &defaultDetectAssetsTask{
		DB:        mysql.GetDbClient(conn...),
		tableName: detectAssetsTaskTableName,
	}
}

func (d *defaultDetectAssetsTask) Create(item *DetectAssetsTask) error {
	return d.DB.Create(item).Error
}

func (d *defaultDetectAssetsTask) First(opts ...mysql.HandleFunc) (*DetectAssetsTask, error) {
	query := d.DB.Table(d.tableName)
	for _, f := range opts {
		f(query)
	}

	var info DetectAssetsTask
	err := query.First(&info).Error
	if err != nil {
		// 如果记录不存在不报错，返回空
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &info, nil
}

func (d *defaultDetectAssetsTask) Count(opts ...mysql.HandleFunc) (int64, error) {
	query := d.DB.Model(DetectAssetsTask{})

	for _, f := range opts {
		f(query)
	}

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return 0, err
	}

	return total, nil
}

// FindAll 根据查询条件获取所有查询记录（不分页）
func (d *defaultDetectAssetsTask) FindAll(opts ...mysql.HandleFunc) ([]DetectAssetsTask, error) {
	query := d.DB.Model(&DetectAssetsTask{})
	for _, f := range opts {
		f(query)
	}

	var list = make([]DetectAssetsTask, 0)
	err := query.Find(&list).Error
	if err != nil {
		return nil, err
	}

	return list, nil
}

// UpdateAny 更新记录
func (d *defaultDetectAssetsTask) UpdateAny(m map[string]interface{}, opts ...mysql.HandleFunc) error {
	query := d.DB.Model(&DetectAssetsTask{})
	for _, f := range opts {
		f(query)
	}
	return query.Updates(m).Error
}

// WithUserID 添加用户ID查询条件
func WithUserID(userID uint64) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("user_id = ?", userID)
	}
}

// WithID 添加ID查询条件
func WithID(id uint64) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("id = ?", id)
	}
}

// WithExpandSource 添加扩展来源查询条件
func WithExpandSource(expandSource int) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("expand_source = ?", expandSource)
	}
}

// WithStepLessThan 添加步骤小于指定值的查询条件
func WithStepLessThan(step int) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("step < ?", step)
	}
}

// WithStatusIn 添加状态在指定范围内的查询条件
func WithStatusIn(statuses []int) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("status IN ?", statuses)
	}
}

// WithGroupID 添加分组ID查询条件
func WithGroupID(groupID uint64) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("group_id = ?", groupID)
	}
}
