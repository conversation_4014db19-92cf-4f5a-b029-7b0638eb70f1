package icp_apps

import (
	"database/sql"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func Test_Equity_Save(t *testing.T) {
	initCfg()

	db := NewModel()
	// save
	l := []*Equity{
		{Name: "深圳市腾讯计算机系统有限公司", Icp: "粤B2-20090059", AppName: "", ParentId: 0, Type: 0, Status: 0},
		{Name: "深圳市腾讯计算机系统有限公司", Icp: "粤B2-20090059-1631X", AppName: "企鹅支教", ParentId: 0, Type: MiniProgram, Status: 1, RecordTime: sql.NullTime{Valid: true, Time: time.Now()}},
	}
	err := db.EquitySave(l)
	assert.Nil(t, err)

	// list
	list, _, err := db.EquityList(1, 2)
	assert.Nil(t, err)
	assert.Equal(t, 2, len(list))
}
