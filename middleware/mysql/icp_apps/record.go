package icp_apps

import (
	"gorm.io/gorm"

	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

const RecordTableName = "icp_apps_record"

type IcpAppModel interface {
	RecordCreate(record *Record) error
	RecordUpdate(Record) error
	RecordFirst(opts ...mysql.HandleFunc) (Record, error)

	EquityCreate(equity *Equity) error
	EquitySave([]*Equity) error
	EquityUpdate(Equity) error
	EquityFirst(opts ...mysql.HandleFunc) (Equity, error)
	EquityList(page, size int, opts ...mysql.HandleFunc) ([]*Equity, int64, error)

	UnregisteredFirst(search string, typeApp int) (UnregisteredIcpApp, error)
	UnregisteredUpsert(*UnregisteredIcpApp) error
}

type Record struct {
	dbx.Model
	Name     string `gorm:"column:name"`      // 查询关键字
	Type     int    `gorm:"column:type"`      // 查询类型
	EquityId uint64 `gorm:"column:equity_id"` // 备案记录ID
}

func (*Record) TableName() string {
	return RecordTableName
}

func NewModel(clients ...*gorm.DB) IcpAppModel {
	return &defaultModel{DB: mysql.GetDbClient(clients...)}
}

type defaultModel struct{ *gorm.DB }

func (d *defaultModel) RecordCreate(record *Record) error {
	return d.DB.Model(&Record{}).Create(&record).Error
}

func (d *defaultModel) RecordFirst(opts ...mysql.HandleFunc) (Record, error) {
	q := d.DB.Model(&Record{})
	for _, f := range opts {
		f(q)
	}

	var info Record
	err := q.First(&info).Error
	return info, err
}

func (d *defaultModel) RecordUpdate(record Record) error {
	return d.DB.Model(&record).Updates(record).Error
}
