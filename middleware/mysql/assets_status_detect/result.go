package assets_status_detect

import (
	"errors"
	"micro-service/middleware/mysql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const AssetsStatusDetectResultTabelName = "assets_status_detect_result"

// 资产状态检测结果表
type (
	AssetsStatusDetectResultModel interface {
		FindByTaskID(in uint64, onlineSattus int, Page, Size int) ([]AssetsStatusDetectResult, int64, error)
		Adds([]AssetsStatusDetectResult) error
	}

	defaultAssetsStatusDetectResultModel struct {
		*gorm.DB
		table string
	}

	AssetsStatusDetectResult struct {
		gorm.Model
		Ip          string `gorm:"column:ip;type:varchar(255);comment:ip;NOT NULL" json:"ip"`
		Port        int64  `gorm:"column:port;type:bigint(20);comment:port;" json:"port"`
		OnlineState int    `gorm:"column:online_state;type:tinyint(1);comment:1:在线，2:离线;NOT NULL" json:"online_state"`
		TaskId      uint64 `gorm:"column:task_id;type:bigint(20) unsigned;comment:资产状态检测任务表id;NOT NULL;Index:idx_task_id" json:"task_id"`
	}
)

func (m *AssetsStatusDetectResult) TableName() string {
	return AssetsStatusDetectResultTabelName
}

func NewAssetsStatusDetectResultModel(conn ...*gorm.DB) AssetsStatusDetectResultModel {
	return &defaultAssetsStatusDetectResultModel{mysql.GetDbClient(conn...), AssetsStatusDetectResultTabelName}
}

func Paginate(page, size int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		offset := (page - 1) * size
		if offset < 0 {
			offset = 0
		}
		return db.Offset(offset).Limit(size)
	}
}

func (d *defaultAssetsStatusDetectResultModel) FindByTaskID(in uint64, onlineSatus int, Page, Size int) (r []AssetsStatusDetectResult, total int64, e error) {
	if in == 0 {
		return nil, 0, errors.New("参数错误")
	}

	e = d.DB.Table(d.table).Where("task_id = ? and online_state = ?", in, onlineSatus).Order("id").Count(&total).Scopes(Paginate(Page, Size)).Find(&r).Error
	return
}

func (d *defaultAssetsStatusDetectResultModel) Adds(in []AssetsStatusDetectResult) error {
	return d.DB.Table(d.table).Clauses(clause.Insert{Modifier: "IGNORE"}).CreateInBatches(in, len(in)).Error
}
