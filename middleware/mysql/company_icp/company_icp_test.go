package company_icp

import (
	"fmt"
	"testing"

	"micro-service/initialize/mysql"
	mysql2 "micro-service/middleware/mysql"
	"micro-service/pkg/cfg"

	"github.com/stretchr/testify/assert"
)

func initCfg() {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())
}

func TestDefaultCompanyIcpModel_Updates(t *testing.T) {
	initCfg()

	icp := NewCompanyIcpModel()
	item, err := icp.First(mysql2.WithColumnValue("`id`", 3))
	assert.Nil(t, err)

	item.Name = "国家烟草专卖局"
	err = icp.Updates(item)
	assert.Nil(t, err)
}

func TestFindByIcp(t *testing.T) {
	initCfg()
	mainIcp, subIcps, err := NewCompanyIcpModel().FindOnlineByIcp("京ICP备18024709号")
	if err != nil {
		panic(err)
	}
	println(fmt.Sprintf("MainICP: %+v", mainIcp))
	println(fmt.Sprintf("SubICP: %+v", subIcps))
}

func TestFindByCompanyName(t *testing.T) {
	initCfg()
	mainIcp, subIcps, err := NewCompanyIcpModel().FindOnlineByCompanyName("北京华顺信安科技有限公司")
	if err != nil {
		panic(err)
	}
	println(fmt.Sprintf("MainICP: %+v", mainIcp))
	println(fmt.Sprintf("SubICP: %+v", subIcps))
}
