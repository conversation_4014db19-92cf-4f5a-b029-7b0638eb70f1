package url_status_detect

import (
	"errors"
	"micro-service/middleware/mysql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const UrlStatusDetectResultTabelName = "url_status_detect_result"

// 资产状态检测结果表
type (
	UrlStatusDetectResultModel interface {
		FindByTaskID(in uint64, Page, Size int) ([]UrlStatusDetectResult, int64, error)
		CountFindByTaskID(in uint64, onlineSattus int) (int64, error)
		Adds([]UrlStatusDetectResult) error
	}

	defaultUrlStatusDetectResultModel struct {
		*gorm.DB
		table string
	}

	UrlStatusDetectResult struct {
		gorm.Model
		Url         string `gorm:"column:url;type:varchar(255);comment:url;NOT NULL" json:"url"`
		Ip          string `gorm:"column:ip;type:varchar(255);comment:ip;NOT NULL" json:"ip"`
		Port        string `gorm:"column:port;type:varchar(255);comment:port;" json:"port"`
		Protocol    string `gorm:"column:protocol;type:varchar(255);comment:protocol;" json:"protocol"`
		OnlineState int    `gorm:"column:online_state;type:tinyint(1);comment:1:在线，2:离线;NOT NULL" json:"online_state"`
		Title       string `gorm:"column:title;type:text;comment:title;" json:"title"`
		StatusCode  string `gorm:"column:status_code;type:varchar(255);comment:状态码" json:"status_code"`
		TaskId      uint64 `gorm:"column:task_id;type:bigint(20) unsigned;comment:资产状态检测任务表id;NOT NULL;Index:idx_task_id" json:"task_id"`
	}
)

func (m *UrlStatusDetectResult) TableName() string {
	return UrlStatusDetectResultTabelName
}

func NewUrlStatusDetectResultModel(conn ...*gorm.DB) UrlStatusDetectResultModel {
	return &defaultUrlStatusDetectResultModel{mysql.GetDbClient(conn...), UrlStatusDetectResultTabelName}
}

func Paginate(page, size int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		offset := (page - 1) * size
		if offset < 0 {
			offset = 0
		}
		return db.Offset(offset).Limit(size)
	}
}

func (d *defaultUrlStatusDetectResultModel) FindByTaskID(in uint64, Page, Size int) (r []UrlStatusDetectResult, total int64, e error) {
	if in == 0 {
		return nil, 0, errors.New("参数错误")
	}

	e = d.DB.Table(d.table).Where("task_id = ? ", in).Order("id").Count(&total).Scopes(Paginate(Page, Size)).Find(&r).Error
	return
}

func (d *defaultUrlStatusDetectResultModel) CountFindByTaskID(in uint64, onlineSatus int) (int64, error) {
	query := d.DB.Model(UrlStatusDetectResult{})
	if in == 0 {
		return 0, errors.New("参数错误")
	}
	if in != 0 {
		query.Where("task_id = ?", in)
	}
	if onlineSatus != 0 {
		query.Where("online_state = ?", onlineSatus)
	}
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return 0, err
	}
	return total, nil
}

func (d *defaultUrlStatusDetectResultModel) Adds(in []UrlStatusDetectResult) error {
	return d.DB.Table(d.table).Clauses(clause.Insert{Modifier: "IGNORE"}).CreateInBatches(in, len(in)).Error
}
