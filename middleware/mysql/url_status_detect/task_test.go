package url_status_detect

import (
	"fmt"
	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func TestDefaultUrlStatusDetectTaskModel_FindByTaskID(t *testing.T) {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())

	//CreatedAtRange1 := "2023-02-26"
	//CreatedAtRange2 := "2023-02-27"
	date := []string{}
	//if CreatedAtRange1 != "" && CreatedAtRange2 != "" {
	//	date = []string{CreatedAtRange1, CreatedAtRange2}
	//}

	d, total, err := NewUrlStatusDetectTaskModel(mysql.GetInstance()).
		FindByUserID(5, 0, 1, 10, "", date)
	if err != nil {
		return
	}
	fmt.Println(d)
	fmt.Println(total)
}

func TestDefaultAssetsStatusDetectTaskModel_Add(t *testing.T) {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())

	r, err := NewUrlStatusDetectTaskModel(mysql.GetInstance()).Add(UrlStatusDetectTask{Total: 10, ProcessedData: 5, ProgressState: 1, OnlineAssets: 1, UnonlineAssets: 1, UserName: "yuui"})
	if err != nil {
		return
	}
	fmt.Println(r)
}

func TestDefaultAssetsStatusDetectTaskModel_Update(t *testing.T) {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())

	err := NewUrlStatusDetectTaskModel(mysql.GetInstance()).
		Update(UrlStatusDetectTask{
			Model:          gorm.Model{ID: 8},
			OnlineAssets:   1,
			UnonlineAssets: 1,
			UserName:       "yuui0"})
	if err != nil {
		return
	}
	fmt.Println("yes")
}

func TestDefaultAssetsStatusDetectTaskModel_HasTaskID(t *testing.T) {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())

	has, err := NewUrlStatusDetectTaskModel(mysql.GetInstance()).HasTaskID(5, 5)
	if err != nil {
		fmt.Println(err)
		return
	}

	if has {
		fmt.Println("yes")
	} else {
		fmt.Println("no")
	}
}

func TestDefaultAssetsStatusDetectTaskModel_Del(t *testing.T) {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())

	CreatedAtRange1 := "2023-02-25"
	CreatedAtRange2 := "2023-02-27"
	date := []string{}
	if CreatedAtRange1 != "" && CreatedAtRange2 != "" {
		date = []string{CreatedAtRange1, CreatedAtRange2}
	}

	err := NewUrlStatusDetectTaskModel(mysql.GetInstance()).Del([]uint{}, 559, date)
	if err != nil {
		return
	}
}

func TestDefaultAssetsStatusDetectTaskModel_Up(t *testing.T) {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())

	err := NewUrlStatusDetectTaskModel(mysql.GetInstance()).Update(UrlStatusDetectTask{
		Model:          gorm.Model{ID: 1},
		Total:          12,
		ProcessedData:  1,
		OnlineAssets:   1,
		UnonlineAssets: 1,
		UserName:       "1",
		UserId:         1,
		FilePath:       "1",
	})
	fmt.Println(err)
}

func TestDefaultAssetsStatusDetectTaskModel_Count(t *testing.T) {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())

	query := NewUrlStatusDetectTaskModel(mysql.GetInstance())
	_, err := query.Count(0, TaskProgressStateDone)

	assert.Equal(t, nil, err)
}
