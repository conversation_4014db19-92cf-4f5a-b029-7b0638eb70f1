package apps

import (
	"errors"
	"gorm.io/gorm"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

type (
	AppStoreIdModel interface {
		Create(companyName, id string) *AppStoreId
		FindByCompanyName(companyName string) *AppStoreId
	}

	defaultAppStoreIdModel struct {
		*gorm.DB
		table string
	}

	AppStoreId struct {
		dbx.Model
		CompanyName string `gorm:"type:varchar(512);Index:appstore_id_company_name;comment '企业名称'"`
		StoreId     string `gorm:"type:varchar(512);uniqueIndex;comment 'AppStoreId'"`
	}
)

const AppStoreIdTable = "appstore_ids"

// TableName 表名
func (h *AppStoreId) TableName() string {
	return AppStoreIdTable
}

func NewAppStoreIdModel(conn ...*gorm.DB) *defaultAppStoreIdModel {
	return &defaultAppStoreIdModel{mysql.GetDbClient(conn...), AppStoreIdTable}
}

func (d *defaultAppStoreIdModel) Create(companyName, id string) *AppStoreId {
	var appid AppStoreId
	d.DB.Table(d.table).Where("store_id", id).Attrs(AppStoreId{
		CompanyName: companyName,
		StoreId:     id,
	}).FirstOrCreate(&appid)
	return &appid
}

func (d *defaultAppStoreIdModel) FindByCompanyName(companyName string) *AppStoreId {
	var appid AppStoreId
	err := d.DB.Table(d.table).Where(AppStoreId{CompanyName: companyName}).First(&appid).Error
	if err != nil {
		return nil
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil
	}
	return &appid
}
