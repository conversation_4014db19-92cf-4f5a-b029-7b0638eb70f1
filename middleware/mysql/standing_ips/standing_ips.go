package standing_ips

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

const TableName = "standing_ips"

type StandingIps struct {
	dbx.Model
	UserId    uint64 `gorm:"column:user_id;type:bigint(20) unsigned;not null;comment:用户id"`
	CompanyId string `gorm:"column:company_id;type:varchar(256);not null;comment:企业ID"`
	Ip        string `gorm:"column:ip;type:varchar(256);not null;comment:ip"`
	Domain    string `gorm:"column:domain;type:varchar(256);not null;comment:域名"`
	Type      uint64 `gorm:"column:type;type:tinyint(4);not null;default:1;comment:IP类型: 1.IPv4 2.IPv6"`
}

type defaultStandingIpsModel struct {
	*gorm.DB
	table string
}

type StandingIpsModel interface {
	Create(standingIps *StandingIps) error
	GetByQuerys(opts ...mysql.HandleFunc) ([]*StandingIps, error)
	UpdateOrCreate(standingIps StandingIps) error
}

func (m *StandingIps) TableName() string {
	return TableName
}

func NewStandingIpsModel(conn ...*gorm.DB) StandingIpsModel {
	return &defaultStandingIpsModel{mysql.GetDbClient(conn...), TableName}
}

func (d *defaultStandingIpsModel) Create(standingIps *StandingIps) error {
	return d.DB.Create(&standingIps).Error
}

func (d *defaultStandingIpsModel) GetByQuerys(opts ...mysql.HandleFunc) ([]*StandingIps, error) {
	var standingIps []*StandingIps
	query := d.DB.Table(d.table)
	for _, f := range opts {
		f(query)
	}
	err := query.Find(&standingIps).Error
	if err != nil {
		return nil, err
	}
	return standingIps, nil
}

func (d *defaultStandingIpsModel) UpdateOrCreate(standingIps StandingIps) error {
	q := d.DB.Model(&standingIps)
	result := q.Where("user_id = ? AND ip = ?", standingIps.UserId, standingIps.Ip).Updates(standingIps)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected > 0 {
		return nil
	}
	return d.DB.Create(&standingIps).Error
}
