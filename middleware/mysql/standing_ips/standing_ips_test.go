package standing_ips

import (
	"errors"
	"testing"

	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	gmysql "gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func setupTestDB(t *testing.T) (*gorm.DB, sqlmock.Sqlmock, error) {
	db, mock, err := sqlmock.New()
	if err != nil {
		return nil, nil, err
	}

	gormDB, err := gorm.Open(gmysql.New(gmysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})

	return gormDB, mock, err
}

func TestStandingIpsModel_Create(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := NewStandingIpsModel(gormDB)

	tests := []struct {
		name    string
		ip      StandingIps
		mockSQL func()
		wantErr bool
	}{
		{
			name: "成功创建IP记录",
			ip: StandingIps{
				UserId:    1,
				CompanyId: "1",
				Ip:        "*******",
				Domain:    "test.com",
				Type:      1,
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("INSERT INTO `standing_ips`").WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name: "创建失败-数据库错误",
			ip: StandingIps{
				Ip:     "*******",
				Domain: "test.com",
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("INSERT INTO `standing_ips`").WillReturnError(errors.New("database error"))
				mock.ExpectRollback()
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSQL()
			err := model.Create(&tt.ip)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestStandingIpsModel_GetByQuerys(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := NewStandingIpsModel(gormDB)

	tests := []struct {
		name       string
		mockSQL    func()
		wantIps    []*StandingIps
		wantErr    bool
		setupQuery func() []mysql.HandleFunc
	}{
		{
			name: "查询成功",
			mockSQL: func() {
				rows := sqlmock.NewRows([]string{"id", "user_id", "company_id", "ip", "domain", "type"}).
					AddRow(1, 1, "1", "*******", "test1.com", 1).
					AddRow(2, 1, "1", "*******", "test2.com", 1)
				mock.ExpectQuery("SELECT (.+) FROM `standing_ips`").WillReturnRows(rows)
			},
			wantIps: []*StandingIps{
				{
					Model:     dbx.Model{Id: 1},
					UserId:    1,
					CompanyId: "1",
					Ip:        "*******",
					Domain:    "test1.com",
					Type:      1,
				},
				{
					Model:     dbx.Model{Id: 2},
					UserId:    1,
					CompanyId: "1",
					Ip:        "*******",
					Domain:    "test2.com",
					Type:      1,
				},
			},
			wantErr: false,
			setupQuery: func() []mysql.HandleFunc {
				return nil
			},
		},
		{
			name: "查询失败",
			mockSQL: func() {
				mock.ExpectQuery("SELECT (.+) FROM `standing_ips`").WillReturnError(errors.New("database error"))
			},
			wantIps: nil,
			wantErr: true,
			setupQuery: func() []mysql.HandleFunc {
				return nil
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSQL()
			got, err := model.GetByQuerys(tt.setupQuery()...)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, len(tt.wantIps), len(got))
				for i := range got {
					assert.Equal(t, tt.wantIps[i].Id, got[i].Id)
					assert.Equal(t, tt.wantIps[i].UserId, got[i].UserId)
					assert.Equal(t, tt.wantIps[i].CompanyId, got[i].CompanyId)
					assert.Equal(t, tt.wantIps[i].Ip, got[i].Ip)
					assert.Equal(t, tt.wantIps[i].Domain, got[i].Domain)
					assert.Equal(t, tt.wantIps[i].Type, got[i].Type)
				}
			}
		})
	}
}

func TestStandingIpsModel_UpdateOrCreate(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := NewStandingIpsModel(gormDB)

	tests := []struct {
		name    string
		ip      StandingIps
		mockSQL func()
		wantErr bool
	}{
		{
			name: "更新已存在记录",
			ip: StandingIps{
				UserId:    1,
				CompanyId: "1",
				Ip:        "*******",
				Domain:    "test.com",
				Type:      1,
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("UPDATE `standing_ips`").WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name: "创建新记录",
			ip: StandingIps{
				UserId:    1,
				CompanyId: "1",
				Ip:        "*******",
				Domain:    "test.com",
				Type:      1,
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("UPDATE `standing_ips`").WillReturnResult(sqlmock.NewResult(0, 0))
				mock.ExpectCommit()
				mock.ExpectBegin()
				mock.ExpectExec("INSERT INTO `standing_ips`").WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name: "更新失败",
			ip: StandingIps{
				UserId: 1,
				Ip:     "*******",
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("UPDATE `standing_ips`").WillReturnError(errors.New("database error"))
				mock.ExpectRollback()
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSQL()
			err := model.UpdateOrCreate(tt.ip)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
