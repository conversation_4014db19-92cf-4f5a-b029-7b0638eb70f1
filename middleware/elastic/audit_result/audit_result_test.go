package audit_result

import (
	"context"
	"fmt"
	"micro-service/initialize/es"
	"micro-service/pkg/cfg"

	"testing"

	"github.com/stretchr/testify/assert"
)

func Init() {
	cfg.InitLoadCfg()
	_ = es.GetInstance(cfg.LoadElastic())
}

func TestListAll(t *testing.T) {
	Init()

	list, err := NewAuditResultModel().ListAll(context.Background(), 1, 1)
	assert.Nil(t, err)
	fmt.Println(len(list))
}

func TestList(t *testing.T) {
	Init()

	list, total, err := NewAuditResultModel().List(context.Background(), 1, 1, 1, 1)
	assert.Nil(t, err)
	fmt.Println(len(list))
	fmt.Println(total)
}

func TestCreate(t *testing.T) {
	Init()

	portList := make([]ResultPort, 0)
	portList = append(portList, ResultPort{Port: 80, IsIncluded: 1, Protocol: "http"})
	portList = append(portList, ResultPort{Port: 443, IsIncluded: 2, Protocol: "https"})
	var list = make([]AuditResult, 0)
	list = append(list, AuditResult{
		Ip:     "localhost",
		TaskId: 1,
		Ports:  portList,
		Source: 1,
	})
	list = append(list, AuditResult{
		Ip:     "127.0.0.1",
		TaskId: 2,
		Ports:  portList,
		Source: 2,
	})
	list = append(list, AuditResult{
		Ip:     "127.0.0.1",
		TaskId: 1,
		Ports:  portList,
		Source: 1,
	})

	err := NewAuditResultModel().Create(context.Background(), list)
	assert.Nil(t, err)
}

func TestDelete(t *testing.T) {
	Init()

	err := NewAuditResultModel().Delete(context.Background(), []uint{2})
	assert.Nil(t, err)
}
