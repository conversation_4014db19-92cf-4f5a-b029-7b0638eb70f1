package elastic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"micro-service/initialize/es"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"reflect"
	"strings"
	"time"

	testcommon "micro-service/initialize/common_test"

	"github.com/olivere/elastic"
)

func GetEsClient(clients ...*elastic.Client) *elastic.Client {
	if testcommon.IsTest() {
		return testcommon.GetElasticClient()
	}
	if len(clients) > 0 {
		return clients[0]
	}
	return es.GetInstance()
}

type IpPort struct {
	Ip      string
	Port    string
	PortInt int
}

func GetSize(size int) int {
	if size <= 0 {
		size = 10
	}
	return size
}

func GetFrom(page, size int) int {
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}
	offset := (page - 1) * size
	if offset < 0 {
		offset = 0
	}
	return offset
}

// Delete 删除
func Delete[T any](query elastic.Query) error {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	if query == nil {
		return errors.New("query is nil")
	}
	_, err := GetEsClient().DeleteByQuery().Index(indexName).
		Query(query).Slices("auto").Pretty(true).Refresh("true").Do(context.TODO())
	if err != nil {
		return err
	}
	return nil
}

// First 查询单个
func First[T any](query *elastic.BoolQuery, sorts []elastic.Sorter, fields ...string) (*T, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	var data *T
	search := GetEsClient().Search(indexName)
	if len(fields) != 0 {
		search.FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...))
	}
	if len(sorts) != 0 {
		search = search.SortBy(sorts...)
	}
	result, err := search.Size(1).Query(query).Do(context.TODO())
	if err == io.EOF {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	// 检查是否有结果
	if result.TotalHits() > 0 {
		// 索引中的第一个文档
		data = ParseHitsValue[T](result.Hits.Hits)[0]
	}
	return data, nil
}

func GetById[T any](id string) (*T, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	search, err := GetEsClient().Get().Index(indexName).Id(id).Do(context.TODO())
	if err != nil {
		return nil, err
	}
	if search.Found {
		var record T
		err = json.Unmarshal(*search.Source, &record)
		if err != nil {
			return nil, err
		}
		return &record, nil
	}
	return nil, fmt.Errorf("record not found")
}
func GetCount(index string, query *elastic.BoolQuery) (int64, error) {
	total, err := GetEsClient().Count(index).Query(query).Do(context.TODO())
	if err != nil {
		return 0, err
	}
	return total, nil
}

// InsertOrUpdate 批量插入或更新
func InsertOrUpdate[T any](indexName, typeName string, l []T, supplementTime bool, batchSize int) (successCount int, errCount int, lastErrorInfo string) {
	// 快速返回
	if len(l) == 0 {
		return 0, 0, ""
	}
	if indexName == "" {
		indexFunc := reflect.ValueOf(new(T)).MethodByName("IndexName")
		if !indexFunc.IsValid() {
			return 0, 0, "结构体缺少IndexName方法"
		}
		indexName = indexFunc.Call([]reflect.Value{})[0].String()
	}
	if typeName == "" {
		typeFunc := reflect.ValueOf(new(T)).MethodByName("TypeName")
		if !typeFunc.IsValid() {
			return 0, 0, "结构体缺少TypeName方法"
		}
		typeName = typeFunc.Call([]reflect.Value{})[0].String()
	}
	// 成功总数
	successCount = 0
	// 失败数量
	errCount = 0
	// 失败信息
	lastErrorInfo = ""
	bulk := GetEsClient().Bulk()
	var now = time.Now().Format(time.DateTime)
	// 默认批量插入500条
	if batchSize <= 0 {
		batchSize = 500
	}
	// 分批插入
	totalBatches := (len(l) + batchSize - 1) / batchSize
	log.Debugf("InsertOrUpdate 开始分批处理: 总记录数=%d, 批次大小=%d, 总批次数=%d", len(l), batchSize, totalBatches)

	for j := 0; j < len(l); j += batchSize {
		end := j + batchSize
		if end > len(l) {
			end = len(l)
		}
		batch := l[j:end]
		currentBatch := (j / batchSize) + 1
		log.Debugf("InsertOrUpdate 处理第 %d/%d 批次: 范围 [%d:%d], 记录数=%d", currentBatch, totalBatches, j, end, len(batch))

		for i := range batch {
			// 计算在原数组中的实际索引
			actualIndex := j + i
			if supplementTime {
				itemValue := reflect.ValueOf(&l[actualIndex]).Elem()
				// 处理 CreatedAt 字段
				if createdAtField := itemValue.FieldByName("CreatedAt"); createdAtField.IsValid() && createdAtField.CanSet() {
					if shouldSetCreatedAt(createdAtField) {
						setTimeField(createdAtField, now)
					}
				}
				// 更新 UpdatedAt 字段
				if updatedAtField := itemValue.FieldByName("UpdatedAt"); updatedAtField.IsValid() && updatedAtField.CanSet() {
					setTimeField(updatedAtField, now)
				}
			}
			id := GetObjectID(l[actualIndex])
			doc := elastic.NewBulkIndexRequest().Index(indexName).Type(typeName).Id(id).Doc(l[actualIndex])
			bulk.Add(doc)

			// 添加调试日志，只打印前几个和后几个
			if actualIndex < 3 || actualIndex >= len(l)-3 || actualIndex%100 == 0 {
				log.Debugf("InsertOrUpdate 批次 %d: 添加记录 [%d] ID=%s", currentBatch, actualIndex, id)
			}
		}
		if bulk.NumberOfActions() > 0 {
			re, err := bulk.Refresh("true").Do(context.TODO())
			if err != nil {
				log.Errorf("InsertOrUpdate 第 %d 批次失败: %v, indexName: %s, typeName: %s, 范围: [%d:%d]", currentBatch, err, indexName, typeName, j, end)
				lastErrorInfo = fmt.Sprintf("batch %d failed, start index: %d, end index: %d, error: %v", currentBatch, j, end, err)
				errCount += len(batch) // 将整个批次标记为失败
				continue
			}

			// 每个item是一个map[string]*BulkResponseItem，因此我们需要迭代这个map
			// 无论re.Errors是否为true，都要检查每个操作的结果
			for _, item := range re.Items {
				for op, res := range item {
					if res.Error != nil {
						errCount++
						// 如果有错误，打印出错误信息
						log.Errorf("InsertOrUpdate bulk.Refresh error: %v, indexName: %s, typeName: %s,operation: %s, reason: %v", res.Error.Type, indexName, typeName, op, res.Error.Reason)
						lastErrorInfo = fmt.Sprintf("op:%s,reason:%s", op, res.Error.Reason)
					} else {
						successCount++
					}
				}
			}
		}
	}

	log.Debugf("InsertOrUpdate 完成: 总记录数=%d, 成功=%d, 失败=%d, 处理率=%.2f%%",
		len(l), successCount, errCount, float64(successCount+errCount)/float64(len(l))*100)

	return successCount, errCount, lastErrorInfo
}

func GetObjectID[T any](obj T) string {
	var value reflect.Value

	// 判断obj是否为指针类型
	if reflect.TypeOf(obj).Kind() == reflect.Ptr {
		value = reflect.ValueOf(obj).Elem()
	} else if reflect.TypeOf(obj).Kind() == reflect.Struct {
		value = reflect.ValueOf(obj)
	} else {
		return ""
	}

	// 尝试多种可能的ID字段名
	idFieldNames := []string{"ID", "Id", "id"}
	for _, fieldName := range idFieldNames {
		if field := value.FieldByName(fieldName); field.IsValid() && field.Kind() == reflect.String {
			return field.String()
		}
	}

	return ""
}

// InsertOrUpdateWithTime 插入或更新,并补充时间字段(CreatedAt,UpdatedAt),支持批量插入
// 补充时间字段需要使用反射，尽量自行设置并使用 InsertOrUpdate方法
// 方法自动处理批量逻辑
func InsertOrUpdateWithTime[T any](indexName, typeName string, l []T, batchSize int) (successCount int, errCount int, lastErrorInfo string) {
	return InsertOrUpdate(indexName, typeName, l, true, batchSize)
}

// UpdateByIds 批量更新
// 根据ID列表批量更新ES数据，更新内容通过m参数传入，所有ID都更新同一个内容
func UpdateByIds[T any](ctx context.Context, indexName string, typeName string, ids []string, m map[string]any) error {
	if len(ids) == 0 {
		return nil
	}
	// 如果indexName为空，则使用T的IndexName方法获取,避免一次反射调用
	if indexName == "" {
		indexName = reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	}
	// 如果typeName为空，则使用T的TypeName方法获取,避免一次反射调用
	if typeName == "" {
		typeName = reflect.ValueOf(new(T)).MethodByName("TypeName").Call([]reflect.Value{})[0].String()
	}
	// Create a BulkProcessor
	bulkProcessor, err := GetEsClient().BulkProcessor().
		Name("UpdateByIdsBulkProcessor").
		Workers(4).                     // Number of concurrent workers
		BulkActions(500).               // Commit after 500 actions
		BulkSize(5 << 20).              // or 5 MB
		FlushInterval(3 * time.Second). // or every 3 seconds
		Do(ctx)
	if err != nil {
		return fmt.Errorf("failed to create bulk processor: %w", err)
	}

	// Add update requests to the processor
	for _, id := range ids {
		req := elastic.NewBulkUpdateRequest().
			Index(indexName).
			Type(typeName).
			Id(id).
			Doc(m)
		bulkProcessor.Add(req)
	}

	// Close the processor and wait for all requests to complete
	err = bulkProcessor.Close()
	if err != nil {
		return fmt.Errorf("failed to close bulk processor: %w", err)
	}

	return nil
}

// UpdateMany 批量更新
// m: id -> 更新数据
func UpdateMany[T any](ctx context.Context, indexName, typeName string, m map[string]map[string]any) error {
	if len(m) == 0 {
		return nil
	}
	// 如果indexName为空，则使用T的IndexName方法获取,避免一次反射调用
	if indexName == "" {
		indexName = reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	}
	// 如果typeName为空，则使用T的TypeName方法获取,避免一次反射调用
	if typeName == "" {
		typeName = reflect.ValueOf(new(T)).MethodByName("TypeName").Call([]reflect.Value{})[0].String()
	}
	// Create a BulkProcessor
	bulkProcessor, err := GetEsClient().BulkProcessor().
		Name("UpdateManyBulkProcessor").
		Workers(4).                     // Number of concurrent workers
		BulkActions(500).               // Commit after 500 actions
		BulkSize(5 << 20).              // or 5 MB
		FlushInterval(3 * time.Second). // or every 3 seconds
		Do(ctx)
	if err != nil {
		return fmt.Errorf("failed to create bulk processor: %w", err)
	}

	// Add update requests to the processor
	for id, data := range m {
		req := elastic.NewBulkUpdateRequest().
			Index(indexName).
			Type(typeName).
			Id(id).
			Doc(data)
		bulkProcessor.Add(req)
	}

	// Close the processor and wait for all requests to complete
	err = bulkProcessor.Close()
	if err != nil {
		return fmt.Errorf("failed to close bulk processor: %w", err)
	}

	return nil
}

// ListSearchAfter 查询列表（使用 search_after 分页）
// 请注意排序必须有唯一的字段，否则会丢数据
// 如果 lastSortValues 为空，则从第一页开始查询
// 返回：总数、数据列表、最后一条数据的排序值、错误信息
func ListSearchAfter[T any](perPage int, query elastic.Query, sorts []elastic.Sorter, lastSortValues []interface{}, fields ...string) (int64, []*T, []interface{}, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()

	// 如果没有提供排序，使用默认排序
	if len(sorts) == 0 {
		sorts = []elastic.Sorter{elastic.NewFieldSort("updated_at").Desc(), elastic.NewFieldSort("created_at").Desc(), elastic.NewFieldSort("id").Desc()}
	}

	// 打印排序条件
	sortInfo := "["
	for i, sort := range sorts {
		src, _ := sort.Source()
		if i > 0 {
			sortInfo += ", "
		}
		sortInfo += utils.AnyToStr(src)
	}
	sortInfo += "]"
	log.Debugf("【ES查询调试】排序条件: %s\n", sortInfo)

	search := GetEsClient().Search(indexName)
	if len(fields) != 0 {
		search.FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...))
	}
	search = search.SortBy(sorts...)

	// 设置 search_after 参数
	if len(lastSortValues) > 0 {
		search = search.SearchAfter(lastSortValues...)
	}

	// 打印请求的每页条数和页码
	log.Debugf("【ES查询调试】开始引索: %v, 每页条数: %d, 索引: %s\n", lastSortValues, perPage, indexName)

	// 执行查询
	result, err := search.TrackTotalHits(true).Size(GetSize(perPage)).Query(query).Do(context.TODO())
	out, _ := query.Source()
	log.Debugf("【ES查询调试】查询条件: %s\n", utils.AnyToStr(out))

	if err == io.EOF {
		log.Debugf("【ES查询调试】查询结果为空 (EOF)\n")
		return 0, nil, nil, nil
	}
	if err != nil {
		return 0, nil, nil, err
	}

	list := ParseHitsValue[T](result.Hits.Hits)

	// 打印查询结果信息
	log.Debugf("【ES查询调试】总命中数: %d, 实际返回数量: %d, 请求的每页条数: %d\n", result.TotalHits(), len(list), perPage)

	// 如果返回的记录数少于请求的条数，打印警告
	if len(list) < GetSize(perPage) && result.TotalHits() > int64(len(list)) {
		log.Debugf("【ES查询警告】返回的记录数(%d)少于请求的条数(%d)，但总命中数为%d\n", len(list), GetSize(perPage), result.TotalHits())
	}

	// 获取最后一条数据的排序值
	var nextSortValues []interface{}
	if len(result.Hits.Hits) > 0 {
		nextSortValues = result.Hits.Hits[len(result.Hits.Hits)-1].Sort
	}

	return result.TotalHits(), list, nextSortValues, nil
}

// List 查询列表（使用 from 分页）
// 请注意排序必须有唯一的字段，否则会丢数据
// 返回：总数、数据列表、错误信息
func List[T any](page, perPage int, query elastic.Query, sorts []elastic.Sorter, fields ...string) (int64, []*T, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()

	// 如果没有提供排序，使用默认排序
	if len(sorts) == 0 {
		sorts = []elastic.Sorter{elastic.NewFieldSort("updated_at").Desc(), elastic.NewFieldSort("created_at").Desc(), elastic.NewFieldSort("id").Desc()}
	}

	// 打印排序条件
	sortInfo := "["
	for i, sort := range sorts {
		src, _ := sort.Source()
		if i > 0 {
			sortInfo += ", "
		}
		sortInfo += utils.AnyToStr(src)
	}
	sortInfo += "]"
	log.Infof("【ES查询调试】排序条件: %s\n", sortInfo)

	search := GetEsClient().Search(indexName)
	if len(fields) != 0 {
		search.FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...))
	}
	search = search.SortBy(sorts...)

	// 打印请求的每页条数和页码
	log.Infof("【ES查询调试】页码: %v, 每页条数: %d, 索引: %s\n", page, perPage, indexName)

	// 记录查询参数
	fromValue := GetFrom(page, perPage)
	sizeValue := GetSize(perPage)
	log.Infof("【ES查询调试】查询参数 - 索引: %s, from: %d, size: %d, page: %d, perPage: %d\n", indexName, fromValue, sizeValue, page, perPage)

	// 执行查询
	result, err := search.TrackTotalHits(true).From(fromValue).Size(sizeValue).Query(query).Do(context.TODO())
	out, _ := query.Source()
	log.Infof("【ES查询调试】查询条件: %s\n", utils.AnyToStr(out))

	if err == io.EOF {
		log.Infof("【ES查询调试】查询结果为空 (EOF)\n")
		return 0, nil, nil
	}
	if err != nil {
		log.Infof("【ES查询调试】查询失败: %v\n", err)
		return 0, nil, err
	}

	// 记录原始命中数据
	hitsCount := len(result.Hits.Hits)
	log.Infof("【ES查询调试】ES原始返回 - 总命中数: %d, 原始hits数量: %d\n", result.TotalHits(), hitsCount)

	list := ParseHitsValue[T](result.Hits.Hits)

	// 打印查询结果信息
	log.Infof("【ES查询调试】解析后结果 - 总命中数: %d, 解析成功数量: %d, 请求的每页条数: %d\n", result.TotalHits(), len(list), perPage)

	// 检查解析过程中是否有数据丢失
	if len(list) != hitsCount {
		log.Infof("【ES解析警告】解析过程中有数据丢失 - 原始hits: %d, 解析成功: %d\n", hitsCount, len(list))
	}

	// 如果返回的记录数少于请求的条数，打印警告
	if len(list) < GetSize(perPage) && result.TotalHits() > int64(len(list)) {
		log.Infof("【ES查询警告】返回的记录数(%d)少于请求的条数(%d)，但总命中数为%d\n", len(list), GetSize(perPage), result.TotalHits())
	}

	return result.TotalHits(), list, nil
}

// All 查询所有数据（使用 search_after 替代 Scroll API） 请注意排序必须有唯一的字段，否则会丢数据
func All[T any](perPage int, query elastic.Query, sorts []elastic.Sorter, fields ...string) ([]*T, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	if len(sorts) == 0 {
		sorts = []elastic.Sorter{elastic.NewFieldSort("_id").Desc()}
		//return nil, errors.New("sorts must be provided when using search_after")
	}
	if perPage <= 0 {
		perPage = 500
	}
	var lastSortValues []interface{}
	var list []*T

	for {
		// 每次循环新建 SearchService
		search := GetEsClient().Search().Index(indexName)
		search = search.SortBy(sorts...) // 必须重新设置排序
		if len(fields) != 0 {
			search = search.FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...))
		}

		// 设置 search_after 参数
		if len(lastSortValues) > 0 {
			search = search.SearchAfter(lastSortValues...)
		}

		// 执行查询
		result, err := search.Size(perPage).Query(query).Do(context.TODO())
		if err != nil {
			return nil, err
		}

		hits := result.Hits.Hits
		if len(hits) == 0 {
			break
		}
		// 添加当前批次数据
		list = append(list, ParseHitsValue[T](hits)...)
		if len(hits) < perPage {
			break
		}
		// 更新 search_after 参数为最后一个文档的排序值
		lastSortValues = hits[len(hits)-1].Sort // 需确保 SortValues 存在

	}

	return list, nil
}
func ParseHitsValue[T any](l []*elastic.SearchHit) []*T {
	var list = make([]*T, 0, len(l))
	var nullSourceCount, parseErrorCount int

	log.Infof("【ES解析调试】开始解析 %d 条记录\n", len(l))

	for i := range l {
		if l[i] == nil {
			log.Infof("【ES解析调试】第 %d 条记录为 nil\n", i)
			continue
		}
		if l[i].Source == nil {
			nullSourceCount++
			log.Infof("【ES解析调试】第 %d 条记录 Source 为 nil, 文档ID: %s\n", i, l[i].Id)
			continue
		}
		var record T
		err := json.Unmarshal(*l[i].Source, &record)
		if err != nil {
			parseErrorCount++
			// 添加更详细的错误日志，打印解析失败的详细信息和原始 JSON 数据
			jsonStr := string(*l[i].Source)

			// 先输出错误信息
			log.Errorf("【ES解析错误】第 %d 条记录解析失败: %v, 文档ID: %s, 索引: %s", i, err, l[i].Id, l[i].Index)

			// 分段输出 JSON 数据，避免日志截断
			if len(jsonStr) > 1000 {
				log.Errorf("【ES解析错误-JSON数据1】原始JSON前1000字符: %s", jsonStr[:1000])
				if len(jsonStr) > 2000 {
					log.Errorf("【ES解析错误-JSON数据2】原始JSON中间1000字符: %s", jsonStr[1000:2000])
					log.Errorf("【ES解析错误-JSON数据3】原始JSON后%d字符: %s", len(jsonStr)-2000, jsonStr[2000:])
				} else {
					log.Errorf("【ES解析错误-JSON数据2】原始JSON后%d字符: %s", len(jsonStr)-1000, jsonStr[1000:])
				}
			} else {
				log.Errorf("【ES解析错误-JSON数据】完整原始JSON: %s", jsonStr)
			}
			continue
		}
		list = append(list, &record)
	}

	log.Infof("【ES解析调试】解析完成 - 输入: %d, 成功: %d, 空Source: %d, 解析错误: %d\n",
		len(l), len(list), nullSourceCount, parseErrorCount)

	return list
}

// ParseQuery 将参数数组解析为 Elasticsearch 查询条件
func ParseQuery(params [][]interface{}) *elastic.BoolQuery {
	query := elastic.NewBoolQuery()

	for _, param := range params {
		if len(param) == 0 {
			continue
		}

		// 处理逻辑运算符
		if len(param) == 2 {
			cmd, ok := param[0].(string)
			if !ok {
				continue
			}

			switch strings.ToUpper(cmd) {
			case "MUST", "SHOULD", "MUST_NOT", "FILTER":
				if subParams, ok := param[1].([][]interface{}); ok {
					for _, subParam := range subParams {
						subQuery := parseNestedQuery(subParam)
						if subQuery != nil {
							switch strings.ToUpper(cmd) {
							case "MUST":
								query.Must(subQuery)
							case "SHOULD":
								query.Should(subQuery)
								query.MinimumNumberShouldMatch(1)
							case "MUST_NOT":
								query.MustNot(subQuery)
							case "FILTER":
								query.Filter(subQuery)
							}
						}
					}
				} else if subParam, ok := param[1].([]interface{}); ok {
					subQuery := parseNestedQuery(subParam)
					if subQuery != nil {
						switch strings.ToUpper(cmd) {
						case "MUST":
							query.Must(subQuery)
						case "SHOULD":
							query.Should(subQuery)
							query.MinimumNumberShouldMatch(1)
						case "MUST_NOT":
							query.MustNot(subQuery)
						case "FILTER":
							query.Filter(subQuery)
						}
					}
				}
				continue
			}
		}

		// 处理普通条件
		subQuery := parseCondition(param)
		if subQuery != nil {
			query.Must(subQuery)
		}
	}

	return query
}

// parseNestedQuery 解析嵌套查询条件
func parseNestedQuery(param []interface{}) elastic.Query {
	if len(param) == 0 {
		return nil
	}

	// 检查是否是嵌套的查询条件
	if len(param) == 2 {
		if cmd, ok := param[0].(string); ok {
			switch strings.ToUpper(cmd) {
			case "MUST", "SHOULD", "MUST_NOT", "FILTER":
				if subParams, ok := param[1].([][]interface{}); ok {
					// 创建新的 bool 查询来处理嵌套条件
					nestedQuery := elastic.NewBoolQuery()
					for _, subParam := range subParams {
						subQuery := parseNestedQuery(subParam)
						if subQuery != nil {
							switch strings.ToUpper(cmd) {
							case "MUST":
								nestedQuery.Must(subQuery)
							case "SHOULD":
								nestedQuery.Should(subQuery)
								nestedQuery.MinimumNumberShouldMatch(1)
							case "MUST_NOT":
								nestedQuery.MustNot(subQuery)
							case "FILTER":
								nestedQuery.Filter(subQuery)
							}
						}
					}
					return nestedQuery
				}
			}
		}
	} else if len(param) == 2 && param[0] == "MUST_NOT" {
		// 特殊处理 MUST_NOT 条件
		if subParams, ok := param[1].([]interface{}); ok && len(subParams) == 2 {
			field, ok := subParams[0].(string)
			if !ok {
				return nil
			}
			value := subParams[1]
			if strValue, ok := value.(string); ok && strings.ToUpper(strValue) == "EXISTS" {
				return elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery(field))
			}
		}
	}

	// 如果不是嵌套查询，则作为普通条件处理
	return parseCondition(param)
}

// parseCondition 解析单个查询条件
func parseCondition(param []interface{}) elastic.Query {
	if len(param) < 2 {
		return nil
	}

	field, ok := param[0].(string)
	if !ok {
		return nil
	}

	if len(param) == 3 {
		op, ok := param[1].(string)
		if !ok {
			return nil
		}
		value := param[2]

		switch op {
		case "match", "MATCH":
			if strValue, ok := value.(string); ok {
				return elastic.NewMatchQuery(field, strValue)
			}
		case "match_phrase", "MATCH_PHRASE":
			if strValue, ok := value.(string); ok {
				return elastic.NewMatchPhraseQuery(field, strValue)
			}
		case "in", "IN":
			switch vals := value.(type) {
			case []string:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []int:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []int32:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []int64:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []int8:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []int16:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []uint:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []uint32:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []uint8:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []uint64:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []uint16:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []float64:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []float32:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			case []interface{}:
				var interfaceValues []interface{}
				for _, val := range vals {
					interfaceValues = append(interfaceValues, val)
				}
				return elastic.NewTermsQuery(field, interfaceValues...)
			}
		case "like", "LIKE":
			if strValue, ok := value.(string); ok {
				return elastic.NewWildcardQuery(field, strValue)
			}
		case "prefix", "PREFIX":
			if strValue, ok := value.(string); ok {
				return elastic.NewPrefixQuery(field, strValue)
			}
		case "regexp", "REGEXP":
			if strValue, ok := value.(string); ok {
				return elastic.NewRegexpQuery(field, strValue)
			}
		case "exists", "EXISTS":
			return elastic.NewExistsQuery(field)
		case ">", ">=", "<", "<=":
			rangeQuery := elastic.NewRangeQuery(field)
			switch op {
			case ">":
				rangeQuery.Gt(value)
			case ">=":
				rangeQuery.Gte(value)
			case "<":
				rangeQuery.Lt(value)
			case "<=":
				rangeQuery.Lte(value)
			}
			return rangeQuery
		default:
			return elastic.NewTermQuery(field, value)
		}
	} else if len(param) == 4 {
		// 处理带参数的 match 查询
		op, ok := param[1].(string)
		if !ok {
			return nil
		}
		value := param[2]
		options, ok := param[3].(map[string]interface{})
		if !ok {
			return nil
		}

		switch op {
		case "match", "MATCH":
			if strValue, ok := value.(string); ok {
				matchQuery := elastic.NewMatchQuery(field, strValue)
				if operator, ok := options["operator"].(string); ok {
					matchQuery.Operator(operator)
				}
				if minimumShouldMatch, ok := options["minimum_should_match"].(int); ok {
					matchQuery.MinimumShouldMatch(fmt.Sprintf("%d", minimumShouldMatch))
				}
				if fuzziness, ok := options["fuzziness"].(string); ok {
					matchQuery.Fuzziness(fuzziness)
				}
				if boost, ok := options["boost"].(float64); ok {
					matchQuery.Boost(boost)
				}
				return matchQuery
			}
		case "match_phrase", "MATCH_PHRASE":
			if strValue, ok := value.(string); ok {
				matchPhraseQuery := elastic.NewMatchPhraseQuery(field, strValue)
				if slop, ok := options["slop"].(int); ok {
					matchPhraseQuery.Slop(slop)
				}
				if boost, ok := options["boost"].(float64); ok {
					matchPhraseQuery.Boost(boost)
				}
				return matchPhraseQuery
			}
		}
	} else {
		// 处理简单条件 [field, value]
		value := param[1]

		// 特殊处理 exists 查询（2参数格式）
		if strValue, ok := value.(string); ok && (strValue == "exists" || strValue == "EXISTS") {
			return elastic.NewExistsQuery(field)
		} else if values, ok := value.([]interface{}); ok {
			return elastic.NewTermsQuery(field, values...)
		} else {
			return elastic.NewTermQuery(field, value)
		}
	}

	return nil
}

// AllByParams 使用参数数组构建查询条件并查询所有数据
// 参数说明：
// - perPage: 每页大小
// - params: 查询参数数组，支持 ParseQuery 中定义的所有查询格式
// - sorts: 排序条件
// - fields: 需要返回的字段列表
//
// 示例：
// 1. 简单查询
//
//	params := [][]interface{}{
//	    {"status", 1},
//	}
//
// 2. 范围查询
//
//	params := [][]interface{}{
//	    {"age", ">=", 20},
//	}
//
// 3. 全文搜索
//
//	params := [][]interface{}{
//	    {"name", "match", "test"},
//	}
//
// 4. 复杂查询
//
//	params := [][]interface{}{
//	    {"MUST", [][]interface{}{
//	        {"status", 1},
//	        {"SHOULD", [][]interface{}{
//	            {"age", ">=", 20},
//	            {"tags", "tag1"},
//	        }},
//	    }},
//	}
//
// 5. 其他查询类型
//
//	params := [][]interface{}{
//	    {"name.keyword", "prefix", "test"},
//	    {"name", "exists"},
//	    {"status", "in", []interface{}{1, 2, 3}},
//	}
//
// 返回值：
// - []*T: 查询结果列表
// - error: 错误信息
func AllByParams[T any](perPage int, params [][]interface{}, sorts []elastic.Sorter, fields ...string) ([]*T, error) {
	query := ParseQuery(params)
	return All[T](perPage, query, sorts, fields...)
}

// ListSearchAfterByParams 使用参数数组构建查询条件并执行分页查询
// 参数说明：
// - page: 页码，从1开始
// - perPage: 每页大小
// - params: 查询参数数组，支持 ParseQuery 中定义的所有查询格式
// - sorts: 排序条件
// - lastSortValues: 上一页最后一条数据的排序值，用于 search_after 分页
// - fields: 需要返回的字段列表
//
// 示例：
// 1. 简单分页查询
//
//	params := [][]interface{}{
//	    {"status", 1},
//	}
//	sorts := []elastic.Sorter{
//	    elastic.NewFieldSort("age").Desc(),
//	    elastic.NewFieldSort("id").Desc(),
//	}
//	total, results, nextSortValues, err := ListSearchAfterByParams[Document](1, 10, params, sorts, nil)
//
// 2. 带字段选择的查询
//
//	params := [][]interface{}{
//	    {"status", 1},
//	}
//	fields := []string{"id", "name", "age"}
//	total, results, _, err := ListSearchAfterByParams[Document](1, 10, params, sorts, nil, fields...)
//
// 3. 复杂查询
//
//	params := [][]interface{}{
//	    {"MUST", [][]interface{}{
//	        {"status", 1},
//	        {"age", ">=", 18},
//	    }},
//	}
//
// 返回值：
// - int64: 总记录数
// - []*T: 当前页的数据列表
// - []interface{}: 当前页最后一条数据的排序值，用于下一页查询
// - error: 错误信息
func ListSearchAfterByParams[T any](perPage int, params [][]interface{}, sorts []elastic.Sorter, lastSortValues []interface{}, fields ...string) (int64, []*T, []interface{}, error) {
	query := ParseQuery(params)
	return ListSearchAfter[T](perPage, query, sorts, lastSortValues, fields...)
}

// ListByParams 使用From和Size分页的版本
func ListByParams[T any](page, perPage int, params [][]interface{}, sorts []elastic.Sorter, fields ...string) (int64, []*T, error) {
	query := ParseQuery(params)
	return List[T](page, perPage, query, sorts, fields...)
}

// CountByParams 使用参数数组构建查询条件并统计记录数量
// 参数说明：
// - params: 查询参数数组，支持 ParseQuery 中定义的所有查询格式
//
// 示例：
// 1. 简单条件统计
//
//	params := [][]interface{}{
//	    {"status", 1},
//	}
//	count, err := CountByParams[Document](params)
//
// 2. 范围查询统计
//
//	params := [][]interface{}{
//	    {"age", ">=", 20},
//	}
//
// 3. 复杂查询统计
//
//	params := [][]interface{}{
//	    {"MUST", [][]interface{}{
//	        {"status", 1},
//	        {"age", ">=", 18},
//	    }},
//	}
//
// 4. EXISTS 查询统计
//
//	params := [][]interface{}{
//	    {"name", "exists"},
//	}
//
// 5. MUST_NOT 查询统计
//
//	params := [][]interface{}{
//	    {"MUST_NOT", [][]interface{}{
//	        {"age", "<", 18},
//	    }},
//	}
//
// 6. 统计所有数据
//
//	params := [][]interface{}{} // 空条件
//
// 返回值：
// - int64: 符合条件的记录总数
// - error: 错误信息
func CountByParams[T any](params [][]interface{}) (int64, error) {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	query := ParseQuery(params)
	return GetCount(indexName, query)
}

// UpdateByParams 使用参数数组构建查询条件并更新文档
// 参数说明：
// - params: 查询参数数组，用于构建查询条件
// - updateData: 要更新的数据
//
// 示例：
// 1. 简单条件更新
//
//	params := [][]interface{}{
//	    {"status", 1},
//	}
//	updateData := map[string]interface{}{
//	    "status": 2,
//	    "updated_at": time.Now().Format(time.DateTime),
//	}
//	err := UpdateByParams[Document](params, updateData)
//
// 2. 范围查询更新
//
//	params := [][]interface{}{
//	    {"age", ">=", 20},
//	}
//	updateData := map[string]interface{}{
//	    "age": 21,
//	}
//
// 3. 复杂查询更新
//
//	params := [][]interface{}{
//	    {"MUST", [][]interface{}{
//	        {"status", 1},
//	        {"age", ">=", 18},
//	    }},
//	}
//
// 返回值：
// - error: 错误信息
func UpdateByParams[T any](params [][]interface{}, updateData map[string]interface{}) error {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	query := ParseQuery(params)

	// 添加更新时间
	if _, exists := updateData["updated_at"]; !exists {
		updateData["updated_at"] = time.Now().Format(time.DateTime)
	}

	// 构建更新脚本
	var scriptSource strings.Builder
	for field := range updateData {
		if scriptSource.Len() > 0 {
			scriptSource.WriteString("; ")
		}
		scriptSource.WriteString(fmt.Sprintf("ctx._source.%s = params.%s", field, field))
	}

	// 执行更新
	_, err := GetEsClient().UpdateByQuery(indexName).
		Query(query).
		Script(elastic.NewScript(scriptSource.String()).Params(updateData)).
		Refresh("true").
		Do(context.TODO())

	return err
}
func FirstByParams[T any](params [][]interface{}, fields ...string) (*T, error) {
	query := ParseQuery(params)
	return First[T](query, nil, fields...)
}

// StructToParams 将结构体转换为ES查询参数数组
func StructToParams(v interface{}, builder *SearchBuilder) {
	// 获取 v 的类型和值
	val := reflect.ValueOf(v)
	for val.Kind() == reflect.Ptr {
		val = val.Elem()
	}
	if val.Kind() != reflect.Struct {
		return
	}
	typeOfV := val.Type()
	for i := 0; i < typeOfV.NumField(); i++ {
		field := typeOfV.Field(i)
		if field.PkgPath != "" { // 私有字段
			continue
		}
		tag := field.Tag.Get("query")
		if tag == "-" {
			continue // 跳过查询标记为 "-" 的字段
		}
		value := val.Field(i)
		for value.Kind() == reflect.Ptr {
			value = value.Elem()
		}
		if !value.IsValid() || (value.Kind() == reflect.Ptr && value.IsNil()) || (value.Kind() == reflect.Slice && value.Len() == 0) {
			continue // 跳过 nil 或空切片
		}
		var query []interface{}
		query = append(query, getQueryFieldName(field))
		if len(strings.Split(tag, ",")) > 1 {
			// 如果有其他操作符或参数，添加到查询中
			operator := strings.Split(tag, ",")[1]
			switch strings.ToLower(operator) {
			case "bool":
				query = append(query, anyToBool(value.Interface()))
				builder.AddMust(query)
				continue
			case "range":
				if value.Kind() == reflect.Slice && value.Len() == 2 {
					fieldName := getQueryFieldName(field)
					t := parseRangeTime(value)
					if t != nil {
						// 为时间范围生成两个独立的查询条件
						builder.AddMust([]interface{}{fieldName, ">=", t[0]})
						builder.AddMust([]interface{}{fieldName, "<=", t[1]})
					} else {
						// 为普通范围生成两个独立的查询条件
						builder.AddMust([]interface{}{fieldName, ">=", value.Index(0).Interface()})
						builder.AddMust([]interface{}{fieldName, "<=", value.Index(1).Interface()})
					}
				}
				continue
			case "like":
				query = append(query, "like", WithRLAsterisk(value.String()))
				builder.AddMust(query)
				continue
			case "in":
				arr := ToInterfaceArray(value.Interface())
				if arr != nil {
					query = append(query, "in", arr)
					builder.AddMust(query)
				}
				continue
			default:
				query = append(query, operator)
			}
		}
		query = append(query, value.Interface())
		builder.AddMust(query)
	}
}

// getQueryFieldName 获取查询字段名
// 根据结构体字段的 tag 优先级顺序：query > form > json > 字段名
// 无tag时使用字段名的小写形式，tag为 "-" 时跳过该字段
func getQueryFieldName(field reflect.StructField) string {
	tag := field.Tag.Get("query")
	if tag != "" && tag != "-" {
		return strings.Split(tag, ",")[0]
	}
	formTag := field.Tag.Get("form")
	if formTag != "" && formTag != "-" {
		return strings.Split(formTag, ",")[0]
	}
	jsonTag := field.Tag.Get("json")
	if jsonTag != "" && jsonTag != "-" {
		return strings.Split(jsonTag, ",")[0]
	}
	return strings.ToLower(field.Name)
}

// anyToBool 将任意类型转换为布尔值
func anyToBool(value any) bool {
	switch v := value.(type) {
	case bool:
		return v
	case string:
		return strings.ToLower(v) == "true"
	case int:
		return v != 0
	case int8:
		return v != 0
	case int16:
		return v != 0
	case int32:
		return v != 0
	case int64:
		return v != 0
	case float32:
		return v != 0
	case float64:
		return v != 0
	default:
		return false
	}
}

func WithRLAsterisk(str string) string {
	if str == "" {
		return "*"
	}
	if !strings.HasPrefix(str, "*") {
		str = "*" + str
	}
	if !strings.HasSuffix(str, "*") {
		str = str + "*"
	}
	return str
}

func ToInterfaceArray(arr interface{}) []interface{} {
	v := reflect.ValueOf(arr)
	if v.Kind() != reflect.Array && v.Kind() != reflect.Slice {
		return nil
	}

	result := make([]interface{}, v.Len())
	for i := 0; i < v.Len(); i++ {
		result[i] = v.Index(i).Interface()
	}
	return result
}

func parseRangeTime(value reflect.Value) []string {
	if value.Kind() != reflect.Slice || value.Len() != 2 {
		return nil
	}
	if value.Index(0).Kind() != reflect.String || value.Index(1).Kind() != reflect.String {
		return nil
	}
	startTime, err1 := time.Parse(time.DateOnly, value.Index(0).Interface().(string))
	endTime, err2 := time.Parse(time.DateOnly, value.Index(1).Interface().(string))
	if err1 != nil || err2 != nil {
		return nil
	}
	endTime = endTime.Add(23*time.Hour + 59*time.Minute + 59*time.Second) // 设置结束时间为当天的最后一秒
	return []string{startTime.Format(time.DateTime), endTime.Format(time.DateTime)}
}

type SearchBuilder struct {
	must    [][]interface{}
	mustNot [][]interface{}
	should  [][]interface{}
	filter  [][]interface{}
}

func (s *SearchBuilder) AddMust(must ...[]interface{}) {
	s.must = append(s.must, must...)
}

func (s *SearchBuilder) AddMustNot(mustNot ...[]interface{}) {
	s.mustNot = append(s.mustNot, mustNot...)
}
func (s *SearchBuilder) AddShould(should ...[]interface{}) {
	s.should = append(s.should, should...)
}
func (s *SearchBuilder) AddFilter(filter ...[]interface{}) {
	s.filter = append(s.filter, filter...)
}
func (s *SearchBuilder) Build() [][]interface{} {
	q := make([][]interface{}, 0)
	if len(s.must) > 0 {
		q = append(q, []interface{}{"MUST", s.must})
	}
	if len(s.mustNot) > 0 {
		q = append(q, []interface{}{"MUST_NOT", s.mustNot})
	}
	if len(s.should) > 0 {
		q = append(q, []interface{}{"SHOULD", s.should})
	}
	if len(s.filter) > 0 {
		q = append(q, []interface{}{"FILTER", s.filter})
	}
	return q
}

// shouldSetCreatedAt 判断是否应该设置CreatedAt字段
func shouldSetCreatedAt(field reflect.Value) bool {
	// 处理指针类型
	if field.Kind() == reflect.Ptr {
		if field.IsNil() {
			return true // nil指针需要设置
		}
		field = field.Elem()
	}
	// 根据字段类型判断是否需要设置
	switch field.Kind() {
	case reflect.String:
		return field.String() == ""
	case reflect.Struct:
		// 检查是否是time.Time类型
		if field.Type() == reflect.TypeOf(time.Time{}) {
			t := field.Interface().(time.Time)
			return t.IsZero()
		}
	default:
		return false
	}
	return false
}

// setTimeField 设置时间字段的值
func setTimeField(field reflect.Value, timeStr string) {
	if !field.CanSet() {
		return
	}
	// 处理指针类型
	if field.Kind() == reflect.Ptr {
		// 如果是nil指针，创建新的实例
		if field.IsNil() {
			field.Set(reflect.New(field.Type().Elem()))
		}
		field = field.Elem()
	}
	// 根据字段类型设置值
	switch field.Kind() {
	case reflect.String:
		field.SetString(timeStr)
	case reflect.Struct:
		// 检查是否是time.Time类型
		if field.Type() == reflect.TypeOf(time.Time{}) {
			if t, err := time.Parse(time.DateTime, timeStr); err == nil {
				field.Set(reflect.ValueOf(t))
			}
		}
	default:
		// 不支持的类型，不做处理
	}
}
