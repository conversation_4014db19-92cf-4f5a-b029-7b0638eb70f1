package general_assets

import (
	"context"
	"micro-service/initialize/es"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
	"testing"

	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
)

func initCfg() {
	cfg.InitLoadCfg()
	_ = es.GetInstance(cfg.LoadElastic())
}

func TestAppendTaskIdByClueHash(t *testing.T) {
	initCfg()
	r, err := NewGeneralAssetModel().AppendTaskIdByClueHash(2, "84b2cc8deca793339a554a0b8f0356fa")
	if err != nil {
		println(err.Error())
	}
	println(r)
}
func Test_defaultGeneralAssetModel_List(t *testing.T) {
	initCfg()
	// 创建模型实例
	var taskId = []uint64{13}
	// 设置你希望查询的字段-查询port和domain字段，指定查询条件为domain不为null的数据，断言验证限制domain不为空，port不为空
	fields := []string{"ip", "port", "domain"}
	query := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("task_id", utils.ListColumn(taskId, func(t uint64) any { return t })...))
	query = query.Filter(elastic.NewExistsQuery("domain"))
	total, data, err := NewGeneralAssetModel().List(context.TODO(), query, 1, 10, fields)
	expectedTotal := 3 // 假设你期望的结果是 10
	assert.Equal(t, expectedTotal, total, "The total should be 10")
	assert.NoError(t, err, "List method should not return an error")
	// 检查返回的数据是否包含了你指定的所有字段
	if len(fields) > 0 && len(data) > 0 {
		for _, item := range data {
			assert.NotNil(t, item.Port)
			assert.NotEmpty(t, item.Domain)
		}
	}
	// 设置你希望查询的字段,查询所有字段,限制domain不为空，断言验证domain值不为空和端口不为空，ip不为空，port不为空
	fieldsNil := []string{}
	queryNil := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("task_id", utils.ListColumn(taskId, func(t uint64) any { return t })...))
	queryNil = queryNil.Filter(elastic.NewExistsQuery("domain"))
	_, dataNil, _ := NewGeneralAssetModel().List(context.TODO(), queryNil, 1, 10, fieldsNil)
	// 检查返回的数据是否包含了你指定的所有字段
	if len(dataNil) > 0 {
		for _, item := range dataNil {
			assert.NotNil(t, item.Port)
			assert.NotNil(t, item.Ip)
			assert.NotEmpty(t, item.Domain)
		}
	}
	//设置你希望查询的字段-查询ip字段，只指定查询条件为task_id为13，domain不为空的数据，断言验证domain值为空和port为空
	fieldsOnlyIp := []string{"ip"}
	queryNew := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("task_id", utils.ListColumn(taskId, func(t uint64) any { return t })...))
	queryNew = queryNew.Filter(elastic.NewExistsQuery("domain"))
	_, dataNew, _ := NewGeneralAssetModel().List(context.TODO(), queryNew, 1, 10, fieldsOnlyIp)
	// 检查返回的数据是否包含了你指定的所有字段
	if len(fieldsOnlyIp) > 0 && len(dataNew) > 0 {
		for _, itemNew := range dataNew {
			assert.Nil(t, itemNew.Port)
			assert.Empty(t, itemNew.Domain)
		}
	}
}
