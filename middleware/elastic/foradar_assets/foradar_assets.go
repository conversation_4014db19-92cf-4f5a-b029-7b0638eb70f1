package foradar_assets

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"runtime"
	"strconv"

	es "micro-service/middleware/elastic"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	jsoniter "github.com/json-iterator/go"
	"github.com/olivere/elastic"
	"github.com/spf13/cast"
)

const IndexName = "foradar_assets"
const TypeName = "ips"

type ForadarAssetModel interface {
	FindByIpPort(ctx context.Context, userId int, l []es.IpPort, status ...int) ([]ForadarAsset, error)
	Create(context.Context, []*ForadarAsset) ([]string, []string, error)
	UpdateWithMap(ctx context.Context, m []map[string]any) error // m["id"] not empty
	UpdateRuleTags(context.Context, []*ForadarAsset) error
	UpdateByQuery(ctx context.Context, query *elastic.BoolQuery, data map[string]any) error
	List(ctx context.Context, query *elastic.BoolQuery, page, prePage int) (int64, []ForadarAsset, error)
	ListAll(ctx context.Context, query *elastic.BoolQuery, fields ...string) ([]ForadarAsset, error)
	Count(ctx context.Context, query *elastic.BoolQuery) (int64, error)
	DeleteById(ctx context.Context, id string) error
	Upsert(context.Context, []*ForadarAsset) error
	DeleteArrFieldKeyByQuery(_ context.Context, query *elastic.BoolQuery, k string, v any) error
	QueryByAgg(ctx context.Context, fields []string, boolQuery *elastic.BoolQuery) (map[string][]any, error)
	DeleteByTaskId(ctx context.Context, taskId uint64) error
}

const (
	StatusSuspectedAsset = iota // 疑似资产
	StatusConfirmAsset          // 已确认资产
	StatusIgnoreAsset           // 忽略资产
	StatusThreatAsset           // 威胁资产
	StatusUploadAsset           // 导入资产
)

const (
	TypeClaimed = iota
	TypeRecommend
)

const (
	ThreatenTypeOther  = iota // 威胁类型: 其他
	ThreatenTypeDY            // 威胁类型: 钓鱼
	ThreatenTypeHDD           // 威胁类型: 黄赌毒
	ThreatenTypeFmICP         // 威胁类型: 仿冒ICP
	ThreatenTypeDomain        // 威胁类型: 域名混淆
)

// 台账状态
var StatusAccount = []int{StatusConfirmAsset, StatusUploadAsset}

const (
	OnlineStatusNO  = iota // 离线
	OnlineStatusYES        // 在线
)
const (
	// status 0 默认无任何状态 1 已认领(已确认) 2 忽略资产 3 威胁  4 上传的资产
	STATUS_DEFAULT  = 0
	STATUS_CLAIMED  = 1
	STATUS_IGNORE   = 2
	STATUS_THREATEN = 3
	STATUS_UPLOAD   = 4
)

type ForadarAsset struct {
	ID              string        `json:"id"` // doc id: md5()
	Fid             string        `json:"fid"`
	Reason          []AssetReason `json:"reason"`
	Cname           []string      `json:"cname"`
	TaskID          []int         `json:"task_id"`
	Cert            AssetCert     `json:"cert"`
	IsIpv6          bool          `json:"is_ipv6"`
	Screenshot      string        `json:"screenshot"`
	Type            int           `json:"type"`
	Title           any           `json:"title"` // type: string
	Body            string        `json:"body"`
	ClueCompanyName any           `json:"clue_company_name"` // should be []string
	Geo             AssetGeo      `json:"geo"`
	Protocol        string        `json:"protocol"`
	Icp             struct {
		Date        any    `json:"date"` // string
		No          string `json:"no"`
		CompanyName string `json:"company_name"`
		Type        string `json:"type"`
	} `json:"icp"`
	// ReliabilityScoreMap []struct { // 废弃字段
	// 	Num0     int `json:"0"`
	// 	Num1     int `json:"1"`
	// 	Num4     int `json:"4"`
	// 	Num99998 int `json:"99998"`
	// 	Num99999 int `json:"99999"`
	// } `json:"reliability_score_map"`
	Logo struct {
		Hash    any    `json:"hash"` // int
		Content string `json:"content"`
	} `json:"logo"`
	IsLogin             int       `json:"is_login"`
	CompanyID           int       `json:"company_id"`
	HTTPStatusCode      any       `json:"http_status_code"` // int
	Level               any       `json:"level"`            // int
	IsLoginPage         int       `json:"is_login_page"`
	Ip                  string    `json:"ip"`
	Banner              string    `json:"banner"`
	IsCopyright         bool      `json:"is_copyright"`
	Url                 string    `json:"url"`
	Tags                []int     `json:"tags"`
	DetectAssetsTasksID []int     `json:"detect_assets_tasks_id"`
	RuleTags            []RuleTag `json:"rule_tags"`
	OnlineState         any       `json:"online_state"` // int: 0-离线, 1-在线
	Port                any       `json:"port"`         // int
	UserID              int       `json:"user_id"`
	ReliabilityScore    int       `json:"reliability_score"`
	Domain              string    `json:"domain"`
	Subdomain           string    `json:"subdomain"`
	Header              string    `json:"header"`
	ThreatenType        any       `json:"threaten_type"` // int 威胁类型
	LoginScreenshot     string    `json:"login_screenshot"`
	IsFakeAssets        bool      `json:"is_fake_assets"`
	Status              any       `json:"status"` // int
	OpenParse           bool      `json:"open_parse"`
	CloudName           string    `json:"cloud_name"` // 云厂商名称
	CreatedAt           string    `json:"created_at"` // string, format: 2006-01-02 15:04:05
	UpdatedAt           string    `json:"updated_at"` // string, format: 2006-01-02 15:04:05
	HotPocId            []uint64  `json:"hot_poc_id"`
	EventId             []uint64  `json:"event_id"`
	DataID              []uint64  `json:"data_id"`
	ReasonString        string    `json:"reason_string"`
	AssetsSource        *int64    `json:"assets_source"`        // 对应ES mapping中的assets_source
	OneforallSource     string    `json:"oneforall_source"`     // 对应ES mapping中的oneforall_source
	SourceUpdatedAt     string    `json:"source_updated_at"`    // 对应ES mapping中的source_updated_at
	AssetsSourceDomain  string    `json:"assets_source_domain"` // 对应ES mapping中的assets_source_domain
	IsCdn               bool      `json:"is_cdn"`
}

type AssetCert struct {
	IssuerCn   string   `json:"issuer_cn"`
	CertDate   any      `json:"cert_date"`
	NotBefore  any      `json:"not_before"`
	NotAfter   any      `json:"not_after"`
	IssuerCns  []string `json:"issuer_cns"`
	Raw        string   `json:"raw"`
	CertNum    any      `json:"cert_num,omitempty"` // type: int
	SigAlth    string   `json:"sig_alth"`
	SubjectCn  string   `json:"subject_cn"`
	IssuerOrg  []string `json:"issuer_org"`
	V          string   `json:"v"`
	ValidType  string   `json:"valid_type"`
	Domain     string   `json:"domain"`
	IsValid    bool     `json:"is_valid"`
	Sn         string   `json:"sn"`
	SubjectKey string   `json:"subject_key"`
}

type RuleTag struct {
	CnProduct        string `json:"cn_product"`
	RuleID           string `json:"rule_id"`
	Product          string `json:"product"`
	CnCategory       string `json:"cn_category"`
	Level            string `json:"level"`
	ParentCategory   string `json:"parent_category"`
	Softhard         string `json:"softhard"`
	Company          string `json:"company"`
	CnParentCategory string `json:"cn_parent_category"`
	Category         string `json:"category"`
	CnCompany        string `json:"cn_company"`
}

type AssetGeo struct {
	Continent string `json:"continent"`
	Zip       string `json:"zip"`
	Country   string `json:"country"`
	City      string `json:"city"`
	Org       string `json:"org"`
	Isp       string `json:"isp"`
	As        string `json:"as"`
	Province  string `json:"province"`
	District  string `json:"district"`
	Asn       any    `json:"asn"` // type: int
	Lat       any    `json:"lat"`
	Lon       any    `json:"lon"`
	AsName    string `json:"as_name"`
}

type AssetReason struct {
	GroupID         int    `json:"group_id"`
	ID              int    `json:"id"`
	Source          int    `json:"source"`
	Type            int    `json:"type"`
	Content         string `json:"content"`
	ClueCompanyName any    `json:"clue_company_name"`
}

type defaultForadarAsset struct {
	*elastic.Client
	Type string
}

func (f *ForadarAsset) IndexName() string {
	return IndexName
}

func NewForadarAssetModel(clients ...*elastic.Client) ForadarAssetModel {
	return &defaultForadarAsset{
		Client: es.GetEsClient(clients...),
		Type:   "ips",
	}
}

func GenIndexId(ip, port, protocol, subdomain string, userId uint64) string {
	userIdStr := strconv.Itoa(int(userId))
	return utils.Md5sHash(ip+port+protocol+subdomain+userIdStr, false)
}

func (d *defaultForadarAsset) Create(ctx context.Context, l []*ForadarAsset) (successIds, failedIds []string, err error) {
	req := d.Client.Bulk().Index(IndexName)
	for i := range l {
		if l[i].ID == "" {
			l[i].ID = GenIndexId(l[i].Ip, cast.ToString(l[i].Port), l[i].Protocol, l[i].Subdomain, uint64(l[i].UserID))
		}
		l[i].CreatedAt = utils.CurrentTime()
		l[i].UpdatedAt = utils.CurrentTime()
		doc := elastic.NewBulkIndexRequest().Type(d.Type).Id(l[i].ID).Doc(l[i])
		req.Add(doc)
	}
	if req.NumberOfActions() == 0 {
		return nil, nil, nil
	}

	resp, err := req.Refresh("true").Do(ctx)
	if err != nil {
		return nil, nil, err
	}

	for _, failed := range resp.Failed() {
		failedIds = append(failedIds, failed.Id)
		s, _ := json.Marshal(failed.Error)
		log.Warnf("es index: %s insert failed, doc id: %s, err: %s\n", d.Index, failed.Id, failed, string(s))
	}
	for _, v := range resp.Succeeded() {
		successIds = append(successIds, v.Id)
	}

	return successIds, failedIds, nil
}

func listMarshal(l []*elastic.SearchHit) []ForadarAsset {
	var list = make([]ForadarAsset, 0, len(l))
	for i := range l {
		if l[i] == nil || l[i].Source == nil {
			continue
		}
		b, marshalErr := l[i].Source.MarshalJSON()
		if marshalErr != nil {
			continue
		}
		var record ForadarAsset
		err := json.Unmarshal(b, &record)
		if err != nil {
			continue
		}
		list = append(list, record)
	}
	return list
}
func (d *defaultForadarAsset) List(ctx context.Context, query *elastic.BoolQuery, page, prePage int) (int64, []ForadarAsset, error) {
	page = (page - 1) * prePage
	search := d.Client.Search(IndexName).Type(d.Type).SortBy(elastic.NewFieldSort("ip"), elastic.NewFieldSort("updated_at").Desc())
	result, err := search.From(page).Size(prePage).Query(query).Do(ctx)
	if err == io.EOF {
		return 0, nil, nil
	}
	if err != nil {
		return 0, nil, err
	}
	var list = make([]ForadarAsset, 0, int(result.TotalHits()))
	list = append(list, listMarshal(result.Hits.Hits)...)
	if err == io.EOF {
		return result.TotalHits(), list, nil
	}
	return result.TotalHits(), list, err
}

func (d *defaultForadarAsset) ListAll(ctx context.Context, query *elastic.BoolQuery, fields ...string) ([]ForadarAsset, error) {
	scroll := d.Client.Scroll().KeepAlive("1m")
	defer scroll.Clear(ctx)

	service := scroll.Index(IndexName).Type(d.Type).Query(query).Size(3000)
	if len(fields) > 0 {
		service.FetchSource(true).FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...))
	}
	result, err := service.Do(ctx)
	if err == io.EOF {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	var list = make([]ForadarAsset, 0, int(result.TotalHits()))
	list = append(list, listMarshal(result.Hits.Hits)...)
	scrollId := result.ScrollId
	for {
		result, err := d.Client.Scroll("1m").ScrollId(scrollId).Do(ctx)
		if err != nil {
			break
		}
		list = append(list, listMarshal(result.Hits.Hits)...)
	}
	if err == io.EOF {
		return list, nil
	}
	return list, err
}

func (d *defaultForadarAsset) FindByIpPort(ctx context.Context, userId int, l []es.IpPort, status ...int) ([]ForadarAsset, error) {
	query := elastic.NewBoolQuery()
	should := make([]elastic.Query, 0, len(l))
	for i := range l {
		must := elastic.NewBoolQuery()
		must.Must(elastic.NewTermQuery("user_id", userId))
		if l[i].Ip != "" {
			must.Must(elastic.NewTermQuery("ip.keyword", l[i].Ip))
		}
		if l[i].PortInt > 0 {
			must.Must(elastic.NewTermQuery("port", l[i].PortInt))
		}
		if len(status) > 0 {
			included := make([]any, 0)
			for _, value := range status {
				included = append(included, value)
			}
			must.Must(elastic.NewTermsQuery("status", included...))
		}
		should = append(should, must)
	}

	if len(l) == 0 && len(status) > 0 {
		included := make([]any, 0)
		for _, value := range status {
			included = append(included, value)
		}
		query.Must(elastic.NewTermsQuery("status", included...))
	}
	if len(should) > 0 {
		query.Should(should...)
	} else {
		query.Must(elastic.NewTermQuery("user_id", userId))
	}

	s, _ := query.Source()
	esQuery, _ := json.Marshal(s)
	fmt.Println("foradar_assets es query:", string(esQuery))

	do, err := d.Client.Scroll().Index(IndexName).Query(query).Scroll("5m").Size(4000).Do(ctx)
	if err == io.EOF {
		return nil, nil
	}
	if err != nil || do.Hits.TotalHits == 0 {
		return nil, err
	}

	listData := make([]ForadarAsset, 0, do.Hits.TotalHits)
	listData = append(listData, searchHitParseFunc(do.Hits.Hits)...)

	scrollId := do.ScrollId
	scrollService := d.Client.Scroll("5m").ScrollId(scrollId)
	searchResult := new(elastic.SearchResult)
	for {
		searchResult, err = scrollService.Do(ctx)
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}
		if err != nil || len(searchResult.Hits.Hits) <= 0 {
			break
		}

		listData = append(listData, searchHitParseFunc(searchResult.Hits.Hits)...)
	}

	return listData, err
}

func searchHitParseFunc(hits []*elastic.SearchHit) []ForadarAsset {
	var list = make([]ForadarAsset, 0, len(hits))
	for _, item := range hits {
		b, err := item.Source.MarshalJSON()
		if err != nil {
			continue
		}

		var record ForadarAsset
		if err = json.Unmarshal(b, &record); err != nil {
			_, file, line, _ := runtime.Caller(0)
			log.Warnf("es index: foradar_assets, meta: {ErrFile: %s, CodeLine: %d}, json.Unmarshal err: %v\n", file, line, err)
			continue
		}
		list = append(list, record)
	}

	return list
}

func (d *defaultForadarAsset) UpdateByQuery(_ context.Context, query *elastic.BoolQuery, data map[string]any) error {
	q := d.Client.UpdateByQuery().Type(d.Type).Index(IndexName).Query(query)
	sciprt := ""
	for k := range data {
		sciprt += fmt.Sprintf("ctx._source.%s = params.%s;", k, k)
	}
	q.Script(elastic.NewScript(sciprt).Params(data))
	// 构建更新请求
	_, err := q.Refresh("true").Do(context.TODO())
	if err != nil {
		return err
	}
	return nil
}

func (d *defaultForadarAsset) DeleteArrFieldKeyByQuery(_ context.Context, query *elastic.BoolQuery, k string, v any) error {
	q := d.Client.UpdateByQuery().Type(d.Type).Index(IndexName).Query(query)
	q.Script(elastic.NewScript(fmt.Sprintf(`
		if (ctx._source.containsKey('%s')) {
			def arrayField = ctx._source['%s'];
			if (arrayField != null && arrayField instanceof List) {
				arrayField.removeIf(item -> item == params.%s);
			}
		}
`, k, k, k)).Param(k, v))
	// 构建更新请求
	_, err := q.Refresh("true").Do(context.TODO())
	if err != nil {
		return err
	}
	return nil
}

func (d *defaultForadarAsset) UpdateWithMap(ctx context.Context, list []map[string]any) error {
	exist := func(m map[string]any) (string, bool) {
		idAny, ok := m["id"]
		if !ok {
			return "", false
		}
		id := idAny.(string)
		if id == "" {
			return "", false
		}
		return id, true
	}

	bulk := d.Client.Bulk()
	for i := range list {
		id, b := exist(list[i])
		if !b {
			continue
		}
		doc := elastic.NewBulkUpdateRequest().Index(IndexName).Type(d.Type).Id(id).Doc(list[i]).DocAsUpsert(true)
		bulk.Add(doc)
	}

	if bulk.NumberOfActions() <= 0 {
		return nil
	}

	result, err := bulk.Refresh("true").Do(ctx)
	if err != nil {
		return err
	}
	for i := range result.Failed() {
		fmt.Println(result.Failed()[i].Error)
	}
	return nil
}

func (d *defaultForadarAsset) UpdateRuleTags(ctx context.Context, l []*ForadarAsset) error {
	if len(l) == 0 {
		return nil
	}
	req := d.Client.Bulk().Index(IndexName)
	for i := range l {
		if l[i].ID == "" {
			continue
		}
		m := make(map[string]any)
		m["rule_tags"] = l[i].RuleTags
		doc := elastic.NewBulkIndexRequest().Type(d.Type).Id(l[i].ID).Doc(m)
		req.Add(doc)
	}

	_, err := req.Refresh("true").Do(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (d *defaultForadarAsset) DeleteById(ctx context.Context, id string) error {
	if id == "" {
		return nil
	}

	_, err := d.Client.Delete().Index(IndexName).Id(id).Refresh("true").Do(ctx)
	return err
}

func (d *defaultForadarAsset) QueryByAgg(ctx context.Context, fields []string, boolQuery *elastic.BoolQuery) (map[string][]any, error) {
	ret := make(map[string][]any, 0)
	query := d.Client.Search(IndexName).Type(d.Type).Query(boolQuery)
	if len(fields) == 0 {
		return ret, errors.New("fileds is empty")
	}
	for x := range fields {
		ret[fields[x]] = nil
		query = query.Aggregation(fields[x], elastic.NewTermsAggregation().Field(fields[x]).Size(1000000))
	}
	result, err := query.Do(context.TODO())
	if err != nil {
		return ret, err
	}
	for x := range fields {
		if agg, found := result.Aggregations.Terms(fields[x]); found {
			ret[fields[x]] = utils.ListColumn(agg.Buckets, func(t *elastic.AggregationBucketKeyItem) any {
				return t.Key
			})
		}
	}
	return ret, nil
}

func (d *defaultForadarAsset) Upsert(ctx context.Context, l []*ForadarAsset) error {
	bulkReq := d.Client.Bulk()
	for i := range l {
		var id string
		if l[i].CreatedAt == "" {
			l[i].CreatedAt = utils.CurrentTime()
		}
		l[i].UpdatedAt = utils.CurrentTime()
		doc := elastic.NewBulkUpdateRequest().
			Index(IndexName).Type(d.Type).Id(id).
			Doc(l[i]).DocAsUpsert(true)

		bulkReq.Add(doc)
	}

	if bulkReq.NumberOfActions() == 0 {
		return nil
	}

	rsp, err := bulkReq.Refresh("true").Do(ctx)
	if err != nil {
		return err
	}

	if len(rsp.Failed()) > 0 {
		s, _ := jsoniter.MarshalToString(rsp.Failed())
		return fmt.Errorf("some doc upsert failed, err: %s", s)
	}
	return nil
}

func (d *defaultForadarAsset) Count(ctx context.Context, query *elastic.BoolQuery) (int64, error) {
	result, err := d.Client.Count(IndexName).Type(d.Type).Query(query).Do(ctx)
	if err != nil {
		return 0, err
	}
	return result, nil
}

func (d *defaultForadarAsset) DeleteByTaskId(ctx context.Context, taskId uint64) error {
	// 构建 script 查询
	script := elastic.NewScript(`doc['task_id'].length == 1 && doc['task_id'][0] == params.value`).
		Lang("painless").
		Param("value", taskId)

	// 构建 query
	query := elastic.NewScriptQuery(script)

	_, err := d.Client.DeleteByQuery(IndexName).Size(500).Type(d.Type).Query(query).Do(ctx)
	if err != nil {
		return err
	}
	return nil
}
