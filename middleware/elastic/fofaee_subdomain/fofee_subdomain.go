package fofaee_subdomain

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	es "micro-service/middleware/elastic"
	"micro-service/pkg/log"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
)

const indexName = "fofaee_subdomain"

type FofeeSubdomainModel interface {
	FindByIpPort(list ...es.IpPort) ([]FofeeSubdomain, error)
	FindByIpPortWithUserIdAndRuleTags(ip string, userId uint64) ([]FofeeSubdomain, error)
	Updates(ctx context.Context, userId uint64, list ...*FofeeSubdomain) error
	Create(ctx context.Context, userId uint64, l []*FofeeSubdomain) (successIds, failedIds []string, err error)
	Upsert(ctx context.Context, userId uint64, l []*FofeeSubdomain) (successIds, failedIds []string, err error)
}

type P struct {
	P0  string `json:"0"`
	P1  string `json:"1"`
	P2  string `json:"2"`
	P3  string `json:"3"`
	P4  string `json:"4"`
	P5  string `json:"5"`
	P6  string `json:"6"`
	P7  string `json:"7"`
	P8  string `json:"8"`
	P9  string `json:"9"`
	P10 string `json:"10"`
	P11 string `json:"11"`
	P12 string `json:"12"`
	P13 string `json:"13"`
	P14 string `json:"14"`
	P15 string `json:"15"`
	P16 string `json:"16"`
	P17 string `json:"17"`
	P18 string `json:"18"`
	P19 string `json:"19"`
	P20 string `json:"20"`
	P21 string `json:"21"`
	P22 string `json:"22"`
	P23 string `json:"23"`
	P24 string `json:"24"`
	P25 string `json:"25"`
	P26 string `json:"26"`
	P27 string `json:"27"`
	P28 string `json:"28"`
	P29 string `json:"29"`
	P30 string `json:"30"`
	P31 string `json:"31"`
	P32 string `json:"32"`
	P33 string `json:"33"`
	P34 string `json:"34"`
	P35 string `json:"35"`
	P36 string `json:"36"`
	P37 string `json:"37"`
	P38 string `json:"38"`
	P39 string `json:"39"`
	P40 string `json:"40"`
	P41 string `json:"41"`
	P42 string `json:"42"`
	P43 string `json:"43"`
	P44 string `json:"44"`
	P45 string `json:"45"`
	P46 string `json:"46"`
	P47 string `json:"47"`
	P48 string `json:"48"`
	P49 string `json:"49"`
	P50 string `json:"50"`
	P51 string `json:"51"`
	P52 string `json:"52"`
	P53 string `json:"53"`
	P54 string `json:"54"`
	P55 string `json:"55"`
	P56 string `json:"56"`
	P57 string `json:"57"`
	P58 string `json:"58"`
	P59 string `json:"59"`
	P60 string `json:"60"`
	P61 string `json:"61"`
	P62 string `json:"62"`
	P63 string `json:"63"`
}

type Rule struct {
	Category         string `json:"category"`
	CnCategory       string `json:"cn_category"`
	CnCompany        string `json:"cn_company"`
	CnParentCategory string `json:"cn_parent_category"`
	CnProduct        string `json:"cn_product"`
	Company          string `json:"company"`
	Level            string `json:"level"`
	ParentCategory   string `json:"parent_category"`
	Product          string `json:"product"`
	RuleId           string `json:"rule_id"`
	SoftHard         string `json:"softhard"`
}

type Geo struct {
	CityName      string  `json:"city_name"`
	ContinentCode string  `json:"continent_code"`
	CountryCode2  string  `json:"country_code2"`
	CountryCode3  string  `json:"country_code3"`
	CountryName   string  `json:"country_name"`
	DmaCode       any     `json:"dma_code"` // type: int
	Latitude      float64 `json:"latitude"`
	Location      struct {
		Lat float64 `json:"lat"`
		Lon float64 `json:"lon"`
	} `json:"location"`
	Longitude      float64 `json:"longitude"`
	PostalCode     string  `json:"postal_code"`
	RealRegionName string  `json:"real_region_name"`
	RegionName     string  `json:"region_name"`
	Timezone       string  `json:"timezone"`
}

type FofeeSubdomain struct {
	Appserver []string `json:"appserver"`
	Asn       struct {
		AsNumber       any    `json:"as_number"` // type: int
		AsOrganization string `json:"as_organization"`
	} `json:"asn"`
	Cert string `json:"cert"`
	Body string `json:"body"`
	Dom  struct {
		Hash     string `json:"hash"`
		P        P      `json:"p"`
		ShashBit string `json:"shash_bit"`
		SimHash  string `json:"sim_hash"`
		TagCount int    `json:"tag_count"`
		TagLen   int    `json:"tag_len"`
	} `json:"dom"`
	Domain  string `json:"domain"`
	Favicon struct {
		Base64 string `json:"base64"`
		Hash   int64  `json:"hash"`
	} `json:"favicon"` // favicon object containing base64 and hash data
	Fid            string         `json:"fid"`
	GeoIp          Geo            `json:"geoip"`
	Header         string         `json:"header"`
	Host           string         `json:"host"`
	Ip             string         `json:"ip"`
	Port           any            `json:"port"` // type: int
	Ipcnet         string         `json:"ipcnet"`
	IsIpv6         bool           `json:"is_ipv6"`
	IsDomain       bool           `json:"isdomain"`
	Product        []string       `json:"product"`
	Protocol       string         `json:"protocol"`
	RuleTags       []Rule         `json:"rule_tags"`
	StatusCode     int            `json:"status_code"`
	V              int            `json:"v"`
	Server         string         `json:"server"`
	Subdomain      string         `json:"subdomain"`
	Title          string         `json:"title"`
	Version        []string       `json:"version"`
	LastCheckTime  string         `json:"lastchecktime"`  // format: YYYY-MM-dd HH:mm:ss
	LastUpdateTime string         `json:"lastupdatetime"` // format: YYYY-MM-dd HH:mm:ss
	Extra          map[string]any `json:"extra"`          // 扩展字段，包含user_id等信息
}

func (m *FofeeSubdomain) IndexName() string {
	return indexName
}

type defaultFofaeeSubdomainModel struct {
	*elastic.Client
	Type      string
	indexName string
}

func GenId(userId int, host string) string {
	return fmt.Sprintf("%d_%s", userId, host)
}

func NewFofeeSubdomainModel(clients ...*elastic.Client) FofeeSubdomainModel {
	return &defaultFofaeeSubdomainModel{
		Client:    es.GetEsClient(clients...),
		Type:      "subdomain",
		indexName: indexName,
	}
}

func marshalHandle(hits []*elastic.SearchHit) []FofeeSubdomain {
	var list = make([]FofeeSubdomain, 0, len(hits))
	for _, item := range hits {
		if item == nil || item.Source == nil {
			continue
		}
		b, err := item.Source.MarshalJSON()
		if err != nil {
			continue
		}
		var record FofeeSubdomain
		if err = json.Unmarshal(b, &record); err != nil {
			log.Errorf("ES index name: %s, json.Unmarshal: %+v\n", indexName, err)
			continue
		}
		list = append(list, record)
	}
	return list
}

func (d *defaultFofaeeSubdomainModel) Updates(ctx context.Context, userId uint64, list ...*FofeeSubdomain) error {
	req := d.Client.Bulk()
	for i := range list {
		id := GenId(cast.ToInt(userId), list[i].Host)
		doc := elastic.NewBulkUpdateRequest().Index(d.indexName).Type(d.Type).Id(id).Doc(list[i])
		req.Add(doc)
	}

	if req.NumberOfActions() <= 0 {
		return nil
	}
	do, err := req.Do(ctx)
	if err != nil {
		return err
	}

	if len(do.Failed()) > 0 {
		for _, v := range do.Failed() {
			log.Warnf("es index_name: %s, doc id: %s update failed: %s\n", v.Index, v.Id, v.Error.Reason)
		}
	}
	return nil
}

func (d *defaultFofaeeSubdomainModel) Create(ctx context.Context, userId uint64, l []*FofeeSubdomain) (successIds, failedIds []string, err error) {
	req := d.Client.Bulk().Index(d.indexName)
	for i := range l {
		id := GenId(cast.ToInt(userId), cast.ToString(l[i].Host))
		doc := elastic.NewBulkIndexRequest().Type(d.Type).Id(id).Doc(l[i])
		req.Add(doc)
	}

	if req.NumberOfActions() <= 0 {
		return nil, nil, nil
	}

	resp, err := req.Refresh("true").Do(ctx)
	if err != nil {
		return nil, nil, err
	}

	for _, failed := range resp.Failed() {
		failedIds = append(failedIds, failed.Id)
		s, _ := json.Marshal(failed.Error)
		log.Errorf("es index: %s insert failed, doc id: %s, err: %s\n", d.Index, failed.Id, failed, string(s))
	}
	for _, v := range resp.Succeeded() {
		successIds = append(successIds, v.Id)
	}

	return successIds, failedIds, nil
}

func (d *defaultFofaeeSubdomainModel) Upsert(ctx context.Context, userId uint64, l []*FofeeSubdomain) (successIds, failedIds []string, err error) {
	bulkReq := d.Client.Bulk()
	for i := range l {
		id := GenId(cast.ToInt(userId), cast.ToString(l[i].Host))
		doc := elastic.NewBulkUpdateRequest().Index(d.indexName).Type(d.Type).Id(id).Doc(l[i]).DocAsUpsert(true)
		bulkReq.Add(doc)
	}

	if bulkReq.NumberOfActions() <= 0 {
		return nil, nil, nil
	}

	resp, err := bulkReq.Refresh("true").Do(ctx)
	if err != nil {
		return nil, nil, err
	}

	for _, failed := range resp.Failed() {
		failedIds = append(failedIds, failed.Id)
		s, _ := json.Marshal(failed.Error)
		log.Errorf("es index: %s upsert failed, doc id: %s, err: %s\n", d.Index, failed.Id, failed, string(s))
	}
	for _, v := range resp.Succeeded() {
		successIds = append(successIds, v.Id)
	}

	return successIds, failedIds, nil
}
func (d *defaultFofaeeSubdomainModel) FindByIpPort(list ...es.IpPort) ([]FofeeSubdomain, error) {
	if len(list) == 0 {
		return nil, nil
	}

	query := elastic.NewBoolQuery()

	qs := make([]elastic.Query, 0)
	for i := range list {
		q := elastic.NewBoolQuery()
		q.Must(elastic.NewTermQuery("ip", list[i].Ip))
		if list[i].Port != "" {
			q.Must(elastic.NewTermQuery("port.keyword", list[i].Port))
		}
		qs = append(qs, q)
	}
	query.Should(qs...)

	s, _ := query.Source()
	esstr, _ := json.Marshal(s)
	fmt.Println("es query:", string(esstr))

	do, err := d.Client.Search().Index(d.indexName).Do(context.Background())
	if err == io.EOF {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	result := marshalHandle(do.Hits.Hits)
	return result, nil
}

// FindByIpPortWithUserIdAndRuleTags 根据IP、用户ID查询子域名资产，且必须有rule_tags
// 对应PHP: AssetSubdomain::query()->where('ip', $res['ip'])->where('extra.user_id',$user_id)->whereNotNull('rule_tags')->get(['rule_tags','port'])->toArray()
func (d *defaultFofaeeSubdomainModel) FindByIpPortWithUserIdAndRuleTags(ip string, userId uint64) ([]FofeeSubdomain, error) {
	if ip == "" || userId == 0 {
		return nil, nil
	}

	query := elastic.NewBoolQuery()

	// 添加IP条件 - 使用正确的字段名
	query.Must(elastic.NewTermQuery("ip", ip))

	// 添加用户ID条件 - 对应PHP的where('extra.user_id',$user_id)
	query.Must(elastic.NewMatchQuery("extra.user_id", cast.ToString(userId)))

	// 添加rule_tags不为空的条件 - 对应PHP的whereNotNull('rule_tags')
	query.Must(elastic.NewExistsQuery("rule_tags"))

	s, _ := query.Source()
	esstr, _ := json.Marshal(s)
	fmt.Println("fofaee_subdomain es query:", string(esstr))

	// 只返回rule_tags和port字段 - 对应PHP的get(['rule_tags','port'])
	do, err := d.Client.Search().
		Index(d.indexName).
		Query(query).
		Size(10000). // 设置较大的size值以返回所有匹配的数据
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include("rule_tags", "port")).
		Do(context.Background())
	if err == io.EOF {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	result := marshalHandle(do.Hits.Hits)
	return result, nil
}
