package response

import (
	"go-micro.dev/v4/errors"
)

// AsDecodeErr decode err to go-micro.errors.Error type.
// so err must generated by go-micro errors package.
// if err Decoded to Error and the Error code not in includedCodes list,
// return msg dont using it.
func DecodeMicroErr(err error, includedCodes ...int) (isCode bool, detail string) {
	e, ok := errors.As(err)
	if !ok {
		return false, "non-micro type error"
	}

	detail = e.Detail
	for i := range includedCodes {
		if includedCodes[i] == int(e.Code) {
			isCode = true
			break
		}
	}
	return isCode, detail
}

// GetMicroDetail return go-micro.errors.Error Detail
func GetMicroDetail(err error) string {
	return errors.Parse(err.Error()).Detail
}

// GetMicroDetail return go-micro.errors.Error Id (Service Name)
func GetMicroId(err error, defaultName string) string {
	if sn := errors.Parse(err.Error()).Id; sn != "" {
		return sn
	}
	return defaultName
}
