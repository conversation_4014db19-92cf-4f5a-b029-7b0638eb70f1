package upload

import (
	"encoding/json"
	"errors"
	"fmt"
	"path"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"

	"micro-service/pkg/utils"
)

const (
	FileAccessPathPrefix = "/api/v1/files/"
	RootStoragePath      = "/data/storage/"
	FileStoragePublicDir = "app/public/"
	filePathComFull      = RootStoragePath + FileStoragePublicDir
)

var uploadFileExtLimit = map[string]struct{}{
	".xlsx": {},
	".xls":  {},
	".zip":  {},
	".png":  {},
	".jpg":  {},
	".gif":  {},
	".jpeg": {},
	".doc":  {},
	".docx": {},
	".txt":  {},
	".rar":  {},
	".7z":   {},
	".gz":   {},
}

type FileResponse struct {
	Size int64  `json:"size,omitempty"`
	Path string `json:"path,omitempty"`
	Name string `json:"name,omitempty"`
	Ext  string `json:"ext,omitempty"`
}

const fileMaxSize = 50

func FileUpload(c *gin.Context) (FileResponse, error) {
	file, err := c.FormFile("file")
	if err != nil {
		return FileResponse{}, errors.New("上传文件无效")
	}

	fileExt := strings.ToLower(path.Ext(file.Filename)) // 文件后缀转为小写
	_, ok := uploadFileExtLimit[fileExt]
	if !ok {
		return FileResponse{}, errors.New("文件格式不在要求范围内")
	}

	// 限制大小
	if FileOverLimit(file.Size, fileMaxSize) {
		return FileResponse{}, fmt.Errorf("文件不能超过 %d M", fileMaxSize)
	}

	// 检查上传目录
	if !utils.IsDirExist(filePathComFull) {
		if mkdirErr := Mkdir(filePathComFull); mkdirErr != nil {
			return FileResponse{}, err
		}
	}

	// 保存文件至指定目录
	fileName := genFileName(file.Filename) + fileExt
	dst := filepath.Join(filePathComFull, fileName)
	err = c.SaveUploadedFile(file, dst)
	if err != nil {
		return FileResponse{}, err
	}

	filePath, _ := utils.LaravelEncrypt(utils.DownloadFile{
		Url:  filepath.Join(FileStoragePublicDir, fileName),
		Name: fileName,
	})

	return FileResponse{
		Path: filepath.Join(FileAccessPathPrefix, filePath) + fileExt,
		Name: fileName,
		Ext:  fileExt,
	}, nil
}

func GetUploadDecryptPath(uploadPath string) string {
	if info, err := UploadDecrypt(uploadPath); err != nil {
		return uploadPath
	} else {
		fmt.Println(fmt.Sprintf("%+v", info))
		return info.Url
	}
}

func UploadDecrypt(uploadPath string) (utils.DownloadFile, error) {
	uploadPath = strings.TrimPrefix(uploadPath, FileAccessPathPrefix)
	if index := strings.LastIndex(uploadPath, "."); index > 0 {
		uploadPath = uploadPath[:index]
	}

	s, err := utils.LaravelDecrypt(uploadPath)
	if err != nil {
		return utils.DownloadFile{}, err
	}

	var info utils.DownloadFile
	err = json.Unmarshal([]byte(s), &info)
	if err != nil {
		return utils.DownloadFile{}, err
	}

	return info, nil
}
