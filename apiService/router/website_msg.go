package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 站内信路由
func websiteMessageRouterRegister(r *gin.RouterGroup) {
	// 列表展示
	r.GET("/list", wrapper.Auth(api.WebsiteMessageList, scopeAll))
	// 标记已读
	r.PUT("/list", wrapper.Auth(api.WebsiteMessageRead, scopeAll))
	// 删除
	r.DELETE("/list", wrapper.Auth(api.WebsiteMessageDelete, scopeAll))
	// 策略展示
	r.GET("/notify", wrapper.Auth(api.WebsiteMessageNotify, scopeAll))
	// 策略更新
	r.POST("/notify", wrapper.Auth(api.WebsiteMessageUpdate, scopeAll))
	// 上次登录以来产生的新数据
	r.GET("/newlist", wrapper.Auth(api.WebsiteMessageNewList, scopeAll))
}
