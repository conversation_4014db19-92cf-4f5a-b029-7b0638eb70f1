package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 资产发现路由注册
func discoverRouterRegister(r *gin.RouterGroup) {
	// ------------------ 集团公司任务
	// 创建发现任务
	r.POST("/org", wrapper.Auth(api.DiscoverCreateGroupTask, ScopeDiscover))
	// 任务线索
	r.GET("/org/:company_name/clues", wrapper.Auth(api.DiscoverGroupClues, ScopeDiscover))
	// 任务结果
	r.GET("/org/:company_name", wrapper.Auth(api.DiscoverGroupResult, ScopeDiscover))
	// 任务进度
	r.GET("/org/:company_name/process", wrapper.Auth(api.DiscoverGroupProcess, ScopeDiscover))
	// 子公司列表
	r.GET("/org/:company_name/orgs", wrapper.Auth(api.DiscoverGroupOrgs, ScopeDiscover))
	// ------------------ 单公司任务
	// 创建发现任务
	r.POST("/", wrapper.Auth(api.DiscoverCreateTask, ScopeDiscover))
	// 任务线索
	r.GET("/:company_name/clues", wrapper.Auth(api.DiscoverClues, ScopeDiscover))
	// 任务结果
	r.GET("/:company_name", wrapper.Auth(api.DiscoverResult, ScopeDiscover))
	// 任务进度
	r.GET("/:company_name/process", wrapper.Auth(api.DiscoverProcess, ScopeDiscover))
}
