package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 禁扫IP路由
func forbidIpsRouterRegister(r *gin.RouterGroup) {
	// 禁扫IP列表
	r.GET("/ips", wrapper.Auth(api.ForbidIpsList, scopeAll))
	// 添加禁扫IP
	r.POST("/ips", wrapper.Auth(api.AddForbidIps, scopeAll))
	// 删除禁扫IP
	r.DELETE("/ips", wrapper.Auth(api.DeleteForbidIps, scopeAll))
	// 导入禁扫IP
	r.POST("/ips/upload", wrapper.Auth(api.ImportForbidIps, scopeAll))
}
