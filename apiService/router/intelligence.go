package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// IntelligenceRouterRegister 任务概览路由
func IntelligenceRouterRegister(r *gin.RouterGroup) {
	// 热点漏洞-列表
	r.GET("/company", wrapper.Auth(api.IntelligenceCompanyList, scopeIntelligenceManager))
	// 热点漏洞-列表
	r.GET("/hot-poc", wrapper.Auth(api.IntelligenceHotPocList, scopeIntelligenceManager))
	// 热点漏洞-级别数量
	r.GET("/hot-poc/count/level", wrapper.Auth(api.IntelligenceHotPocCountByLevel, scopeTenantOrSafe))
	// 热点漏洞-添加
	r.POST("/hot-poc", wrapper.Auth(api.IntelligenceHotPocCreate, scopeIntelligenceManager))
	// 热点漏洞-删除
	r.DELETE("/hot-poc", wrapper.Auth(api.IntelligenceHotPocDelete, scopeIntelligenceManager))
	// 热点漏洞-更新
	r.POST("/hot-poc/:id", wrapper.Auth(api.IntelligenceHotPocUpdate, scopeIntelligenceManager))
	// 用户-热点漏洞-列表
	r.GET("/hot-poc/user", wrapper.Auth(api.IntelligenceHotPocUserList, scopeAll+"|"+scopeTenantOrSafe))
	// 用户-热点漏洞-更新
	r.POST("/hot-poc/user", wrapper.Auth(api.IntelligenceHotPocUserUpdate, scopeAdminOrSafe))
	// 用户-热点漏洞-检测
	r.POST("/hot-poc/check", wrapper.Auth(api.IntelligenceHotPocCheck, scopeTenantOrSafe))
	// 用户-热点漏洞-资产详情
	r.GET("/hot-poc/:id/asset", wrapper.Auth(api.IntelligenceUserHotPocInfoAsset, scopeTenantOrSafe))
	// 用户-热点漏洞-资产详情-IP列表
	r.GET("/hot-poc/asset", wrapper.Auth(api.IntelligenceUserHotPocAssetList, scopeTenantOrSafe))
	// 用户-热点漏洞-资产详情-导出
	r.GET("/hot-poc/asset/export", wrapper.Auth(api.IntelligenceUserHotPocAssetExport, scopeTenantOrSafe))
	// 用户-热点漏洞-检测进度
	r.GET("/hot-poc/check/process", wrapper.Auth(api.IntelligenceHotPocUserMatchProcess, scopeTenantOrSafe))
	hotpocReportGroup := r.Group("/hot-poc/:id/report")
	{
		// 用户-热点漏洞-上传报告
		hotpocReportGroup.POST("/upload", wrapper.Auth(api.IntelligenceHotPocUploadReport, scopeAdminOrSafe))
		// 用户-热点漏洞-下载脱敏报告
		hotpocReportGroup.GET("/masked", wrapper.Auth(api.IntelligenceHotPocDownloadMaskedReport, scopeTenantOrSafe))
		// 用户-热点漏洞-下载原始报告
		hotpocReportGroup.GET("/original", wrapper.Auth(api.IntelligenceHotPocDownloadReport, scopeAdminOrSafe))
	}
	// 用户-热点漏洞-高级筛选数据源
	r.GET("/hot-poc/condition", wrapper.Auth(api.IntelligenceHotPocCondition, scopeTenantOrSafe))

	// 钓鱼仿冒-列表
	r.GET("/fake", wrapper.Auth(api.IntelligenceFakeList, scopeIntelligenceManager))
	// 钓鱼仿冒-添加
	r.POST("/fake", wrapper.Auth(api.IntelligenceFakeCreate, scopeIntelligenceManager))
	// 钓鱼仿冒-删除
	r.DELETE("/fake", wrapper.Auth(api.IntelligenceFakeDelete, scopeIntelligenceManager))
	// 钓鱼仿冒-更新
	r.POST("/fake/:id", wrapper.Auth(api.IntelligenceFakeUpdate, scopeIntelligenceManager))
	// 用户-钓鱼仿冒-列表
	r.GET("/fake/user", wrapper.Auth(api.IntelligenceFakeUserList, scopeTenantOrSafe))

	// 威胁情报-列表
	r.GET("/threat", wrapper.Auth(api.IntelligenceThreatList, scopeIntelligenceManager))
	// 威胁情报-添加
	r.POST("/threat", wrapper.Auth(api.IntelligenceThreatCreate, scopeIntelligenceManager))
	// 威胁情报-删除
	r.DELETE("/threat", wrapper.Auth(api.IntelligenceThreatDelete, scopeIntelligenceManager))
	// 威胁情报-更新
	r.POST("/threat/:id", wrapper.Auth(api.IntelligenceThreatUpdate, scopeIntelligenceManager))
	// 用户-威胁情报-列表
	r.GET("/threat/user", wrapper.Auth(api.IntelligenceThreatUserList, scopeTenantOrSafe))
	// 用户-威胁情报-一键匹配
	r.POST("/threat/match", wrapper.Auth(api.IntelligenceThreatUserMatch, scopeTenantOrSafe))
	// 用户-威胁情报-匹配进度
	r.GET("/threat/match/process", wrapper.Auth(api.IntelligenceThreatUserMatchProcess, scopeTenantOrSafe))

	// 其他情报-列表
	r.GET("/other", wrapper.Auth(api.IntelligenceOtherList, scopeIntelligenceManager))
	// 其他情报-添加
	r.POST("/other", wrapper.Auth(api.IntelligenceOtherCreate, scopeIntelligenceManager))
	// 其他情报-删除
	r.DELETE("/other", wrapper.Auth(api.IntelligenceOtherDelete, scopeIntelligenceManager))
	// 其他情报-更新
	r.POST("/other/:id", wrapper.Auth(api.IntelligenceOtherUpdate, scopeIntelligenceManager))
	// 用户-其他情报-列表
	r.GET("/other/user", wrapper.Auth(api.IntelligenceOtherUserList, scopeTenantOrSafe))

	// 事件专项-用户上次登录后新产生的事件专项数量
	r.GET("count/event/user", wrapper.Auth(api.IntelligenceEventCountByUser, scopeTenantOrSafe))
	// 事件专项-IP count
	r.GET("count/event/ip", wrapper.Auth(api.IntelligenceEventIPCount, scopeTenantOrSafe))
	// 事件专项-数量
	r.GET("count/event", wrapper.Auth(api.IntelligenceEventCount, scopeTenantOrSafe))
	// 事件专项-列表-情报中心
	r.GET("/event/user", wrapper.Auth(api.IntelligenceEventListForUser, scopeTenantOrSafe))
	// 事件专项-列表-情报管理
	r.GET("/event", wrapper.Auth(api.IntelligenceEventList, scopeIntelligenceManager))
	// 事件专项-列表-高级搜索条件数据源
	r.GET("event/condition", wrapper.Auth(api.IntelligenceEventCondition, scopeTenantOrSafe))
	// 事件专项-详情
	r.GET("/event/:id", wrapper.Auth(api.IntelligenceEventDetail, scopeTenantOrSafe))
	// 事件专项-所有类别
	r.GET("/event/category", wrapper.Auth(api.IntelligenceEventCategoryList, scopeTenantOrSafe))
	// 事件专项-修改类别
	r.PUT("/event/:id/category", wrapper.Auth(api.IntelligenceEventUpdateCategory, scopeIntelligenceManager))
	eventReportGroup := r.Group("/event/:id/report")
	{
		// 事件专项-上传报告
		eventReportGroup.POST("/upload", wrapper.Auth(api.IntelligenceEventUploadReport, scopeIntelligenceManager))
		// 事件专项-下载脱敏报告
		eventReportGroup.GET("/masked", wrapper.Auth(api.IntelligenceEventDownloadMaskedReport, scopeTenantOrSafe))
		// 事件专项-下载原始报告
		eventReportGroup.GET("/original", wrapper.Auth(api.IntelligenceEventDownloadReport, scopeIntelligenceManager))
	}
	// 事件专项-一键匹配
	r.POST("/event/match", wrapper.Auth(api.IntelligenceEventUserMatch, scopeTenantOrSafe))
	// 事件专项-匹配进度
	r.GET("/event/match/progress", wrapper.Auth(api.IntelligenceEventUserMatchProgress, scopeTenantOrSafe))
	// 事件专项-事件专项预警数据
	r.GET("/event/warning", wrapper.Auth(api.IntelligenceEventWarning, scopeTenantOrSafe))

	// 数据专项-数量
	r.GET("count/data", wrapper.Auth(api.IntelligenceDataCount, scopeTenantOrSafe))
	// 数据专项-汇总列表-情报中心
	r.GET("/dataSummary/user", wrapper.Auth(api.IntelligenceDataSummaryListForUser, scopeTenantOrSafe))
	// 数据专项-汇总列表-情报管理
	r.GET("/dataSummary", wrapper.Auth(api.IntelligenceDataSummaryList, scopeIntelligenceManager))
	// 数据专项-汇总列表-高级搜索条件数据源
	r.GET("dataSummary/condition", wrapper.Auth(api.IntelligenceDataSummaryCondition, scopeTenantOrSafe))
	// 数据专项-详情列表-情报中心
	r.GET("/dataSummary/:dataSummaryId/list/user", wrapper.Auth(api.IntelligenceDataListForUser, scopeTenantOrSafe))
	// 数据专项-详情列表-情报管理
	r.GET("/dataSummary/:dataSummaryId/list", wrapper.Auth(api.IntelligenceDataList, scopeIntelligenceManager))
	// 数据专项-一键匹配
	r.POST("/data/match", wrapper.Auth(api.IntelligenceDataUserMatch, scopeTenantOrSafe))
	// 数据专项-匹配进度
	r.GET("/data/match/progress", wrapper.Auth(api.IntelligenceDataUserMatchProgress, scopeTenantOrSafe))
	// 数据专项-详情
	r.GET("/data/:id", wrapper.Auth(api.IntelligenceDataDetail, scopeTenantOrSafe))
	dataGroup := r.Group("/data/:id/report")
	{
		// 数据专项-上传报告
		dataGroup.POST("/upload", wrapper.Auth(api.IntelligenceDataUploadReport, scopeIntelligenceManager))
		// 数据专项-下载脱敏报告
		dataGroup.GET("/masked", wrapper.Auth(api.IntelligenceDataDownloadMaskedReport, scopeTenantOrSafe))
		// 数据专项-下载原始报告
		dataGroup.GET("/original", wrapper.Auth(api.IntelligenceDataDownloadReport, scopeIntelligenceManager))
	}

	// 关联情报-列表
	r.GET("/related/list", wrapper.Auth(api.IntelligenceRelatedList, scopeTenantOrSafe))
	// 关联情报-导出
	r.GET("/related/export", wrapper.Auth(api.IntelligenceRelatedExport, scopeTenantOrSafe))
	// 关联情报-高级搜索条件数据源
	r.GET("/related/condition", wrapper.Auth(api.IntelligenceRelatedCondition, scopeTenantOrSafe))

	// 分类数量统计
	r.GET("count", wrapper.Auth(api.IntelligenceCount, scopeTenantOrSafe))
}
