package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 数字资产路由注册
func digitalRouterRegister(r *gin.RouterGroup) {
	// 数字资产-appstore
	r.GET("/appstore/:company_name", wrapper.Auth(api.DigitalAppstore, scopeClient))
	// 获取Apps列表
	r.POST("/apps", wrapper.Auth(api.GetAppsList, scopeAdmin))
	// 获取Apps列表
	r.POST("/wechat", wrapper.Auth(api.GetWechatList, scopeAdmin))
	// 搜狗微信-数字资产
	r.GET("/wechat/:keyword", wrapper.Auth(api.DigitalSougouWeChat, scopeClient))

	// 数字资产-微信公众号 任务模式
	wechatRouter := r.Group("/wechat/task")
	wechatRouter.POST("/", wrapper.Auth(api.DigitalWechatTaskUpsert, scopeClient+"|"+scopeAll))               // 新建任务
	wechatRouter.GET("/:task_id", wrapper.Auth(api.DigitalWechatTaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	wechatRouter.GET("/result/:task_id", wrapper.Auth(api.DigitalWechatTaskResult, scopeClient+"|"+scopeAll)) // 任务结果

	// 数字资产-Android app
	r.POST("/android_app", wrapper.Auth(api.DigitalAndroidAppTaskCreate, scopeClient+"|"+scopeAll))                // 新建任务
	r.GET("/android_app/:task_id", wrapper.Auth(api.DigitalAndroidAppTaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	r.GET("/android_app/result/:task_id", wrapper.Auth(api.DigitalAndroidAppTaskResult, scopeClient+"|"+scopeAll)) // 任务结果

	// 数字资产-小程序
	miniappRouter := r.Group("/mini_app/task")
	miniappRouter.POST("/", wrapper.Auth(api.DigitalMiniAppTaskCreate, scopeClient+"|"+scopeAll))               // 新建任务
	miniappRouter.GET("/:task_id", wrapper.Auth(api.DigitalMiniAppTaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	miniappRouter.GET("/result/:task_id", wrapper.Auth(api.DigitalMiniAppTaskResult, scopeClient+"|"+scopeAll)) // 任务结果
}
