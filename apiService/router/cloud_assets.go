package router

import (
	"github.com/gin-gonic/gin"
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"
)

// CloudAssetsRouterRegister 资产总库: 注册路由
func CloudAssetsRouterRegister(r *gin.RouterGroup) {
	// 添加推荐任务
	r.POST("/task", wrapper.Auth(api.AddRecommendTask, scopeClient))
	// 获取推荐任务进度
	r.GET("/task/process/:id", wrapper.Auth(api.GetRecommendProcess, scopeClient))
	// 获取推荐任务结果
	r.GET("/task/:id", wrapper.Auth(api.GetRecommendResult, scopeClient))
}
