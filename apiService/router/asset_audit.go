package router

import (
	"github.com/gin-gonic/gin"

	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"
)

// 资产核查功能: 注册路由
func assetAuditRouterRegister(r *gin.RouterGroup) {
	// 检查核查任务上传文件
	r.GET("/file_check", wrapper.Auth(api.AssetAuditFileCheck, scopeTenantOrSafe))
	// 新建核查任务
	r.POST("/task/create", wrapper.Auth(api.AssetAuditTaskCreate, scopeTenantOrSafe))
	// 获取核查任务列表
	r.GET("/task/list", wrapper.Auth(api.AssetAuditTaskList, scopeTenantOrSafe))
	// 删除核查任务
	r.DELETE("/task/delete", wrapper.Auth(api.AssetAuditTaskDelete, scopeTenantOrSafe))
	// websocket获取核查任务进度
	r.GET("/task/progress", wrapper.Auth(api.AssetAuditTaskProgress, scopeTenantOrSafe))
	// 获取核查结果列表
	r.GET("/result/list", wrapper.Auth(api.AssetAuditTaskResultList, scopeTenantOrSafe))
	// 下载核查结果
	r.GET("/result/download", wrapper.Auth(api.AssetAuditTaskDownload, scopeTenantOrSafe))
	// 同步核查结果至台账
	r.POST("/result/sync_assets", wrapper.Auth(api.AssetAuditResultSync, scopeTenantOrSafe))
	// 更新任务为完成
	r.PUT("/task/finished", wrapper.Auth(api.AssetAuditTaskFinished, scopeTenantOrSafe))
}
