package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 数据泄露路由注册
func dlpRouterRegister(r *gin.RouterGroup) {
	// 数据泄露-Github-代码片段搜索
	r.POST("/github/search/code", wrapper.Auth(api.DlpGitHubByCode, scopeClient))
	// GitHub任务模式
	r.POST("/github/task", wrapper.Auth(api.DlpGitHubSearchByTask, scopeClient+"|"+scopeAll))              // 任务创建
	r.GET("/github/task/:task_id", wrapper.Auth(api.DlpGitHubTaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	r.GET("/github/task/result/:task_id", wrapper.Auth(api.DlpGitHubTaskResult, scopeClient+"|"+scopeAll)) // 任务结果

	// 数据泄露-Gitee-Repo搜索
	r.POST("/gitee/search/repo", wrapper.Auth(api.DlpGiteeByRepo, scopeClient))
	// Gitee任务模式
	r.POST("/gitee/task", wrapper.Auth(api.DlpGiteeSearchByTask, scopeClient+"|"+scopeAll))              // 任务创建
	r.GET("/gitee/task/:task_id", wrapper.Auth(api.DlpGiteeTaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	r.GET("/gitee/task/result/:task_id", wrapper.Auth(api.DlpGiteeTaskResult, scopeClient+"|"+scopeAll)) // 任务结果

	// 数据泄露: 百度文库
	// 代码片段搜索
	r.GET("/baidu/:keyword", wrapper.Auth(api.DlpBaiduLibraryByKeyword, scopeClient))
	// 百度文库任务模式
	r.POST("/baidu/task", wrapper.Auth(api.DlpBaiduLibrarySearchByTask, scopeClient+"|"+scopeAll))              // 任务创建
	r.GET("/baidu/task/:task_id", wrapper.Auth(api.DlpBaiduLibraryTaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	r.GET("/baidu/task/result/:task_id", wrapper.Auth(api.DlpBaiduLibraryTaskResult, scopeClient+"|"+scopeAll)) // 任务结果

	// 数据泄露: 网盘
	// 56网盘搜索
	r.POST("/net_disk/56pan", wrapper.Auth(api.Dlp56panByKeyword, scopeClient))
	// 56网盘任务模式
	r.POST("/56pan/task", wrapper.Auth(api.Dlp56panSearchByTask, scopeClient+"|"+scopeAll))              // 任务创建
	r.GET("/56pan/task/:task_id", wrapper.Auth(api.Dlp56panTaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	r.GET("/56pan/task/result/:task_id", wrapper.Auth(api.Dlp56panTaskResult, scopeClient+"|"+scopeAll)) // 任务结果
	// PanSoso
	r.POST("/net_disk/pansoso", wrapper.Auth(api.DlpPanSoSoByKeyword, scopeClient))
	// PanSoso任务模式
	r.POST("/pansoso/task", wrapper.Auth(api.DlpPanSosoSearchByTask, scopeClient+"|"+scopeAll))              // 任务创建
	r.GET("/pansoso/task/:task_id", wrapper.Auth(api.DlpPanSosoTaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	r.GET("/pansoso/task/result/:task_id", wrapper.Auth(api.DlpPanSosoTaskResult, scopeClient+"|"+scopeAll)) // 任务结果

	// 数据泄露: 豆丁文档
	r.GET("/douin/:keyword", wrapper.Auth(api.DlpDouinByKeyword, scopeClient)) // 搜索同步模式
	// 豆丁文档任务模式
	r.POST("/douin/task", wrapper.Auth(api.DlpDocinSearchByTask, scopeClient+"|"+scopeAll))              // 任务创建
	r.GET("/douin/task/:task_id", wrapper.Auth(api.DlpDocinTaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	r.GET("/douin/task/result/:task_id", wrapper.Auth(api.DlpDocinTaskResult, scopeClient+"|"+scopeAll)) // 任务结果

	// 数据泄露: 道客巴巴任务模式
	r.POST("/doc88/task", wrapper.Auth(api.DlpDoc88SearchByTask, scopeClient+"|"+scopeAll))              // 任务创建
	r.GET("/doc88/task/:task_id", wrapper.Auth(api.DlpDoc88TaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	r.GET("/doc88/task/result/:task_id", wrapper.Auth(api.DlpDoc88TaskResult, scopeClient+"|"+scopeAll)) // 任务结果

	// 语雀文档
	r.POST("/yuque", wrapper.Auth(api.DlpYuque, scopeClient))

	// 数据泄露-总库任务管理
	r.GET("/task/agg", wrapper.Auth(api.DlpTaskAgg, scopeAdmin))             // 指标聚合
	r.POST("/task/result", wrapper.Auth(api.DlpTaskList, scopeAdmin))        // 任务列表
	r.PUT("/task/result", wrapper.Auth(api.DlpTaskResultUpdate, scopeAdmin)) // 任务编辑

	// Postman任务模式
	r.POST("/postman/task", wrapper.Auth(api.DlpPostmanSearchByTask, scopeClient+"|"+scopeAll))              // 任务创建
	r.GET("/postman/task/:task_id", wrapper.Auth(api.DlpPostmanTaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	r.GET("/postman/task/result/:task_id", wrapper.Auth(api.DlpPostmanTaskResult, scopeClient+"|"+scopeAll)) // 任务结果

	// 秒搜网盘任务模式
	r.POST("/miaosou/task", wrapper.Auth(api.DlpMiaosouSearchByTask, scopeAll+"|"+scopeAll))              // 任务创建
	r.GET("/miaosou/task/:task_id", wrapper.Auth(api.DlpMiaosouTaskInfo, scopeAll+"|"+scopeAll))          // 任务详情
	r.GET("/miaosou/task/result/:task_id", wrapper.Auth(api.DlpMiaosouTaskResult, scopeAll+"|"+scopeAll)) // 任务结果

	// 奇妙搜索
	r.POST("/magicalsearch/task", wrapper.Auth(api.DlpMagicalSearchByTask, scopeClient+"|"+scopeAll))                // 任务创建
	r.GET("/magicalsearch/task/:task_id", wrapper.Auth(api.DlpMagicalSearchTaskInfo, scopeClient+"|"+scopeAll))      // 任务详情
	r.GET("/magicalsearch/task/result/:task_id", wrapper.Auth(api.DlpMagicalSearchResult, scopeClient+"|"+scopeAll)) // 任务结果

	// 大圣盘
	r.POST("/dashengpan/task", wrapper.Auth(api.DlpDashengpanByTask, scopeClient))                // 任务创建
	r.GET("/dashengpan/task/:task_id", wrapper.Auth(api.DlpDashengpanTaskInfo, scopeClient))      // 任务详情
	r.GET("/dashengpan/task/result/:task_id", wrapper.Auth(api.DlpDashengpanResult, scopeClient)) // 任务结果

	// Gitcode任务模式
	r.POST("/gitcode/task", wrapper.Auth(api.DlpGitcodeSearchByTask, scopeClient+"|"+scopeAll))              // 任务创建
	r.GET("/gitcode/task/:task_id", wrapper.Auth(api.DlpGitcodeTaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	r.GET("/gitcode/task/result/:task_id", wrapper.Auth(api.DlpGitcodeTaskResult, scopeClient+"|"+scopeAll)) // 任务结果

	// 豆丁文档任务模式
	r.POST("/renrendoc/task", wrapper.Auth(api.DlpRenrendocSearchByTask, scopeClient+"|"+scopeAll))              // 任务创建
	r.GET("/renrendoc/task/:task_id", wrapper.Auth(api.DlpRenrendocTaskInfo, scopeClient+"|"+scopeAll))          // 任务详情
	r.GET("/renrendoc/task/result/:task_id", wrapper.Auth(api.DlpRenrendocTaskResult, scopeClient+"|"+scopeAll)) // 任务结果

}
