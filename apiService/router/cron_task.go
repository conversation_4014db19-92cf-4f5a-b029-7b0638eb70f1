package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 周期扫描任务: 注册路由
func cronTaskRouterRegister(r *gin.RouterGroup) {
	// 列表
	r.GET("/cycle", wrapper.Auth(api.CrontabTaskList, scopeAll))
	// 创建定时任务

	// 删除定时任务
	r.DELETE("/cycle", wrapper.Auth(api.CrontabTaskDelete, scopeAll))
	// 周期任务开关设置
	r.POST("/cycle/task_switch", wrapper.Auth(api.CrontabTaskSwitch, scopeAll))
	// 周期任务详情
	r.GET("/cycle/:id", wrapper.Auth(api.CrontabTaskDetail, scopeAll))
	// 创建周期任务
	r.POST("/cycle", wrapper.Auth(api.CrontabTaskCreate, scopeAll))
	// 编辑周期任务
	r.POST("/cycle/:id", wrapper.Auth(api.CrontabTaskEdit, scopeAll))
}
