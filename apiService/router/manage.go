package router

import (
	"github.com/gin-gonic/gin"

	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"
)

func manageRegister(r *gin.RouterGroup) {
	keyword := r.Group("/keyword")
	// 关键词管理-数据同步
	keyword.POST("/sync", wrapper.Auth(api.KeywordManageDataSync, scopeAll))

	manage := r.Group("/manage")
	// api 调用统计 /manage/api_count
	manage.GET("/api_count", wrapper.Auth(api.RequestCountList, scopeAdmin))
	// ICP备案管理 /manage/icp
	icp := manage.Group("/icp")
	icp.GET("/domain", wrapper.Auth(api.ManageCompanyIcpList, scopeAdmin))
	icp.POST("/domain", wrapper.Auth(api.ManageCompanyIcpCreate, scopeAdmin))
	icp.PUT("/domain", wrapper.Auth(api.ManageCompanyIcpUpdate, scopeAdmin))
	icp.GET("/app", wrapper.Auth(api.<PERSON>p<PERSON>ppList, scopeAdmin))
	icp.POST("/app", wrapper.Auth(api.ManageIcpAppCreate, scopeAdmin))
	icp.PUT("/app", wrapper.Auth(api.ManageIcpAppUpdate, scopeAdmin))

	// 数字资产路由
	digitalAsset := manage.Group("digital_asset")
	digitalAsset.POST("/filter_group", wrapper.Auth(api.ManageDigitalFilterGroup, scopeAdmin)) // 筛选下拉
	// 数字资产-关键词管理
	digitalAsset.GET("/task", wrapper.Auth(api.ManageDigitalKeywordInfo, scopeAdmin))      // 关键词详情
	digitalAsset.POST("/task", wrapper.Auth(api.ManageDigitalKeywordUpsert, scopeAdmin))   // 关键词新建或更新
	digitalAsset.GET("/task/list", wrapper.Auth(api.ManageDigitalKeywordList, scopeAdmin)) // 关键词列表
	digitalAsset.DELETE("/task", wrapper.Auth(api.ManageDigitalKeywordDelete, scopeAdmin)) // 关键词删除
	// 数字资产-任务结果
	digitalAsset.GET("/task_result", wrapper.Auth(api.ManageDigitalKeywordResultList, scopeAdmin)) // 任务结果列表
	// 数字资产-总库资产管理
	digitalAsset.GET("/assets/list", wrapper.Auth(api.ManageDigitalAssetsResultList, scopeAdmin))      // 资产列表
	digitalAsset.POST("/assets", wrapper.Auth(api.ManageDigitalAssetsResultCreate, scopeAdmin))        // 资产新建
	digitalAsset.POST("/assets/import", wrapper.Auth(api.ManageDigitalAssetsResultImport, scopeAdmin)) // 资产导入
	digitalAsset.PUT("/assets", wrapper.Auth(api.ManageDigitalAssetsResultUpdate, scopeAdmin))         // 资产更新
	digitalAsset.DELETE("/assets", wrapper.Auth(api.ManageDigitalAssetsResultDelete, scopeAdmin))      // 资产删除

	// client申请审核
	r.GET("/manage/apply", wrapper.Auth(api.ManageClientApplyList, scopeAdmin))  // 列表
	r.PUT("/manage/apply", wrapper.Auth(api.ManageClientApplyAudit, scopeAdmin)) // goby client申请审核

	// 黄赌毒
	prohibit := r.Group("/manage/pgd_keyword")
	prohibit.GET("/type", wrapper.Auth(api.ManageBlackKeywordTypeList, scopeAdmin+"|"+scopeTenantOrSafe)) // 分类列表
	prohibit.POST("/type", wrapper.Auth(api.ManageBlackKeywordTypeCreate, scopeAdmin))                    // 分类新建
	prohibit.GET("/audit", wrapper.Auth(api.BlackKeywordList, scopeAdmin))                                // 审核列表
	prohibit.PUT("/audit", wrapper.Auth(api.BlackKeywordUpdate, scopeAdmin))                              // 审核操作
	prohibit.GET("/system", wrapper.Auth(api.ManageBlackKeywordList, scopeAdmin))                         // 总库列表
	prohibit.POST("/system", wrapper.Auth(api.ManageBlackKeywordCreate, scopeAdmin))                      // 总库新建
	prohibit.PUT("/system", wrapper.Auth(api.ManageBlackKeywordUpdate, scopeAdmin))                       // 总库更新
	// prohibit.DELETE("/system", wrapper.Auth(api.ManageBlackKeywordDelete, scopeAdmin))                    // 总库删除
}
