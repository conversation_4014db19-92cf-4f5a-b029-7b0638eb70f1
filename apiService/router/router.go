package router

import (
	"micro-service/apiService/auth"
	"micro-service/apiService/wsocket"
	"net/http"
	"sync"
	"time"

	"github.com/gin-contrib/gzip"
	ginzap "github.com/gin-contrib/zap"
	"github.com/gin-gonic/gin"

	"micro-service/apiService/middleware/limiter"
	"micro-service/pkg/utils"

	"micro-service/apiService/api"
	"micro-service/apiService/middleware"
	pb "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/apiService/wrapper"
	"micro-service/pkg/log"
)

const (
	scopeAll                 = "*"                    // 所有用户
	scopeAdmin               = "admin"                // Admin用户
	scopeSafe                = "safe"                 // 安服用户
	scopeAdminOrSafe         = "admin|safe"           // Admin或安服用户
	scopeTenant              = "tenant"               // 普通用户
	scopeClient              = "client"               // 客户端
	scopeTenantOrSafe        = "tenant|safe"          // 普通或安服
	scopeAfterSale           = "sale"                 // 售后
	ScopeDiscover            = "discover"             // 资产发现
	scopeQccBasicDetail      = "QccBasicDetail"       // 企查查-企业信息
	scopeInformation         = "information_domain"   // IP/domain 查信息
	scopeFofaScan            = "fofa_scan"            // FOFA扫描
	scopeIntelligenceManager = "intelligence_manager" // 情报中心-管理
)

var once sync.Once

var router *gin.Engine

func GetRouteInstance() *gin.Engine {
	if router == nil {
		once.Do(func() {
			router = initRouter()
		})
	}
	return router
}

func initRouter() *gin.Engine {
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()
	router.UseH2C = true
	// 路由中间件
	router.Use(
		gin.Recovery(),
		middleware.WithCors(), // Cors 访问控制
		ginzap.Ginzap(log.GetLogger(), "2006-01-02 15:04:05.000", false),
		ginzap.RecoveryWithZap(log.GetLogger(), true),
		gzip.Gzip(gzip.DefaultCompression),
		middleware.WithTracer(), // Trace 链路追踪
	)

	v1 := router.Group("/api/v1")
	// 所有涉及超管权限的,统一使用admin权限进行限制
	// 权限校验说明 v1.POST("/crawler", wrapper.Auth(api.Crawler,"crawler")) 校验 crawler
	// 权限校验说明 v1.POST("/crawler", wrapper.Auth(api.Crawler,"crawler|client")) 满足其中一个即可
	// 权限校验说明 v1.POST("/crawler", wrapper.Auth(api.Crawler,"crawler","client")) 必须同时满足两个
	// 权限校验说明 v1.POST("/crawler", wrapper.Auth(api.Crawler,"*")) 登录就可使用
	// 权限校验说明 v1.POST("/crawler", wrapper.Auth(api.Crawler)) 无权限认证
	// test
	v2 := router.Group("/api/v2", limiter.NewRateLimiter(time.Minute, 2, func(ctx *gin.Context) (string, error) {
		clientIp := ctx.ClientIP()
		log.WithContextInfof(ctx, "RequestClientIp:"+clientIp)
		// 未取到IP,不限制
		if clientIp == "" {
			return time.UnixMicro(1000).String(), nil
		}
		// 私有地址,或者不是IP不限制
		isIp, isPrivate := utils.IsPrivateIP(clientIp)
		if !isIp || isPrivate {
			return time.UnixMicro(1000).String(), nil
		}
		return clientIp, nil
	}).Middleware())
	v2.GET("/test-v2", limiter.NewRateLimiter(time.Minute, 5, func(ctx *gin.Context) (string, error) {
		clientIp := ctx.ClientIP()
		log.WithContextInfof(ctx, "RequestClientIp:"+clientIp)
		// 未取到IP,不限制x
		if clientIp == "" {
			return time.UnixMicro(1000).String(), nil
		}
		// 私有地址,或者不是IP不限制
		isIp, isPrivate := utils.IsPrivateIP(clientIp)
		if !isIp || isPrivate {
			return time.UnixMicro(1000).String(), nil
		}
		return clientIp, nil
	}).Middleware(), func(context *gin.Context) { context.String(http.StatusOK, "hello test-foradar") })
	v2.GET("/test-v2-2", func(context *gin.Context) { context.String(http.StatusOK, "hello test-foradar") })
	v1.GET("/test-foradar", func(context *gin.Context) { context.String(http.StatusOK, "hello test-foradar") })

	// WebSocket服务
	v1.GET("/ws", func(context *gin.Context) {
		// 获取登录Token
		tokenInfo, tokenErr := auth.GetOauth2().ValidationBearerToken(context.Request)
		if tokenErr != nil {
			_ = response.Gen(context).SendError(wrapper.APIServiceName, http.StatusUnauthorized, tokenErr.Error(), nil)
			return
		}
		// 保存登录信息
		wrapper.SetAuthInfo(context, tokenInfo)
		wsocket.SocketHandler(context)
	})
	// 登录
	v1.POST("/auth/login", wrapper.Auth(api.Login))
	// oauth Token
	v1.POST("/auth/token", wrapper.Auth(api.GetToken))
	// 用户中心获取API-Token
	v1.GET("/auth/api-token", wrapper.Auth(api.GenApiToken, scopeAll))
	// 刷新token
	v1.POST("/auth/refresh_token", wrapper.Auth(api.RefreshToken, scopeAll))
	// 获取client的详情信息
	v1.GET("/auth/token_info", wrapper.Auth(api.GetTokenInfo, scopeAll))
	// client的账户限制信息更新(中航金网)
	v1.POST("/auth/detect_limit", wrapper.Auth(api.UpdateDetectLimit, scopeAll))
	// 权限列表
	v1.GET("/auth/scopes", wrapper.Auth(api.Scopes, scopeAdmin+"|"+scopeAfterSale))

	// auth for a client
	crawlerRouterRegister(v1.Group("/crawler")) // 爬虫
	// 备案查询
	beianRouterRegister(v1)
	// whois-根据域名反查
	v1.GET("/whois/reverse/domain/:domain", wrapper.Auth(api.ReverseDomain, scopeClient))
	// ICP-根据域名查询备案
	v1.GET("/chaziyu/domain/:domain", wrapper.Auth(api.Subdomain, scopeClient))
	// Ip138-根据IP查询
	v1.POST("/ip138/ip", wrapper.Auth(api.Ip138Ip, scopeClient))
	// Ip138-根据域名查询
	v1.POST("/ip138/domain", wrapper.Auth(api.Ip138Domain, scopeClient))
	// 根据根域获取登录入口资产暴露及截图
	v1.POST("/screenshot/domain_search", wrapper.Auth(api.ScreenshotByDomainClue, scopeAll))

	// 数据泄露 /api/v1/digital
	digitalGroupV1 := v1.Group("/digital")
	digitalRouterRegister(digitalGroupV1)

	manageRegister(v1) // 仅由管理员操作管理的一些API
	// 搜索引擎-获取子域名
	v1.GET("/engine/site/:domain", wrapper.Auth(api.EngineWebSite, scopeClient))

	// 数据泄露 /api/v1/dlp
	dlpGroupV1 := v1.Group("/dlp")
	dlpRouterRegister(dlpGroupV1)

	// ---------------fofa数据推荐
	fofaGroupV1 := v1.Group("/fofa")
	FofaRegister(fofaGroupV1)

	// ---------------hunter数据推荐
	hunterGroupV1 := v1.Group("/hunter")
	HunterRegister(hunterGroupV1)

	// ---------------企查查接口 /api/v1/qcc
	qccGroupV1 := v1.Group("/qcc")
	QccRegister(qccGroupV1)

	// ---------------定时任务 /api/v1/cron
	cronGroupV1 := v1.Group("/cron")
	cronRouterRegister(cronGroupV1)

	// ---------------系统配置 /api/v1/config
	configGroupV1 := v1.Group("/config")
	configRouterRegister(configGroupV1)

	// ---------------情报
	informationGroupV1 := v1.Group("/information")
	informationRouterRegister(informationGroupV1)
	// 子域名爆破
	v1.POST("/subdomain/burst", wrapper.Auth(api.BurstSubdomain, scopeClient))
	v1.DELETE("subdomain/burst", wrapper.Auth(api.BurstSubdomainTaskDelete, scopeClient)) // Go域名爆破任务删除
	// 获取whois信息
	v1.GET("/whois/domain/:domain", wrapper.Auth(api.ReverseWhois, scopeClient))
	// 控股公司数量统计
	v1.POST("/count/holding_company", wrapper.Auth(api.HoldingCompanyCount, scopeClient))
	// 检查是否是cdn
	v1.POST("/check_cdn", wrapper.Auth(api.CheckCdn, scopeClient))
	// 根据banner或header数据匹配CVE漏洞
	// v1.POST("/vulns/vulns_list", wrapper.Auth(api.GetCVEVulnsByHoB, "client"))

	// ---------------公告 /api/v1/public_notice
	publicNoticeGroupV1 := v1.Group("/public_notice")
	publicNoticeRegister(publicNoticeGroupV1)

	// ---------------资产概览 /api/v1/assets_overview
	assetsOverviewGroupV1 := v1.Group("/assets_overview")
	assetsOverviewRouterRegister(assetsOverviewGroupV1)

	// 资产状态检测功能: /api/v1/assets_status_detect
	statusDetectGroupV1 := v1.Group("/assets_status_detect")
	assetStatusDetectRouterRegister(statusDetectGroupV1)
	// url状态检测功能: /api/v1/url_status_detect
	urlStatusDetectGroupV1 := v1.Group("/url_status_detect")
	urlStatusDetectRouterRegister(urlStatusDetectGroupV1)
	assetAuditRouterRegister(v1.Group("/asset_audit")) // 资产核查功能：/api/v1/asset_audit
	// 任务概览功能: /api/v1/task_overview
	taskOverviewGroupV1 := v1.Group("/task_overview")
	taskOverviewRouterRegister(taskOverviewGroupV1)
	systemDetectGroupV1 := v1.Group("/system_detect") // 业务系统功能 /api/v1/system_detect
	systemDetectRouterRegister(systemDetectGroupV1)
	phishingFakeRouteRegister(v1.Group("fake_detect_assets")) // 钓鱼仿冒任务 /api/v1/fake_detect_assets
	// 域名资产功能 /api/v1/domain_assets
	domainAssetsRouterRegister(v1.Group("domain_assets"))
	detectAssetTaskRouterRegister(v1.Group("detect_assets")) // 单位资产测绘/云端资产推荐
	domainSearchRouterRegister(v1.Group("domain_search"))    // 域名搜索

	// 线索总库 /api/v1/clue
	clueRouterRegister(v1.Group("/clue"))
	// 资产总库 /api/v1/cloud_assets
	CloudAssetsRouterRegister(v1.Group("/cloud_assets"))

	// 线索库-公共线索库 /api/v1/clue_company
	clueCompanyRouterRegister(v1.Group("/clue_company"))
	clueCloudRouterRegister(v1.Group("/cloud"))
	// 线索库-供应链线索库 /api/v1/supply_chain
	clueSupplyChainRouterRegister(v1.Group("/supply_chain"))

	// 线索库-企业线索库
	clueLibCompanyRegitster(v1.Group("/clu_lib/company"))

	// 根据线索计算fofa资产的数量
	v1.GET("/clues/fofa_num", wrapper.Auth(api.GetFofaAssetsNum, scopeAll))

	// 规则引擎 /api/v1/system_rule
	engineRulesRouterRegister(v1.Group("system_rule"))
	// 情报中心 /api/v1/intelligence
	IntelligenceRouterRegister(v1.Group("/intelligence"))

	// 资产台账
	// IP资产列表
	v1.GET("/assets/account", wrapper.Auth(api.ListIpAccount, scopeAll))
	// IP资产删除
	v1.DELETE("/assets/account", wrapper.Auth(api.DeleteIpAccount, scopeAll))
	// IP资产导出
	v1.POST("/assets/account/export", wrapper.Auth(api.ExportIpAccount, scopeAll))
	// IP资产确权
	v1.POST("/assets/account/confirm", wrapper.Auth(api.ConfirmIpAccount, scopeAll))
	// IP资产筛选条件
	v1.POST("/assets/account/condition", wrapper.Auth(api.IpAssetConditions, scopeAll))
	// IP资产端口筛选条件
	v1.POST("assets/account/ansys_condition", wrapper.Auth(api.IpPortAssetConditions, scopeAll))
	// IP资产自定义标签
	v1.POST("/tables_assets/set_tags", wrapper.Auth(api.TableAssetsCustomerTag, scopeAll))
	//核对台账模版
	v1.POST("/assets/account/check", wrapper.Auth(api.CheckBookData, scopeAll))
	//批量编辑ip的企业名称
	v1.POST("/tables_assets/set_clue_company_name", wrapper.Auth(api.TableIpCompanyName, scopeAll))
	//IP端口维度台账列表
	v1.GET("assets/account/port", wrapper.Auth(api.SurePassetsList, scopeAll))
	v1.POST("assets/account/port/export", wrapper.Auth(api.ExportSurePassetsList, scopeAll))
	// IP画像
	v1.GET("/assets/account/ip_profile", wrapper.Auth(api.IPProfile, scopeAll))
	// IP资产/情报漏洞信息
	v1.GET("/assets/account/ip_vulns", wrapper.Auth(api.IpAssetVulns, scopeAll))
	// IP资产 导入文件解析
	v1.POST("/assets/account/upload", wrapper.Auth(api.ImportKnownAssets, scopeAll))
	v1.POST("/assets/account/import", wrapper.Auth(api.AssetAccountImportScan, scopeAll))
	v1.POST("/assets/save_import", wrapper.Auth(api.AssetsImportData, scopeAll))
	//资产扫描上传模版ip文件
	v1.POST("/assets/task/import_ip_file", wrapper.Auth(api.ImportIpFile, scopeAll))
	v1.POST("/ip/ip_domain_history", wrapper.Auth(api.IpDomainHistory, scopeAll))

	// ---------------公共接口
	v1.POST("/public/upload", wrapper.Auth(api.FileUpload, scopeAll)) // /api/v1/public/upload

	// 统计当前在线的用户
	v1.GET("/user/online_list", wrapper.Auth(api.UserTokenList, scopeAdmin))

	// --------------资产发现接口
	discoverRouterRegister(v1.Group("/discover"))

	// 创建FOFA扫描任务
	v1.POST("/fofa-scan/task/create", wrapper.Auth(api.CreateFofaScanTask, scopeClient))
	// 创建FOFA扫描任务
	v1.POST("/fofa-scan/task/stop", wrapper.Auth(api.StopFofaScanTask, scopeClient))

	// ---------------站内信
	websiteMessageRouterRegister(v1.Group("/website_message"))

	// -------------------------------------业务拆分---------------------------------
	// ---------端口管理
	portsGroupV1 := v1.Group("/ports")
	portGroupRouterRegister(portsGroupV1)
	// ---------事件规则管理
	systemRuleRoute := v1.Group("/system_rule")
	systemRuleRouteRegister(systemRuleRoute)
	// ---------POC管理
	pocGroupRoute := v1.Group("/poc")
	pocGroupRouteRegister(pocGroupRoute)
	userRouterRegister(v1.Group("/user")) // 用户管理

	// 申请单位资产权限的key
	v1.POST("/public/apply", wrapper.Auth(api.ApplyApi))
	// 账户管理
	accountRouteRegister(v1.Group("/account"))

	// 禁扫IP管理
	forbidIpsRouterRegister(v1.Group("/forbid"))

	// 404 处理
	router.NoRoute(wrapper.Auth(func(c *gin.Context) error {
		return response.Gen(c).SendError(pb.ServiceName, 404, "api not found", nil)
	}))

	//dnschecker数据源接入
	v1.GET("/dnschecker/:domain", wrapper.Auth(api.Dnschecker, scopeClient))

	// api_analyze
	apiAnalyzeRouteRegister(v1.Group("/api_analyze"))

	// detect_assets
	// 修改router.go中的路由定义
	v1.GET("/detect_assets/task/info", wrapper.Auth(api.DetectAssetsInfo, scopeAll))     // 任务详情
	v1.GET("/detect_assets/task/info/", wrapper.Auth(api.DetectAssetsInfo, scopeAll))    // 任务详情
	v1.GET("/detect_assets/task/info/:id", wrapper.Auth(api.DetectAssetsInfo, scopeAll)) // 任务详情
	v1.GET("/detect_assets/job", wrapper.Auth(api.StartSyncJob, scopeAll))
	//线索管理-线索数量统计
	v1.GET("/clues/count/:group_id", wrapper.Auth(api.GetClueCount, scopeAll))
	//资产测绘-获取线索列表
	v1.GET("/detect_assets/clues", wrapper.Auth(api.GetCluesIndex, scopeAll))
	//线索管理-线索列表
	v1.GET("/clues/:type", wrapper.Auth(api.GetCluesIndex, scopeAll))
	//资产测绘-重新获取原始线索
	v1.POST("detect_assets/company/repeat_clues", wrapper.Auth(api.GetRepeatOriginClues, scopeAll))
	//资产测绘-停止任务
	v1.DELETE("detect_assets/result/stop", wrapper.Auth(api.StopAndDeleteTask, scopeAll))
	//资产测绘-删除资产测绘任务
	v1.DELETE("detect_assets/task", wrapper.Auth(api.DelDetectAssetsTask, scopeAll))
	//资产测绘-二度扩展线索
	v1.POST("detect_assets/clues", wrapper.Auth(api.ExpandClues, scopeAll))
	//资产测绘-生成线索总表
	v1.POST("detect_assets/confirm_clues", wrapper.Auth(api.ConfirmAllClues, scopeAll))
	// 资产测绘-标记线索到黑名单
	v1.POST("clue/set_black", wrapper.Auth(api.ClueBlack, scopeAdminOrSafe))
	// 获取域名的Whois信息
	v1.GET("whois", wrapper.Auth(api.WhoisInfo, scopeAll))
	// 推荐资产
	v1.POST("detect_assets/cloud", wrapper.Auth(api.RecommendAssets, scopeAll))

	// 资产测绘-资产列表5
	v1.GET("detect_assets/result/assets", wrapper.Auth(api.GetDetectAssetsResultList, scopeAll))

	// 资产台账 - 登录入口
	loginAssetsRouterRegister(v1.Group("/login_assets"))
	certAssetsRouterRegister(v1.Group("/cert_assets"))

	// 线索管理-审核线索|线索处置
	v1.POST("clues/pass_clue", wrapper.Auth(api.PassClue, scopeAll))

	// 线索管理-导入线索
	v1.POST("clues", wrapper.Auth(api.ImportClues, scopeAll))

	//任务列表
	v1.GET("detect_assets/task", wrapper.Auth(api.TaskIndex, scopeAll))

	//单位测绘-生成报告
	v1.POST("detect_assets/result/create_reports", wrapper.Auth(api.CreateReport, scopeAll))
	//云端推荐-推荐结果
	v1.GET("cloud/recommend/:flag", wrapper.Auth(api.CloudRecommendResults, scopeAll))
	v1.GET("cloud/recommend_clues/:flag", wrapper.Auth(api.CloudRecommendResultsClues, scopeAll))
	v1.POST("cloud/recommend/:flag/export", wrapper.Auth(api.CloudRecommendResultsExport, scopeAll))
	// Hunter SQL生成接口
	v1.GET("detect_assets/clue/hunter_sql/:id", wrapper.Auth(api.HunterSql, scopeAll))
	// 资产测绘-三方数据确认-导入推荐资产数据
	v1.POST("detect_assets/cloud/other_confirm", wrapper.Auth(api.ImportResultsConfirm, scopeAll))
	// IP维度推荐结果
	v1.GET("assets/scan/check/:flag", wrapper.Auth(api.GroupIpResults, scopeAll))
	// 删除推荐结果
	v1.DELETE("assets/scan/check/:flag", wrapper.Auth(api.DeleteRecommendResult, scopeAll))
	// 更新资产可信度
	v1.POST("detect_assets/assets_confidence_level/update/:flag", wrapper.Auth(api.UpdateAssetsConfidenceLevel, scopeAll))

	// 资产扫描任务相关接口
	scanAssetsRouteRegister(v1.Group("/assets"))

	// 疑似资产
	v1.GET("unsure/assets", wrapper.Auth(api.UnsureAssetsList, scopeAll))
	v1.GET("unsure/passets", wrapper.Auth(api.PassetsList, scopeAll))
	v1.POST("unsure/assets/export", wrapper.Auth(api.UnclaimAssetIPExport, scopeAll))
	// 批量匹配未知资产的黄赌毒关键词规则
	v1.POST("unsure/assets/bacth_match", wrapper.Auth(api.BatchMatchBlackWord, scopeAll))
	//威胁资产列表/IP维度批量修改威胁资产类型
	v1.POST("threaten/assets/set_threaten_type", wrapper.Auth(api.SetThreatenType, scopeAll))
	// 疑似的IP资产删除
	v1.DELETE("unsure/assets", wrapper.Auth(api.DeleteUnsureIpAccount, scopeAll))
	v1.POST("unsure/passets/export", wrapper.Auth(api.ExportPassetsList, scopeAll))
	// 忽略资产
	v1.GET("ignore/assets", wrapper.Auth(api.IgnoreAssetsList, scopeAll))
	v1.GET("ignore/passets", wrapper.Auth(api.IngorePassetsList, scopeAll))
	v1.POST("ignore/passets/export", wrapper.Auth(api.ExportIngorePassetsList, scopeAll))
	v1.POST("ignore/assets/export", wrapper.Auth(api.IgnoreAssetIPExport, scopeAll))
	// 忽略的IP资产删除
	v1.DELETE("ignore/assets", wrapper.Auth(api.DeleteIgnoreIpAccount, scopeAll))
	// 威胁资产
	v1.GET("threaten/assets", wrapper.Auth(api.ThreatenAssetsList, scopeAll))
	v1.GET("threaten/passets", wrapper.Auth(api.ThreatenPassetsList, scopeAll))
	v1.POST("threaten/passets/export", wrapper.Auth(api.ExportThreatenPassetsList, scopeAll))
	v1.POST("threaten/assets/export", wrapper.Auth(api.ThreatenAssetIPExport, scopeAll))
	// 威胁的IP资产删除
	v1.DELETE("threaten/assets", wrapper.Auth(api.DeleteThreatenIpAccount, scopeAll))
	// 统计资产数据维度
	v1.GET("statistics", wrapper.Auth(api.Statistics, scopeAll))
	// 微内核回调-完成
	v1.GET("/scan/asset/finish", wrapper.Auth(api.ScanAssetFinish))
	// 微内核回调-进度
	v1.GET("/scan/asset/progress", wrapper.Auth(api.ScanAssetProgress))
	//周期扫描任务 // assets
	cronTaskGroupV1 := v1.Group("/assets")
	cronTaskRouterRegister(cronTaskGroupV1)

	// 资产任务IP详情接口
	v1.GET("/assets/task/ipdetail", wrapper.Auth(api.IpDetail, scopeAll))

	// IP获取域名的解析记录
	v1.GET("assets/ipdetail/history", wrapper.Auth(api.IpHistory, scopeAll))

	// 设置标题黑名单关键词
	v1.POST("/title/set_black", wrapper.Auth(api.SetTitleBlackKeyword, scopeAll))

	// 资产处置
	v1.POST("/table/assets/unsign", wrapper.Auth(api.TableAssetsSetStatus, scopeAll))
	// 忽略资产处置
	v1.POST("/ignore/assets/sign", wrapper.Auth(api.IgnoreAssetsSetStatus, scopeAll))
	// 威胁资产处置
	v1.POST("/threaten/assets/sign", wrapper.Auth(api.ThreatenAssetsSetStatus, scopeAll))
	// 疑似资产处置
	v1.POST("/unsure/assets/sign", wrapper.Auth(api.UnsureAssetsSetStatus, scopeAll))

	return router
}
