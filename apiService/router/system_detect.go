package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 业务系统路由
func systemDetectRouterRegister(r *gin.RouterGroup) {
	// 获取列表
	r.POST("list", wrapper.Auth(api.SystemDetectList, scopeTenantOrSafe))
	// 新建
	r.POST("create", wrapper.Auth(api.SystemDetectCreate, scopeTenantOrSafe))
	// 更新
	r.PUT("update", wrapper.Auth(api.SystemDetectUpdate, scopeTenantOrSafe))
	// 下载
	r.POST("download", wrapper.Auth(api.SystemDetectDownload, scopeTenantOrSafe))
	// 删除
	r.DELETE("delete", wrapper.Auth(api.SystemDetectDelete, scopeTenantOrSafe))
	//忽略确认
	r.POST("ignore_confirm", wrapper.Auth(api.SystemDetectIgnoreConfirm, scopeTenantOrSafe))
	// 同步台账
	r.POST("sync_assets", wrapper.Auth(api.SystemDetectSyncAssets, scopeTenantOrSafe))
	// IP关联
	r.PUT("ip_association", wrapper.Auth(api.SystemDetectIpAssociation, scopeTenantOrSafe))
	// 进度
	r.GET("progress", wrapper.Auth(api.SystemDetectProgress, scopeTenantOrSafe))
}
