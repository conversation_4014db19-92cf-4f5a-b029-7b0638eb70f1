package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 定时任务: 注册路由
func cronRouterRegister(r *gin.RouterGroup) {
	// 创建定时任务
	r.POST("/", wrapper.Auth(api.CronAdd, scopeAdmin))
	// 删除定时任务
	r.DELETE("/", wrapper.Auth(api.CronDelete, scopeAdmin))
	// 定时任务列表
	r.GET("/", wrapper.Auth(api.CronList, scopeAdmin))
	// 更新定时任务
	r.POST("/:id", wrapper.Auth(api.CronUpdate, scopeAdmin))
	// 定时任务执行记录
	r.GET("/:id/history", wrapper.Auth(api.CronHistory, scopeAdmin))
	// 清除任务执行记录
	r.DELETE("/:id/history", wrapper.Auth(api.CronHistoryClear, scopeAdmin))
}
