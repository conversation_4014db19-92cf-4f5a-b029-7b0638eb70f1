package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// QccRegister 企查查接口
func QccRegister(r *gin.RouterGroup) {
	// 企查查-分支机构信息
	r.GET("/branch_list/:search", wrapper.Auth(api.QCCBranchList, scopeClient))
	// 企查查-企业对外投资穿透
	r.GET("/investment_through/:search", wrapper.Auth(api.QCCInvestmentThrough, scopeClient))
	// 企查查-企业搜索
	r.GET("/company_search/:search", wrapper.Auth(api.QCCNameSearch, scopeClient+"|"+ScopeDiscover))
	// 企查查-企业工商照面 GetBasicDetailsByName
	r.GET("/basic_detail/:search", wrapper.Auth(api.QCCGetBasicDetailsByName, scopeClient+"|"+scopeQccBasicDetail))
	// 天眼查-微博
	r.GET("/weibo", wrapper.Auth(api.TYCWeiboAccounts, scopeClient))
}
