package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

func scanAssetsRouteRegister(r *gin.RouterGroup) {

	// 资产扫描任务列表
	r.GET("/task", wrapper.Auth(api.GetTaskList, scopeAll))

	// 资产扫描删除任务接口(资产任务和漏洞任务)
	r.DELETE("/task", wrapper.Auth(api.DeleteTask, scopeAll))

	// 资产扫描任务详情
	r.GET("/task/:task_id", wrapper.Auth(api.GetTaskDetail, scopeAll))

	// 资产扫描获取任务详情
	r.GET("/task/get_task_detail", wrapper.Auth(api.GetTaskDetail, scopeAll))

	// 资产扫描获取任务结果列表
	r.GET("/task/get_task_result", wrapper.Auth(api.GetTaskResult, scopeAll))

	// 资产扫描获取任务结果筛选条件
	r.GET("/task/get_task_result_condition", wrapper.Auth(api.GetTaskResultCondition, scopeAll))

	// 资产扫描任务结果导出
	r.POST("/task/task_result_export", wrapper.Auth(api.TaskResultExport, scopeAll))

	// 资产扫描扫描结果数据汇总
	r.GET("/task/task_analyse", wrapper.Auth(api.TaskResultAnalyse, scopeAll))

	// 下发资产扫描任务
	r.POST("/task", wrapper.Auth(api.CreateScanTask, scopeAll))
}
