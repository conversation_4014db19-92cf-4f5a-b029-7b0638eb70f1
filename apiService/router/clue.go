package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 扩展线索: 注册路由
func clueRouterRegister(r *gin.RouterGroup) {
	// 通过企业名称扩展线索
	r.POST("/expand/company", wrapper.Auth(api.ExpandByCompanyName, scopeClient))
	// 线索列表
	r.GET("/expand/task", wrapper.Auth(api.ExpandTaskList, scopeAdmin))
	// 通过IP/IP段扩展线索
	r.POST("/expand/ip", wrapper.Auth(api.ExpandByIp, scopeClient))
	// 通过ICP扩展线索
	r.POST("/expand/icp", wrapper.Auth(api.ExpandByIcp, scopeClient))
	// 通过证书扩展线索
	r.POST("/expand/cert", wrapper.Auth(api.ExpandByCert, scopeClient))
	// 通过域名扩展线索
	r.POST("/expand/domain", wrapper.Auth(api.ExpandByDomain, scopeClient))
	// 通过子域名扩展线索
	r.POST("/expand/subdomain", wrapper.Auth(api.ExpandBySubDomain, scopeClient))
	// 通过关键字扩展线索
	r.POST("/expand/keyword", wrapper.Auth(api.ExpandByKeyword, scopeClient))
	// 通过ICON扩展线索
	r.POST("/expand/icon", wrapper.Auth(api.ExpandByIcon, scopeClient))
	// 获取扩展结果
	r.POST("/expand/result", wrapper.Auth(api.ExpandResult, scopeClient))
	// 获取数据库线索
	r.POST("/expand/search", wrapper.Auth(api.SearchDbClueByCompanyName, scopeClient))

	// 线索总库管理
	// 获取线索列表
	r.POST("/", wrapper.Auth(api.GetClueList, scopeAdmin))
	// 线索列表下拉条件
	r.GET("/filter_group", wrapper.Auth(api.ClueFilterGroup, scopeAdmin))
	// 根据关键信息, 进行更新
	r.POST("/update", wrapper.Auth(api.UpdateByKeyword, scopeAdmin))
	// 创建线索
	r.POST("/add", wrapper.Auth(api.CreateClue, scopeAdmin))
	// 更新线索信息
	r.POST("/:id", wrapper.Auth(api.UpdateClueInfo, scopeAdmin))
	// 批量更新线索信息
	r.POST("/batch_update", wrapper.Auth(api.CluesBatchUpdate, scopeAdmin))
}

// region todo wufj scope
// 线索库-公共线索库 /api/v1/clue_company
func clueCompanyRouterRegister(r *gin.RouterGroup) {
	// 公共线索库企业下拉接口 /api/v1/clue_company/drop_list
	r.GET("/drop_list", wrapper.Auth(api.ClueCompanyDropListAPI))
	// fofa的全量数量统计接口(icon数量，域名数量，证书数量，icp数量) /api/v1/clue_company/count
	r.GET("/count", wrapper.Auth(api.ClueCompanyCountAPI))
	// 企业线索库列表 /api/v1/clue_company/list
	r.GET("/list", wrapper.Auth(api.ClueCompanyListAPI))
	// 企业线索库列表导出 /api/v1/clue_company/list/export
	r.GET("/list/export", wrapper.Auth(api.ClueCompanyListExportAPI))
}

// 线索库-供应链线索库 /api/v1/supply_chain
func clueSupplyChainRouterRegister(r *gin.RouterGroup) {
	// 线索供应链列表 /api/v1/supply_chain/clues/{type}
	r.GET("/clues/:type")
	// 供应链线索导出 /api/v1/supply_chain/clues/export
	r.POST("/clues/export")

}

// 线索库-企业线索库 云端推荐 /api/v1/cloud
func clueCloudRouterRegister(r *gin.RouterGroup) {
	r.GET("/company_cascade_equity", wrapper.Auth(api.GetCompanyCascadeEquity, scopeAll))
	// 云端推荐
	r.POST("/recommend", wrapper.Auth(api.Recommend, scopeAll))
}

// 线索库-企业线索库
func clueLibCompanyRegitster(r *gin.RouterGroup) {
	// 下属企业层级
	r.GET("/subordinate/enterprise/level", wrapper.Auth(api.SubordinateEnterpriseLevel))
	// 指定企业线索总览
	r.GET("/clue/overview", wrapper.Auth(api.ClueLibCompanyClueOverview))
}

// endregion
