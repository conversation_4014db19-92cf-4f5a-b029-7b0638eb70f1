package api

import (
	"go-micro.dev/v4"
	"sync"
	"time"

	"micro-service/pkg/cfg"

	"github.com/go-micro/plugins/v4/selector/registry"
	ocplugin "github.com/go-micro/plugins/v4/wrapper/trace/opentracing"
	opentracing "github.com/opentracing/opentracing-go"
	"go-micro.dev/v4/client"
	"go-micro.dev/v4/selector"
)

var once sync.Once

var singleInstance client.Client

func GetMicroClient() client.Client {
	return client.NewClient(
		client.Selector(selector.NewSelector(
			selector.SetStrategy(selector.RoundRobin),
			selector.Registry(cfg.GetInstance().GetConsulReg()),
			registry.TTL(5*time.Second),
		)),
		client.Wrap(ocplugin.NewClientWrapper(opentracing.GlobalTracer())),
	)
}

func GetProtoClient() WService {
	return NewWService(ServiceGrpcName, getInstance())
}

// getInstance
func getInstance() client.Client {
	if singleInstance == nil {
		once.Do(func() {
			srv := micro.NewService(
				micro.RegisterTTL(5*time.Second),      // TTL指定从上一次心跳间隔起，超过这个时间服务会被服务发现移除
				micro.RegisterInterval(4*time.Second), // 让服务在指定时间内重新注册，保持TTL获取的注册时间有效
				micro.WrapHandler(ocplugin.NewHandlerWrapper(opentracing.GlobalTracer())),
				micro.WrapClient(ocplugin.NewClientWrapper(opentracing.GlobalTracer())),
			)
			singleInstance = srv.Client()
			singleInstance.Init(
				client.Registry(cfg.GetInstance().GetConsulReg()),
				client.Wrap(ocplugin.NewClientWrapper(opentracing.GlobalTracer())),
				client.Retries(0),
				client.PoolSize(50),
				client.DialTimeout(60*time.Second),
				client.RequestTimeout(60*time.Second),
			)
		})
	}
	return singleInstance
}
