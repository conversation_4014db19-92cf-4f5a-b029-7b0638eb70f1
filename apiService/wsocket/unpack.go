package wsocket

type UnPack struct {
}

var unpack *UnPack

func init() {
	if unpack == nil {
		unpack = &UnPack{}
	}
}

func (u *UnPack) Users(client *Client, args string) {
	var clients []*Client
	wsocket.lockClients.RLock()
	clients = append(clients, wsocket.clients...)
	wsocket.lockClients.RUnlock()
	wsocket.SendMessage(&WsSendPack{
		ToClient: client,
		Cmd:      "users",
		Data:     clients,
	})
}
