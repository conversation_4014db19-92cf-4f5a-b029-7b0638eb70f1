package api

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"math"
	"micro-service/apiService/response"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	es "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/fofaee_task_assets"
	middleware_mysql "micro-service/middleware/mysql"
	"micro-service/middleware/mysql/scan_task"
	task_model "micro-service/middleware/mysql/task"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/pkg/websocket_message"
	"micro-service/scanService/handler/microkernel"
	"os"
	"strconv"
	"strings"
	"time"

	asyncq "micro-service/pkg/queue_helper"

	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic"
)

const (
	ScanAssetCallbackServiceName = "scan_asset_callback"
)

// ScanAssetProgress 处理资产扫描进度回调
func ScanAssetProgress(c *gin.Context) error {
	// 记录回调信息
	log.Infof("微内核-进度回调-progress 微内核-进度回调 微内核回调信息: %v", c.Request.URL.Query())

	taskID := c.Query("task_id")
	if taskID == "" {
		return response.Gen(c).SendSuccess(ScanAssetCallbackServiceName, response.MsgSuccess, nil)
	}

	taskIDUint, _ := strconv.ParseUint(taskID, 10, 64)
	taskModel := scan_task.NewModel()
	task, err := taskModel.First(scan_task.WithID(taskIDUint))
	if err != nil {
		log.Infof("微内核-进度回调-progress 任务不存在 微内核回调信息: %v", c.Request.URL.Query())
		// 任务不存在，调用停止微内核任务接口
		microkernel.GetInstance().Stop(taskIDUint)
		return response.Gen(c).SendSuccess(ScanAssetCallbackServiceName, response.MsgSuccess, nil)
	}

	log.Infof("微内核-进度回调-progress 微内核-进度回调 tid: %d, start_time: %d, setp: 回调第一步, now: %d, cost: %d",
		task.ID, task.StartAt.Unix(), time.Now().Unix(), getTaskDuration(task))

	// 处理暂停再继续的情况，进度能延续暂停前的进度
	progressStr := c.Query("progress")
	if progressStr == "" {
		progressStr = "1"
	}
	reciveProgress, _ := strconv.ParseFloat(progressStr, 64)
	var progress float64
	// 状态信息追踪
	ip := utils.GetNetworkIp(cfg.LoadCommon().Network)
	hostname, _ := os.Hostname()
	now := time.Now()
	progressEntry := map[string]interface{}{
		"hostname":  hostname,
		"ip":        ip,
		"task_id":   task.ID,
		"progress":  reciveProgress,
		"time_unix": now.UnixMilli(),
		"time_str":  now.Format("2006-01-02 15:04:05.000"),
	}
	log.Infof("[scan-callback] 进度上报 trace => hostname: %s, ip: %s, progress: %d,task_id: %d,time: %s", hostname, ip, reciveProgress, task.ID, progressEntry["time_str"])
	data, _ := json.Marshal(progressEntry)
	_ = redis.GetInstance().RPush(context.TODO(), "scan:progress:trace", data).Err()
	// 添加调试日志
	calculatedProgress := round(reciveProgress*0.5, 2)
	log.Infof("微内核-进度计算调试 task_id: %d, 接收进度: %f, 计算进度: %f, 当前数据库进度: %f",
		task.ID, reciveProgress, calculatedProgress, task.Progress)

	if reciveProgress == 0 || calculatedProgress < task.Progress {
		progress = task.Progress
		log.Infof("微内核-进度计算调试 task_id: %d, 使用数据库进度: %f (原因: 接收进度为0或计算进度小于数据库进度)",
			task.ID, progress)
	} else {
		progress = calculatedProgress
		log.Infof("微内核-进度计算调试 task_id: %d, 使用计算进度: %f", task.ID, progress)
	}

	// 构造虚拟进度
	if progress < 5 {
		progress = 5
	}
	if progress < 10 && progress > 5 {
		progress = 10
	}
	if progress < 20 && progress > 10 {
		progress = 20
	}

	if progress >= 50 {
		progress = 49
	}

	// 更新任务进度
	task.Progress = progress
	task.UseSeconds = strconv.FormatInt(getTaskDuration(task), 10)
	taskModel.Update(task)

	// 设置消息内容
	var message string
	if progress > 40 && progress < 50 {
		message = "资产入库中"
	} else {
		message = c.Query("scan_info")
	}

	// 推送扫描信息
	sendProgress(task, progress, "", message)

	log.Infof("微内核-进度回调-progress 微内核-更新扫描进度-进度回调 tid: %d, now: %d, progress: %f",
		task.ID, time.Now().Unix(), progress)

	return response.Gen(c).SendSuccess(ScanAssetCallbackServiceName, response.MsgSuccess, nil)
}

// round 四舍五入到指定小数位
func round(val float64, precision int) float64 {
	ratio := math.Pow(10, float64(precision))
	return math.Round(val*ratio) / ratio
}

// getTaskDuration 计算任务持续时间，如果结果小于0则返回0
func getTaskDuration(task *scan_task.ScanTasks) int64 {
	duration := time.Now().Unix() - task.StartAt.Unix()
	if duration < 0 {
		return 0
	}
	return duration
}

// ScanAssetFinish 处理资产扫描完成回调
func ScanAssetFinish(c *gin.Context) error {
	taskID := c.Query("task_id")
	state := c.Query("state")
	message := c.Query("message")

	log.Infof("微内核-完成回调-finish-开始 task_id: %s, request: %v", taskID, c.Request.URL.Query())

	taskIDUint, _ := strconv.ParseUint(taskID, 10, 64)
	taskModel := scan_task.NewModel()
	task, err := taskModel.First(scan_task.WithID(taskIDUint))
	if err != nil {
		log.Infof("微内核-完成回调-finish 任务已被删除 taskId: %s, request: %v", taskID, c.Request.URL.Query())
		return response.Gen(c).SendSuccess(ScanAssetCallbackServiceName, response.MsgSuccess, nil)
	}

	stateInt, _ := strconv.Atoi(state)

	// 处理任务状态
	switch stateInt {
	case 1, 2:
		log.Infof("微内核-完成回调-finish 微内核不是真正的完成扫描任务 request: %v", c.Request.URL.Query())
		return response.Gen(c).SendSuccess(ScanAssetCallbackServiceName, response.MsgSuccess, nil)
	case 3, 4:
		log.Infof("微内核-完成回调-finish 微内核告诉我任务停止删除了 request: %v", c.Request.URL.Query())
		handleTaskStopped(taskModel, task, message)
		return response.Gen(c).SendSuccess(ScanAssetCallbackServiceName, response.MsgSuccess, nil)
	case 6:
		log.Infof("微内核-完成回调-finish 微内核告诉我任务失败了 request: %v", c.Request.URL.Query())
		handleTaskFailed(taskModel, task, message)
		return response.Gen(c).SendSuccess(ScanAssetCallbackServiceName, response.MsgSuccess, nil)
	}

	// 处理暂停任务
	if task.Status == scan_task.StatusPause {
		log.Infof("微内核-完成回调-finish 任务暂停处理 request: %v", c.Request.URL.Query())
		return response.Gen(c).SendSuccess(ScanAssetCallbackServiceName, response.MsgSuccess, nil)
	}

	// 检查任务状态
	if task.Status != scan_task.StatusDoing {
		log.Infof("微内核-完成回调-finish 任务信息获取失败 request: %v", c.Request.URL.Query())
		return response.Gen(c).SendSuccess(ScanAssetCallbackServiceName, response.MsgSuccess, nil)
	}

	// 更新任务进度
	updateTaskProgress(taskModel, task, message)

	// 处理扫描结果
	cleanInvalidScanResults(task)

	// 发送异步任务
	dispatchAsyncJob(task)

	log.Infof("微内核-完成回调-finish 微内核-完成回调-finish-结束-处理完成-下发异步job task_id: %s, request: %v", taskID, c.Request.URL.Query())

	return response.Gen(c).SendSuccess(ScanAssetCallbackServiceName, response.MsgSuccess, nil)
}

func handleTaskStopped(taskModel scan_task.Model, task *scan_task.ScanTasks, message string) {
	decodedMessage, _ := base64.StdEncoding.DecodeString(message)
	task.Step = scan_task.StepFinished
	task.Progress = 100.00
	task.UseSeconds = strconv.FormatInt(getTaskDuration(task), 10)
	task.Status = scan_task.StatusFinished
	taskModel.Update(task)
	sendProgress(task, 100.00, string(decodedMessage), "扫描任务删除了，扫完已经完成")
}

func handleTaskFailed(taskModel scan_task.Model, task *scan_task.ScanTasks, message string) {
	decodedMessage, _ := base64.StdEncoding.DecodeString(message)
	task.Step = scan_task.StepFinished
	task.Progress = 100.00
	task.UseSeconds = strconv.FormatInt(getTaskDuration(task), 10)
	task.Status = scan_task.StatusFailed
	taskModel.Update(task)
	sendProgress(task, 100.00, string(decodedMessage), "扫描任务失败了，扫完已经完成")
}

func updateTaskProgress(taskModel scan_task.Model, task *scan_task.ScanTasks, message string) {
	decodedMessage, _ := base64.StdEncoding.DecodeString(message)
	task.Step = scan_task.StepSyncData
	task.Progress = 50.00
	task.UseSeconds = strconv.FormatInt(getTaskDuration(task), 10)
	taskModel.Update(task)
	sendProgress(task, 50.00, string(decodedMessage), "资产已入库")

	log.Infof("微内核-打印时间-finish tid: %d, start_time: %d, step: 微内核回调第三步完成回调了打印时间, now: %d, cost: %d",
		task.ID, task.StartAt.Unix(), time.Now().Unix(), getTaskDuration(task))
}

func cleanInvalidScanResults(task *scan_task.ScanTasks) {
	log.Infof("cleanInvalidScanResults-开始清理无效扫描结果 task_id: %d, user_id: %d, ip_type: %d",
		task.ID, task.UserId, task.IpType)

	time.Sleep(5 * time.Second)

	// 获取任务目标IP列表
	log.Infof("cleanInvalidScanResults-开始获取任务目标IP列表 task_id: %d", task.ID)
	thisTaskIps, err := getScanTargetIps(task)
	if err != nil {
		log.Errorf("cleanInvalidScanResults-获取扫描目标IP失败 task_id: %d, error: %s", task.ID, err.Error())
		return
	}

	log.Infof("cleanInvalidScanResults-任务目标IP列表获取完成 task_id: %d, 目标IP数量: %d, 目标IP列表: %v",
		task.ID, len(thisTaskIps), thisTaskIps)

	if len(thisTaskIps) == 0 {
		log.Infof("cleanInvalidScanResults-任务目标IP列表为空，跳过清理 task_id: %d", task.ID)
		return
	}

	// 获取ES客户端
	log.Infof("cleanInvalidScanResults-开始从ES获取扫描结果 task_id: %d", task.ID)
	esClient := es.GetEsClient()
	taskAssetsList, err := getTaskAssets(esClient, uint64(task.ID))
	if err != nil {
		log.Errorf("cleanInvalidScanResults-获取任务资产失败 task_id: %d, error: %s", task.ID, err.Error())
		return
	}

	log.Infof("cleanInvalidScanResults-ES扫描结果获取完成 task_id: %d, 扫描结果数量: %d",
		task.ID, len(taskAssetsList))

	// 打印扫描结果中的所有IP
	var scannedIps []string
	for _, asset := range taskAssetsList {
		scannedIps = append(scannedIps, asset.Ip)
	}
	log.Infof("cleanInvalidScanResults-扫描结果IP列表 task_id: %d, 扫描到的IP: %v",
		task.ID, scannedIps)

	if task.IpType == scan_task.IP_TYPE_V6 {
		log.Infof("cleanInvalidScanResults-处理IPv6任务 task_id: %d", task.ID)

		// 构建完整的IPv6目标列表（包含原始和完整格式）
		newTaskIpsAll := make([]string, 0)
		for _, pip := range thisTaskIps {
			newTaskIpsAll = append(newTaskIpsAll, pip)
			completedIPv6 := utils.CompleteIPV6(pip)
			newTaskIpsAll = append(newTaskIpsAll, completedIPv6)
		}

		log.Infof("cleanInvalidScanResults-IPv6目标列表构建完成 task_id: %d, 原始目标数量: %d, 扩展后目标数量: %d, 扩展后目标列表: %v",
			task.ID, len(thisTaskIps), len(newTaskIpsAll), newTaskIpsAll)

		var validIps []string
		var invalidIps []string
		var deletedIps []string
		var deleteFailedIps []string

		for _, ipinfo := range taskAssetsList {
			thisIp := ipinfo.Ip
			completedThisIp := utils.CompleteIPV6(thisIp)

			// 检查IP是否在目标范围内
			isInRange := utils.CheckIPIsInTaskIP(thisIp, newTaskIpsAll) || utils.CheckIPIsInTaskIP(completedThisIp, newTaskIpsAll)

			if !isInRange {
				log.Infof("cleanInvalidScanResults-发现超出范围的IPv6 task_id: %d, ip: %s, completed_ip: %s, 不在目标范围内，准备删除",
					task.ID, thisIp, completedThisIp)
				invalidIps = append(invalidIps, thisIp)

				err := deleteTaskAsset(esClient, uint64(task.ID), thisIp)
				if err != nil {
					log.Errorf("cleanInvalidScanResults-删除多余IPv6资产失败 ip: %s, task_id: %d, user_id: %d, error: %s",
						thisIp, task.ID, task.UserId, err.Error())
					deleteFailedIps = append(deleteFailedIps, thisIp)
					continue
				}
				log.Infof("cleanInvalidScanResults-成功删除多余IPv6资产 ip: %s, task_id: %d, user_id: %d",
					thisIp, task.ID, task.UserId)
				deletedIps = append(deletedIps, thisIp)
			} else {
				log.Infof("cleanInvalidScanResults-IPv6在目标范围内 task_id: %d, ip: %s, completed_ip: %s, 保留",
					task.ID, thisIp, completedThisIp)
				validIps = append(validIps, thisIp)
			}
		}

		log.Infof("cleanInvalidScanResults-IPv6处理完成 task_id: %d, 有效IP数量: %d, 无效IP数量: %d, 成功删除: %d, 删除失败: %d",
			task.ID, len(validIps), len(invalidIps), len(deletedIps), len(deleteFailedIps))
		log.Infof("cleanInvalidScanResults-IPv6有效IP列表 task_id: %d, valid_ips: %v", task.ID, validIps)
		log.Infof("cleanInvalidScanResults-IPv6无效IP列表 task_id: %d, invalid_ips: %v", task.ID, invalidIps)
		log.Infof("cleanInvalidScanResults-IPv6成功删除IP列表 task_id: %d, deleted_ips: %v", task.ID, deletedIps)
		if len(deleteFailedIps) > 0 {
			log.Errorf("cleanInvalidScanResults-IPv6删除失败IP列表 task_id: %d, failed_ips: %v", task.ID, deleteFailedIps)
		}
	} else {
		log.Infof("cleanInvalidScanResults-处理IPv4任务 task_id: %d", task.ID)

		var validIps []string
		var invalidIps []string
		var deletedIps []string
		var deleteFailedIps []string

		for _, ipinfo := range taskAssetsList {
			thisIp := ipinfo.Ip

			// 检查IP是否在目标范围内
			isInRange := utils.CheckIPIsInTaskIP(thisIp, thisTaskIps)

			if !isInRange {
				log.Infof("cleanInvalidScanResults-发现超出范围的IPv4 task_id: %d, ip: %s, 不在目标范围内，准备删除",
					task.ID, thisIp)
				invalidIps = append(invalidIps, thisIp)

				err := deleteTaskAsset(esClient, uint64(task.ID), thisIp)
				if err != nil {
					log.Errorf("cleanInvalidScanResults-删除多余IPv4资产失败 ip: %s, task_id: %d, user_id: %d, error: %s",
						thisIp, task.ID, task.UserId, err.Error())
					deleteFailedIps = append(deleteFailedIps, thisIp)
					continue
				}
				log.Infof("cleanInvalidScanResults-成功删除多余IPv4资产 ip: %s, task_id: %d, user_id: %d",
					thisIp, task.ID, task.UserId)
				deletedIps = append(deletedIps, thisIp)
			} else {
				log.Infof("cleanInvalidScanResults-IPv4在目标范围内 task_id: %d, ip: %s, 保留",
					task.ID, thisIp)
				validIps = append(validIps, thisIp)
			}
		}

		log.Infof("cleanInvalidScanResults-IPv4处理完成 task_id: %d, 有效IP数量: %d, 无效IP数量: %d, 成功删除: %d, 删除失败: %d",
			task.ID, len(validIps), len(invalidIps), len(deletedIps), len(deleteFailedIps))
		log.Infof("cleanInvalidScanResults-IPv4有效IP列表 task_id: %d, valid_ips: %v", task.ID, validIps)
		log.Infof("cleanInvalidScanResults-IPv4无效IP列表 task_id: %d, invalid_ips: %v", task.ID, invalidIps)
		log.Infof("cleanInvalidScanResults-IPv4成功删除IP列表 task_id: %d, deleted_ips: %v", task.ID, deletedIps)
		if len(deleteFailedIps) > 0 {
			log.Errorf("cleanInvalidScanResults-IPv4删除失败IP列表 task_id: %d, failed_ips: %v", task.ID, deleteFailedIps)
		}
	}

	log.Infof("cleanInvalidScanResults-清理无效扫描结果完成 task_id: %d", task.ID)
}

func dispatchAsyncJob(task *scan_task.ScanTasks) {
	time.Sleep(5 * time.Second)
	asyncq.Enqueue(context.Background(), asyncq.ScanForadarAssetsJob, asyncq.TaskIdPayload{
		TaskId: uint64(task.ID),
	})
}

func sendProgress(task *scan_task.ScanTasks, progress float64, message string, status string) {
	log.Infof("发送进度 task_id: %d, progress: %f, message: %s, status: %s",
		task.ID, progress, message, status)

	// 区分推荐资产扫描和本地扫描的cmd
	var cmd string
	if task.AssetType == scan_task.TASK_ASSET_CLOUD {
		cmd = "scan_task_recpmmand_progress"
	} else if task.OrganizationDiscoverTaskId != nil && *task.OrganizationDiscoverTaskId > 0 {
		cmd = "org_detect_assets_tasks"
	} else {
		cmd = "scan_task_progress"
	}

	// 获取当前正在扫描的IP
	currentScanIP := "" // 这里需要实现获取当前扫描IP的逻辑，暂时设为空字符串

	// 准备发送的参数
	taskDuration := getTaskDuration(task)
	params := map[string]interface{}{
		"task_id":     task.ID,
		"status":      task.Status,
		"step":        task.Step,
		"progress":    round(progress, 2),
		"type":        0,
		"name":        task.Name,
		"target":      "正在扫描资产: " + currentScanIP,
		"use_seconds": taskDuration,
		"us": map[string]interface{}{
			"y":     0,
			"m":     0,
			"d":     0,
			"h":     0,
			"i":     0,
			"s":     int(taskDuration),
			"total": int(taskDuration),
		},
		"message":  message,
		"user_id":  task.UserId,
		"start_at": task.StartAt.Format("2006-01-02 15:04:05"),
	}

	// 发送消息
	if message != "" {
		err := websocket_message.PublishError(int64(task.UserId), cmd, params)
		if err != nil {
			log.Errorf("发送进度错误消息失败: %v", err)
		}
	} else {
		err := websocket_message.PublishSuccess(int64(task.UserId), cmd, params)
		if err != nil {
			log.Errorf("发送进度成功消息失败: %v", err)
		}

		// 判断是否需要发送单位测绘的任务进度
		// 判断当前任务是否为资产测绘下发的扫描任务，如果是的话需要推送进度
		if task.DetectAssetsTasksId > 0 {
			// 查询总共多少个任务
			var totalExpend []scan_task.ScanTasks
			err := mysql.GetInstance().Where("user_id = ? AND detect_assets_tasks_id = ?", task.UserId, task.DetectAssetsTasksId).Find(&totalExpend).Error
			if err != nil {
				log.Errorf("获取资产测绘任务失败: %v", err)
				return
			}

			// 计算完成任务数量
			var finishNum int
			totalExpendNum := len(totalExpend)
			for _, t := range totalExpend {
				if t.Status == scan_task.StatusFinished {
					finishNum++
				}
			}

			// 计算整体进度
			expendProgress := (100.0 * (float64(finishNum) / float64(totalExpendNum))) + progress*(1.0/float64(totalExpendNum))

			// 获取测绘任务信息
			db := mysql.GetInstance()
			var info struct {
				ID         uint64  `gorm:"column:id"`
				Progress   float64 `gorm:"column:progress"`
				StepStatus int     `gorm:"column:step_status"`
				Step       int     `gorm:"column:step"`
				StepDetail string  `gorm:"column:step_detail"`
				Name       string  `gorm:"column:name"`
				UserId     uint64  `gorm:"column:user_id"`
			}
			err = db.Table("detect_assets_tasks").Where("id = ? AND user_id = ?", task.DetectAssetsTasksId, task.UserId).First(&info).Error
			if err != nil {
				log.Errorf("获取测绘任务信息失败: %v", err)
				return
			}

			// 更新测绘任务进度
			if (info.Progress != expendProgress) && (info.Progress < expendProgress) {
				err = db.Table("detect_assets_tasks").Where("id = ? AND user_id = ?", task.DetectAssetsTasksId, task.UserId).
					Update("progress", expendProgress).Error
				if err != nil {
					log.Errorf("更新测绘任务进度失败: %v", err)
				}
			}

			// 发送测绘任务进度
			err = websocket_message.PublishSuccess(int64(task.UserId), "detect_assets_tasks", map[string]interface{}{
				"detect_assets_tasks_id": task.DetectAssetsTasksId,
				"status":                 1,
				"step_status":            info.StepStatus,
				"progress":               round(expendProgress, 2),
				"step":                   info.Step,
				"step_detail":            info.StepDetail,
				"name":                   info.Name,
				"user_id":                task.UserId,
			})
			if err != nil {
				log.Errorf("发送测绘任务进度失败: %v", err)
			}
		}

		// 组织架构测绘
		if task.OrganizationDiscoverTaskId != nil && *task.OrganizationDiscoverTaskId > 0 {
			// 查询总共多少个任务
			var totalExpend []scan_task.ScanTasks
			err := mysql.GetInstance().Where("user_id = ? AND organization_discover_task_id = ?", task.UserId, *task.OrganizationDiscoverTaskId).Find(&totalExpend).Error
			if err != nil {
				log.Errorf("获取组织架构测绘任务失败: %v", err)
				return
			}

			// 计算完成任务数量
			var finishNum int
			totalExpendNum := len(totalExpend)
			for _, t := range totalExpend {
				if t.Status == scan_task.StatusFinished {
					finishNum++
				}
			}

			// 计算整体进度
			expendProgress := (100.0 * (float64(finishNum) / float64(totalExpendNum))) + progress*(1.0/float64(totalExpendNum))

			// 获取组织架构测绘任务信息
			db := mysql.GetInstance()
			var info struct {
				ID           uint64  `gorm:"column:id"`
				ScanProgress float64 `gorm:"column:scan_progress"`
				StepStatus   int     `gorm:"column:step_status"`
				Step         int     `gorm:"column:step"`
				Name         string  `gorm:"column:name"`
				UserId       uint64  `gorm:"column:user_id"`
			}
			err = db.Table("organization_discover_task").Where("id = ? AND user_id = ?", *task.OrganizationDiscoverTaskId, task.UserId).First(&info).Error
			if err != nil {
				log.Errorf("获取组织架构测绘任务信息失败: %v", err)
				return
			}

			// 更新组织架构测绘任务进度
			if (info.ScanProgress != expendProgress) && (info.ScanProgress < expendProgress) {
				err = db.Table("organization_discover_task").Where("id = ? AND user_id = ?", *task.OrganizationDiscoverTaskId, task.UserId).
					Update("scan_progress", expendProgress).Error
				if err != nil {
					log.Errorf("更新组织架构测绘任务进度失败: %v", err)
				}
			}

			// 发送组织架构测绘任务进度
			err = websocket_message.PublishSuccess(int64(task.UserId), "org_detect_assets_tasks", map[string]interface{}{
				"organization_discover_task_id": *task.OrganizationDiscoverTaskId,
				"step_status":                   info.StepStatus,
				"progress":                      round(expendProgress, 2),
				"step":                          info.Step,
				"name":                          info.Name,
				"user_id":                       task.UserId,
			})
			if err != nil {
				log.Errorf("发送组织架构测绘任务进度失败: %v", err)
			}
		}
	}
}

func getScanTargetIps(task *scan_task.ScanTasks) ([]string, error) {
	if task.PortRange == 0 {
		// 获取任务指定的IP列表
		ips, err := task_model.NewTaskIpsModel().FindByQuerys(middleware_mysql.WithColumnValue("task_id", task.ID))
		if err != nil {
			return nil, err
		}
		if len(ips) > 0 {
			var result []string
			for _, ip := range ips {
				result = append(result, ip.Ip.String)
			}
			return result, nil
		}

		log.Infof("ScanAssetCallbackController getScanTargetIps-当前任务在TaskIps表找不到扫描目标，取台账所有ip扫描啊 task_id: %d", task.ID)

		// 如果是单位测绘类型的任务，那么就不能取台账的ip，就返回空就行
		if task.DetectAssetsTasksId != 0 {
			log.Infof("ScanAssetCallbackController getScanTargetIps-当前任务在TaskIps表找不到扫描目标，但是是单位测绘类型的任务，ip直接返回空 task_id: %d", task.ID)
			return []string{}, nil
		}

		log.Infof("ScanAssetCallbackController getScanTargetIps-当前任务在TaskIps表找不到扫描目标，正常的资产扫描任务，取台账的ip task_id: %d", task.ID)

		// 如果ip为空的话，那么需要判断，取台账的ip类型是ipv4还是ipv6进行扫描
		query := fofaee_assets.NewFindCondition()
		query.UserId = task.UserId
		query.Status = []int{fofaee_assets.StatusConfirmAsset}

		// 设置超时时间,避免查询时间过长
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		ipAssets, _, err := fofaee_assets.NewFofaeeAssetsModel().FindByCondition(ctx, query, 0, 0, "ip")
		if err != nil {
			return nil, err
		}

		if len(ipAssets) > 0 {
			var result []string
			for _, ip := range ipAssets {
				// 根据任务类型过滤IP类型
				if task.IpType == scan_task.IP_TYPE_V6 {
					if strings.Contains(ip.Ip, ":") {
						result = append(result, ip.Ip)
					}
				} else {
					if !strings.Contains(ip.Ip, ":") {
						result = append(result, ip.Ip)
					}
				}
			}
			return result, nil
		}
		return []string{}, nil
	}

	return []string{}, nil
}

func getTaskAssets(esClient *elastic.Client, taskID uint64) ([]fofaee_task_assets.FofaeeTaskAssets, error) {
	// 使用ES查询获取任务资产
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 创建查询条件
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("task_id", taskID))

	// 执行查询
	searchResult, err := esClient.Search().
		Index("fofaee_task_assets").
		Type("ips").
		Query(query).
		Size(10000). // 设置一个较大的值，确保获取所有结果
		Do(ctx)

	if err != nil {
		return nil, err
	}

	// 处理查询结果
	var assets []fofaee_task_assets.FofaeeTaskAssets
	for _, hit := range searchResult.Hits.Hits {
		var asset fofaee_task_assets.FofaeeTaskAssets
		if err := json.Unmarshal(*hit.Source, &asset); err != nil {
			log.Errorf("微内核-完成回调 解析任务资产失败 task_id: %d, error: %s", taskID, err.Error())
			continue
		}
		assets = append(assets, asset)
	}

	return assets, nil
}

func deleteTaskAsset(esClient *elastic.Client, taskID uint64, ip string) error {
	// 使用ES删除任务资产
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 创建查询条件
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("task_id", taskID))
	query.Must(elastic.NewTermQuery("ip", ip))

	// 执行删除操作
	_, err := esClient.DeleteByQuery().
		Index("fofaee_task_assets").
		Type("ips").
		Query(query).
		Refresh("true").
		Do(ctx)

	return err
}
