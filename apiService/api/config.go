package api

import (
	"encoding/json"
	"fmt"
	"github.com/tidwall/sjson"
	pb "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/pkg/cfg"
	"strings"

	"github.com/gin-gonic/gin"
	consulApi "github.com/hashicorp/consul/api"
)

type configKV struct {
	Key string `json:"key"` // validate:"required" zh:"Key"
	Val string `json:"val"` // validate:"required" zh:"Value"
}

const consulKey = "consul"
const redisKey = "redis"
const mysqlKey = "mysql"
const rabbitmqKey = "rabbitmq"
const elasticKey = "elastic"

// ConfigGet 获取配置信息
func ConfigGet(ctx *gin.Context) error {
	kv := make(map[string]any, 0)
	// 返回数据
	cfgBytes, _ := json.Marshal(cfg.GetInstance())
	configs := string(cfgBytes)
	// 屏蔽指定数据
	configs, _ = sjson.Set(configs, consulKey, nil)
	configs, _ = sjson.Set(configs, redisKey, nil)
	configs, _ = sjson.Set(configs, mysqlKey, nil)
	configs, _ = sjson.Set(configs, rabbitmqKey, nil)
	configs, _ = sjson.Set(configs, elasticKey, nil)
	if err := json.Unmarshal([]byte(configs), &kv); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", kv)
}

// ConfigSet 更新配置信息
func ConfigSet(ctx *gin.Context) error {
	var kv configKV
	var vJson map[string]any
	if vErr := ctx.ShouldBindJSON(&kv); vErr != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, vErr)
	}
	// 返回数据
	client, err := consulApi.NewClient(&consulApi.Config{
		Address: fmt.Sprintf("%s:%d", cfg.LoadConsul().Address, cfg.LoadConsul().Port),
		Token:   cfg.LoadConsul().Token,
	})
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if jErr := json.Unmarshal([]byte(kv.Val), &vJson); jErr != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, "Value必须是JSON格式:"+jErr.Error())
	}
	kv.Key = strings.TrimPrefix(kv.Key, "/")
	// 跳过指定配置更新
	switch kv.Key {
	case consulKey:
		return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", nil)
	case redisKey:
		return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", nil)
	case mysqlKey:
		return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", nil)
	case elasticKey:
		return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", nil)
	case rabbitmqKey:
		return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", nil)
	}
	// 写入KV数据
	_, wErr := client.KV().Put(&consulApi.KVPair{
		Key:   strings.TrimPrefix(cfg.LoadConsul().Prefix+"/"+kv.Key, "/"),
		Flags: 0,
		Value: []byte(kv.Val),
	}, nil)
	if wErr != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", nil)
}
