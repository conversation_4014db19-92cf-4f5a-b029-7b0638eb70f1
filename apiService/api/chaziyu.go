package api

import (
	"micro-service/apiService/middleware"
	"micro-service/apiService/response"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/microx"
	"micro-service/pkg/utils"
	"time"

	"github.com/gin-gonic/gin"
)

func Subdomain(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var rsp *pb.SubdomainResponse
	param := pb.SubdomainRequest{Domain: ctx.Param("domain")}
	rsp, err := pb.GetProtoCoreClient().Subdomain(middleware.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(3*time.Minute), microx.ServerTimeoutDur(3*time.Minute))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp.Subdomains)
}
