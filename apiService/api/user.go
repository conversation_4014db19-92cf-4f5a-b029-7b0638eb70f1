package api

import (
	"micro-service/apiService/middleware"
	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	"micro-service/pkg/utils"
	"micro-service/pkg/validate"
	web "micro-service/webService/proto"

	"github.com/gin-gonic/gin"
)

func UserList(ctx *gin.Context) error {
	var param = &web.UserListRequest{}
	_ = ctx.ShouldBindQuery(param)

	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}
	if safe_company.IsAdminUser(ctx) && param.GetAll == 1 {
		param.Page, param.PerPage = 0, 0
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := web.GetProtoClient().UserList(ctxSpan, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByError(web.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(web.ServiceName, response.MsgSuccess, rsp)
}

func SetFirstTaskNotice(ctx *gin.Context) error {
	var param = &struct {
		UserId uint64 `form:"user_id" validate:"required,number"`
	}{}
	_ = ctx.ShouldBindQuery(param)

	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := web.GetProtoClient().SetFirstTaskNotice(ctxSpan, &web.SetFirstTaskNoticeRequest{
		UserId: param.UserId,
	}, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByError(web.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(web.ServiceName, response.MsgSuccess, rsp)
}
