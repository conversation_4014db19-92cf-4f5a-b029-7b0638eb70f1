package api

import (
	"strconv"

	"micro-service/apiService/middleware"
	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/pkg/microx"
	"micro-service/pkg/utils"
	"micro-service/pkg/validate"
	pb "micro-service/scanService/proto"

	"github.com/gin-gonic/gin"
)

// CreateFofaScanTask 创建扫描任务
func CreateFofaScanTask(ctx *gin.Context) error {
	var rsp *pb.FofaScanTaskResponse
	param := pb.FofaScanTaskRequest{}
	paramsErr := ctx.BindJSON(&param)
	if paramsErr != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, paramsErr)
	}
	rsp, err := pb.GetProtoClient().CreateFofaScanTask(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp.Data.Id)
}

// StopFofaScanTask 创建扫描任务
func StopFofaScanTask(ctx *gin.Context) error {
	param := pb.FofaScanTaskRequest{}
	paramsErr := ctx.BindJSON(&param)
	if paramsErr != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, paramsErr)
	}
	_, err := pb.GetProtoClient().StopFofaScanTask(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", nil)
}

func DomainSearchTaskInfo(ctx *gin.Context) error {
	taskId, _ := strconv.Atoi(ctx.Query("task_id"))
	if taskId == 0 {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "查询任务不能为空")
	}

	req := &pb.DomainSearchTaskInfoRequest{
		TaskId: uint64(taskId),
	}
	// _, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	// if err != nil {
	// 	return response.Gen(ctx).SendByErrorMsg(api.ServiceName, err.Error())
	// }
	// req.UserId = userId
	rsp, err := pb.GetProtoClient().DomainSearchTaskInfo(ctx, req, microx.SetTimeout(25, 15)...)
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

func DomainSearchTaskCreate(ctx *gin.Context) error {
	var req = &pb.DomainSearchTaskCreateRequest{}
	_ = ctx.ShouldBindJSON(req)

	// sfId, userId, cId, err := safe_company.GetSafeCompanyUser(ctx)
	// if err != nil {
	// 	return response.Gen(ctx).SendByErrorMsg(api.ServiceName, err.Error())
	// }
	//
	// req.UserId = userId
	// req.SafeUserId = sfId
	// req.CompanyId = uint64(cId)
	rsp, err := pb.GetProtoClient().DomainSearchTaskCreate(ctx, req, microx.SetTimeout(60, 45)...)
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

func DomainSearchTaskResultList(ctx *gin.Context) error {
	var req = &pb.DomainSearchResultListRequest{}
	_ = ctx.ShouldBindQuery(req)
	if ok, msg := validate.Validator(req); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	// _, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	// if err != nil {
	// 	return response.Gen(ctx).SendByErrorMsg(api.ServiceName, err.Error())
	// }
	//
	// req.UserId = userId
	rsp, err := pb.GetProtoClient().
		DomainSearchResultList(ctx, req, microx.SetTimeout(60, 45)...)
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}
