package api

import (
	"micro-service/apiService/middleware"
	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/pkg/ginx"
	"micro-service/pkg/microx"
	"micro-service/pkg/utils"
	"micro-service/pkg/validate"
	pb "micro-service/webService/proto"

	"github.com/gin-gonic/gin"
)

func RequestCountList(ctx *gin.Context) error {
	var param = &pb.ApiRequestCountListRequest{}
	_ = ctx.ShouldBindQuery(param)

	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	rsp, err := pb.GetProtoClient().ApiRequestCountList(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageCompanyIcpList 备案管理列表
func ManageCompanyIcpList(ctx *gin.Context) error {
	var param = &pb.ManageBeianListRequest{}
	_ = ctx.ShouldBindQuery(param)

	param.Source = ginx.QueryArray(ctx, "source", true)
	param.CompanyType = utils.StringsToUints64(ginx.QueryArray(ctx, "company_type"))
	param.RecordTime = ginx.QueryArray(ctx, "record_time", true)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	rsp, err := pb.GetProtoClient().ManageBeianList(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageCompanyIcpCreate 创建备案信息
func ManageCompanyIcpCreate(ctx *gin.Context) error {
	var param = &pb.ManageBeianCreateRequest{}
	_ = ctx.ShouldBindJSON(param)

	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	_, err := pb.GetProtoClient().ManageBeianCreate(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(api.ServiceName, response.MsgSuccess, nil)
}

// ManageCompanyIcpUpdate 更新备案信息
func ManageCompanyIcpUpdate(ctx *gin.Context) error {
	var req = &pb.ManageBeianCreateRequest{}
	_ = ctx.ShouldBindJSON(req)

	// 强制更新
	if req.Force == 1 {
		if req.Ids = utils.ListDistinct(req.Ids); len(req.Ids) == 0 {
			return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "更新记录不可为空")
		}
	} else {
		if ok, msg := validate.Validator(req); !ok {
			return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
		}
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	_, err := pb.GetProtoClient().ManageBeianUpdate(ctxSpan, req, utils.SetRpcTimeoutOpt(30), microx.ServerTimeout(120))
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(api.ServiceName, response.MsgSuccess, nil)
}

// ManageDigitalKeywordInfo 数字资产-任务管理：关键词详情
func ManageDigitalKeywordInfo(ctx *gin.Context) error {
	var param = &pb.ManageDigitalKeywordUpsertRequest{}
	_ = ctx.ShouldBindQuery(param)
	if param.Id == 0 {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "查询任务不可为空")
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	rsp, err := pb.GetProtoClient().ManageDigitalKeywordInfo(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageDigitalKeywordUpsert 数字资产-任务管理：新建或更新关键词
func ManageDigitalKeywordUpsert(ctx *gin.Context) error {
	var param = &pb.ManageDigitalKeywordUpsertRequest{}
	_ = ctx.ShouldBindJSON(param)
	if len(param.Name) == 0 && len(param.Ids) == 0 {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "缺失关键词或关键词ID")
	}
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	_, err := pb.GetProtoClient().ManageDigitalKeywordUpsert(ctxSpan, param, utils.SetRpcTimeoutOpt(30), microx.ServerTimeout(180))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, nil)
}

// ManageDigitalKeywordList 数字资产-任务管理：关键词列表
func ManageDigitalKeywordList(ctx *gin.Context) error {
	var param = &pb.ManageDigitalKeywordListRequest{}
	_ = ctx.ShouldBindQuery(param)
	if param.Page == 0 || param.PerPage == 0 {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "分页参数错误")
	}
	param.CreatedAt = ginx.QueryArray(ctx, "created_at", true)
	param.UpdatedAt = ginx.QueryArray(ctx, "updated_at", true)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	rsp, err := pb.GetProtoClient().ManageDigitalKeywordList(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageDigitalKeywordDelete 数字资产-任务管理：删除关键词
func ManageDigitalKeywordDelete(ctx *gin.Context) error {
	var param = &pb.ManageDigitalKeywordListRequest{}
	_ = ctx.ShouldBindJSON(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}

	rsp, err := pb.GetProtoClient().ManageDigitalKeywordDelete(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageDigitalKeywordResultList 数字资产-任务详情：关键词任务结果列表
func ManageDigitalKeywordResultList(ctx *gin.Context) error {
	var param = &pb.ManageDigitalAssetsResultListRequest{}
	_ = ctx.ShouldBindQuery(param)
	if param.Page == 0 || param.PerPage == 0 {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "分页参数错误")
	}
	if param.TaskId == 0 {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "查询任务不可为空")
	}
	param.CreatedAt = ginx.QueryArray(ctx, "created_at_range", true)
	param.UpdatedAt = ginx.QueryArray(ctx, "updated_at_range", true)
	param.CompanyName = ginx.QueryArray(ctx, "company_name", true)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	rsp, err := pb.GetProtoClient().ManageDigitalKeywordResultList(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageDigitalAssetsResultList 数字资产-资产管理：结果列表
func ManageDigitalAssetsResultList(ctx *gin.Context) error {
	var param = &pb.ManageDigitalAssetsResultListRequest{}
	_ = ctx.ShouldBindQuery(param)
	if param.Page == 0 || param.PerPage == 0 {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "分页参数错误")
	}
	param.CreatedAt = ginx.QueryArray(ctx, "created_at_range", true)
	param.UpdatedAt = ginx.QueryArray(ctx, "updated_at_range", true)
	param.CompanyName = ginx.QueryArray(ctx, "company_name", true)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	rsp, err := pb.GetProtoClient().ManageDigitalAssetsResultList(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageDigitalAssetsResultCreate 数字资产-资产管理：新增资产
func ManageDigitalAssetsResultCreate(ctx *gin.Context) error {
	var param = &pb.ManageDigitalAssetsResultItem{}
	_ = ctx.ShouldBindJSON(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	rsp, err := pb.GetProtoClient().ManageDigitalAssetsResultCreate(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageDigitalAssetsResultImport 数字资产-资产管理：总库数据导入
func ManageDigitalAssetsResultImport(ctx *gin.Context) error {
	var param = &pb.ManageDigitalAssetsResultImportRequest{}
	_ = ctx.ShouldBindJSON(&param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	_, err := pb.GetProtoClient().
		ManageDigitalAssetsResultImport(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, nil)
}

// ManageDigitalAssetsResultUpdate 数字资产-资产管理：更新资产
func ManageDigitalAssetsResultUpdate(ctx *gin.Context) error {
	var param = &pb.ManageDigitalAssetsResultItem{}
	_ = ctx.ShouldBindJSON(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := pb.GetProtoClient().ManageDigitalAssetsResultUpdate(ctxSpan, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

func ManageDigitalAssetsResultDelete(ctx *gin.Context) error {
	var param = &pb.ManageDigitalAssetsResultListRequest{}
	_ = ctx.ShouldBindJSON(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	rsp, err := pb.GetProtoClient().ManageDigitalAssetsResultDelete(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageDigitalFilterGroup 分组下拉
func ManageDigitalFilterGroup(ctx *gin.Context) error {
	var param = &pb.ManageDigitalAssetsResultImportRequest{}
	_ = ctx.ShouldBindJSON(param)
	if param.Source == 0 {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "来源不可为空")
	}

	rsp, err := pb.GetProtoClient().ManageDigitalFilterGroup(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageClientApplyList goby client申请列表
func ManageClientApplyList(ctx *gin.Context) error {
	var param = &pb.ApplyListRequest{}
	_ = ctx.ShouldBindQuery(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := pb.GetProtoClient().ManageApplyList(ctxSpan, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageClientApplyAudit goby client申请审核
func ManageClientApplyAudit(ctx *gin.Context) error {
	var param = &pb.ApplyAuditRequest{}
	_ = ctx.ShouldBindJSON(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	_, err := pb.GetProtoClient().ManageApplyAudit(ctxSpan, param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, nil)
}

func ManageIcpAppCreate(ctx *gin.Context) error {
	var param = &pb.IcpAppListItem{}
	_ = ctx.ShouldBindJSON(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	_, err := pb.GetProtoClient().ManageIcpAppCreate(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, nil)
}

func ManageIcpAppUpdate(ctx *gin.Context) error {
	var param = &pb.IcpAppListItem{}
	_ = ctx.ShouldBindJSON(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	param.ForceUpdate = param.Force == 1
	_, err := pb.GetProtoClient().ManageIcpAppUpdate(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, nil)
}

func ManageIcpAppList(ctx *gin.Context) error {
	var param = &pb.IcpAppListRequest{}
	_ = ctx.ShouldBindQuery(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	param.RecordTime = ctx.QueryArray("record_time")
	rsp, err := pb.GetProtoClient().ManageIcpAppList(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// BlackKeywordList 黄赌毒 审核-列表
func BlackKeywordList(ctx *gin.Context) error {
	var param = &pb.BlackKeywordListRequest{}
	_ = ctx.ShouldBindQuery(param)

	param.UserId = utils.StringsToUints64(ginx.QueryArray(ctx, "user_id"))
	param.TypeId = utils.StringsToUints64(ginx.QueryArray(ctx, "type_id"))
	param.Status = utils.ListDistinctNonZero(ginx.QueryArray(ctx, "status"))
	param.CreatedAt = ginx.QueryArray(ctx, "created_at", true)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := pb.GetProtoClient().BlackKeywordList(ctxSpan, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// BlackKeywordUpdate 黄赌毒 审核-审核
func BlackKeywordUpdate(ctx *gin.Context) error {
	var param = &pb.BlackKeywordUpdateRequest{}
	_ = ctx.ShouldBindJSON(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := pb.GetProtoClient().BlackKeywordUpdate(ctxSpan, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageBlackKeywordTypeList 黄赌毒 分类-列表
func ManageBlackKeywordTypeList(ctx *gin.Context) error {
	var param = &pb.Empty{}
	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := pb.GetProtoClient().ManageBlackKeywordTypeList(ctxSpan, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageBlackKeywordTypeCreate 黄赌毒 分裂-新建
func ManageBlackKeywordTypeCreate(ctx *gin.Context) error {
	var param = &pb.BlackKeywordTypeCreateRequest{}
	_ = ctx.ShouldBindJSON(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	_, err := pb.GetProtoClient().ManageBlackKeywordTypeCreate(ctxSpan, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, nil)
}

// ManageBlackKeywordList 黄赌毒 总库-列表
func ManageBlackKeywordList(ctx *gin.Context) error {
	var param = &pb.BlackKeywordListRequest{}
	_ = ctx.ShouldBindQuery(param)

	param.UserId = utils.StringsToUints64(ginx.QueryArray(ctx, "user_id"))
	param.TypeId = utils.StringsToUints64(ginx.QueryArray(ctx, "type_id"))
	param.Status = utils.ListDistinctNonZero(ginx.QueryArray(ctx, "status"))
	param.CreatedAt = utils.ListDistinctNonZero(ginx.QueryArray(ctx, "created_at"))
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := pb.GetProtoClient().ManageBlackKeywordList(ctxSpan, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageBlackKeywordCreate 黄赌毒 总库-新建
func ManageBlackKeywordCreate(ctx *gin.Context) error {
	var param = &pb.ManageBlackKeywordCreateRequest{}
	_ = ctx.ShouldBindJSON(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := pb.GetProtoClient().ManageBlackKeywordCreate(ctxSpan, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageBlackKeywordUpdate 黄赌毒 总库-更新
func ManageBlackKeywordUpdate(ctx *gin.Context) error {
	var param = &pb.ManageBlackKeywordUpdateRequest{}
	_ = ctx.ShouldBindJSON(param)
	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := pb.GetProtoClient().ManageBlackKeywordUpdate(ctxSpan, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ManageBlackKeywordDelete 黄赌毒 总库-删除
func ManageBlackKeywordDelete(ctx *gin.Context) error {
	var param = &pb.BlackKeywordListRequest{}
	_ = ctx.ShouldBindJSON(param)

	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := pb.GetProtoClient().ManageBlackKeywordDelete(ctxSpan, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}
