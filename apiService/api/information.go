package api

import (
	"context"
	api "micro-service/apiService/proto"
	"micro-service/pkg/microx"
	"time"

	"micro-service/apiService/middleware"
	"micro-service/apiService/response"
	pb "micro-service/coreService/proto"
	ipdomain "micro-service/middleware/mysql/ip_domain"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/gin-gonic/gin"
)

func InformationDomain(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.InformationDomainRequest{Domain: ctx.Param("domain")}
	if err := ctx.ShouldBindUri(&param); err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	go informationReqRecord(ctxSpan, "domain", param.Domain)
	rsp, err := pb.GetProtoCoreClient().InformationDomain(ctxSpan, &param, utils.RpcTimeoutDur(3*time.Minute), microx.ServerTimeoutDur(3*time.Minute))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// IP情报获取
func InformationIp(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.InformationIpRequest{Ip: ctx.Param("ip")}
	if err := ctx.ShouldBindUri(&param); err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	ctxSpan := middleware.ContextWithSpan(ctx)
	go informationReqRecord(ctxSpan, "ip", param.Ip)
	rsp, err := pb.GetProtoCoreClient().InformationIp(ctxSpan, &param, utils.RpcTimeoutDur(3*time.Minute), microx.ServerTimeoutDur(3*time.Minute))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

func informationReqRecord(ctx context.Context, x, content string) {
	if content == "" {
		return
	}
	err := ipdomain.NewRecorder(x).Upsert(content)
	if err != nil {
		log.WithContextErrorf(ctx, "[API Service] Information: record %s param failed, err: %+v", x, err)
	}
}
