package api

import (
	api "micro-service/apiService/proto"

	"github.com/gin-gonic/gin"

	"micro-service/apiService/middleware"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

func RuleCount(ctx *gin.Context) error {
	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	req := &pb.AssetsOverviewRuleCountRequest{UserId: userId}
	resp, err := pb.GetProtoClient().
		AssetsOverviewRuleCount(middleware.ContextWithSpan(ctx), req,
			utils.SetRpcTimeoutOpt(30), microx.ServerTimeout(20))
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", resp)
}

func DigitalAssets(ctx *gin.Context) error {
	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	req := &pb.AssetsOverviewDigitalAssetsRequest{UserId: userId}
	resp, err := pb.GetProtoClient().
		AssetsOverviewDigitalAssets(ctx, req, utils.SetRpcTimeoutOpt(30), microx.ServerTimeout(15))
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", resp)
}

func Rate(ctx *gin.Context) error {
	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	resp, err := pb.GetProtoClient().
		AssetsOverviewComparedLastMonth(middleware.ContextWithSpan(ctx),
			&pb.ComparedLastMonthRequest{
				UserId: userId},
			utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", resp)
}

func AssetsDynamic(ctx *gin.Context) error {
	param := &pb.AssetsOverviewAssetsDynamicRequest{}
	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	param.UserId = userId
	if err = ctx.ShouldBindQuery(param); err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	resp, err := pb.GetProtoClient().
		AssetsOverviewAssetsDynamic(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", resp)
}

func AssetsOverviewAssetsDynamicCount(ctx *gin.Context) error {
	param := &pb.AssetsOverviewAssetsDynamicCountRequest{}
	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	param.UserId = userId
	if err = ctx.ShouldBindQuery(param); err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	resp, err := pb.GetProtoClient().
		AssetsOverviewAssetsDynamicCount(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", resp)
}
