package api

import (
	"errors"
	"fmt"
	"net/url"
	"strconv"

	"micro-service/apiService/auth"
	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/auth_access_client"

	"github.com/gin-gonic/gin"
	oauth2 "github.com/go-oauth2/oauth2/v4"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

type TokenInfoOutput struct {
	CompanyName       string `json:"company_name"`
	ClientId          string `json:"client_id"`
	Secret            string `json:"secret"`
	ExpiredAt         string `json:"expired_at"`
	IpWhitelist       string `json:"ip_whitelist"`
	CreatedAt         string `json:"created_at"`
	AccountType       int8   `json:"account_type"`
	DateKey           int32  `json:"date_key"`
	PerCnameLimit     int32  `json:"per_cname_limit"`
	DayAssetsLimit    int32  `json:"day_assets_limit"`
	MonDetectLimit    int32  `json:"mon_detect_limit"`
	DayAssetsCount    int32  `json:"day_assets_count"`
	UsedDetectCount   int32  `json:"used_detect_count"`
	ApiQuotaRemaining int32  `json:"api_quota_remaining"`
}

type UpdateLimitParam struct {
	AssetsCount int32 `json:"assets_count"`
	DetectCount int32 `json:"detect_count"`
	FieldType   int8  `json:"field_type"`
}

// Login 用户登录
func Login(ctx *gin.Context) error {
	ctx.Request.Form = make(url.Values)
	data, err := ctx.GetRawData()
	if err != nil {
		return response.Gen(ctx).SendError(api.ServiceName, 400, err.Error(), nil)
	}

	ctx.Request.Form.Set("username", gjson.Get(string(data), "username").String())
	ctx.Request.Form.Set("password", gjson.Get(string(data), "password").String())
	ctx.Request.Form.Set("grant_type", oauth2.PasswordCredentials.String()) // 授权方式: 用户/密码登陆

	isWechat := gjson.Get(string(data), "is_wechat_login").Int()
	ctx.Request.Form.Set("is_wechat_login", strconv.FormatBool(isWechat == 1))

	err = auth.GetOauth2().GenToken(ctx)

	return err
}

// GetToken 客户端登录
func GetToken(ctx *gin.Context) error {
	ctx.Request.Form = make(url.Values)
	data, err := ctx.GetRawData()
	if err != nil {
		return response.Gen(ctx).SendError(api.ServiceName, 400, err.Error(), nil)
	}
	ctx.Request.Form.Set("client_id", gjson.Get(string(data), "client_id").String())
	ctx.Request.Form.Set("client_secret", gjson.Get(string(data), "client_secret").String())
	ctx.Request.Form.Set("grant_type", oauth2.ClientCredentials.String())
	return auth.GetOauth2().GenToken(ctx)
}

// GenApiToken 客户端登录
func GenApiToken(ctx *gin.Context) error {
	userId, ok := ctx.Get("user_id_with_token")
	if !ok {
		return errors.New("获取用户信息失败")
	}
	ref := cast.ToBool(ctx.Query("ref"))
	ti, err := auth.GetOauth2().GenApiToken(ctx, cast.ToUint64(userId), ref)
	if err != nil {
		return fmt.Errorf("生成API-TOKEN失败:%s", err.Error())
	}
	return response.Gen(ctx).SendSuccess(api.ServiceName, "success", auth.GetOauth2().GServer.GetTokenData(ti))
}

// RefreshToken 刷新Token
func RefreshToken(ctx *gin.Context) error {
	tokenInfo := ctx.MustGet("user_token").(oauth2.TokenInfo)
	ctx.Request.Form = make(url.Values)
	data, err := ctx.GetRawData()
	if err != nil {
		return response.Gen(ctx).SendError(api.ServiceName, 400, err.Error(), nil)
	}
	refToken := gjson.Get(string(data), "refresh_token").String()
	if refToken == "" {
		return response.Gen(ctx).SendError(api.ServiceName, 400, "refresh_token不能为空", nil)
	}
	ctx.Request.Form.Set("refresh_token", gjson.Get(string(data), "refresh_token").String())
	ctx.Request.Form.Set("client_id", tokenInfo.GetClientID())
	ctx.Request.Form.Set("grant_type", oauth2.Refreshing.String())
	return auth.GetOauth2().GenToken(ctx)
}

// 获取client的信息
func GetTokenInfo(ctx *gin.Context) error {
	tokenInfo := ctx.MustGet("user_token").(oauth2.TokenInfo)
	var clientId = tokenInfo.GetClientID()
	conn := auth_access_client.NewAuthAccessClientModel(mysql.GetInstance())
	info, err := conn.GetClientInfoByID(clientId)
	if err != nil {
		return response.Gen(ctx).SendSuccess(api.ServiceName, "Success", "")
	}
	var tokenInfoOutput TokenInfoOutput
	tokenInfoOutput.ClientId = info.ClientId
	tokenInfoOutput.CompanyName = info.CompanyName
	tokenInfoOutput.Secret = info.Secret
	tokenInfoOutput.IpWhitelist = info.IpWhitelist
	tokenInfoOutput.ApiQuotaRemaining = info.ApiQuotaRemaining
	// 解析时间字符串
	tokenInfoOutput.ExpiredAt = info.ExpiredAt.Format("2006-01-02 15:04:05")
	tokenInfoOutput.CreatedAt = info.CreatedAt.Format("2006-01-02 15:04:05")
	if info.AccountType > 0 {
		tokenInfoOutput.AccountType = info.AccountType
		tokenInfoOutput.DateKey = info.DateKey
		tokenInfoOutput.PerCnameLimit = info.PerCnameLimit
		tokenInfoOutput.DayAssetsLimit = info.DayAssetsLimit
		tokenInfoOutput.MonDetectLimit = info.MonDetectLimit
		tokenInfoOutput.DayAssetsCount = info.DayAssetsCount
		tokenInfoOutput.UsedDetectCount = info.UsedDetectCount
	}
	return response.Gen(ctx).SendSuccess(api.ServiceName, "Success", tokenInfoOutput)
}

// UpdateDetectLimit 更新client的限制数量信息
func UpdateDetectLimit(ctx *gin.Context) error {
	tokenInfo := ctx.MustGet("user_token").(oauth2.TokenInfo)
	var clientId = tokenInfo.GetClientID()
	conn := auth_access_client.NewAuthAccessClientModel(mysql.GetInstance())
	var param UpdateLimitParam
	err := ctx.BindJSON(&param)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	err = conn.UpdateLimitByID(clientId, param.AssetsCount, param.DetectCount, param.FieldType)
	if err != nil {
		return response.Gen(ctx).SendSuccess(api.ServiceName, "Success", "更新数据异常")
	}
	successMsg := map[string]string{"update_info": "更新测绘数据限制成功"}
	return response.Gen(ctx).SendSuccess(api.ServiceName, "Success", successMsg)
}

func Scopes(ctx *gin.Context) error {
	return response.Gen(ctx).SendSuccess(api.ServiceName, "Success", gin.H{
		"*":                    "超管(所有接口权限)",
		"client":               "客户端",
		"tenant":               "企业租户",
		"safe":                 "安服",
		"sale":                 "售后人员",
		"QccBasicDetail":       "企查查-企业工商照面",
		"information_domain":   "(IP/域名)情报",
		"discover":             "资产发现",
		"fofa_scan":            "fofa扫描", // router.ScopeFofaScan
		"intelligence_manager": "情报管理",
	})
}
