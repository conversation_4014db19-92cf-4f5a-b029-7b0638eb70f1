package api

import (
	"encoding/json"
	"fmt"
	"micro-service/apiService/middleware"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	"micro-service/pkg/ginx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// GetTaskList 获取任务列表
func GetTaskList(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 验证请求参数
	var param struct {
		Page                int32    `form:"page"`
		PerPage             int32    `form:"per_page"`
		TaskType            int32    `form:"task_type" binding:"required,oneof=1 2"`
		Status              string   `form:"status"`
		CreatedAtRange      []string // 移除form标签，使用ginx.QueryArray处理
		DispatchedAtRange   []string // 移除form标签，使用ginx.QueryArray处理
		EndAtRange          []string // 移除form标签，使用ginx.QueryArray处理
		SortField           string   `form:"sort_field"`
		SortOrder           string   `form:"sort_order"`
		IsSchedule          string   `form:"is_schedule"`
		Name                string   `form:"name"`
		OpId                int32    `form:"op_id"`
		Type                string   `form:"type"`
		OperateCompanyID    int64    `form:"operate_company_id"`
		DetectAssetsTasksId int64    `form:"detect_assets_tasks_id"`
	}

	if err := ctx.ShouldBindQuery(&param); err != nil {
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 使用ginx.QueryArray处理数组参数
	param.CreatedAtRange = ginx.QueryArray(ctx, "created_at_range", true)
	param.DispatchedAtRange = ginx.QueryArray(ctx, "dispatched_at_range", true)
	param.EndAtRange = ginx.QueryArray(ctx, "end_at_range", true)

	// 如果提供了操作企业ID，则使用该ID
	if param.OperateCompanyID > 0 {
		companyID = param.OperateCompanyID
	}

	// 构建请求参数
	req := &pb.GetTaskListRequest{
		UserId:              int64(userID),
		CompanyId:           companyID,
		Page:                param.Page,
		PerPage:             param.PerPage,
		TaskType:            param.TaskType,
		CreatedAtRange:      param.CreatedAtRange,
		DispatchedAtRange:   param.DispatchedAtRange,
		EndAtRange:          param.EndAtRange,
		SortField:           param.SortField,
		SortOrder:           param.SortOrder,
		Name:                param.Name,
		OpId:                int64(param.OpId),
		DetectAssetsTasksId: param.DetectAssetsTasksId,
	}

	// 处理特殊参数 - Status
	if param.Status == "" {
		req.Status = -1
	} else {
		statusInt, err := strconv.Atoi(param.Status)
		if err != nil {
			req.Status = -1
		} else {
			req.Status = int32(statusInt)
		}
	}
	// 处理特殊参数 - IsSchedule
	if param.Type == "" {
		req.Type = -1
	} else {
		isScheduleInt, err := strconv.Atoi(param.Type)
		if err != nil {
			req.Type = -1
		} else {
			req.Type = int32(isScheduleInt)
		}
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().GetTaskList(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextErrorf(ctx, "[获取任务列表] 获取任务列表失败: %v", err)
		return response.Gen(ctx).SendByError("scan_task", err)
	}
	//如果为空，赋值为空数组
	if rsp.Items == nil {
		rsp.Items = []*pb.TaskItem{}
	}

	// 构建响应数据
	data := map[string]interface{}{
		"total":        rsp.Total,
		"per_page":     rsp.PerPage,
		"current_page": rsp.CurrentPage,
		"last_page":    rsp.LastPage,
		"from":         rsp.From,
		"to":           rsp.To,
		"wait_num":     rsp.WaitNum,
		"items":        rsp.Items,
	}

	return response.Gen(ctx).SendSuccess("scan_task", response.MsgSuccess, data)
}

// DeleteTask 删除扫描任务
func DeleteTask(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 验证请求参数
	var param struct {
		TaskType          int32    `json:"task_type" binding:"required,oneof=1 2"`
		Status            string   `json:"status"`
		CreatedAtRange    []string `json:"created_at_range"`
		DispatchedAtRange []string `json:"dispatched_at_range"`
		IsSchedule        int32    `json:"is_schedule"`
		Name              string   `json:"name"`
		OperateCompanyID  int64    `json:"operate_company_id"`
		ID                []int64  `json:"id"`
		UserID            string   `json:"user_id"`
		Type              string   `form:"type"`
	}

	if err := ctx.ShouldBindJSON(&param); err != nil {
		log.WithContextErrorf(ctx, "[删除扫描任务] 参数解析错误: %v, body: %v", err, ctx.Request.Body)
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 构建请求参数
	req := &pb.DeleteTaskRequest{
		UserId:            int64(userID),
		CompanyId:         int64(companyID),
		TaskType:          param.TaskType,
		Name:              param.Name,
		CreatedAtRange:    param.CreatedAtRange,
		DispatchedAtRange: param.DispatchedAtRange,
		OperateCompanyId:  param.OperateCompanyID,
	}

	// 处理特殊参数 - Status
	if param.Status == "" {
		req.Status = -1
	} else {
		statusInt, err := strconv.Atoi(param.Status)
		if err != nil {
			req.Status = -1
		} else {
			req.Status = int32(statusInt)
		}
	}

	// 处理特殊参数 - IsSchedule
	if param.Type == "" {
		req.Type = -1
	} else {
		isScheduleInt, err := strconv.Atoi(param.Type)
		if err != nil {
			req.Type = -1
		} else {
			req.Type = int32(isScheduleInt)
		}
	}

	// 添加任务ID列表
	if len(param.ID) > 0 {
		req.Id = param.ID
	}

	// 调用RPC服务，设置较长的超时时间
	_, err = pb.GetProtoClient().DeleteTask(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		log.WithContextErrorf(ctx, "[删除扫描任务] 删除扫描任务失败: %v", err)
		// 检查是否是超时错误
		if strings.Contains(err.Error(), "Request Timeout") || strings.Contains(err.Error(), "408") {
			// 超时错误，但任务可能已经被删除，返回成功
			log.WithContextWarnf(ctx, "[删除扫描任务] RPC调用超时，但任务可能已删除成功")
			return response.Gen(ctx).SendSuccess("scan_task", response.MsgSuccess, nil)
		}
		return response.Gen(ctx).SendByError("scan_task", err)
	}

	return response.Gen(ctx).SendSuccess("scan_task", response.MsgSuccess, nil)
}

// GetTaskDetail 获取扫描任务详情
func GetTaskDetail(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 获取任务ID
	taskIDStr := ctx.Param("task_id")
	if taskIDStr == "" {
		return response.Gen(ctx).SendByErrorMsg("scan_task", "任务ID不能为空!")
	}

	taskID, err := strconv.ParseInt(taskIDStr, 10, 64)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg("scan_task", "任务ID格式错误!")
	}

	// 获取操作企业ID
	operateCompanyID := ctx.Query("operate_company_id")
	var opCompanyID int64
	if operateCompanyID != "" {
		opCompanyID, err = strconv.ParseInt(operateCompanyID, 10, 64)
		if err != nil {
			return response.Gen(ctx).SendByErrorMsg("scan_task", "操作企业ID格式错误!")
		}
	}

	// 如果提供了操作企业ID，则使用该ID
	if opCompanyID > 0 {
		companyID = opCompanyID
	}

	// 构建请求参数
	req := &pb.GetTaskDetailRequest{
		UserId:           int64(userID),
		CompanyId:        companyID,
		TaskId:           taskID,
		OperateCompanyId: opCompanyID,
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().GetTaskDetail(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextErrorf(ctx, "[获取任务详情] 获取任务详情失败: %v", err)
		return response.Gen(ctx).SendByError("scan_task", err)
	}

	return response.Gen(ctx).SendSuccess("scan_task", response.MsgSuccess, rsp)
}

// GetTaskResult 获取已经完成扫描的资产扫描任务的扫描结果列表
func GetTaskResult(ctx *gin.Context) error {
	// 打印原始URL参数
	log.WithContextInfof(ctx, "【调试】原始URL: %s", ctx.Request.URL.String())
	log.WithContextInfof(ctx, "【调试】原始URL参数: %v", ctx.Request.URL.Query())

	// 手动解析参数，因为protobuf生成的结构体没有form标签
	var param pb.GetTaskResultRequest

	// 解析基本参数
	if id := ctx.Query("id"); id != "" {
		if idInt, err := strconv.ParseInt(id, 10, 64); err == nil {
			param.Id = idInt
		}
	}

	if page := ctx.Query("page"); page != "" {
		if pageInt, err := strconv.ParseInt(page, 10, 32); err == nil {
			param.Page = int32(pageInt)
		}
	}

	if perPage := ctx.Query("per_page"); perPage != "" {
		if perPageInt, err := strconv.ParseInt(perPage, 10, 32); err == nil {
			param.PerPage = int32(perPageInt)
		}
	}

	param.Keyword = ctx.Query("keyword")

	// 解析数组参数
	param.RuleTags = ginx.QueryArray(ctx, "rule_tags", true)
	param.City = ginx.QueryArray(ctx, "city", true)
	param.Ports = ginx.QueryArray(ctx, "ports", true)
	param.Protocols = ginx.QueryArray(ctx, "protocols", true)
	param.CompanyTags = ginx.QueryArray(ctx, "company_tags", true)
	param.SecondCatTag = ginx.QueryArray(ctx, "second_cat_tag", true)

	// 解析state数组
	stateStrings := ginx.QueryArray(ctx, "state", true)
	for _, stateStr := range stateStrings {
		if stateInt, err := strconv.ParseInt(stateStr, 10, 32); err == nil {
			param.State = append(param.State, int32(stateInt))
		}
	}

	// 获取用户ID和企业ID
	_, userID, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError("ignore_assets", err)
	}
	// 打印解析后的参数
	log.WithContextInfof(ctx, "【调试】解析后的参数: rule_tags=%+v, id=%d, page=%d", param.RuleTags, param.Id, param.Page)
	// 设置用户ID和企业ID
	param.UserId = int64(userID)
	// 调用RPC服务
	rsp, err := pb.GetProtoClient().GetTaskResult(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果] 获取扫描任务结果失败: %v", err)
		return response.Gen(ctx).SendByError("scan_task", err)
	}

	//处理items，添加 _id 字段
	var items []map[string]interface{}
	if err := json.Unmarshal(rsp.Items, &items); err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果] 解析扫描任务结果失败: %v", err)
		return response.Gen(ctx).SendByError("scan_task", err)
	}

	for i := range items {
		items[i]["_id"] = items[i]["id"]
	}

	// 将修改后的items重新序列化
	modifiedItems, err := json.Marshal(items)
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果] 序列化修改后的结果失败: %v", err)
		return response.Gen(ctx).SendByError("scan_task", err)
	}

	// 序列化结果
	return response.Gen(ctx).SendSuccess("scan_task", response.MsgSuccess, gin.H{
		"total":        rsp.Total,
		"per_page":     rsp.PerPage,
		"current_page": rsp.CurrentPage,
		"last_page":    rsp.LastPage,
		"from":         rsp.From,
		"to":           rsp.To,
		"items":        json.RawMessage(modifiedItems),
		"has_data":     rsp.HasData,
	})
}

// GetTaskResultCondition 获取扫描任务结果筛选条件
func GetTaskResultCondition(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 验证请求参数
	var param struct {
		ID               int64    `form:"id" binding:"required"`
		IP               string   `form:"ip"`
		State            []int32  `form:"state"`
		RuleTags         []string `form:"rule_tags"`
		City             []string `form:"city"`
		Ports            []string `form:"ports"`
		Protocols        []string `form:"protocols"`
		CompanyTags      []string `form:"company_tags"`
		SecondCatTag     []string `form:"second_cat_tag"`
		Keyword          []string `form:"keyword"`
		OperateCompanyID int64    `form:"operate_company_id"`
	}

	if err := ctx.ShouldBindQuery(&param); err != nil {
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 构建请求参数
	req := &pb.GetTaskResultConditionRequest{
		Id:               param.ID,
		UserId:           int64(userID),
		Ip:               param.IP,
		State:            param.State,
		RuleTags:         param.RuleTags,
		City:             param.City,
		Ports:            param.Ports,
		Protocols:        param.Protocols,
		CompanyTags:      param.CompanyTags,
		SecondCatTag:     param.SecondCatTag,
		Keyword:          param.Keyword,
		OperateCompanyId: int64(companyID),
	}

	// 如果提供了操作企业ID，则使用该ID
	if param.OperateCompanyID > 0 {
		req.OperateCompanyId = param.OperateCompanyID
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().GetTaskResultCondition(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果筛选条件] 获取筛选条件失败: %v", err)
		return response.Gen(ctx).SendByError("scan_task", err)
	}

	return response.Gen(ctx).SendSuccess("scan_task", response.MsgSuccess, rsp)
}

// TaskResultExport 任务结果导出
func TaskResultExport(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 验证请求参数 - 添加前端可能发送的所有字段
	var param struct {
		ID               string        `json:"id" binding:"required"`
		IpID             []string      `json:"ip_id"`
		Ip               string        `json:"ip"`
		ExpectIp         string        `json:"expect_ip"` // 前端发送的字段
		State            []int32       `json:"state"`
		RuleTags         []string      `json:"rule_tags"`
		City             []string      `json:"city"`
		Ports            []interface{} `json:"ports"`    // 改为interface{}以支持混合类型
		ChPorts          []interface{} `json:"ch_ports"` // 前端发送的字段
		Protocols        []string      `json:"protocols"`
		CompanyTags      []string      `json:"company_tags"`
		SecondCatTag     []string      `json:"second_cat_tag"`
		Keyword          string        `json:"keyword"`
		OperateCompanyID int64         `json:"operate_company_id"`
		Page             int32         `json:"page"`     // 前端发送的字段
		PerPage          int32         `json:"per_page"` // 前端发送的字段
	}

	if err := ctx.ShouldBindJSON(&param); err != nil {
		log.WithContextErrorf(ctx, "[任务结果导出] 参数解析失败: %v", err)
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 转换任务ID为int64
	taskID, err := strconv.ParseInt(param.ID, 10, 64)
	if err != nil {
		log.WithContextErrorf(ctx, "[任务结果导出] 任务ID格式错误: %v", err)
		return response.Gen(ctx).SendByErrorMsg("scan_task", "任务ID格式错误!")
	}

	// 处理端口参数，将interface{}数组转换为字符串数组
	var ports []string
	for _, port := range param.Ports {
		switch v := port.(type) {
		case string:
			ports = append(ports, v)
		case float64:
			ports = append(ports, strconv.FormatFloat(v, 'f', 0, 64))
		case int:
			ports = append(ports, strconv.Itoa(v))
		case int64:
			ports = append(ports, strconv.FormatInt(v, 10))
		default:
			ports = append(ports, fmt.Sprintf("%v", v))
		}
	}

	// 构建请求参数
	req := &pb.TaskResultExportRequest{
		Id:               taskID,
		UserId:           int64(userID),
		CompanyId:        int64(companyID),
		IpId:             param.IpID,
		Ip:               param.Ip,
		State:            param.State,
		RuleTags:         param.RuleTags,
		City:             param.City,
		Ports:            ports,
		Protocols:        param.Protocols,
		CompanyTags:      param.CompanyTags,
		SecondCatTag:     param.SecondCatTag,
		Keyword:          param.Keyword,
		OperateCompanyId: param.OperateCompanyID,
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().TaskResultExport(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(300))
	if err != nil {
		log.WithContextErrorf(ctx, "[任务结果导出] 导出失败: %v", err)
		return response.Gen(ctx).SendByError("scan_task", err)
	}

	return response.Gen(ctx).SendSuccess("scan_task", response.MsgSuccess, rsp)
}

// TaskResultAnalyse 扫描结果数据汇总
func TaskResultAnalyse(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 验证请求参数
	var param struct {
		ID               string   `form:"id" binding:"required"`
		IP               string   `form:"ip"`
		State            []int32  `form:"state"`
		RuleTags         []string `form:"rule_tags"`
		City             []string `form:"city"`
		Ports            []string `form:"ports"`
		Protocols        []string `form:"protocols"`
		CompanyTags      []string `form:"company_tags"`
		SecondCatTag     []string `form:"second_cat_tag"`
		Keyword          []string `form:"keyword"`
		OperateCompanyID int64    `form:"operate_company_id"`
	}

	if err := ctx.ShouldBindQuery(&param); err != nil {
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 构建请求参数
	req := &pb.TaskResultAnalyseRequest{
		Id:               param.ID,
		Ip:               param.IP,
		State:            param.State,
		RuleTags:         param.RuleTags,
		City:             param.City,
		Ports:            param.Ports,
		Protocols:        param.Protocols,
		CompanyTags:      param.CompanyTags,
		SecondCatTag:     param.SecondCatTag,
		Keyword:          param.Keyword,
		UserId:           int64(userID),
		OperateCompanyId: param.OperateCompanyID,
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().TaskResultAnalyse(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(300))
	if err != nil {
		log.WithContextErrorf(ctx, "[扫描结果数据汇总] 获取数据失败: %v", err)
		return response.Gen(ctx).SendByError("scan_task", err)
	}

	return response.Gen(ctx).SendSuccess("scan_task", response.MsgSuccess, rsp)
}

// CreateScanTask 下发扫描任务
func CreateScanTask(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 验证请求参数
	var param struct {
		Name                string                   `json:"name" binding:"required"`
		TaskType            int32                    `json:"task_type" binding:"required,oneof=1 2"`
		WebLogoSwitch       int32                    `json:"web_logo_switch" binding:"required_if=TaskType 1,omitempty,oneof=0 1"`
		Bandwidth           int32                    `json:"bandwidth" binding:"required,min=100,max=5000"`
		ScanRange           int32                    `json:"scan_range" binding:"required,oneof=0 1 2 3 4 5 6"`
		PocScanType         *int32                   `json:"poc_scan_type" binding:"omitempty,oneof=0 1 2"`
		Ips                 []string                 `json:"ips" binding:"omitempty"`
		PingSwitch          *int32                   `json:"ping_switch" binding:"omitempty"`
		ScanType            int32                    `json:"scan_type" binding:"required,oneof=0 1 2"`
		ProtocolConcurrency *int32                   `json:"protocol_concurrency" binding:"omitempty,min=0,max=300"`
		PocIds              []int64                  `json:"poc_ids" binding:"omitempty"`
		PocGroupIds         string                   `json:"poc_group_ids" binding:"omitempty"`
		PortIds             []int64                  `json:"port_ids" binding:"omitempty"`
		PortGroupIds        int64                    `json:"port_group_ids" binding:"omitempty"`
		IpType              int32                    `json:"ip_type" binding:"required_if=TaskType 1,omitempty,oneof=1 2"`
		OperateCompanyID    int64                    `json:"operate_company_id" binding:"omitempty"`
		FileName            string                   `json:"file_name" binding:"omitempty"`
		Urls                []string                 `json:"urls" binding:"omitempty"`
		PortRange           *int32                   `json:"port_range" binding:"omitempty,oneof=0 1"`
		RawgrabPorts        []map[string]interface{} `json:"rawgrab_ports" binding:"omitempty"`
		IsDefinePort        int32                    `json:"is_define_port" binding:"required,oneof=0 1"`
		DefinePorts         []int32                  `json:"define_ports" binding:"required_if=IsDefinePort 1,omitempty"`
		DefinePortProtocols []int32                  `json:"define_port_protocols" binding:"required_if=IsDefinePort 1,omitempty"`
		Type                int32                    `json:"type" binding:"omitempty"`
		TableAssetsType     int32                    `json:"table_assets_type" binding:"omitempty"`
		ScanEngine          string                   `json:"scan_engine" binding:"omitempty"`
		DayOfX              string                   `json:"day_of_x" binding:"omitempty"`
		ScheduleTime        string                   `json:"schedule_time" binding:"omitempty"`
	}

	if err := ParseJSON(ctx, &param); err != nil {
		log.WithContextErrorf(ctx, "[下发扫描任务] 参数解析错误: %v", err)
		return response.Gen(ctx).SendByErrorMsg("scan_task", "参数错误!")
	}

	// 构建请求参数
	req := &pb.CreateScanTaskRequest{
		UserId:              int64(userID),
		CompanyId:           companyID,
		Name:                param.Name,
		TaskType:            param.TaskType,
		Bandwidth:           param.Bandwidth,
		ScanRange:           param.ScanRange,
		Ips:                 param.Ips,
		ScanType:            param.ScanType,
		IpType:              param.IpType,
		OperateCompanyId:    param.OperateCompanyID,
		FileName:            param.FileName,
		Urls:                param.Urls,
		IsDefinePort:        param.IsDefinePort,
		DefinePorts:         param.DefinePorts,
		DefinePortProtocols: param.DefinePortProtocols,
		PortGroupIds:        param.PortGroupIds,
		PortIds:             param.PortIds,
		PocGroupIds:         param.PocGroupIds,
		PocIds:              param.PocIds,
		Type:                param.Type,
		TableAssetsType:     param.TableAssetsType,
		ScanEngine:          param.ScanEngine,
		DayOfX:              param.DayOfX,
		ScheduleTime:        param.ScheduleTime,
	}

	// 处理可选参数
	if param.WebLogoSwitch > 0 {
		req.WebLogoSwitch = param.WebLogoSwitch
	}

	if param.PingSwitch != nil {
		req.PingSwitch = *param.PingSwitch
	}

	if param.ProtocolConcurrency != nil {
		req.ProtocolConcurrency = *param.ProtocolConcurrency
	}

	if param.PocScanType != nil {
		req.PocScanType = *param.PocScanType
	}

	if param.PortRange != nil {
		req.PortRange = *param.PortRange
		// 处理RawgrabPorts参数
		if len(param.RawgrabPorts) > 0 {
			jsonData, err := json.Marshal(param.RawgrabPorts)
			if err == nil {
				req.RawgrabPorts = string(jsonData)
			}
		}
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().CreateScanTask(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(300))
	if err != nil {
		log.WithContextErrorf(ctx, "[下发扫描任务] 下发扫描任务失败: %v", err)
		return response.Gen(ctx).SendByError("scan_task", err)
	}

	return response.Gen(ctx).SendSuccess("scan_task", response.MsgSuccess, map[string]interface{}{
		"task_id":      rsp.TaskId,
		"warn_message": rsp.WarnMessage,
	})
}
