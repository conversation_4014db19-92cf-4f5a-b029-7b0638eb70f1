package api

import (
	"github.com/gin-gonic/gin"
	"micro-service/apiService/middleware"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

func SetTitleBlackKeyword(ctx *gin.Context) error {
	// 解析请求参数
	var req pb.TitleBlackKeywordRequest
	err := ParseJSON(ctx, &req)
	if err != nil {
		log.WithContextErrorf(ctx, "参数解析错误: %v", err)
		return response.Gen(ctx).SendByError("unsure_assets", err)
	}

	_, userID, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError("unsure_assets", err)
	}
	// 设置用户ID
	req.UserId = userID

	// 调用RPC服务导出数据
	rsp, err := pb.GetProtoClient().AddTitleBlackKeyword(middleware.ContextWithSpan(ctx), &req, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextErrorf(ctx, "[导出疑似资产] 导出失败: %v", err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	// 返回导出结果
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}
