package api

import (
	"errors"
	"micro-service/apiService/middleware"
	"micro-service/apiService/response"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/pkg/validate"
	pb "micro-service/webService/proto"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

func AccountApplyByHand(ctx *gin.Context) error {
	var req pb.AccountOpenByHandRequest
	_ = ctx.ShouldBindJSON(&req)

	if ok, msg := validate.Validator(&req); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}

	userId, ok := ctx.Get("user_id_with_token")
	if !ok {
		return errors.New("获取用户信息失败")
	}
	uid := cast.ToInt64(userId)
	if uid != cfg.LoadCommon().AccountApplyByHandUid {
		return errors.New("非指定用户无权限")
	}
	req.UserId = uid
	rsp, err := pb.GetProtoClient().AccountOpenByHand(middleware.ContextWithSpan(ctx), &req, utils.SetRpcTimeoutOpt(3))
	if err != nil {
		log.WithContextErrorf(ctx, "request api %s: %+v", ctx.Request.URL.Path, err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

func ListAccountApplyByHand(ctx *gin.Context) error {
	userId, ok := ctx.Get("user_id_with_token")
	if !ok {
		return errors.New("获取用户信息失败")
	}
	uid := cast.ToInt64(userId)
	if uid != cfg.LoadCommon().AccountApplyByHandUid {
		return errors.New("非指定用户无权限")
	}
	req := &pb.ListAccountOpenByHandRequest{
		UserId: uid,
	}
	resp, err := pb.GetProtoClient().ListAccountOpenByHand(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(3))
	if err != nil {
		log.WithContextErrorf(ctx, "request api %s: %+v", ctx.Request.URL.Path, err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	//暂时没有很好的方法，先这样处理，jsonpb，protojson在使用时有很多限制
	var result []*AccountOpenByHandInfoResult
	for _, v := range resp.Data {
		t := &AccountOpenByHandInfoResult{
			CompanyName:     v.CompanyName,
			Username:        v.Username,
			Email:           v.Email,
			Mobile:          v.Mobile,
			InitialPassword: v.InitialPassword,
			Status:          v.Status,
			IsFormal:        v.IsFormal,
			ExpiresAt:       v.ExpiresAt,
			CreatedAt:       v.CreatedAt,
			UpdatedAt:       v.UpdatedAt,
			BlackIpSwitch:   v.BlackIpSwitch,

			CompanyInfo: &CompaniesInfoResult{},
		}
		if v.CompanyInfo != nil {
			t.CompanyInfo = &CompaniesInfoResult{
				DataLeakRate:        v.CompanyInfo.DataLeakRate,
				NewAssetRate:        v.CompanyInfo.NewAssetRate,
				LimitCloudRecommend: v.CompanyInfo.LimitCloudRecommend,
				LimitIpAsset:        v.CompanyInfo.LimitIpAsset,
				LimitNewAsset:       v.CompanyInfo.LimitNewAsset,
				LimitPocScan:        v.CompanyInfo.LimitPocScan,
				LimitDataLeak:       v.CompanyInfo.LimitDataLeak,
				LimitMonitorKeyword: v.CompanyInfo.LimitMonitorKeyword,
				UsedIpAsset:         v.CompanyInfo.UsedIpAsset,
				UsedCloudRecommend:  v.CompanyInfo.UsedCloudRecommend,
				UsedNewAsset:        v.CompanyInfo.UsedNewAsset,
				UsedPocScan:         v.CompanyInfo.UsedPocScan,
				UsedDataLeak:        v.CompanyInfo.UsedDataLeak,
				UsedMonitorKeyword:  v.CompanyInfo.UsedMonitorKeyword,
			}
		}
		result = append(result, t)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, result)

}

type AccountOpenByHandInfoResult struct {
	CompanyName     string               `json:"company_name"`
	Username        string               `json:"username"`
	Email           string               `json:"email"`
	Mobile          string               `json:"mobile"`
	InitialPassword string               `json:"initial_password"`
	Status          int64                `json:"status"`     // 状态【0账号生成中、1启用、2禁用】
	IsFormal        bool                 `json:"is_formal"`  // 用户属性【0测试用户、1正式用户】
	ExpiresAt       string               `json:"expires_at"` // 授权到期时间
	CreatedAt       string               `json:"created_at"`
	UpdatedAt       string               `json:"updated_at"`
	BlackIpSwitch   string               `json:"black_ip_switch"` // 黑ip封禁
	CompanyInfo     *CompaniesInfoResult `json:"company_info"`
}

type CompaniesInfoResult struct {
	DataLeakRate        int64 `json:"data_leak_rate"`        // 数据泄露推荐频率 0/1/2/3 关闭/每月一次/每两个月一次/每季度一次
	NewAssetRate        int64 `json:"new_asset_rate"`        // 新型资产推荐频率 0/1/2/3 关闭/每月一次/每两个月一次/每季度一次
	LimitCloudRecommend int64 `json:"limit_cloud_recommend"` // 云端推荐限制次数
	LimitIpAsset        int64 `json:"limit_ip_asset"`        // IP资产限制数量/个
	LimitNewAsset       int64 `json:"limit_new_asset"`       // 新型资产限制次数
	LimitPocScan        int64 `json:"limit_poc_scan"`        // 漏洞管理次数
	LimitDataLeak       int64 `json:"limit_data_leak"`       // 数据泄露推荐次数
	LimitMonitorKeyword int64 `json:"limit_monitor_keyword"` // 关键字监控/个
	UsedIpAsset         int64 `json:"used_ip_asset"`         // 已使用IP资产/个
	UsedCloudRecommend  int64 `json:"used_cloud_recommend"`  // 已使用云端推荐/次
	UsedNewAsset        int64 `json:"used_new_asset"`        // 已使用新型资产/次
	UsedPocScan         int64 `json:"used_poc_scan"`         // 已使用漏洞管理/次
	UsedDataLeak        int64 `json:"used_data_leak"`        // 已使用数据泄露推荐/次
	UsedMonitorKeyword  int64 `json:"used_monitor_keyword"`  // 已使用关键字监控/个
}
