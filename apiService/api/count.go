package api

import (
	"errors"
	"micro-service/apiService/middleware"
	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/utils"

	"github.com/gin-gonic/gin"
)

func HoldingCompanyCount(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var param pb.HoldingCompanyCountRequest
	err := ctx.ShouldBindJSON(&param)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, errors.New("参数校验失败"))
	}

	rsp, err := pb.GetProtoCoreClient().HoldingCompanyCount(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}
