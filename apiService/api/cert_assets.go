package api

import (
	"bytes"
	"encoding/json"
	"io"
	"micro-service/apiService/middleware"
	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	"micro-service/pkg/ginx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/pkg/validate"
	pb "micro-service/webService/proto"
	"strconv"

	"github.com/gin-gonic/gin"
)

func GetCertAssetsList(ctx *gin.Context) error {
	var param pb.CertAssetListRequest
	_ = ctx.ShouldBindQuery(&param)

	// 使用ginx.QueryArray处理数组参数
	// 注意：protobuf字段名是created_at_range，但form标签是created_at
	param.CreatedAtRange = ginx.QueryArray(ctx, "created_at", true)
	param.UpdatedAtRange = ginx.QueryArray(ctx, "updated_at", true)

	// 处理company_name数组参数，支持PHP风格的数组格式
	param.CompanyName = ginx.QueryArray(ctx, "company_name", true)

	// 添加调试输出
	log.WithContextInfof(ctx, "证书资产列表请求参数", map[string]interface{}{
		"CompanyName":    param.CompanyName,
		"CreatedAtRange": param.CreatedAtRange,
		"UpdatedAtRange": param.UpdatedAtRange,
	})

	// 手动处理not_after参数，区分不传、传0、传1的情况
	notAfterParam := ctx.Query("not_after")
	if notAfterParam != "" {
		// 参数存在，解析其值
		if notAfterValue, err := strconv.ParseInt(notAfterParam, 10, 64); err == nil {
			param.NotAfter = notAfterValue
		}
	} else {
		// 参数不存在，设置为特殊值-1表示不过滤
		param.NotAfter = -1
	}

	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	param.UserId = userId
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}
	if len(param.CompanyName) == 1 && param.CompanyName[0] == "" {
		param.CompanyName = nil
	}
	// 如果ginx.QueryArray返回空数组，也设置为nil
	if len(param.CompanyName) == 0 {
		param.CompanyName = nil
	}

	rsp, err := pb.GetProtoClient().CertAssetList(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	// 处理ip_domain_list格式，将CertAssetDomainArray转换为直接的字符串数组
	processedRsp := processCertAssetListResponse(rsp)
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, processedRsp)
}

func ExportCertAssets(ctx *gin.Context) error {
	// 先读取原始JSON body来检查not_after参数的原始值
	bodyBytes, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	// 解析原始JSON以检查not_after参数
	var rawBody map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &rawBody); err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	// 重新设置body，以便ParseJSON使用
	ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 使用ParseJSON来处理字符串到数字的转换
	var param pb.CertAssetExportRequest
	err = ParseJSON(ctx, &param)
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	// 特殊处理not_after参数，区分空字符串和数值
	if notAfterValue, exists := rawBody["not_after"]; exists {
		switch v := notAfterValue.(type) {
		case string:
			switch v {
			case "":
				// 空字符串表示不过滤
				param.NotAfter = -1
			case "0":
				// 字符串"0"表示过滤已过期证书
				param.NotAfter = 0
			case "1":
				// 字符串"1"表示过滤未过期证书
				param.NotAfter = 1
			}
			// ParseJSON已经处理了其他数值字符串的转换
		}
	} else {
		// 参数不存在，设置为特殊值-1表示不过滤
		param.NotAfter = -1
	}

	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	param.UserId = userId
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	rsp, err := pb.GetProtoClient().CertAssetExport(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

func DeleteCertAssets(ctx *gin.Context) error {
	// 先读取原始JSON body来检查not_after参数的原始值
	bodyBytes, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	// 解析原始JSON以检查not_after参数
	var rawBody map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &rawBody); err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	// 重新设置body，以便ParseJSON使用
	ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 使用ParseJSON来处理字符串到数字的转换
	var param pb.CertAssetExportRequest
	err = ParseJSON(ctx, &param)
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	// 特殊处理not_after参数，区分空字符串和数值
	if notAfterValue, exists := rawBody["not_after"]; exists {
		switch v := notAfterValue.(type) {
		case string:
			switch v {
			case "":
				// 空字符串表示不过滤
				param.NotAfter = -1
			case "0":
				// 字符串"0"表示过滤已过期证书
				param.NotAfter = 0
			case "1":
				// 字符串"1"表示过滤未过期证书
				param.NotAfter = 1
			}
			// ParseJSON已经处理了其他数值字符串的转换
		}
	} else {
		// 参数不存在，设置为特殊值-1表示不过滤
		param.NotAfter = -1
	}

	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	param.UserId = userId
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	rsp, err := pb.GetProtoClient().CertAssetDelete(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// processCertAssetListResponse 处理证书资产列表响应，将ip_domain_list格式转换为PHP兼容格式
func processCertAssetListResponse(rsp *pb.CertAssetListResponse) map[string]interface{} {
	result := make(map[string]interface{})

	// 复制基本字段
	result["total"] = rsp.Total
	result["current_page"] = rsp.CurrentPage
	result["per_page"] = rsp.PerPage
	result["condition"] = rsp.Condition

	// 处理items数组
	items := make([]map[string]interface{}, 0, len(rsp.Items))
	for _, item := range rsp.Items {
		itemMap := make(map[string]interface{})

		// 复制所有基本字段
		itemMap["id"] = item.Id
		itemMap["user_id"] = item.UserId
		itemMap["company_id"] = item.CompanyId
		itemMap["company_name"] = item.CompanyName
		itemMap["sn"] = item.Sn
		itemMap["issuer_cn"] = item.IssuerCn
		itemMap["issuer_cns"] = item.IssuerCns
		itemMap["issuer_org"] = item.IssuerOrg
		itemMap["issuer_ou"] = item.IssuerOu
		itemMap["cert"] = item.Cert
		itemMap["cert_num"] = item.CertNum
		itemMap["sig_alth"] = item.SigAlth
		itemMap["subject_cn"] = item.SubjectCn
		itemMap["subject_org"] = item.SubjectOrg
		itemMap["subject_ou"] = item.SubjectOu
		itemMap["subject_key"] = item.SubjectKey
		itemMap["valid_type"] = item.ValidType
		itemMap["is_valid"] = item.IsValid
		itemMap["version"] = item.Version
		itemMap["sha256"] = item.Sha256
		itemMap["sha1"] = item.Sha1
		itemMap["cert_date"] = item.CertDate
		itemMap["not_before"] = item.NotBefore
		itemMap["not_after"] = item.NotAfter
		itemMap["created_at"] = item.CreatedAt
		itemMap["updated_at"] = item.UpdatedAt
		itemMap["detect_task_id"] = item.DetectTaskId
		itemMap["website_message_id"] = item.WebsiteMessageId
		itemMap["is_self_sign"] = item.IsSelfSign
		itemMap["ip_domain"] = item.IpDomain

		// 处理ip_domain_list，将CertAssetDomainArray转换为直接的字符串数组
		ipDomainList := make(map[string][]string)
		for ip, domainArray := range item.IpDomainList {
			if domainArray != nil {
				ipDomainList[ip] = domainArray.Values
			} else {
				ipDomainList[ip] = []string{}
			}
		}
		itemMap["ip_domain_list"] = ipDomainList

		items = append(items, itemMap)
	}

	result["items"] = items
	return result
}

func CertAssetsDetail(ctx *gin.Context) error {
	var param pb.CertAssetDetailRequest
	paramId := ctx.Param("id")
	var err error
	param.Id, err = strconv.ParseUint(paramId, 10, 64)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "Invalid asset id: "+paramId)
	}

	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	param.UserId = userId
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	rsp, err := pb.GetProtoClient().CertAssetDetail(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextApiErrorf(ctx, err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}
