package api

import (
	"micro-service/apiService/middleware"
	"micro-service/apiService/response"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/utils"

	"github.com/gin-gonic/gin"
)

func ReverseDomain(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var rsp *pb.WhoisResponse
	param := pb.WhoisDomainRequest{Domain: ctx.Param("domain")}
	rsp, err := pb.GetProtoCoreClient().Whois(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp.Whois)
}

func ReverseWhois(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var rsp *pb.WhoisBasicResponse
	param := pb.WhoisDomainBasicRequest{Domain: ctx.Param("domain")}
	rsp, err := pb.GetProtoCoreClient().WhoisBasic(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}
