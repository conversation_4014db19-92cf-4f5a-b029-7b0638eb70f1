package api

import (
	api "micro-service/apiService/proto"
	"micro-service/pkg/microx"
	"time"

	"micro-service/apiService/middleware"
	"micro-service/apiService/response"
	pb "micro-service/crawlerService/proto"
	"micro-service/pkg/utils"
	"micro-service/pkg/validate"
	scan "micro-service/scanService/proto"

	"github.com/gin-gonic/gin"
)

func Crawler(req *gin.Context) error {
	var (
		param pb.GetRequest
		rsp   *pb.GetResponse
		err   error
	)
	crawlerService := pb.GetProtoClient()
	if err = req.ShouldBindJSON(&param); err != nil {
		return response.Gen(req).SendByError(pb.ServiceName, err)
	}
	if param.Timeout == 0 {
		param.Timeout = 10
	}
	if param.Method == "" {
		param.Method = "CURL:GET"
	}
	if param.Timeout <= 30 {
		param.Timeout = 30
	}
	rsp, err = crawlerService.Get(middleware.ContextWithSpan(req), &param, utils.RpcTimeoutDur(time.Duration(param.Timeout)*time.Second), microx.ServerTimeoutDur(time.Duration(param.Timeout)*time.Second))
	// 开始请求 接口
	if err != nil {
		return response.Gen(req).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(req).SendSuccess(pb.ServiceName, "success", gin.H{
		"body":       rsp.Body,
		"favicon":    rsp.Favicon,
		"script_res": rsp.ScriptRes,
		"headers":    rsp.Headers,
		"screenshot": rsp.Screenshot,
	})
}

// Screenshot 截图
func Screenshot(ctx *gin.Context) error {
	param := pb.ScreenshotRequest{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoClient().Screenshot(middleware.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(60*time.Second), microx.ServerTimeoutDur(60*time.Second))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp.Img)
}

func CrawlerChromeGet(ctx *gin.Context) error {
	var req = &pb.ChromeGetRequest{}
	_ = ctx.ShouldBindJSON(req)

	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := pb.GetProtoClient().ChromeGet(ctxSpan, req, utils.RpcTimeoutDur(60*time.Second), microx.ServerTimeoutDur(60*time.Second))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

func CrawlerIcon(ctx *gin.Context) error {
	var req = &pb.IconRequest{}
	_ = ctx.ShouldBindJSON(req)

	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := pb.GetProtoClient().Icon(ctxSpan, req, utils.RpcTimeoutDur(60*time.Second), microx.ServerTimeoutDur(60*time.Second))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// ScreenshotByDomainClue 通过搜索引擎获取根域的域名登录资产
func ScreenshotByDomainClue(ctx *gin.Context) error {
	var param = &scan.LoginPageScreenshotByTopDomainRequest{}
	_ = ctx.ShouldBindJSON(param)

	if param.UserId == 0 {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "用户参数不可为空")
	}

	// _, companyUserId, companyId, err := safe_company.GetSafeCompanyUser(ctx)
	// if err != nil {
	//	 return response.Gen(ctx).SendByError(api.ServiceName, err)
	// }
	// param.UserId = companyUserId
	// param.CompanyId = uint64(companyId)

	spanCtx := middleware.ContextWithSpan(ctx)
	_, err := scan.GetProtoClient().LoginPageScreenshotByTopDomain(spanCtx, param, utils.RpcTimeoutDur(30*time.Minute), microx.ServerTimeoutDur(30*time.Minute))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(scan.ServiceName, response.MsgSuccess, nil)
}
