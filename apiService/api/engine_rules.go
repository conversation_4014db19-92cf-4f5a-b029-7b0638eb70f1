package api

import (
	"errors"
	"micro-service/pkg/cfg"
	"micro-service/pkg/microx"
	webpb "micro-service/webService/proto"
	"time"

	mw "micro-service/apiService/middleware"
	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/pkg/validate"
	sc "micro-service/scanService/proto"

	"github.com/gin-gonic/gin"
)

func EngineRuleList(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var param pb.EngineRuleListRequest
	_ = ctx.ShouldBindQuery(&param)
	param.UpdatedAt = utils.GetQueryTimeRange("updated_at", ctx)

	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx, param.OperateCompanyId)
	if err != nil {
		log.WithContextErrorf(ctx, "Get user and company info failed: %v", err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	param.UserId = userId

	if safe_company.IsAdminUser(ctx) {
		param.Identity = 1
	} else if safe_company.IsSafeUser(ctx) {
		// 安服下属企业
		if param.OperateCompanyId != -1 {
			param.Identity = 3
		} else if param.OperateCompanyId == -1 { // 安服账号
			param.Identity = 2
		}
	} else { // 普通用户
		param.Identity = 3
	}

	rsp, err := pb.GetProtoCoreClient().EngineRuleList(
		mw.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(time.Minute))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

func EngineRuleCategory(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var param pb.EngineRuleCategoryRequest
	_ = ctx.ShouldBindJSON(&param)
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}
	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx, param.OperateCompanyId)
	if err != nil {
		log.WithContextErrorf(ctx, "Get user and company info failed: %v", err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	param.UserId = userId

	rsp, err := pb.GetProtoCoreClient().EngineRuleCategory(
		mw.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(time.Minute))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

func EngineRuleCreate(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var param pb.EngineRuleCreateRequest
	_ = ctx.ShouldBindJSON(&param)
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}
	opId, userId, _, err := safe_company.GetSafeCompanyUser(ctx, param.OperateCompanyId)
	if err != nil {
		log.WithContextErrorf(ctx, "Get user and company info failed: %v", err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	param.UserId = userId
	param.OperateUserId = opId
	_, err = pb.GetProtoCoreClient().EngineRuleCreate(
		mw.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(time.Minute))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, nil)
}

func EngineRuleUpdate(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var param pb.EngineRuleUpdateRequest
	_ = ctx.ShouldBindJSON(&param)
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx, param.OperateCompanyId)
	if err != nil {
		log.WithContextErrorf(ctx, "Get user and company info failed: %v", err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	param.UserId = userId
	if safe_company.IsAdminUser(ctx) {
		param.Identity = 1
	}
	_, err = pb.GetProtoCoreClient().EngineRuleUpdate(
		mw.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(time.Minute))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, nil)
}

func EngineRuleUpdateStatus(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var param pb.EngineRuleListRequest
	_ = ctx.ShouldBindJSON(&param)
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	_, userId, companyID, err := safe_company.GetSafeCompanyUser(ctx, param.OperateCompanyId)
	if err != nil {
		log.WithContextErrorf(ctx, "Get user and company info failed: %v", err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	param.UserId = userId
	param.OperateCompanyId = companyID
	_, err = pb.GetProtoCoreClient().EngineRuleUpdateStatus(
		mw.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(time.Minute))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, nil)
}

func EngineRuleDelete(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var param pb.EngineRuleListRequest
	_ = ctx.ShouldBindJSON(&param)
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx, param.OperateCompanyId)
	if err != nil {
		log.WithContextErrorf(ctx, "Get user and company info failed: %v", err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	param.UserId = userId
	if safe_company.IsAdminUser(ctx) {
		param.Identity = 1
	}
	_, err = pb.GetProtoCoreClient().EngineRuleDelete(
		mw.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(time.Minute))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, nil)
}

// EngineRuleMatch 规则匹配
func EngineRuleMatch(ctx *gin.Context) error {
	var param sc.EngineRuleAssetMatchRequest
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return err
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}
	rsp, err := sc.GetProtoClient().EngineRuleAssetMatch(
		mw.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(time.Minute), microx.ServerTimeoutDur(60*time.Second))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(sc.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(sc.ServiceName, response.MsgSuccess, rsp)
}

// EngineRuleDataSync 本地化同步线上规则
func EngineRuleDataSync(ctx *gin.Context) error {
	if !cfg.IsLocalClient() {
		return response.Gen(ctx).SendByErrorMsg(webpb.ServiceName, response.GetMicroDetail(errors.New("没有权限")))
	}
	rsp, err := webpb.GetProtoClient().EngineRuleDataSync(
		mw.ContextWithSpan(ctx), &webpb.Empty{}, utils.RpcTimeoutDur(time.Minute))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(webpb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(webpb.ServiceName, response.MsgSuccess, rsp)
}

// EngineRuleExpose saas平台暴露规则
func EngineRuleExpose(ctx *gin.Context) error {
	if cfg.IsLocalClient() {
		return response.Gen(ctx).SendByErrorMsg(webpb.ServiceName, response.GetMicroDetail(errors.New("没有权限")))
	}
	var param webpb.EngineRuleListRequest
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return err
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}
	rsp, err := webpb.GetProtoClient().EngineRuleExpose(
		mw.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(time.Minute))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(webpb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(webpb.ServiceName, response.MsgSuccess, rsp)
}
