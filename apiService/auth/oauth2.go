package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-oauth2/oauth2/v4/errors"
	"github.com/spf13/cast"
	"micro-service/middleware/mysql/auth_access_client"
	"micro-service/middleware/mysql/user"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	oauth "github.com/go-oauth2/oauth2/v4"
	"github.com/go-oauth2/oauth2/v4/generates"
	"github.com/go-oauth2/oauth2/v4/manage"
	oauthModels "github.com/go-oauth2/oauth2/v4/models"
	"github.com/go-oauth2/oauth2/v4/server"

	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/auth_access_token"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

type Oauth2 struct {
	GServer     *server.Server
	GClient     auth_access_client.AuthAccessClientModel
	GToken      auth_access_token.AuthAccessTokenModel
	GManage     *manage.Manager
	GManageConf *manage.Config
}

var oauth2 Oauth2

func GetOauth2() *Oauth2 {
	if oauth2.GManage == nil {
		Init()
	}
	return &oauth2
}

const tokenExpireIn = 24 * time.Hour

func Init() {
	if oauth2.GManage != nil {
		return
	}
	oauth2.GManageConf = &manage.Config{
		AccessTokenExp:    tokenExpireIn,
		RefreshTokenExp:   30 * 24 * time.Hour,
		IsGenerateRefresh: true,
	}

	oauth2.GManage = manage.NewDefaultManager()
	// oauth2.GManage.MustTokenStorage(store.NewMemoryTokenStore())
	oauth2.GClient = auth_access_client.NewAuthAccessClientModel()
	oauth2.GToken = auth_access_token.NewAuthAccessTokenModel()
	// 设置存储
	oauth2.GManage.MapClientStorage(oauth2.GClient)
	oauth2.GManage.MapTokenStorage(oauth2.GToken)
	oauth2.GServer = server.NewDefaultServer(oauth2.GManage)
	oauth2.GServer.SetAllowedGrantType(oauth.PasswordCredentials, oauth.ClientCredentials, oauth.Refreshing)
	oauth2.GServer.SetAllowGetAccessRequest(true)
	oauth2.GServer.SetClientInfoHandler(ClientFormHandler)
	oauth2.GServer.SetPasswordAuthorizationHandler(user.NewUserModel().Oauth2Authorization)
	oauth2.GManage.SetRefreshTokenCfg(manage.DefaultRefreshTokenCfg)
	// 设置账号密码登录Token过期时间
	oauth2.GManage.SetPasswordTokenCfg(oauth2.GManageConf)
	// 设置客户端登录Token过期时间
	oauth2.GManage.SetClientTokenCfg(oauth2.GManageConf)

	oauth2.GServer.SetInternalErrorHandler(func(err error) (re *errors.Response) {
		log.Warnf("[oauth2] %s", err.Error())
		return re
	})
}

func (o *Oauth2) GenToken(ctx *gin.Context) error {
	gt, tgr, err := o.ValidationTokenRequest(ctx.Request)
	if err != nil {
		// data, statusCode, header := o.GServer.GetErrorData(err)
		// for key := range header {
		// 	ctx.Writer.Header().Set(key, header.Get(key))
		// }
		// return response.Gen(ctx).SendError(api.ServiceHttpName, statusCode, err.Error(), data)
		return response.Gen(ctx).SendError(api.ServiceName, http.StatusBadRequest, err.Error(), nil)
	}

	ti, err := o.GetAccessToken(gt, tgr)
	if err != nil {
		data, statusCode, header := o.GServer.GetErrorData(err)
		for key := range header {
			ctx.Writer.Header().Set(key, header.Get(key))
		}
		return response.Gen(ctx).SendError(api.ServiceName, statusCode, err.Error(), data)
	}
	return response.Gen(ctx).SendSuccess(api.ServiceName, "success", o.GServer.GetTokenData(ti))
}

// ValidationBearerToken validation the bearer tokens
// https://tools.ietf.org/html/rfc6750
func (o *Oauth2) ValidationBearerToken(r *http.Request) (oauth.TokenInfo, error) {
	// 获取请求中的token
	accessToken, ok := o.GServer.BearerAuth(r)
	if !ok || accessToken == "" {
		return nil, errors.ErrInvalidAccessToken
	}

	l := strings.Split(accessToken, "|")
	if len(l) > 1 {
		accessToken = l[1]
	}
	return o.LoadAccessToken(accessToken)
}

// LoadAccessToken according to the access token for corresponding token information
func (o *Oauth2) LoadAccessToken(access string) (oauth.TokenInfo, error) {
	ct := time.Now()
	ti, err := o.GToken.GetByAccess(context.Background(), access)
	switch {
	case err != nil:
		return nil, err
	case ti == nil:
		return nil, errors.ErrInvalidAccessToken
	case ti.GetAccess() != access:
		return nil, errors.ErrInvalidAccessToken
	case ti.GetRefresh() != "" &&
		ti.GetRefreshExpiresIn() != 0 &&
		ti.GetRefreshCreateAt().Add(ti.GetRefreshExpiresIn()).Before(ct):
		return nil, errors.ErrExpiredRefreshToken
	case ti.GetAccessExpiresIn() != 0 &&
		ti.GetAccessCreateAt().Add(ti.GetAccessExpiresIn()).Before(ct):
		return nil, errors.ErrExpiredAccessToken
	}

	// update: token expire
	// go refreshToken(access)

	return ti, nil
}

// ValidationTokenRequest the token request validation
func (o *Oauth2) ValidationTokenRequest(r *http.Request) (oauth.GrantType, *oauth.TokenGenerateRequest, error) {
	if v := r.Method; !(v == "POST" ||
		(o.GServer.Config.AllowGetAccessRequest && v == "GET")) {
		return "", nil, errors.ErrInvalidRequest
	}

	var tgr *oauth.TokenGenerateRequest
	gt := oauth.GrantType(r.FormValue("grant_type"))

	// client
	if gt.String() == oauth.ClientCredentials.String() {
		clientID, clientSecret, err := o.GServer.ClientInfoHandler(r)
		if err != nil {
			return "", nil, err
		}
		userId, scope := auth_access_client.NewAuthAccessClientModel().GetScope(clientID, clientSecret)
		tgr = &oauth.TokenGenerateRequest{
			ClientID:     clientID,
			ClientSecret: clientSecret,
			UserID:       userId,
			Scope:        scope,
			Request:      r,
		}
	}

	// password
	if gt.String() == oauth.PasswordCredentials.String() {
		isWechatLogin := r.FormValue("is_wechat_login") == "true"
		username, password := r.FormValue("username"), r.FormValue("password")
		if username == "" || password == "" {
			return "", nil, errors.ErrInvalidRequest
		}

		ctx := context.WithValue(context.Background(), "is_wechat", isWechatLogin) //nolint:staticcheck,gocritic
		userID, err := o.GServer.PasswordAuthorizationHandler(ctx, "", username, password)

		if err != nil {
			return "", nil, err
		} else if userID == "" {
			return "", nil, errors.ErrInvalidGrant
		}
		tgr = &oauth.TokenGenerateRequest{
			ClientID:     utils.Md5Hash(username),
			ClientSecret: utils.Md5Hash(password),
			UserID:       userID,
			Scope:        user.NewUserModel().GetRoleScopeById(userID),
			Request:      r,
		}
	}

	// refresh
	if gt.String() == oauth.Refreshing.String() {
		tgr = &oauth.TokenGenerateRequest{
			Refresh:  r.FormValue("refresh_token"),
			ClientID: r.FormValue("client_id"),
			Request:  r,
		}
		if tgr.Refresh == "" {
			return "", nil, errors.ErrInvalidRequest
		}
	}
	if gt.String() == oauth.AuthorizationCode.String() {
		tgr = &oauth.TokenGenerateRequest{
			RedirectURI: r.FormValue("redirect_uri"),
			Code:        r.FormValue("code"),
			Request:     r,
		}
		if tgr.RedirectURI == "" || tgr.Code == "" {
			return "", nil, errors.ErrInvalidRequest
		}
	}
	if gt.String() == "" {
		return "", nil, errors.ErrUnsupportedGrantType
	}
	return gt, tgr, nil
}

// GetAccessToken access token
func (o *Oauth2) GetAccessToken(gt oauth.GrantType, tgr *oauth.TokenGenerateRequest) (oauth.TokenInfo, error) {
	if allowed := o.GServer.CheckGrantType(gt); !allowed {
		return nil, errors.ErrUnauthorizedClient
	}
	switch gt {
	case oauth.AuthorizationCode:
		ti, err := o.GenerateAccessToken(gt, tgr)
		if err != nil {
			switch err {
			case errors.ErrInvalidAuthorizeCode:
				return nil, errors.ErrInvalidGrant
			case errors.ErrInvalidClient:
				return nil, errors.ErrInvalidClient
			default:
				return nil, err
			}
		}
		return ti, nil
	case oauth.PasswordCredentials:
		return o.GenerateAccessToken(gt, tgr)
	case oauth.ClientCredentials:
		if fn := o.GServer.ClientScopeHandler; fn != nil {
			allowed, err := fn(tgr)
			if err != nil {
				return nil, err
			} else if !allowed {
				return nil, errors.ErrInvalidScope
			}
		}
		return o.GenerateAccessToken(gt, tgr)
	case oauth.Refreshing:
		rti, err := o.GServer.Manager.LoadRefreshToken(context.Background(), tgr.Refresh)
		if err != nil {
			if err == errors.ErrInvalidRefreshToken || err == errors.ErrExpiredRefreshToken {
				return nil, errors.ErrInvalidGrant
			}
			return nil, err
		}
		tgr.UserID = rti.GetUserID()
		tgr.Scope = rti.GetScope()
		if client, _ := auth_access_client.NewAuthAccessClientModel().GetClientInfoByID(rti.GetClientID()); client != nil {
			tgr.ClientSecret = client.Secret
		} else {
			if tgr.ClientID == "" {
				return nil, errors.ErrInvalidClient
			}
			if user, _ := user.NewUserModel().FindById(rti.GetUserID()); user != nil {
				tgr.ClientSecret = utils.Md5Hash(user.Password)
			} else {
				return nil, errors.ErrInvalidClient
			}
		}
		ti, err := o.RefreshAccessToken(tgr)
		if err != nil {
			if err == errors.ErrInvalidRefreshToken || err == errors.ErrExpiredRefreshToken {
				return nil, errors.ErrInvalidGrant
			}
			return nil, err
		}
		return ti, nil
	}
	return nil, errors.ErrUnsupportedGrantType
}

// GenApiToken 生成API-TOKEN
func (o *Oauth2) GenApiToken(ctx *gin.Context, userId uint64, ref bool) (oauth.TokenInfo, error) {
	code := fmt.Sprintf("API-TOKEN-%d", userId)
	userInfo, err := user.NewUserModel().FindById(userId)
	if err != nil {
		return nil, err
	}
	// 刷新的时候,删除旧Token
	if ref {
		if delErr := auth_access_token.NewAuthAccessTokenModel().Delete(mysql.WithWhere("code", code)); delErr != nil {
			log.WithContextWarnf(ctx, "删除API-TOKEN失败:%s", delErr.Error())
		}
	}
	// 获取已有的Token
	tokenInfo, tErr := auth_access_token.NewAuthAccessTokenModel().GetByCode(ctx, code)
	if tErr != nil {
		return nil, tErr
	}
	// API-Token已存在时,返回已存在的Token
	if tokenInfo != nil {
		return tokenInfo, nil
	}
	createAt := time.Now()
	// 不存在API-TOKEN时,生成新的API-TOKEN
	tgr := &auth_access_client.AuthAccessClient{
		ClientId: utils.Md5Hash(userInfo.Email),
		Secret:   utils.Md5Hash(userInfo.Password),
		Scope:    user.NewUserModel().GetRoleScopeById(userId),
		UserId:   cast.ToString(userInfo.Id),
	}
	ti := oauthModels.NewToken()
	ti.SetClientID(utils.Md5Hash(userInfo.Email))
	ti.SetUserID(cast.ToString(userId))
	ti.SetScope(user.NewUserModel().GetRoleScopeById(userId))
	ti.SetAccessCreateAt(createAt)
	ti.SetCode(code)
	// set access token expires
	ti.SetAccessExpiresIn(100 * 365 * 24 * time.Hour)
	td := &oauth.GenerateBasic{
		Client:    tgr,
		UserID:    cast.ToString(userId),
		CreateAt:  createAt,
		TokenInfo: ti,
		Request:   ctx.Request,
	}
	av, rv, err := generates.NewAccessGenerate().Token(context.Background(), td, false)
	if err != nil {
		return nil, err
	}
	ti.SetAccess(av)
	if rv != "" {
		ti.SetRefresh(rv)
	}
	err = o.GToken.Create(context.Background(), ti)
	if err != nil {
		return nil, err
	}
	return ti, nil
}

// RefreshAccessToken refreshing an access token
func (o *Oauth2) RefreshAccessToken(tgr *oauth.TokenGenerateRequest) (oauth.TokenInfo, error) {
	cli := &auth_access_client.AuthAccessClient{
		ClientId: tgr.ClientID,
		Secret:   tgr.ClientSecret,
		Scope:    tgr.Scope,
		UserId:   tgr.UserID,
	}
	ti, err := o.GManage.LoadRefreshToken(context.Background(), tgr.Refresh)
	if err != nil {
		return nil, err
	} else if ti.GetClientID() != tgr.ClientID {
		return nil, errors.ErrInvalidRefreshToken
	}

	oldAccess, oldRefresh := ti.GetAccess(), ti.GetRefresh()

	td := &oauth.GenerateBasic{
		Client:    cli,
		UserID:    ti.GetUserID(),
		CreateAt:  time.Now(),
		TokenInfo: ti,
		Request:   tgr.Request,
	}
	rcfg := manage.DefaultRefreshTokenCfg

	ti.SetAccessCreateAt(td.CreateAt)
	if v := rcfg.AccessTokenExp; v > 0 {
		ti.SetAccessExpiresIn(v)
	}

	if v := rcfg.RefreshTokenExp; v > 0 {
		ti.SetRefreshExpiresIn(v)
	}

	if rcfg.IsResetRefreshTime {
		ti.SetRefreshCreateAt(td.CreateAt)
	}

	if scope := tgr.Scope; scope != "" {
		ti.SetScope(scope)
	}

	tv, rv, err := generates.NewAccessGenerate().Token(context.Background(), td, rcfg.IsGenerateRefresh)
	if err != nil {
		return nil, err
	}

	ti.SetAccess(tv)
	if rv != "" {
		ti.SetRefresh(rv)
	}

	if err := o.GToken.Create(context.Background(), ti); err != nil {
		return nil, err
	}

	if rcfg.IsRemoveAccess {
		// remove the old access token
		if err := o.GToken.RemoveByAccess(context.Background(), oldAccess); err != nil {
			return nil, err
		}
	}

	if rcfg.IsRemoveRefreshing && rv != "" {
		// remove the old refresh token
		if err := o.GToken.RemoveByRefresh(context.Background(), oldRefresh); err != nil {
			return nil, err
		}
	}

	if rv == "" {
		ti.SetRefresh("")
		ti.SetRefreshCreateAt(time.Now())
		ti.SetRefreshExpiresIn(0)
	}

	return ti, nil
}

// delete authorization code data
func (o *Oauth2) delAuthorizationCode(code string) error {
	return o.GToken.RemoveByCode(context.Background(), code)
}

// get and delete authorization code data
func (o *Oauth2) getAndDelAuthorizationCode(tgr *oauth.TokenGenerateRequest) (oauth.TokenInfo, error) {
	code := tgr.Code
	ti, err := o.getAuthorizationCode(code)
	if err != nil {
		return nil, err
	} else if ti.GetClientID() != tgr.ClientID {
		return nil, errors.ErrInvalidAuthorizeCode
	} else if codeURI := ti.GetRedirectURI(); codeURI != "" && codeURI != tgr.RedirectURI {
		return nil, errors.ErrInvalidAuthorizeCode
	}

	err = o.delAuthorizationCode(code)
	if err != nil {
		return nil, err
	}
	return ti, nil
}

// get authorization code data
func (o *Oauth2) getAuthorizationCode(code string) (oauth.TokenInfo, error) {
	ti, err := o.GToken.GetByCode(context.Background(), code)
	if err != nil {
		return nil, err
	} else if ti == nil || ti.GetCode() != code || ti.GetCodeCreateAt().Add(ti.GetCodeExpiresIn()).Before(time.Now()) {
		return nil, errors.ErrInvalidAuthorizeCode
	}
	return ti, nil
}

// GetClient get the client information
func (o *Oauth2) GetClient(clientID string) (cli *auth_access_client.AuthAccessClient, err error) {
	cli, err = o.GClient.GetClientInfoByID(clientID)
	if err != nil {
		return
	} else if cli == nil {
		err = errors.ErrInvalidClient
	} else if cli.ExpiredAt.Before(time.Now()) {
		err = errors.ErrInvalidClient
	}

	return
}

// GenerateAccessToken generate the access token
func (o *Oauth2) GenerateAccessToken(gt oauth.GrantType, tgr *oauth.TokenGenerateRequest) (oauth.TokenInfo, error) {
	var cli *auth_access_client.AuthAccessClient
	var err error
	var ti oauth.TokenInfo
	if gt == oauth.PasswordCredentials {
		cli = &auth_access_client.AuthAccessClient{
			ClientId: tgr.ClientID,
			Secret:   tgr.ClientSecret,
			Scope:    tgr.Scope,
			UserId:   tgr.UserID,
		}
	} else {
		cli, err = o.GetClient(tgr.ClientID)
		if err != nil {
			return nil, errors.ErrInvalidClient
		} else if tgr.ClientSecret != cli.Secret {
			return nil, errors.ErrInvalidClient
		} else if tgr.RedirectURI != "" {
			if manageErr := manage.DefaultValidateURI(cli.Domain, tgr.RedirectURI); manageErr != nil {
				return nil, manageErr
			}
		}
	}

	if gt == oauth.AuthorizationCode {
		ti, err = o.getAndDelAuthorizationCode(tgr)
		if err != nil {
			return nil, err
		}
		tgr.UserID = ti.GetUserID()
		tgr.Scope = ti.GetScope()
		if exp := ti.GetAccessExpiresIn(); exp > 0 {
			tgr.AccessTokenExp = exp
		}
	}

	ti = oauthModels.NewToken()
	ti.SetClientID(tgr.ClientID)
	ti.SetUserID(tgr.UserID)
	ti.SetRedirectURI(tgr.RedirectURI)
	ti.SetScope(tgr.Scope)

	createAt := time.Now()
	ti.SetAccessCreateAt(createAt)

	// set access token expires
	gcfg := o.GManageConf
	aexp := gcfg.AccessTokenExp
	if exp := tgr.AccessTokenExp; exp > 0 {
		aexp = exp
	}
	ti.SetAccessExpiresIn(aexp)
	if gcfg.IsGenerateRefresh {
		ti.SetRefreshCreateAt(createAt)
		ti.SetRefreshExpiresIn(gcfg.RefreshTokenExp)
	}

	td := &oauth.GenerateBasic{
		Client:    cli,
		UserID:    tgr.UserID,
		CreateAt:  createAt,
		TokenInfo: ti,
		Request:   tgr.Request,
	}

	av, rv, err := generates.NewAccessGenerate().Token(context.Background(), td, gcfg.IsGenerateRefresh)
	if err != nil {
		return nil, err
	}
	ti.SetAccess(av)

	if rv != "" {
		ti.SetRefresh(rv)
	}

	err = o.GToken.Create(context.Background(), ti)
	if err != nil {
		return nil, err
	}

	return ti, nil
}

func ClientFormHandler(r *http.Request) (string, string, error) {
	clientID := r.Form.Get("client_id")
	clientSecret := r.Form.Get("client_secret")
	if r.Form.Get("grant_type") == "client_credentials" {
		if clientID == "" || clientSecret == "" {
			return "", "", errors.ErrInvalidClient
		}
	}
	return clientID, clientSecret, nil
}

// nolint:unused,gocritic
func refreshToken(token string) {
	client := auth_access_token.NewAuthAccessTokenModel(mysql.GetDbClient())

	tokenInfo, err := client.GetByAccess(context.TODO(), token)
	if err != nil {
		log.Errorf("%+v", err)
		return
	}

	bs, err := json.Marshal(tokenInfo)
	if err != nil {
		log.Errorf("%+v", err)
		return
	}

	var info oauthModels.Token
	err = json.Unmarshal(bs, &info)
	if err != nil {
		log.Errorf("%+v", err)
		return
	}

	info.AccessCreateAt = time.Now()
	info.AccessExpiresIn = tokenExpireIn

	_ = info
}
