package table_assets

import (
	"context"
	"fmt"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/pkg/cfg"
	"path/filepath"
	"strings"

	"github.com/xuri/excelize/v2"
	"go-micro.dev/v4/errors"

	"micro-service/middleware/mysql/operate_logs"
	"micro-service/pkg/log"
	"micro-service/pkg/network"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/storage"
	pb "micro-service/webService/proto"
)

// AssetsImportData 全局台账、疑似数据的导入：IP+端口+协议
func AssetsImportData(ctx context.Context, req *pb.AssetsImportDataRequest) error {
	log.WithContextInfof(ctx, "Received table_assets.AssetsImportData request: %v", req)

	// 检查参数
	if len(req.File) == 0 {
		return errors.BadRequest(pb.ServiceName, "文件不能为空")
	}

	// 批量读取文件内容
	sheetData, err := batchImportRead(req.File)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}

	// 根据资产类型处理不同的导入逻辑
	switch req.AssetsType {
	case "account_assets":
		return importAccountAssets(ctx, req, sheetData)
	case "unsure_assetss":
		return importUnsureAssets(ctx, req, sheetData)
	case "threaten_asset":
		return importThreatenAsset(ctx, req, sheetData)
	default:
		return errors.BadRequest(pb.ServiceName, "不支持的资产类型")
	}
}

// batchImportRead 批量读取导入文件
func batchImportRead(files []string) ([]map[string]string, error) {
	var result []map[string]string

	for _, filePath := range files {
		// 检查文件类型
		if !strings.HasSuffix(strings.ToLower(filePath), ".xlsx") {
			return nil, fmt.Errorf("不支持的文件类型: %s", filePath)
		}

		fmt.Println("filePath: ", filePath)
		// 读取Excel文件
		excelData, err := readExcelFile(filePath)
		if err != nil {
			return nil, err
		}

		// 将数据添加到结果中
		result = append(result, excelData...)
	}

	return result, nil
}

// readExcelFile 读取Excel文件
func readExcelFile(filePath string) ([]map[string]string, error) {
	// 打开Excel文件
	f, err := excelize.OpenFile(filepath.Join(storage.GetRootPath(), "app/public/"+filePath))
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %v", err)
	}
	defer f.Close()

	// 获取第一个工作表的名称
	sheetName := f.GetSheetName(0)

	// 获取所有行
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取工作表失败: %v", err)
	}

	if len(rows) < 2 {
		return nil, fmt.Errorf("文件内容为空")
	}

	// 第一行是表头
	headers := rows[0]

	// 处理数据行
	var result []map[string]string
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		rowData := make(map[string]string)

		// 跳过空行
		if isEmptyRow(row) {
			continue
		}

		// 将行数据映射到表头
		for j := 0; j < len(headers) && j < len(row); j++ {
			rowData[headers[j]] = row[j]
		}

		result = append(result, rowData)
	}

	return result, nil
}

// isEmptyRow 判断一行是否为空
func isEmptyRow(row []string) bool {
	for _, cell := range row {
		if cell != "" {
			return false
		}
	}
	return true
}

// getValueOrEmpty 获取值，如果不存在或为空则返回空字符串
func getValueOrEmpty(data map[string]string, key string) interface{} {
	if value, exists := data[key]; exists && value != "" {
		return value
	}
	return ""
}

// 导入台账资产
func importAccountAssets(ctx context.Context, req *pb.AssetsImportDataRequest, sheetData []map[string]string) error {
	// 记录成功导入的IP数量
	successCount := 0
	var err error

	// 按IP分组的资产数据 - 直接使用PHP格式：IP -> 资产数组
	assetsData := make(map[string][]map[string]interface{})
	// 所有IP的列表
	allIp := make([]string, 0)

	// 处理每一行数据
	for _, item := range sheetData {
		// 检查必要字段
		if isEmpty(item["*IP地址"]) || isEmpty(item["*端口"]) || isEmpty(item["*协议"]) {
			continue
		}

		ip := item["*IP地址"]

		// 验证IP
		if !network.IsValidIPv4(ip) && !network.IsValidIPv6(ip) {
			continue
		}

		// 校验是否为内网IP，生产环境下跳过内网IP
		if network.IsInsideIP(ip) {
			continue
		}

		// 创建标准化的资产数据，确保所有字段都存在
		assetData := map[string]interface{}{
			"企业名称":  getValueOrEmpty(item, "企业名称"),
			"*IP地址": getValueOrEmpty(item, "*IP地址"),
			"*端口":   getValueOrEmpty(item, "*端口"),
			"*协议":   getValueOrEmpty(item, "*协议"),
			"域名":    getValueOrEmpty(item, "域名"),
			"URL":   getValueOrEmpty(item, "URL"),
			"网站标题":  getValueOrEmpty(item, "网站标题"),
			"状态码":   getValueOrEmpty(item, "状态码"),
			"云厂商":   getValueOrEmpty(item, "云厂商"),
			"组件信息":  getValueOrEmpty(item, "组件信息"),
			"地理位置":  getValueOrEmpty(item, "地理位置"),
			"资产状态":  getValueOrEmpty(item, "资产状态"),
			"探测方式":  getValueOrEmpty(item, "探测方式"),
			"资产标签":  getValueOrEmpty(item, "资产标签"),
			"更新时间":  getValueOrEmpty(item, "更新时间"),
		}

		// 将数据按IP分组存储 - 直接使用PHP格式
		assetsData[ip] = append(assetsData[ip], assetData)
		// 添加到IP列表（重复IP也添加，与PHP实现保持一致）
		allIp = append(allIp, ip)

		// 导入成功则计数加1
		successCount++
	}

	// 调用异步任务AssetsImportDataJob处理台账资产导入
	payload := &asyncq.AssetsImportDataJobPayload{
		AssetsData: assetsData, // IP -> assets array 格式的数据
		UserId:     req.UserId,
		Status:     "account_assets", // 台账资产类型
		CompanyId:  uint64(req.CompanyId),
	}

	log.WithContextInfof(ctx, "[台账资产导入] 开始调用异步任务AssetsImportDataJob, 用户ID: %d, 资产数量: %d, 状态: %s",
		req.UserId, len(assetsData), "account_assets")

	// 打印具体的Excel读取数据
	i := 0
	for ip, assets := range assetsData {
		i++
		log.WithContextInfof(ctx, "[台账资产导入] 第%d组数据 - IP: %s, 资产数量: %d", i, ip, len(assets))
		for j, assetData := range assets {
			log.WithContextInfof(ctx, "[台账资产导入] 资产%d 数据: %+v", j, assetData)
		}
	}
	if cfg.ExecGolangJob() {
		//err = asyncq.Enqueue(ctx, asyncq.AssetsImportDataJob, payload)
		//if err != nil {
		//	log.WithContextErrorf(ctx, "[台账资产导入] 调用异步任务AssetsImportDataJob失败: %v", err)
		//	return err
		//}

		err = asyncq.AssetsImportDataPhpJob.Dispatch(payload.AssetsData, payload.UserId, fofaee_assets.STATUS_UPLOAD, payload.CompanyId)
		if err != nil {
			log.WithContextErrorf(ctx, "[台账资产导入] 调用异步任务AssetsImportDataPhpJob失败: %v", err)
			return err
		}
	} else {
		// AssetsImportDataPhpJob 是PHP实现的异步任务
		err = asyncq.AssetsImportDataPhpJob.Dispatch(payload.AssetsData, payload.UserId, fofaee_assets.STATUS_UPLOAD, payload.CompanyId)
		if err != nil {
			log.WithContextErrorf(ctx, "[台账资产导入] 调用异步任务AssetsImportDataPhpJob失败: %v", err)
			return err
		}
	}

	log.WithContextInfof(ctx, "[台账资产导入] 成功调用异步任务AssetsImportDataJob, 用户ID: %d, 资产数量: %d", req.UserId, len(assetsData))
	return nil
}

// 导入疑似资产
func importUnsureAssets(ctx context.Context, req *pb.AssetsImportDataRequest, sheetData []map[string]string) error {
	// 记录成功导入的IP数量
	successCount := 0
	totalCount := len(sheetData)
	var err error

	// 按IP分组的资产数据 - 直接使用PHP格式：IP -> 资产数组
	assetsData := make(map[string][]map[string]interface{})
	// 所有IP的列表
	allIp := make([]string, 0)

	// 处理每一行数据
	for _, item := range sheetData {
		// 检查必要字段
		if isEmpty(item["*IP地址"]) || isEmpty(item["*端口"]) || isEmpty(item["*协议"]) {
			continue
		}

		ip := item["*IP地址"]

		// 验证IP
		if !network.IsValidIPv4(ip) && !network.IsValidIPv6(ip) {
			continue
		}

		// 校验是否为内网IP，生产环境下跳过内网IP
		if network.IsInsideIP(ip) {
			continue
		}

		// 创建标准化的资产数据，确保所有字段都存在
		assetData := map[string]interface{}{
			"企业名称":  getValueOrEmpty(item, "企业名称"),
			"*IP地址": getValueOrEmpty(item, "*IP地址"),
			"*端口":   getValueOrEmpty(item, "*端口"),
			"*协议":   getValueOrEmpty(item, "*协议"),
			"域名":    getValueOrEmpty(item, "域名"),
			"URL":   getValueOrEmpty(item, "URL"),
			"网站标题":  getValueOrEmpty(item, "网站标题"),
			"状态码":   getValueOrEmpty(item, "状态码"),
			"云厂商":   getValueOrEmpty(item, "云厂商"),
			"组件信息":  getValueOrEmpty(item, "组件信息"),
			"地理位置":  getValueOrEmpty(item, "地理位置"),
			"资产状态":  getValueOrEmpty(item, "资产状态"),
			"探测方式":  getValueOrEmpty(item, "探测方式"),
			"资产标签":  getValueOrEmpty(item, "资产标签"),
			"更新时间":  getValueOrEmpty(item, "更新时间"),
		}

		// 将数据按IP分组存储 - 直接使用PHP格式
		assetsData[ip] = append(assetsData[ip], assetData)
		// 添加到IP列表（重复IP也添加，与PHP实现保持一致）
		allIp = append(allIp, ip)

		// 导入成功则计数加1
		successCount++
	}

	// 调用异步任务AssetsImportDataJob处理疑似资产导入
	payload := &asyncq.AssetsImportDataJobPayload{
		AssetsData: assetsData, // IP -> assets array 格式的数据
		UserId:     req.UserId,
		Status:     "unsure_assets", // 疑似资产类型
		CompanyId:  uint64(req.CompanyId),
	}

	log.WithContextInfof(ctx, "[疑似资产导入] 开始调用异步任务AssetsImportDataJob, 用户ID: %d, 资产数量: %d, 状态: %s",
		req.UserId, len(assetsData), "unsure_assets")

	// 打印具体的Excel读取数据
	i := 0
	for ip, assets := range assetsData {
		i++
		log.WithContextInfof(ctx, "[疑似资产导入] 第%d组数据 - IP: %s, 资产数量: %d", i, ip, len(assets))
		for j, assetData := range assets {
			log.WithContextInfof(ctx, "[疑似资产导入] 资产%d 数据: %+v", j, assetData)
		}
	}

	if cfg.ExecGolangJob() {
		//err = asyncq.Enqueue(ctx, asyncq.AssetsImportDataJob, payload)
		//if err != nil {
		//	log.WithContextErrorf(ctx, "[疑似资产导入] 调用异步任务AssetsImportDataJob失败: %v", err)
		//	return err
		//}
		err = asyncq.AssetsImportDataPhpJob.Dispatch(payload.AssetsData, payload.UserId, fofaee_assets.STATUS_DEFAULT, payload.CompanyId)
		if err != nil {
			log.WithContextErrorf(ctx, "[疑似资产导入] 调用异步任务AssetsImportDataPhpJob失败: %v", err)
			return err
		}
	} else {
		// AssetsImportDataPhpJob 是PHP实现的异步任务
		err = asyncq.AssetsImportDataPhpJob.Dispatch(payload.AssetsData, payload.UserId, fofaee_assets.STATUS_DEFAULT, payload.CompanyId)
		if err != nil {
			log.WithContextErrorf(ctx, "[疑似资产导入] 调用异步任务AssetsImportDataPhpJob失败: %v", err)
			return err
		}
	}
	// 记录操作日志
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		req.UserId,
		fmt.Sprintf("导入疑似资产数据，成功导入%d/%d条", successCount, totalCount),
		req.ClientIp,
		uint64(req.CompanyId),
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.WithContextErrorf(ctx, "创建操作日志失败: %v", err)
	}

	return nil
}

// 导入威胁资产
func importThreatenAsset(ctx context.Context, req *pb.AssetsImportDataRequest, sheetData []map[string]string) error {
	// 记录成功导入的IP数量
	successCount := 0
	totalCount := len(sheetData)
	var err error

	// 按IP分组的资产数据 - 直接使用PHP格式：IP -> 资产数组
	assetsData := make(map[string][]map[string]interface{})
	// 所有IP的列表
	allIp := make([]string, 0)

	// 处理每一行数据
	for _, item := range sheetData {
		// 检查必要字段
		if isEmpty(item["*IP地址"]) || isEmpty(item["*端口"]) || isEmpty(item["*协议"]) {
			continue
		}

		ip := item["*IP地址"]

		// 验证IP
		if !network.IsValidIPv4(ip) && !network.IsValidIPv6(ip) {
			continue
		}

		// 校验是否为内网IP，生产环境下跳过内网IP
		if network.IsInsideIP(ip) {
			continue
		}

		// 创建标准化的资产数据，确保所有字段都存在
		assetData := map[string]interface{}{
			"企业名称":  getValueOrEmpty(item, "企业名称"),
			"*IP地址": getValueOrEmpty(item, "*IP地址"),
			"*端口":   getValueOrEmpty(item, "*端口"),
			"*协议":   getValueOrEmpty(item, "*协议"),
			"域名":    getValueOrEmpty(item, "域名"),
			"URL":   getValueOrEmpty(item, "URL"),
			"网站标题":  getValueOrEmpty(item, "网站标题"),
			"状态码":   getValueOrEmpty(item, "状态码"),
			"云厂商":   getValueOrEmpty(item, "云厂商"),
			"组件信息":  getValueOrEmpty(item, "组件信息"),
			"地理位置":  getValueOrEmpty(item, "地理位置"),
			"资产状态":  getValueOrEmpty(item, "资产状态"),
			"探测方式":  getValueOrEmpty(item, "探测方式"),
			"资产标签":  getValueOrEmpty(item, "资产标签"),
			"更新时间":  getValueOrEmpty(item, "更新时间"),
		}

		// 将数据按IP分组存储 - 直接使用PHP格式
		assetsData[ip] = append(assetsData[ip], assetData)
		// 添加到IP列表（重复IP也添加，与PHP实现保持一致）
		allIp = append(allIp, ip)

		// 导入成功则计数加1
		successCount++
	}

	// 调用异步任务AssetsImportDataJob处理威胁资产导入
	payload := &asyncq.AssetsImportDataJobPayload{
		AssetsData: assetsData, // IP -> assets array 格式的数据
		UserId:     req.UserId,
		Status:     "threaten_asset", // 威胁资产类型
		CompanyId:  uint64(req.CompanyId),
	}

	log.WithContextInfof(ctx, "[威胁资产导入] 开始调用异步任务AssetsImportDataJob, 用户ID: %d, 资产数量: %d, 状态: %s",
		req.UserId, len(assetsData), "threaten_asset")

	// 打印具体的Excel读取数据
	i := 0
	for ip, assets := range assetsData {
		i++
		log.WithContextInfof(ctx, "[威胁资产导入] 第%d组数据 - IP: %s, 资产数量: %d", i, ip, len(assets))
		for j, assetData := range assets {
			log.WithContextInfof(ctx, "[威胁资产导入] 资产%d 数据: %+v", j, assetData)
		}
	}

	if cfg.ExecGolangJob() {
		//err = asyncq.Enqueue(ctx, asyncq.AssetsImportDataJob, payload)
		//if err != nil {
		//	log.WithContextErrorf(ctx, "[威胁资产导入] 调用异步任务AssetsImportDataJob失败: %v", err)
		//	return err
		//}
		err = asyncq.AssetsImportDataPhpJob.Dispatch(payload.AssetsData, payload.UserId, fofaee_assets.STATUS_THREATEN, payload.CompanyId)
		if err != nil {
			log.WithContextErrorf(ctx, "[威胁资产导入] 调用异步任务AssetsImportDataPhpJob失败: %v", err)
			return err
		}
	} else {
		// AssetsImportDataPhpJob 是PHP实现的异步任务
		err = asyncq.AssetsImportDataPhpJob.Dispatch(payload.AssetsData, payload.UserId, fofaee_assets.STATUS_THREATEN, payload.CompanyId)
		if err != nil {
			log.WithContextErrorf(ctx, "[威胁资产导入] 调用异步任务AssetsImportDataPhpJob失败: %v", err)
			return err
		}
	}
	// 记录操作日志
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		req.UserId,
		fmt.Sprintf("导入威胁资产数据，成功导入%d/%d条", successCount, totalCount),
		req.ClientIp,
		uint64(req.CompanyId),
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.WithContextErrorf(ctx, "创建操作日志失败: %v", err)
	}

	return nil
}

// isEmpty 检查字符串是否为空
func isEmpty(s string) bool {
	return strings.TrimSpace(s) == ""
}
