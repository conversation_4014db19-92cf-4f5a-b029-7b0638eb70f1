// 资产台账

package asset_account

import (
	"context"
	"errors"
	"fmt"
	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/clues_groups"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/forbid"
	"micro-service/middleware/mysql/standing_ips"
	"micro-service/middleware/mysql/task"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dns"
	"micro-service/pkg/log"
	"micro-service/pkg/network"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gorm.io/gorm"
)

func AssetAccountImportScan(_ context.Context, req *pb.AssetAccountImportScanRequest, rsp *pb.AssetAccountImportScanResponse) error {
	log.Infof("[台账导入并扫描] 导入并扫描数据请求: %v", req)

	// 参数验证
	if req.Source == "" || (req.Source != "input" && req.Source != "file" && req.Source != "domain") {
		return fmt.Errorf("source参数必须为input、file或domain")
	}
	if req.TaskParam == nil {
		return fmt.Errorf("缺少task_param参数")
	}
	if req.IsDefinePort == 1 {
		if len(req.DefinePorts) == 0 || len(req.DefinePortProtocols) == 0 {
			return fmt.Errorf("自定义端口扫描时必须指定端口和协议")
		}
		for _, port := range req.DefinePorts {
			if port < 0 || port > 65536 {
				return fmt.Errorf("端口范围必须在0-65536之间")
			}
		}
	}

	var data []*pb.AssetAccountImportScanIpDataItem
	var errorList []string
	isNeed := false
	var domainArr []string
	var allIpsToString []string

	if req.Source == "input" {
		log.Infof("[台账导入并扫描] 导入开始 - time: %s, user_id: %d", time.Now().Format(time.DateTime), req.UserId)

		// 提取IP列表
		ips := utils.ListColumn(req.Data, func(item *pb.AssetAccountImportScanIpDataItem) string { return item.Ip })

		var errDomain []string
		var returnData []string
		taskParam := req.TaskParam

		// 校验IP格式
		if taskParam.IpType == task.IP_TYPE_V4 {
			for _, checkIp := range ips {
				if strings.Contains(checkIp, "-") || strings.Contains(checkIp, "/") {
					// 校验IPv4段格式
					if strings.Contains(checkIp, "/") {
						parts := strings.Split(checkIp, "/")
						if !utils.IsIPv4(parts[0]) {
							errDomain = append(errDomain, checkIp)
							continue
						}
						mask, _ := strconv.Atoi(parts[1])
						if mask > 32 || mask < 0 {
							errDomain = append(errDomain, checkIp)
							continue
						}
					} else if strings.Contains(checkIp, "*") {
						// 192.168.1-10.*格式
						if !utils.IsIPv4(strings.Split(checkIp, "-")[0] + ".1") {
							errDomain = append(errDomain, checkIp)
							continue
						}
						octet, _ := strconv.Atoi(strings.Split(strings.Split(checkIp, "-")[1], ".")[0])
						if octet > 255 || octet < 0 {
							errDomain = append(errDomain, checkIp)
							continue
						}
					} else {
						if !utils.IsIPv4(strings.Split(checkIp, "-")[0]) {
							errDomain = append(errDomain, checkIp)
							continue
						}
						lastPart := strings.Split(checkIp, "-")[1]
						num, _ := strconv.Atoi(lastPart)
						if num > 255 || num < 0 || strings.Contains(lastPart, ".") {
							errDomain = append(errDomain, checkIp)
							continue
						}
					}
				} else {
					if !utils.IsIPv4(checkIp) {
						errDomain = append(errDomain, checkIp)
						continue
					}
				}
				returnData = append(returnData, checkIp)
			}
		} else {
			// IPv6校验
			for _, checkIp := range ips {
				if strings.Contains(checkIp, "-") || strings.Contains(checkIp, "/") {
					continue
				} else {
					if !utils.IsIPv6(checkIp) {
						errDomain = append(errDomain, checkIp)
						continue
					}
					// 校验是否是可以解析为IPv4的IPv6格式
					expandedIp := utils.CompleteIPV6(checkIp)
					if dns.IsIPv4MappedIPv6(net.ParseIP(expandedIp)) {
						log.Warnf("[台账导入并扫描] 非法的IPv6 - %s", expandedIp)
						errDomain = append(errDomain, checkIp)
						continue
					}
					returnData = append(returnData, checkIp)
				}
			}
		}

		if len(errDomain) > 0 {
			rsp.DataList = returnData
			rsp.ErrorList = utils.ListDistinct(errDomain)
			return nil
		}

		if len(ips) == 0 {
			return fmt.Errorf("扫描目标ip为空，请重新下发扫描任务")
		}

		log.Infof("[台账导入并扫描] IP校验结束 - time: %s, user_id: %d", time.Now().Format(time.DateTime), req.UserId)

		// 判断端口是否超出数量限制
		if req.IsDefinePort == 1 {
			ports := utils.ListDistinct(req.DefinePorts)
			protocols := utils.ListDistinct(req.DefinePortProtocols)
			definedPortNum := len(ports) * len(protocols)
			if definedPortNum > 100 {
				return fmt.Errorf("自定义端口扫描下，端口协议随机组合最多100个，现在已经超出数量限制，请重新设置")
			}
			// 如果是0-65535端口扫描，扫描类型必须是masscan
			if len(ports) == 1 && ports[0] == task.ALL_PORT_DEFINE_PORT && definedPortNum == 1 {
				req.TaskParam.ScanType = task.SCAN_TYPE_SPEED
			}
		}

		// 拆分IP为单个IP
		if taskParam.IpType == task.IP_TYPE_V4 {
			for _, ip := range ips {
				if strings.Contains(ip, "*") {
					// 连续网段处理
					networks, err := network.ParseIPRange(ip)
					if err != nil {
						log.Errorf("当前ip连续段解析错误: %s", ip)
						continue
					}
					allIpsToString = append(allIpsToString, networks...)
				} else {
					// 处理其余IPv4
					var ipLong string
					if strings.Contains(ip, "-") {
						ipLong = network.FullIPRange(ip)
					} else {
						ipLong = ip
					}
					networks, err := network.ParseIPRange(ipLong)
					if err != nil {
						log.Errorf("当前ip段解析错误: %s", ip)
						continue
					}
					allIpsToString = append(allIpsToString, networks...)
				}
			}
		} else {
			// IPv6不支持段格式，只支持单个
			for _, ipone := range ips {
				ip := strings.TrimSpace(ipone)
				if !strings.Contains(ip, "-") {
					allIpsToString = append(allIpsToString, ip)
				}
			}
		}

		if len(allIpsToString) == 0 {
			return fmt.Errorf("扫描目标ip为空或者ip格式错误，请重新下发扫描任务")
		}

		log.Infof("[台账导入并扫描] IP段解析结束 - time: %s, user_id: %d", time.Now().Format(time.DateTime), req.UserId)

		// 展开IP段为单个IP
		var insertIp []string
		for _, singleIp := range allIpsToString {
			ips, err := network.ParseIPRange(singleIp)
			if err != nil {
				log.Errorf("ip格式不正确: %s", singleIp)
				return fmt.Errorf("ip格式不正确，请重新填写 %s", singleIp)
			}
			insertIp = append(insertIp, ips...)
		}
		insertIp = utils.ListDistinct(insertIp)

		for _, ip := range insertIp {
			data = append(data, &pb.AssetAccountImportScanIpDataItem{Ip: ip})
		}

		isNeed = false

	} else if req.Source == "domain" {
		log.Infof("[台账导入并扫描] 域名导入开始 - time: %s, user_id: %d", time.Now().Format(time.DateTime), req.UserId)

		// 域名扫描处理
		domainList := make([]string, 0)
		for _, val := range req.Data {
			domainList = append(domainList, val.Ip)
		}

		domainList = utils.ListDistinct(domainList)

		if len(domainList) > 200 {
			return fmt.Errorf("单次任务不可以超过200个域名！")
		}

		var errDomain []string
		var returnData []string
		for _, item := range req.Data {
			if !utils.IsDomain(item.Ip) {
				errDomain = append(errDomain, item.Ip)
			} else {
				returnData = append(returnData, item.Ip)
			}
		}

		if len(errDomain) > 0 {
			rsp.DataList = returnData
			rsp.ErrorList = utils.ListDistinct(errDomain)
			return nil
		}

		if len(returnData) == 0 {
			return fmt.Errorf("扫描目标为空，请重新下发扫描任务！")
		}

		// 域名解析
		for _, domain := range req.Data {
			value := domain.Ip
			if value == "" || !utils.IsDomain(value) {
				continue
			}

			log.Infof("[台账导入并扫描] 开始解析域名: %s", value)

			var domainIps []string
			var err error
			if req.TaskParam.IpType == task.IP_TYPE_V4 {
				domainIps, err = utils.GetDNSARecords(value)
			} else {
				domainIps, err = utils.GetDNSAAAARecords(value)
			}
			if err != nil {
				log.Errorf("[台账导入并扫描] 域名解析失败: %s, error: %v", value, err)
				errorList = append(errorList, fmt.Sprintf("域名解析失败: %s", value))
				continue
			}

			log.Infof("[台账导入并扫描] 域名 %s 解析出 %d 个IP: %v", value, len(domainIps), domainIps)

			for _, dnsIp := range domainIps {
				resolvedIp := strings.ToLower(utils.CompleteIPV6(dnsIp))
				log.Infof("[台账导入并扫描] 添加解析IP: %s -> %s", value, resolvedIp)
				data = append(data, &pb.AssetAccountImportScanIpDataItem{
					Ip:     resolvedIp,
					Domain: value,
				})
			}
		}
		isNeed = true

	} else {
		log.Infof("[台账导入并扫描] 文件导入开始 - time: %s, user_id: %d", time.Now().Format(time.DateTime), req.UserId)

		// 提取IP列表
		ips := utils.ListColumn(req.Data, func(item *pb.AssetAccountImportScanIpDataItem) string { return item.Ip })

		var errDomain []string
		var returnData []string
		taskParam := req.TaskParam

		// 校验IP格式
		if taskParam.IpType == task.IP_TYPE_V4 {
			for _, checkIp := range ips {
				if strings.Contains(checkIp, "-") || strings.Contains(checkIp, "/") {
					// 校验IPv4段格式
					if strings.Contains(checkIp, "/") {
						parts := strings.Split(checkIp, "/")
						if !utils.IsIPv4(parts[0]) {
							errDomain = append(errDomain, checkIp)
							continue
						}
						mask, _ := strconv.Atoi(parts[1])
						if mask > 32 || mask < 0 {
							errDomain = append(errDomain, checkIp)
							continue
						}
					} else if strings.Contains(checkIp, "*") {
						// 192.168.1-10.*格式
						if !utils.IsIPv4(strings.Split(checkIp, "-")[0] + ".1") {
							errDomain = append(errDomain, checkIp)
							continue
						}
						octet, _ := strconv.Atoi(strings.Split(strings.Split(checkIp, "-")[1], ".")[0])
						if octet > 255 || octet < 0 {
							errDomain = append(errDomain, checkIp)
							continue
						}
					} else {
						if !utils.IsIPv4(strings.Split(checkIp, "-")[0]) {
							errDomain = append(errDomain, checkIp)
							continue
						}
						lastPart := strings.Split(checkIp, "-")[1]
						num, _ := strconv.Atoi(lastPart)
						if num > 255 || num < 0 || strings.Contains(lastPart, ".") {
							errDomain = append(errDomain, checkIp)
							continue
						}
					}
				} else {
					if !utils.IsIPv4(checkIp) {
						errDomain = append(errDomain, checkIp)
						continue
					}
				}
				returnData = append(returnData, checkIp)
			}
		} else {
			// IPv6校验
			for _, checkIp := range ips {
				if strings.Contains(checkIp, "-") || strings.Contains(checkIp, "/") {
					continue
				} else {
					if !utils.IsIPv6(checkIp) {
						errDomain = append(errDomain, checkIp)
						continue
					}
					// 校验是否是可以解析为IPv4的IPv6格式
					expandedIp := utils.CompleteIPV6(checkIp)
					if dns.IsIPv4MappedIPv6(net.ParseIP(expandedIp)) {
						log.Warnf("[台账导入并扫描] 非法的IPv6 - %s", expandedIp)
						errDomain = append(errDomain, checkIp)
						continue
					}
					returnData = append(returnData, checkIp)
				}
			}
		}

		if len(errDomain) > 0 {
			rsp.DataList = returnData
			rsp.ErrorList = utils.ListDistinct(errDomain)
			return nil
		}

		if len(ips) == 0 {
			return fmt.Errorf("扫描目标ip为空，请重新下发扫描任务")
		}
		data = req.Data
	}

	if len(data) == 0 {
		errDomain := utils.ListColumn(req.Data, func(item *pb.AssetAccountImportScanIpDataItem) string { return item.Ip })
		if len(errDomain) > 0 {
			rsp.ErrorList = utils.ListDistinct(errDomain)
			return nil
		}
		if req.TaskParam.IpType == task.IP_TYPE_V4 {
			return fmt.Errorf("填写的数据无法解析为IPV4，请确认后重新填写！")
		} else {
			return fmt.Errorf("填写的数据无法解析为IPV6，请确认后重新填写！")
		}
	}

	// 调试：打印解析后的数据
	log.Infof("[台账导入并扫描] 解析后的数据总数: %d", len(data))
	for i, item := range data {
		log.Infof("[台账导入并扫描] data[%d]: IP=%s, Domain=%s", i, item.Ip, item.Domain)
	}

	// 提取所有IP
	allIpArr := utils.ListColumn(data, func(item *pb.AssetAccountImportScanIpDataItem) string { return item.Ip })
	log.Infof("[台账导入并扫描] 提取的所有IP: %v", allIpArr)

	//内网开发环境已经扫描内网，加个配置项
	if !cfg.CanScanInsideIp() {
		if hasInsideIp := CheckHasInsideIP(allIpArr); hasInsideIp != "" {
			return fmt.Errorf("您下发的扫描任务里面包含内网IP地址:%s，系统无法扫描内网IP，请去掉内网IP后再下发任务", hasInsideIp)
		}
	}

	// 判断是否为禁扫IP
	forbidIps, err := forbid.NewForbidIpsModel().FindByUserID(req.UserId)
	if err != nil {
		log.Errorf("[IP资产] 获取禁扫IP失败: %v", err)
		return err
	}

	forbidList := utils.ListColumn(forbidIps, func(item *forbid.ForbidIps) string { return item.IpSegment })

	var canNotScanIps []string
	for _, ip := range allIpArr {
		if utils.ListContains(forbidList, ip) || utils.ListContains(forbidList, ip+"/32") {
			canNotScanIps = append(canNotScanIps, ip)
		}
	}
	if len(canNotScanIps) > 0 {
		return fmt.Errorf("不能下发扫描任务，以下ip为禁扫ip:%s", strings.Join(canNotScanIps, ","))
	}

	// 限制IP导入数量
	if len(allIpArr) > 1000 {
		return fmt.Errorf("一次最多只能导入1000个IP")
	}

	// 提取域名为线索
	domainSet := make(map[string]bool)
	for _, item := range data {
		if strings.TrimSpace(item.Domain) != "" {
			domainSet[item.Domain] = true
		}
	}
	for domain := range domainSet {
		domainArr = append(domainArr, domain)
	}

	isSafe := req.UserId != req.OperatorId

	log.Infof("[台账导入并扫描] 开始处理域名线索 - time: %s, user_id: %d", time.Now().Format(time.DateTime), req.UserId)

	// 处理域名线索
	if len(domainArr) > 0 {
		for _, domain := range domainArr {
			log.Infof("[台账导入并扫描] 解析出来的域名: %s", domain)

			// 获取根域名并创建线索
			topDomain := utils.GetTopDomain(domain)
			if topDomain != "" {
				clueGroup, err := clues_groups.NewCluesGrouper().First(mysql.WithWhere("user_id = ?", req.UserId))
				if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
					log.Errorf("[台账导入并扫描] 获取线索组失败: %v", err)
					return err
				}
				if clueGroup.Id > 0 {
					existClue, err := clues.NewCluer().First(mysql.WithWhere("user_id = ? AND group_id = ? AND content = ? AND type = ? AND is_deleted = ?", req.UserId, clueGroup.Id, topDomain, clues.TYPE_DOMAIN, clues.NOT_DELETE))
					if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
						log.Errorf("[IP资产] 查询线索失败: %v", err)
						return err
					}
					if errors.Is(err, gorm.ErrRecordNotFound) {
						// 创建新线索
						newClue := &clues.Clue{
							Status:     clues.CLUE_PASS_STATUS,
							Content:    topDomain,
							Type:       clues.TYPE_DOMAIN,
							UserId:     req.UserId,
							GroupId:    clueGroup.Id,
							CompanyId:  req.CompanyId,
							Source:     clues.SOURCE_MANUAL_ADD,
							SafeUserId: utils.If(isSafe, req.OperatorId, 0),
						}
						if err := clues.NewCluer().Create(newClue); err != nil {
							log.Errorf("[IP资产] 创建线索失败: %v", err)
						}
					} else if existClue.Status != clues.CLUE_PASS_STATUS {
						// 更新状态
						if err := clues.NewCluer().UpdateAny(map[string]any{
							"status": clues.CLUE_PASS_STATUS,
						}, mysql.WithWhere("id = ?", existClue.Id)); err != nil {
							log.Errorf("[IP资产] 更新线索状态失败: %v", err)
						}
					}
				} else {
					log.Warnf("[台账导入并扫描] 未找到线索组，无法创建线索: %s", topDomain)
				}
			}
		}
	}

	// 检查用户权限和限制
	userInfo, err := user.NewUserModel().FindById(req.UserId)
	if err != nil {
		return err
	}

	if userInfo.Role == user.UserRoleTenant {
		companyInfo, err := company.NewCompanyModel().FindById(req.CompanyId, req.UserId)
		if err != nil {
			return err
		}
		hasIpNum := companyInfo.LimitIpAsset - companyInfo.UsedIpAsset
		if hasIpNum < 1 {
			return fmt.Errorf("您的台账数据已经超过限制，请联系售后人员！")
		}
	}

	// 准备扫描数据
	log.Infof("[台账导入并扫描] 准备扫描数据 - time: %s, user_id: %d", time.Now().Format(time.DateTime), req.UserId)
	var scanData []string
	param := [][]any{
		{"user_id", req.UserId},
	}
	query := elastic.ParseQuery(param)
	existingIpTags, err := elastic.All[fofaee_assets.FofaeeAssets](500, query, nil, "ip", "tags")
	if err != nil {
		log.Errorf("[台账导入并扫描] 获取IP标签失败: %v", err)
	}
	existingMap := make(map[string]*fofaee_assets.FofaeeAssets)
	for _, asset := range existingIpTags {
		existingMap[asset.Ip] = asset
	}

	var sureDataList []fofaee_assets.FofaeeAssets
	var updateDataTagList []map[string]interface{}

	// 统计计数器
	var newIpCount, existingIpCount int

	log.Infof("[台账导入并扫描] 开始构建插入数据, data数组长度: %d", len(data))

	for _, ipData := range data {
		ip := ipData.Ip
		ip = strings.ToLower(utils.CompleteIPV6(ip))
		scanData = append(scanData, ip)

		domain := ipData.Domain
		if isNeed && domain != "" {
			// 更新或创建StandingIps
			standing := standing_ips.StandingIps{
				UserId:    req.UserId,
				CompanyId: cast.ToString(req.CompanyId),
				Ip:        ip,
				Domain:    domain,
				Type:      utils.If(ipData.Type != 0, uint64(ipData.Type), 1), // 默认IPv4
			}
			if err := standing_ips.NewStandingIpsModel().UpdateOrCreate(standing); err != nil {
				log.Errorf("[台账导入并扫描] 更新StandingIps失败: %v", err)
			}
		}

		tags := []int{utils.If(isSafe, fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN)}

		// 检查IP是否已存在
		if existTags, exists := existingMap[ip]; exists {
			existingIpCount++
			log.Debugf("[台账导入并扫描] IP已存在，执行更新: %s", ip)

			// 更新已存在的IP状态
			whereParams := [][]interface{}{
				{"user_id", req.UserId},
				{"ip", ip},
				{"status", "in", []interface{}{fofaee_assets.STATUS_DEFAULT, fofaee_assets.STATUS_THREATEN, fofaee_assets.STATUS_IGNORE}},
			}
			if err := elastic.UpdateByParams[fofaee_assets.FofaeeAssets](whereParams, map[string]interface{}{
				"status":    fofaee_assets.STATUS_UPLOAD,
				"is_shadow": fofaee_assets.NOT_SHADOW,
			}); err != nil {
				log.Errorf("[台账导入并扫描] 更新IP资产状态失败: %v", err)
			}
			if err := elastic.UpdateByParams[foradar_assets.ForadarAsset](whereParams, map[string]interface{}{
				"status": fofaee_assets.STATUS_UPLOAD,
			}); err != nil {
				log.Errorf("[台账导入并扫描] 更新Foradar资产状态失败: %v", err)
			}

			// 合并标签
			mergedTags := utils.ListDistinct(utils.ListMerge(existTags.Tags, tags))
			updateDataTagList = append(updateDataTagList, map[string]interface{}{
				"id":   fmt.Sprintf("%d_%s", req.UserId, ip),
				"tags": mergedTags,
			})
			continue
		}

		// 准备新数据
		newIpCount++
		sureData := fofaee_assets.FofaeeAssets{
			Id:        fmt.Sprintf("%d_%s", req.UserId, ip),
			Ip:        ip,
			Status:    fofaee_assets.STATUS_UPLOAD,
			Type:      fofaee_assets.TYPE_CLAIMED,
			Tags:      tags,
			UserId:    int(req.UserId),
			IsIpv6:    utils.IsIPv6(ip),
			CreatedAt: time.Now().Format(time.DateTime),
			UpdatedAt: time.Now().Format(time.DateTime),
		}
		sureDataList = append(sureDataList, sureData)

		if newIpCount <= 10 || newIpCount%100 == 0 {
			log.Infof("[台账导入并扫描] 添加新IP到sureDataList: %s (第%d个)", ip, newIpCount)
		}
	}

	log.Infof("[台账导入并扫描] 数据构建完成统计:")
	log.Infof("  - 输入data数组长度: %d", len(data))
	log.Infof("  - 新IP数量: %d", newIpCount)
	log.Infof("  - 已存在IP数量: %d", existingIpCount)
	log.Infof("  - sureDataList长度: %d", len(sureDataList))
	log.Infof("  - updateDataTagList长度: %d", len(updateDataTagList))

	originIpNum := len(sureDataList)
	scanIpNum := originIpNum

	// 调试：打印最终的扫描数据
	log.Infof("[台账导入并扫描] 最终扫描数据 scanData: %v", scanData)
	log.Infof("[台账导入并扫描] scanData 长度: %d", len(scanData))

	// 处理资产限制
	if cfg.IsLocalClient() {
		// 本地化部署的限制处理
		limitIpAsset := cast.ToInt(userInfo.LimitIPAsset)
		params := [][]interface{}{
			{"user_id", req.UserId},
			{"status", "in", []interface{}{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD}},
		}
		usedNum, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](params)
		if err != nil {
			return err
		}

		canScanIpNum := limitIpAsset - int(usedNum)
		log.Infof("[台账导入并扫描] 本地化部署限制检查:")
		log.Infof("  - 授权IP数量: %d", limitIpAsset)
		log.Infof("  - 已使用数量: %d", usedNum)
		log.Infof("  - 可扫描数量: %d", canScanIpNum)
		log.Infof("  - 原始IP数量: %d", originIpNum)

		if canScanIpNum <= 0 {
			return fmt.Errorf("台账扫描数量已经超过授权数量，无法下发扫描任务!")
		}

		if canScanIpNum < originIpNum {
			log.Warnf("[台账导入并扫描] 触发本地化限制，截断数据: %d -> %d", originIpNum, canScanIpNum)
			sureDataList = sureDataList[:canScanIpNum]
			scanData = scanData[:canScanIpNum]
			scanIpNum = canScanIpNum
		} else {
			log.Infof("[台账导入并扫描] 本地化限制检查通过，无需截断")
		}
	} else if req.CompanyId != 0 {

		companyInfo, err := company.NewCompanyModel().FindById(req.CompanyId, req.UserId)
		if err != nil {
			return err
		}

		canScanIpNum := companyInfo.LimitIpAsset - companyInfo.UsedIpAsset
		log.Infof("[台账导入并扫描] 公司限制检查:")
		log.Infof("  - 公司授权IP数量: %d", companyInfo.LimitIpAsset)
		log.Infof("  - 公司已使用数量: %d", companyInfo.UsedIpAsset)
		log.Infof("  - 公司可扫描数量: %d", canScanIpNum)
		log.Infof("  - 原始IP数量: %d", originIpNum)

		if canScanIpNum <= 0 {
			return fmt.Errorf("台账授权数量已经用完，无法下发扫描任务")
		}
		if canScanIpNum < originIpNum {
			log.Warnf("[台账导入并扫描] 触发公司限制，截断数据: %d -> %d", originIpNum, canScanIpNum)
			sureDataList = sureDataList[:canScanIpNum]
			scanData = scanData[:canScanIpNum]
			scanIpNum = canScanIpNum
		} else {
			log.Infof("[台账导入并扫描] 公司限制检查通过，无需截断")
		}
	}

	// 批量插入IP资产
	if len(sureDataList) > 0 {
		log.Infof("[台账导入并扫描] 准备批量插入IP资产, 总数: %d, 批次大小: 500", len(sureDataList))

		successCount, errCount, lastErrorInfo := elastic.InsertOrUpdateWithTime(fofaee_assets.FofaeeAssetsIndex, fofaee_assets.FofaeeAssetsType, sureDataList, 500)

		log.Infof("[台账导入并扫描] 批量插入IP资产完成, 总数: %d, 成功: %d, 失败: %d, 错误信息: %s",
			len(sureDataList), successCount, errCount, lastErrorInfo)

		// 检查是否有数据丢失
		if successCount+errCount != len(sureDataList) {
			log.Warnf("[台账导入并扫描] 数据可能丢失! 总数: %d, 成功: %d, 失败: %d, 处理总数: %d",
				len(sureDataList), successCount, errCount, successCount+errCount)
		}

		if errCount > 0 {
			log.Errorf("[台账导入并扫描] 批量插入IP资产有失败记录, 失败数: %d, 错误信息: %s", errCount, lastErrorInfo)
			// 不要因为部分失败就返回错误，继续执行
		}
	}

	// 更新标签
	for _, item := range updateDataTagList {
		id := utils.AnyToStr(item["id"])
		tags := utils.AnyToStr(item["tags"])
		updateData := map[string]interface{}{
			"tags":      tags,
			"is_shadow": fofaee_assets.NOT_SHADOW,
		}
		params := [][]interface{}{
			{"user_id", req.UserId},
			{"_id", id},
		}
		if err := elastic.UpdateByParams[fofaee_assets.FofaeeAssets](params, updateData); err != nil {
			log.Errorf("[台账导入并扫描] 更新标签失败: %v", err)
		}
	}

	// 更新公司使用数量
	if req.CompanyId != 0 {
		params := [][]interface{}{
			{"user_id", req.UserId},
			{"status", "in", []interface{}{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD}},
		}
		usedNum, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](params)
		if err == nil {
			comp, err := company.NewCompanyModel().FindById(req.CompanyId, req.UserId)
			if err != nil {
				log.Errorf("[台账导入并扫描] 更新公司IP资产使用数量失败: %v", err)
			}
			err = comp.UpdateLimit(company.LIMIT_TYPE_IP, int(usedNum), false)
			if err != nil {
				log.Errorf("[台账导入并扫描] 更新公司IP资产使用数量失败: %v", err)
			}
		}
	}

	// 标记安服扫描
	if req.UserId != req.OperatorId {
		req.TaskParam.TaskFrom = 1
	}

	// 构建警告信息
	var warnMessage string
	if req.CompanyId != 0 && originIpNum != scanIpNum {
		warnMessage = fmt.Sprintf("下发资产数超入账限制，自动下发%d个资产", scanIpNum)
	}

	// 域名资产同步
	if len(domainArr) > 0 {
		log.WithContextInfof(context.Background(), "[台账导入并扫描] 需要同步域名资产: %v", domainArr)
		if cfg.ExecGolangJob() {

			// 对应PHP: TableAssetsDoaminsSync::dispatch($user_id,null,DomainAssets::DOMAIN_SCAN,null,null,$domainArr);
			err = asyncq.TableAssetsDoaminsSyncPhpJob.Dispatch(
				req.UserId, // user_id
				nil,        // task_id (null)
				6,          // from (DomainAssets::DOMAIN_SCAN = 6)
				nil,        // groupId (null)
				nil,        // domain_task_id (null)
				domainArr,  // import_domains ($domainArr)
				nil,        // flag (null)
				nil,        // detect_task_id (null)
				nil,        // organization_discover_task_id (null)
			)
			if err != nil {
				log.Errorf("[台账导入并扫描] 调用PHP域名同步job失败: %v", err)
				return err
			}

		} else {
			// 调用PHP的域名同步job
			// 对应PHP: TableAssetsDoaminsSync::dispatch($user_id,null,DomainAssets::DOMAIN_SCAN,null,null,$domainArr);
			err = asyncq.TableAssetsDoaminsSyncPhpJob.Dispatch(
				req.UserId, // user_id
				nil,        // task_id (null)
				6,          // from (DomainAssets::DOMAIN_SCAN = 6)
				nil,        // groupId (null)
				nil,        // domain_task_id (null)
				domainArr,  // import_domains ($domainArr)
				nil,        // flag (null)
				nil,        // detect_task_id (null)
				nil,        // organization_discover_task_id (null)
			)
			if err != nil {
				log.Errorf("[台账导入并扫描] 调用PHP域名同步job失败: %v", err)
				return err
			}
		}
	}

	// 分发扫描任务
	taskParam := asyncq.TaskParam{
		Name:                "资产台账-扫描任务(" + time.Now().Format(time.DateTime) + ")",
		Bandwidth:           int(req.TaskParam.Bandwidth),
		ProtocolConcurrency: int(req.TaskParam.ProtocolConcurrency),
		PingSwitch:          int(req.TaskParam.PingSwitch),
		WebLogoSwitch:       int(req.TaskParam.WebLogoSwitch),
		ScanType:            int(req.TaskParam.ScanType),
		IpType:              int(req.TaskParam.IpType),
		PortGroupIds:        req.PortGroupIds,
	}

	if cfg.ExecGolangJob() {
		//todo 调用go的job
		err = asyncq.Enqueue(context.Background(), asyncq.StandingAssetsScanJob, asyncq.StandingAssetsScanJobPayload{
			Ips:       scanData,
			UserId:    req.UserId,
			CompanyId: cast.ToString(req.CompanyId),
			ScanParams: struct {
				TaskParam           asyncq.TaskParam `json:"task_param"`
				IsDefinePort        int              `json:"is_define_port"`
				TaskFrom            int              `json:"task_from"`
				DefinePorts         []uint64         `json:"define_ports"`
				DefinePortProtocols []uint64         `json:"define_port_protocols"`
			}{
				TaskParam:           taskParam,
				IsDefinePort:        int(req.IsDefinePort),
				TaskFrom:            int(req.TaskParam.TaskFrom),
				DefinePorts:         req.DefinePorts,
				DefinePortProtocols: req.DefinePortProtocols,
			},
			OpUserId:  &req.OperatorId,
			DomainArr: domainArr,
		})
		if err != nil {
			log.Errorf("[IP资产] 分发扫描任务失败: %v", err)
			return err
		}
	} else {
		//todo 调用php的job
		log.Infof("[台账导入并扫描] 开始调用PHP job - time: %s, user_id: %d", time.Now().Format(time.DateTime), req.UserId)

		// 构造 scanParams 参数，匹配 PHP 期望的格式
		scanParams := map[string]interface{}{
			"operate_company_id": utils.If(req.CompanyId != 0, int64(req.CompanyId), int64(-1)),
			"data": func() []map[string]interface{} {
				var dataList []map[string]interface{}
				for _, ip := range scanData {
					dataList = append(dataList, map[string]interface{}{
						"ip": ip,
					})
				}
				return dataList
			}(),
			"port_group_ids": req.PortGroupIds,
			"source":         req.Source,
			"task_param": map[string]interface{}{
				"bandwidth":            taskParam.Bandwidth,
				"task_type":            1, // 固定值，对应资产扫描
				"ip_type":              taskParam.IpType,
				"protocol_concurrency": taskParam.ProtocolConcurrency,
				"scan_type":            taskParam.ScanType,
				"type":                 6, // 固定值，对应台账扫描
				"ping_switch":          taskParam.PingSwitch,
				"web_logo_switch":      taskParam.WebLogoSwitch,
				"port_group_ids":       req.PortGroupIds,
			},
			"is_define_port":        int(req.IsDefinePort),
			"define_ports":          req.DefinePorts,
			"define_port_protocols": req.DefinePortProtocols,
		}

		// 详细打印调试信息
		log.Infof("[台账导入并扫描] PHP job 参数详情:")
		log.Infof("  - ips: %+v", scanData)
		log.Infof("  - userId: %d", req.UserId)
		log.Infof("  - companyId: %s", cast.ToString(req.CompanyId))
		log.Infof("  - scanParams: %+v", scanParams)
		log.Infof("  - opUserId: %d", req.UserId)
		log.Infof("  - domainArr: %+v", domainArr)

		// 特别检查 scanParams.data 的内容
		if dataField, ok := scanParams["data"].([]map[string]interface{}); ok {
			log.Infof("[台账导入并扫描] scanParams.data 详细内容:")
			for i, item := range dataField {
				log.Infof("  - data[%d]: %+v", i, item)
			}
		}

		// 调用 PHP job
		err = asyncq.StandingAssetsScanPhpJob.Dispatch(
			scanData,                     // ips
			req.UserId,                   // userId
			cast.ToString(req.CompanyId), // companyId (可以为null)
			scanParams,                   // scanParams
			req.OperatorId,               // OperatorId
			domainArr,                    // domainArr
		)
		if err != nil {
			log.Errorf("[台账导入并扫描] 调用PHP job失败: %v", err)
			return err
		}

		log.Infof("[台账导入并扫描] PHP job调用成功 - time: %s, user_id: %d", time.Now().Format(time.DateTime), req.UserId)
	}

	// 记录操作日志
	log.Infof("[IP资产] 扫描任务完成，用户: %d，扫描数据: %v", req.UserId, scanData)

	rsp.ErrorList = utils.ListDistinct(errorList)
	rsp.WarnMessage = warnMessage
	return nil
}

// CheckHasInsideIP 检查是否存在内网IP，存在返回内网IP，否则返回空字符串
// 对应PHP的checkHasInsideIp函数，使用FILTER_VALIDATE_IP with FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE逻辑
func CheckHasInsideIP(ips []string) string {
	for _, value := range ips {
		// 先只判断ipv4类型的ip是否为内网ip
		if !strings.Contains(value, ":") {
			originValue := value

			// 处理CIDR格式，提取IP部分
			if strings.Contains(value, "/") {
				parts := strings.Split(value, "/")
				value = parts[0]
			} else if strings.Contains(value, "-") {
				// 处理IP范围格式，如 *************-30 或 ***********-10
				parts := strings.Split(value, "-")
				value = parts[0]
			}

			// 解析IP地址
			ip := net.ParseIP(value)
			if ip == nil {
				// IP格式无效，认为是内网IP
				log.Infof("检测到内网ip - IP格式无效: %s", originValue)
				return originValue
			}

			// 检查是否为IPv4
			if ip.To4() == nil {
				// 不是IPv4，跳过
				continue
			}

			// 使用Go的IsPrivate()方法检查是否为私有地址
			// 这等同于PHP的FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE
			if ip.IsPrivate() || ip.IsLoopback() || ip.IsLinkLocalUnicast() {
				log.Infof("检测到内网ip: %s", originValue)
				return originValue
			}
		}
	}
	return ""
}
