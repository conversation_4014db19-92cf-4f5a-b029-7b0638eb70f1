module micro-service

go 1.23.3

toolchain go1.23.8

require (
	github.com/DATA-DOG/go-sqlmock v1.5.2
	github.com/PuerkitoBio/goquery v1.8.1
	github.com/agiledragon/gomonkey/v2 v2.13.0
	github.com/alicebob/miniredis/v2 v2.35.0
	github.com/antchfx/htmlquery v1.3.0
	github.com/antchfx/xpath v1.2.3
	github.com/bytedance/sonic v1.13.2
	github.com/chromedp/cdproto v0.0.0-20230201081901-5f7573a87050
	github.com/chromedp/chromedp v0.8.7
	github.com/cloudwego/eino v0.3.25
	github.com/domainr/whois v0.1.0
	github.com/doorbash/bridge v0.1.10
	github.com/elliotchance/pie/v2 v2.8.0
	github.com/fghwett/icp v0.0.0-20211012072823-6d196a674cfa
	github.com/gin-contrib/gzip v0.0.6
	github.com/gin-contrib/zap v0.1.0
	github.com/gin-gonic/gin v1.9.1
	github.com/go-errors/errors v1.0.1
	github.com/go-micro/plugins/v4/broker/rabbitmq v1.2.1
	github.com/go-micro/plugins/v4/config/source/consul v1.2.0
	github.com/go-micro/plugins/v4/logger/zap v1.2.0
	github.com/go-micro/plugins/v4/registry/consul v1.2.0
	github.com/go-micro/plugins/v4/selector/registry v1.2.0
	github.com/go-micro/plugins/v4/wrapper/trace/opentracing v1.2.0
	github.com/go-oauth2/oauth2/v4 v4.5.2
	github.com/go-playground/locales v0.14.1
	github.com/go-playground/universal-translator v0.18.1
	github.com/go-playground/validator/v10 v10.14.1
	github.com/go-redis/redis/v8 v8.11.5
	github.com/go-redis/redismock/v8 v8.11.5
	github.com/go-resty/resty/v2 v2.1.1-0.20191201195748-d7b97669fe48
	github.com/goccy/go-json v0.10.2
	github.com/google/go-querystring v1.0.0
	github.com/google/uuid v1.6.0
	github.com/gorilla/websocket v1.4.2
	github.com/hashicorp/consul/api v1.20.0
	github.com/hashicorp/go-hclog v1.2.0
	github.com/hedon954/go-mysql-mocker v1.0.6
	github.com/idoubi/goz v1.4.2
	github.com/ipipdotnet/datx-go v0.0.0-20181123035258-af996d4701a0
	github.com/jedib0t/go-pretty/v6 v6.4.4
	github.com/jinzhu/copier v0.4.0
	github.com/jinzhu/now v1.1.5
	github.com/joho/godotenv v1.5.1
	github.com/jordan-wright/email v4.0.1-0.20210109023952-943e75fe5223+incompatible
	github.com/jpillora/go-tld v1.2.1
	github.com/json-iterator/go v1.1.12
	github.com/juju/ratelimit v1.0.2
	github.com/likexian/gokit v0.25.13
	github.com/likexian/whois v1.15.0
	github.com/likexian/whois-parser v1.24.8
	github.com/mark3labs/mcp-go v0.21.1
	github.com/miekg/dns v1.1.55
	github.com/nadoo/glider v0.15.0
	github.com/natefinch/lumberjack v2.0.0+incompatible
	github.com/olivere/elastic v6.2.37+incompatible
	github.com/opentracing/opentracing-go v1.2.0
	github.com/orcaman/concurrent-map/v2 v2.0.1
	github.com/panda843/go-migrate v1.3.6
	github.com/panda843/golang-sdk/api-sdk/kdl v0.0.0-20221126200401-ec520ed22a4b
	github.com/panda843/icp v0.0.0-20230902141226-b9384e8e67e8
	github.com/panda843/ksubdomain v1.0.1
	github.com/pkg/errors v0.9.1
	github.com/rabbitmq/amqp091-go v1.10.0
	github.com/rfyiamcool/cronlib v1.2.1
	github.com/smartystreets/assertions v1.1.1
	github.com/smartystreets/goconvey v1.8.1
	github.com/spf13/cast v1.8.0
	github.com/stretchr/testify v1.9.0
	github.com/tealeg/xlsx v1.0.5
	github.com/tidwall/gjson v1.14.4
	github.com/tidwall/sjson v1.2.5
	github.com/twmb/murmur3 v1.1.6
	github.com/uber/jaeger-client-go v2.30.0+incompatible
	github.com/urfave/cli/v2 v2.25.7
	github.com/weppos/publicsuffix-go v0.30.0
	github.com/xuri/excelize/v2 v2.7.0
	github.com/yeka/zip v0.0.0-20231116150916-03d6312748a9
	github.com/zaffka/zap-to-hclog v0.10.5
	go-micro.dev/v4 v4.11.0
	go.uber.org/goleak v1.3.0
	go.uber.org/mock v0.4.0
	go.uber.org/zap v1.24.0
	golang.org/x/crypto v0.38.0
	golang.org/x/exp v0.0.0-20231006140011-7918f672742d
	golang.org/x/net v0.40.0
	golang.org/x/time v0.11.0
	google.golang.org/grpc v1.53.0
	google.golang.org/protobuf v1.36.6
	gopkg.in/gomail.v2 v2.0.0-20160411212932-81ebce5c23df
	gopkg.in/yaml.v2 v2.4.0
	gorm.io/driver/mysql v1.5.7
	gorm.io/gorm v1.25.7
)

require (
	dario.cat/mergo v1.0.0 // indirect
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/HdrHistogram/hdrhistogram-go v1.1.2 // indirect
	github.com/Microsoft/go-winio v0.6.1 // indirect
	github.com/ProtonMail/go-crypto v0.0.0-20230923063757-afb1ddc0824c // indirect
	github.com/aead/chacha20 v0.0.0-20180709150244-8b13a72661da // indirect
	github.com/andybalholm/cascadia v1.3.2 // indirect
	github.com/antlr/antlr4 v0.0.0-20210105192202-5c2b686f95e1 // indirect
	github.com/armon/go-metrics v0.4.0 // indirect
	github.com/basgys/goxml2json v1.1.0 // indirect
	github.com/bitly/go-simplejson v0.5.1 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/chromedp/sysutil v1.0.0 // indirect
	github.com/cloudflare/circl v1.3.6 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/cpuguy83/go-md2man/v2 v2.0.2 // indirect
	github.com/cyphar/filepath-securejoin v0.2.4 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dolthub/flatbuffers/v23 v23.3.3-dh.2 // indirect
	github.com/dolthub/go-icu-regex v0.0.0-20241215010122-db690dd53c90 // indirect
	github.com/dolthub/go-mysql-server v0.19.0 // indirect
	github.com/dolthub/jsonpath v0.0.2-0.20240227200619-19675ab05c71 // indirect
	github.com/dolthub/vitess v0.0.0-20241211024425-b00987f7ba54 // indirect
	github.com/doorbash/go-shadowsocks2 v1.0.2 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/emirpasic/gods v1.18.1 // indirect
	github.com/evanphx/json-patch/v5 v5.5.0 // indirect
	github.com/fatih/color v1.14.1 // indirect
	github.com/felixge/httpsnoop v1.0.1 // indirect
	github.com/fortytw2/leaktest v1.3.0 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.2 // indirect
	github.com/getkin/kin-openapi v0.118.0 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/go-acme/lego/v4 v4.4.0 // indirect
	github.com/go-git/gcfg v1.5.1-0.20230307220236-3a3c6141e376 // indirect
	github.com/go-git/go-billy/v5 v5.5.0 // indirect
	github.com/go-git/go-git/v5 v5.11.0 // indirect
	github.com/go-kit/kit v0.10.0 // indirect
	github.com/go-openapi/jsonpointer v0.19.5 // indirect
	github.com/go-openapi/swag v0.19.5 // indirect
	github.com/go-sql-driver/mysql v1.7.2-0.20231213112541-0004702b931d // indirect
	github.com/gobwas/httphead v0.1.0 // indirect
	github.com/gobwas/pool v0.2.1 // indirect
	github.com/gobwas/ws v1.1.0 // indirect
	github.com/gofrs/uuid v4.1.0+incompatible // indirect
	github.com/golang-collections/collections v0.0.0-20130729185459-604e922904d3 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/google/gopacket v1.1.18 // indirect
	github.com/google/martian v2.1.0+incompatible // indirect
	github.com/goph/emperror v0.17.2 // indirect
	github.com/gopherjs/gopherjs v1.17.2 // indirect
	github.com/gorilla/handlers v1.5.1 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.2 // indirect
	github.com/hashicorp/go-immutable-radix v1.3.1 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-rootcerts v1.0.2 // indirect
	github.com/hashicorp/go-version v1.6.0 // indirect
	github.com/hashicorp/golang-lru v0.5.4 // indirect
	github.com/hashicorp/serf v0.10.1 // indirect
	github.com/idoubi/goutils v1.1.0 // indirect
	github.com/imdario/mergo v0.3.16 // indirect
	github.com/invopop/yaml v0.1.0 // indirect
	github.com/jbenet/go-context v0.0.0-20150711004518-d14ea06fba99 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jmoiron/sqlx v1.3.4 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/kevinburke/ssh_config v1.2.0 // indirect
	github.com/klauspost/cpuid/v2 v2.2.5 // indirect
	github.com/laijunbin/go-solve-kit v0.2.1 // indirect
	github.com/leodido/go-urn v1.2.4 // indirect
	github.com/lestrrat-go/strftime v1.0.4 // indirect
	github.com/logrusorgru/aurora v0.0.0-20200102142835-e9ef32dff381 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.19 // indirect
	github.com/mattn/go-runewidth v0.0.13 // indirect
	github.com/mattn/go-sqlite3 v1.14.15 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/hashstructure v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/nikolalohinski/gonja v1.5.3 // indirect
	github.com/nxadm/tail v1.4.8 // indirect
	github.com/oxtoacart/bpool v0.0.0-20190530202638-03653db5a59c // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pelletier/go-toml/v2 v2.0.9 // indirect
	github.com/perimeterx/marshmallow v1.1.4 // indirect
	github.com/pjbgf/sha1cd v0.3.0 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/rakyll/statik v0.1.7 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.3 // indirect
	github.com/rivo/uniseg v0.2.0 // indirect
	github.com/russross/blackfriday/v2 v2.1.0 // indirect
	github.com/saintfish/chardet v0.0.0-20230101081208-5e3ef4b5456d // indirect
	github.com/sergi/go-diff v1.3.1 // indirect
	github.com/shopspring/decimal v1.3.1 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/skeema/knownhosts v1.2.1 // indirect
	github.com/slongfield/pyfmt v0.0.0-20220222012616-ea85ff4c361f // indirect
	github.com/smarty/assertions v1.15.0 // indirect
	github.com/streadway/amqp v1.0.0 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/tetratelabs/wazero v1.8.2 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.1 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/uber/jaeger-lib v2.4.1+incompatible // indirect
	github.com/ugorji/go/codec v1.2.11 // indirect
	github.com/valyala/fasthttp v1.47.0 // indirect
	github.com/xanzy/ssh-agent v0.3.3 // indirect
	github.com/xrash/smetrics v0.0.0-20201216005158-039620a65673 // indirect
	github.com/xuri/efp v0.0.0-20220603152613-6918739fd470 // indirect
	github.com/xuri/nfp v0.0.0-20220409054826-5e722a1d9e22 // indirect
	github.com/yargevad/filepathx v1.0.0 // indirect
	github.com/yoda-of-soda/map2xml v1.0.2 // indirect
	github.com/yosida95/uritemplate/v3 v3.0.2 // indirect
	github.com/yuin/gopher-lua v1.1.1 // indirect
	github.com/zonedb/zonedb v1.0.4250 // indirect
	go.opentelemetry.io/otel v1.31.0 // indirect
	go.opentelemetry.io/otel/trace v1.31.0 // indirect
	golang.org/x/arch v0.11.0 // indirect
	golang.org/x/mod v0.17.0 // indirect
	golang.org/x/term v0.32.0 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	google.golang.org/genproto v0.0.0-20230306155012-7f2fa6fef1f4 // indirect
	gopkg.in/alexcesaro/quotedprintable.v3 v3.0.0-20150716171945-2caba252f4dc // indirect
	gopkg.in/src-d/go-errors.v1 v1.0.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	layeh.com/gopher-json v0.0.0-20201124131017-552bb3c4c3bf // indirect
)

require (
	github.com/bilibili/gengine v1.5.7
	github.com/dlclark/regexp2 v1.8.0
	github.com/forgoer/openssl v1.5.0
	github.com/gin-contrib/cors v1.4.0
	github.com/golang-jwt/jwt v3.2.2+incompatible // indirect
	github.com/leeqvip/gophp v1.0.0
	github.com/panjf2000/ants/v2 v2.7.4
	github.com/sourcegraph/conc v0.3.0
	github.com/tidwall/btree v1.6.0 // indirect
	github.com/tidwall/buntdb v1.2.10 // indirect
	go.uber.org/atomic v1.10.0
	go.uber.org/multierr v1.9.0 // indirect
	golang.org/x/sync v0.14.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.25.0
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/tomb.v1 v1.0.0-20141024135613-dd632973f1e7 // indirect
	gopkg.in/warnings.v0 v0.1.2 // indirect
)
