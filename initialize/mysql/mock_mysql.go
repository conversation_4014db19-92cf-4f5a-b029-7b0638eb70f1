package mysql

import (
	"fmt"
	"micro-service/pkg/cfg"
	"os"
	"runtime"
	"strings"
	"sync"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// GetMockInstance 获取mock实例
func GetMockInstance(opt ...cfg.MySql) sqlmock.Sqlmock {
	if singleInstance == nil {
		once.Do(
			func() {
				if len(opt) > 0 {
					singleInstance, singleMockInstance = initMysql(&opt[0])
				} else {
					singleInstance, singleMockInstance = initMysqlMock()
				}
			})
	}

	return singleMockInstance
}

func initMysqlMock() (*gorm.DB, sqlmock.Sqlmock) {
	mDB, mock, err := sqlmock.New()
	if err != nil {
		panic(fmt.Sprintf("sqlmock.New err:%v \n", err))
	}

	db, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      mDB,
		SkipInitializeWithVersion: true, // 跳过版本配置
	}))
	if err != nil {
		panic(fmt.Sprintf("gorm.Open err:%v \n", err))
	}

	return db, mock
}

func IsTest() bool {
	if !testFlag {
		return false
	}
	if len(os.Args) > 1 && strings.HasPrefix(os.Args[1], "-test") {
		return true
	}

	buf := make([]byte, 1<<16)
	runtime.Stack(buf, false)
	stack := string(buf)
	return strings.Contains(stack, "testing.tRunner")
}

var testFlag = true

func SetTestEnv(f bool) {
	testFlag = f
}

var lock sync.Mutex

func ForceTest(f bool) {
	lock.Lock()
	defer lock.Unlock()
	SetTestEnv(f)

	// 重置once，允许重新初始化
	once = sync.Once{}

	if f {
		// 如果强制测试模式，使用Mock
		singleInstance, singleMockInstance = initMysqlMock()
	} else {
		// 否则使用真实数据库
		conf := cfg.LoadMysql()
		singleInstance, singleMockInstance = initMysql(&conf)
	}
}

// ResetMockInstance 重置mock实例，用于测试用例之间的隔离
func ResetMockInstance() {
	lock.Lock()
	defer lock.Unlock()

	if IsTest() {
		singleInstance, singleMockInstance = initMysqlMock()
	}
}
