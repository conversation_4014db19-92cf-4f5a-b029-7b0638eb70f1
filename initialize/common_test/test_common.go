package testcommon

import (
	"encoding/json"
	"flag"
	"runtime"
	"strings"
)

func IsTest() bool {
	if !testFlag {
		return false
	}
	for _, arg := range flag.Args() {
		if arg == "-test.v" || arg == "-test.run" || arg == "-test.timeout" || arg == "-test.coverprofile" {
			return true
		}
	}

	buf := make([]byte, 1<<16)
	runtime.Stack(buf, false)
	stack := string(buf)
	return strings.Contains(stack, "testing.tRunner")
}

var testFlag = true

func SetTestEnv(f bool) {
	testFlag = f
}

var newMysqlMock = false

func SetNewMysqlMock(b bool) {
	newMysqlMock = b
}
func IsNewMysqlMock() bool {
	return newMysqlMock
}

// MockConsulConfig
// mock 一个 consul 的配置
func MockConsulConfig() []byte {
	m := map[string]map[string]interface{}{
		"consul": {
			"address": "127.0.0.1", "port": 8500, "prefix": "/fobrain", "token": "bec44458-2b65-300d-07d2-c25bfb49d971",
		}, "redis": {
			"address": "127.0.0.1", "port": 63796379, "password": "", "database": 0,
		}, "mysql": {
			"address": "127.0.0.1", "port": 3306, "username": "root", "password": "",
			"database": "fobrain_test", "charset": "utf8mb4", "log-level": "debug", "slow-time": 15,
		}, "rabbitmq": {
			"address": "", "port": 0, "username": "", "password": "", "vhost": "",
		}, "elastic": {
			"address": "127.0.0.1", "port": 9200, "username": "", "password": "", "sniff": false,
		}, "common": {
			"env": "dev", "log_level": "debug", "listen": ":8090", "network": "en0",
		}, "logger": {
			"level": "info", "output_console": true, "output_file": true,
			"file_name":           "/Users/<USER>/program/go/src/xinan/FOBrain/fobrain/storage/log/fobrain.log",
			"file_name_scheduler": "logs/scheduler-logs.log",
			"max_size":            64, "max_age": 30, "max_backups": 5, "local_time": true, "compress": true,
		}, "merge_logger": {
			"level": "info", "output_console": true, "output_file": true,
			"file_name_asset":  "logs/merge_asset.log",
			"file_name_vuln":   "logs/merge_vuln.log",
			"file_name_person": "logs/merge_person.log",
			"max_size":         64, "max_age": 30, "max_backups": 5, "local_time": true, "compress": true,
		}, "queue": {
			"asset_merge_queue": "", "asset_merge_concurrent": 0,
			"del_msg_batch": 5,
		},
	}

	jsonData, _ := json.Marshal(m)
	return jsonData
}
