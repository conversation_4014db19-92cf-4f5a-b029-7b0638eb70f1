package es

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"sync"

	"micro-service/pkg/cfg"

	"github.com/olivere/elastic"
)

var once sync.Once

var singleInstance *elastic.Client

func GetInstance(opt ...cfg.ElasticSearch) *elastic.Client {
	if singleInstance == nil {
		if len(opt) > 0 {
			once.Do(
				func() {
					singleInstance = initES(&opt[0])
				})
		} else {
			once.Do(
				func() {
					config := cfg.LoadElastic()
					singleInstance = initES(&config)
				})
		}
	}

	return singleInstance
}

func initES(conf *cfg.ElasticSearch) *elastic.Client {
	// 如果是测试环境，则使用mock模拟es
	if IsTest() {
		return GetElasticMockClient()
	}

	host := fmt.Sprintf("http://%s:%d", conf.Address, conf.Port)
	username := conf.UserName
	password := conf.Password

	errorLog := log.New(os.Stdout, "elasticsearch-log", log.LstdFlags)
	var err error
	esClient, err := elastic.NewClient(
		elastic.SetURL(host),
		elastic.SetBasicAuth(username, password), // 账号密码
		elastic.SetErrorLog(errorLog),
		elastic.SetGzip(true),
		elastic.SetSniff(false), //注意此处有坑, 如果es运行在docker中，并且未正确设置publish_host，会导致sniff拿到的IP为容器IP，无法连接，默认为开启，如果有遇到，方案1：设置es的network.publish_host，方案2：此处注释去掉，sniff功能将无法使用
	)

	if err != nil {
		panic(err)
	}

	_, _, err = esClient.Ping(host).Do(context.Background())
	if err != nil {
		panic(err)
	}

	return esClient
}

func PrintEsQuery(query *elastic.BoolQuery) {
	src, _ := query.Source()
	data, err := json.MarshalIndent(src, "", "  ")
	if err != nil {
		log.Fatalf("Error marshalling the query to JSON: %s", err)
	}
	fmt.Println(string(data))
}
