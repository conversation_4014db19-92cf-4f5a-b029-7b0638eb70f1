package icp

import (
	"context"
	"fmt"
	microZap "github.com/go-micro/plugins/v4/logger/zap"
	"github.com/hashicorp/go-hclog"
	"github.com/urfave/cli/v2"
	toZap "github.com/zaffka/zap-to-hclog"
	"go-micro.dev/v4/logger"
	"go.uber.org/zap"
	"micro-service/cliService/command"
	"micro-service/coreService/handler/icp"
	pb "micro-service/coreService/proto"
	"micro-service/initialize/es"
	iniMysql "micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/company_icp"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/rule_engine/engine"
	"os"
	"time"
)

var flags = []cli.Flag{
	&cli.IntFlag{
		Name: "page", Value: 1, Usage: "抓取最大页数",
	},
}

func onInit() {
	cfg.InitLoadCfg()

	// 初始化日志
	zap.ReplaceGlobals(log.GetLogger())
	hclog.SetDefault(toZap.Wrap(log.GetLogger()))
	z, _ := microZap.NewLogger(microZap.WithConfig(log.GetZapConfig()))
	logger.DefaultLogger = z
	// Mysql & Redis & elastic
	_ = iniMysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	_ = es.GetInstance(cfg.LoadElastic())
	engine.InitEnginePool() // init rule engine
}

func init() {
	command.GetFCLI().Register(&cli.Command{
		Name:  "icp",
		Usage: "ICP检查",
		Subcommands: []*cli.Command{
			{
				Name:    "check",
				Aliases: []string{"i"},
				Usage:   "检查备案异常的ICP信息,例: foradar_cli icp check",
				Action:  CheckIcp,
				Flags:   flags,
			},
		},
	})
}

func CheckIcp(ctx *cli.Context) error {
	onInit()
	page := 1
	current := 1
	if ctx.Int("page") != 0 {
		page = ctx.Int("page")
		current = (page - 1) * 1000
	}
	for {
		icps, total, err := company_icp.NewCompanyIcpModel().List(
			page, 100, mysql.WithWhere("parent_id = 0 and (select count(*) as num from company_icp b where b.parent_id = id and b.status = 1) < 2"), mysql.WithOrder("updated_at asc"),
		)
		if err != nil {
			return fmt.Errorf("获取ICP信息失败:%s,当前Page:%d", err.Error(), page)
		}
		_, err = fmt.Fprintf(os.Stdout, "处理数据总数:%d\n", total)
		if err != nil {
			return err
		}
		for i := range icps {
			current += 1
			rsp := pb.IcpResponse{}
			if rErr := icp.CompanyName(context.TODO(), &pb.IcpCompanyNameRequest{CompanyName: icps[i].Name, Force: true}, &rsp); rErr != nil {
				_, _ = fmt.Fprintf(os.Stdout, "总条数:%d,当前:%d,刷新备案信息失败:%s", total, current, rErr.Error())
			} else {
				_, _ = fmt.Fprintf(os.Stdout, "总条数:%d,当前:%d,刷新备案信息成功:%+v", total, current, &rsp)
			}
			time.Sleep(1 * time.Second)
		}
		page += 1
	}
}
