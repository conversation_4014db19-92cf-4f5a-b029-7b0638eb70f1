package command

import (
	"os"

	"micro-service/pkg/cfg"
	"micro-service/pkg/log"

	cli2 "github.com/urfave/cli/v2"
	"go-micro.dev/v4/util/cmd"
	"go.uber.org/zap/zapcore"
)

var fCli *FCLI

type FCLI struct {
	Fcmd cmd.Cmd
}

func GetFCLI() *FCLI {
	return fCli
}

func (f *FCLI) Register(command ...*cli2.Command) {
	f.Fcmd.App().Commands = append(f.Fcmd.App().Commands, command...)
}

func init() {
	log.Level.SetLevel(zapcore.ErrorLevel)
	// 获取配置
	consulReg := cfg.GetInstance().GetConsulReg()
	// 初始化CMD
	fCli = &FCLI{
		Fcmd: cmd.NewCmd(
			cmd.Registry(&consulReg),
			cmd.Name("FORadar CLI"),
			cmd.Version("v1.0.0"),
			cmd.Description("The FORadar CLI tool"),
		),
	}
	fCli.Fcmd.App().Flags = nil
}

func Run() error {
	args := os.Args
	if len(args) <= 1 {
		args = append(args, "help")
	}
	return fCli.Fcmd.App().Run(args)
}
