package migrations

import (
	apicounter "micro-service/middleware/mysql/api_counter"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateApiCounterTable())
// }

type ApiCounterTable struct{}

func CreateApiCounterTable() interfaces.Migration {
	return &ApiCounterTable{}
}

func (t *ApiCounterTable) Up() error {
	return mysql.Schema.Create(apicounter.TableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("client_id", 255).Comment("client_id")
		table.BigInt("user_id", 22).Default(0).Comment("用户ID")
		table.Integer("year", 5).Comment("年份")
		table.Integer("month", 2).Comment("月份")
		table.Integer("total", 10).Default(0).Comment("请求总数")
		table.String("path", 255).Default(`''`).Comment("请求路径")
		table.Index("client_id", "year", "month").IndexName("idx_client_year_month")
	})
}

func (t *ApiCounterTable) Down() error {
	return mysql.Schema.DropIfExists(apicounter.TableName)
}
