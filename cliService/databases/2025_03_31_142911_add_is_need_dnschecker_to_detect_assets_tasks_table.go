package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AddIsNeedDnscheckerToDetectAssetsTasksTable struct{}

func CreateAddIsNeedDnscheckerToDetectAssetsTasksTable() interfaces.Migration {
	return &AddIsNeedDnscheckerToDetectAssetsTasksTable{}
}

func (t *AddIsNeedDnscheckerToDetectAssetsTasksTable) Up() error {
	return mysql.Schema.Table("detect_assets_tasks", func(table interfaces.Blueprint) {
		table.Integer("is_need_dnschecker", 1).Default(0).Comment("这个测绘任务是否是否需要dnschecker 0/1  否/是")
	})
}

func (t *AddIsNeedDnscheckerToDetectAssetsTasksTable) Down() error {
	return nil
}
