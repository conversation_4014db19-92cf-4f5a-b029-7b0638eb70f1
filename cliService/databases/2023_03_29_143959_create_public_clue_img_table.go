package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreatePublicClueImgTable())
// }

type PublicClueImgTable struct{}

func CreatePublicClueImgTable() interfaces.Migration {
	return &PublicClueImgTable{}
}

func (t *PublicClueImgTable) Up() error {
	return mysql.Schema.Create("public_clue_img", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.LongText("img").Comment("图片(base64)")
		table.String("img_comment", 255).Comment("说明").Nullable()
		table.TableComment("公共线索库图片")
		table.Timestamps()
	})
}

func (t *PublicClueImgTable) Down() error {
	return mysql.Schema.DropIfExists("public_clue_img")
}
