package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AlterIntelligenceRelatedIntelligenceAddProjectNameTable struct{}

func CreateAlterIntelligenceRelatedIntelligenceAddProjectNameTable() interfaces.Migration {
	return &AlterIntelligenceRelatedIntelligenceAddProjectNameTable{}
}

func (t *AlterIntelligenceRelatedIntelligenceAddProjectNameTable) Up() error {
	return mysql.Schema.Table("intelligence_related_intelligence", func(table interfaces.Blueprint) {
		table.String("special_project_name", 255).Nullable().Comment("专项名称")
		table.String("enterprise_name", 255).Nullable().Comment("企业名称")
	})
}

func (t *AlterIntelligenceRelatedIntelligenceAddProjectNameTable) Down() error {
	return nil
}
