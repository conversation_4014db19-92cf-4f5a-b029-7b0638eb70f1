package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateAuthAccessTokensTable())
// }

type AuthAccessTokensTable struct{}

func CreateAuthAccessTokensTable() interfaces.Migration {
	return &AuthAccessTokensTable{}
}

func (t *AuthAccessTokensTable) Up() error {
	return mysql.Schema.Create("auth_access_tokens", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.DateTime("expired_at").Comment("过期时间").Index().Nullable()
		table.String("code", 512).Comment("code码").Nullable()
		table.String("access_token", 512).Comment("AccessToken").Index()
		table.String("refresh_token", 512).Comment("刷新key").Index()
		table.LongText("data").Comment("Data数据").Nullable()
		table.TableComment("Token信息表")
		table.Timestamps()
	})
}

func (t *AuthAccessTokensTable) Down() error {
	return mysql.Schema.DropIfExists("auth_access_tokens")
}
