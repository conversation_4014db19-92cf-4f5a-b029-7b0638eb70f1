package migrations

import (
	"micro-service/middleware/mysql/dataleak_doc88"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateDlpDoc88RelationTable())
// }

type DlpDoc88Relation struct{}

func CreateDlpDoc88RelationTable() interfaces.Migration {
	return &DlpDoc88Relation{}
}

func (*DlpDoc88Relation) Up() error {
	return mysql.Schema.Create(dataleak_doc88.RelationTable, func(table interfaces.Blueprint) {
		// 建表字段
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Default(0).Comment("任务id")
		table.BigInt("result_id", 22).Default(0).Comment("总库id")
		// 表索引
		table.Index("task_id").IndexName("idx_task_id")
	})
}

func (*DlpDoc88Relation) Down() error {
	return mysql.Schema.DropIfExists(dataleak_doc88.RelationTable)
}
