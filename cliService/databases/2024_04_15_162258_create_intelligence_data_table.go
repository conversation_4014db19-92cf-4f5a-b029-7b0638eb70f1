package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type IntelligenceDataTable struct{}

func CreateIntelligenceDataTable() interfaces.Migration {
	return &IntelligenceDataTable{}
}

func (t *IntelligenceDataTable) Up() error {
	return mysql.Schema.Create("intelligence_data", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("summary_file_hash", 255).Comment("汇总文件hash")
		table.String("special_project_name", 255).Comment("专项名称")
		table.String("event_name", 255).Comment("事件名称")
		table.String("special_project_category", 255).Comment("专项分类")
		table.String("overview", 1000).Comment("概述")
		table.DateTime("disclosure_time").Nullable().Comment("披露时间")
		table.String("leak_source_ip", 255).Comment("泄漏源地址")
		table.String("ip_related_location", 255).Comment("IP关联地点")
		table.String("leak_service_component", 255).Comment("泄露服务/组件")
		table.Integer("data_volume", 11).Comment("数据量")
		table.String("data_content", 255).Comment("数据内容")
		table.String("data_entity", 255).Comment("数据所属实体")
		table.String("data_entity_location", 255).Comment("数据所属实体地点")
		table.String("leak_reason", 255).Comment("泄露原因")
		table.String("fix_solution", 255).Comment("修复方案")
		// Report 相关字段
		table.DateTime("creation_time").Comment("创建时间")
		table.String("download_link", 255).Comment("文件下载链接")
		table.String("filename", 255).Comment("文件名")
		table.String("hash", 255).Comment("文件hash")
		table.String("tags", 255).Comment("事件标签")
	})
}

func (t *IntelligenceDataTable) Down() error {
	return mysql.Schema.DropIfExists("intelligence_data")
}
