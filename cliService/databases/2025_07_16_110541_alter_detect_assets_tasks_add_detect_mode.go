package migrations

import (
	"github.com/panda843/go-migrate/config"
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

func init() {
	config.Migrations = append(config.Migrations, CreateAlterDetectAssetsTasksAddDetectMode())
}

type AlterDetectAssetsTasksAddDetectMode struct{}

func CreateAlterDetectAssetsTasksAddDetectMode() interfaces.Migration {
	return &AlterDetectAssetsTasksAddDetectMode{}
}

func (t *AlterDetectAssetsTasksAddDetectMode) Up() error {
	return mysql.Schema.Table("detect_assets_tasks", func(table interfaces.Blueprint) {
		table.Integer("detect_mode", 4).Default(0).Comment("测绘模式 0/1/2/3/4 无/智能/标准/专家/自动")
	})
}

func (t *AlterDetectAssetsTasksAddDetectMode) Down() error {
	return mysql.Schema.Table("detect_assets_tasks", func(table interfaces.Blueprint) {
		table.DropColumn("detect_mode")
	})
}
