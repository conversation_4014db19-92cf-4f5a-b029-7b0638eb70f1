package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
	miaosou "micro-service/middleware/mysql/dataleak_miaosou"
)

type DataLeakMiaosouRelationTable struct{}

func CreateDataLeakMiaosouRelationTable() interfaces.Migration {
	return &DataLeakMiaosouRelationTable{}
}

func (t *DataLeakMiaosouRelationTable) Up() error {
	return mysql.Schema.Create(miaosou.RelationTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.BigInt("task_id", 22).Comment("任务id")
		table.BigInt("result_id", 22).Comment("结果id")
		table.Index("task_id", "result_id").IndexName("idx_task_result")
		table.Timestamps()
	})
}

func (t *DataLeakMiaosouRelationTable) Down() error {
	return mysql.Schema.DropIfExists(miaosou.RelationTableName)
}
