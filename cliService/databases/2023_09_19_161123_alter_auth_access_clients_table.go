package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	"micro-service/middleware/mysql/auth_access_client"
)

type AlterAccessClient230919 struct{}

func CreateAlterAccessClient230919Table() interfaces.Migration {
	return &AlterAccessClient230919{}
}

func (t *AlterAccessClient230919) Up() error {
	return mysql.Schema.Table(auth_access_client.AuthAccessClientTable, func(table interfaces.Blueprint) {
		table.Integer("local_arch", 5).Default(0).Nullable().Comment("本地化部署架构：1-x86, 2-arm")
	})
}

func (t *AlterAccessClient230919) Down() error {
	return mysql.Schema.Table(auth_access_client.AuthAccessClientTable, func(table interfaces.Blueprint) {
		table.DropColumn("local_arch")
	})
}
