package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateCloudAssetsTaskTable())
// }

type CloudAssetsTaskTable struct{}

func CreateCloudAssetsTaskTable() interfaces.Migration {
	return &CloudAssetsTaskTable{}
}

func (t *CloudAssetsTaskTable) Up() error {
	return mysql.Schema.Create("cloud_assets_task", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Decimal("process", 11, 2).Comment("任务进度")
		table.String("hash", 64).Index().Comment("任务Hash")
		table.Timestamps()
	})
}

func (t *CloudAssetsTaskTable) Down() error {
	return mysql.Schema.DropIfExists("cloud_assets_task")
}
