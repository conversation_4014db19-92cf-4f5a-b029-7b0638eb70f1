package migrations

import (
	"github.com/panda843/go-migrate/config"
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

func init() {
	config.Migrations = append(config.Migrations, CreateCustomPocTable())
}

type CustomPocTable struct{}

func CreateCustomPocTable() interfaces.Migration {
	return &CustomPocTable{}
}

func (t *CustomPocTable) Up() error {
	return mysql.Schema.Create("custom_pocs", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.DeletedAt(true)
		table.BigInt("user_id", 20).Unsigned().Default(0).Comment("用户ID").Index()
		table.String("name", 255).Nullable().Comment("poc名称")
		table.Text("description").Nullable().Comment("描述")
		table.String("product", 255).Nullable().Comment("对应产品")
		table.String("homepage", 255).Nullable().Comment("产品主页")
		table.Date("disclosure_date").Nullable().Comment("披露日期")
		table.String("author", 255).Nullable().Comment("作者")
		table.Text("fofa_query").Nullable().Comment("fofa查询语句")
		table.Integer("level", 11).Default(0).Comment("等级")
		table.Text("impact").Nullable().Comment("漏洞危害")
		table.MediumText("recommendation").Nullable().Comment("修复建议")
		table.Text("references").Nullable().Comment("引用地址")
		table.Boolean("is0day").Default(0).Comment("是否是0day")
		table.Text("scan_steps").Nullable().Comment("测试内容")
		table.Text("tags").Nullable().Comment("标签")
		table.Text("vul_type").Nullable().Comment("漏洞类型")
		table.String("cve", 255).Nullable().Comment("Cve编号")
		table.String("cnnvd", 255).Nullable().Comment("CNNVD编号")
		table.String("cnvd", 255).Nullable().Comment("CNVD编号")
		table.String("cvssscore", 255).Nullable().Comment("CVSS评分")
		table.Text("attack_surfaces").Nullable().Comment("攻击面")
		table.Text("editor").Nullable().Comment("文本内容")
		table.Boolean("publish").Default(0).Comment("是否发布")
		table.Integer("fofa_records", 11).Default(0).Comment("fofa影响ip数")
		table.BigInt("poc_id", 20).Unsigned().Default(0).Comment("scan_pocs表的id").Index()
		table.TableComment("自定义POC表")
	})
}

func (t *CustomPocTable) Down() error {
	return mysql.Schema.DropIfExists("custom_pocs")
}
