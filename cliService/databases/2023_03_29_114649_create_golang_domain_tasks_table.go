package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateGolangDomainTasksTable())
// }

type GolangDomainTasksTable struct{}

func CreateGolangDomainTasksTable() interfaces.Migration {
	return &GolangDomainTasksTable{}
}

func (t *GolangDomainTasksTable) Up() error {
	return mysql.Schema.Create("golang_domain_tasks", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("name", 300).Nullable().Comment("任务名称")
		table.Text("domain_list").Nullable().Comment("域名列表")
		table.Boolean("status").Comment("任务状态: 0/1/2/3/4 等待/处理中/成功/失败/停止").Default(0)
		table.Integer("level", 11).Comment("发现层级: 1/2/3 一二三层").Default(1)
		table.String("bandwidth", 12).Comment("扫描带宽").Nullable()
		table.Boolean("modify").Default(0).Comment("任务模式: 0/1 枚举/验证模式")
		table.Decimal("progress", 11, 2).Comment("任务进度").Default(0.00)
		table.Text("verify_domain_list").Nullable().Comment("验证模式下返回域名列表")
		table.TableComment("golang域名爆破任务表")
		table.Timestamps()
	})
}

func (t *GolangDomainTasksTable) Down() error {
	return mysql.Schema.DropIfExists("golang_domain_tasks")
}
