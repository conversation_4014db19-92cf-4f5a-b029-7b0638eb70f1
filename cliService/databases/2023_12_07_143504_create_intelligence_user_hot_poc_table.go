package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type IntelligenceUserHotPocTable struct{}

func CreateIntelligenceUserHotPocTable() interfaces.Migration {
	return &IntelligenceUserHotPocTable{}
}

func (t *IntelligenceUserHotPocTable) Up() error {
	return mysql.Schema.Create("intelligence_user_hot_poc", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.BigInt("user_id", 22).Index().Unsigned().Comment("用户ID")
		table.BigInt("poc_id", 22).Index().Unsigned().Comment("POC ID")
		table.BigInt("risk_count", 22).Default(0).Nullable().Comment("风险资产数量")
		table.Timestamps()
	})
}

func (t *IntelligenceUserHotPocTable) Down() error {
	return mysql.Schema.DropIfExists("intelligence_user_hot_poc")
}
