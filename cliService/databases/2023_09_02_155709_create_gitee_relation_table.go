package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	gitee "micro-service/middleware/mysql/dataleak_gitee"
)

type giteeRelation struct{}

func CreateGiteeRelationTable() interfaces.Migration {
	return &giteeRelation{}
}

func (t *giteeRelation) Up() error {
	return mysql.Schema.Create(gitee.RelationTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务id")
		table.BigInt("result_id", 22).Comment("结果id")
		table.Index("task_id", "result_id").IndexName("idx_task_result")
		table.TableComment("gitee: 任务与结果关系表")
	})
}

func (t *giteeRelation) Down() error {
	return mysql.Schema.DropIfExists(gitee.RelationTableName)
}
