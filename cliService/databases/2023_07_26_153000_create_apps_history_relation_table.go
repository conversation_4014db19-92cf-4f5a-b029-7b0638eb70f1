package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	"micro-service/middleware/mysql/apps"
)

type AppHistoryRelation struct{}

func CreateAppHistoryRelationTable() interfaces.Migration {
	return &AppHistoryRelation{}
}

func (*AppHistoryRelation) Up() error {
	return mysql.Schema.Create(apps.RelationTable, func(table interfaces.Blueprint) {
		// 建表字段
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("history_id", 22).Default(0).Comment("任务id")
		table.BigInt("app_id", 22).Default(0).Comment("app id")
		// 表索引
		table.Index("history_id").IndexName("idx_history_id")
	})
}

func (*AppHistoryRelation) Down() error {
	return mysql.Schema.DropIfExists(apps.RelationTable)
}
