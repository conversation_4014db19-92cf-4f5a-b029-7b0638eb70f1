package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
	"micro-service/middleware/mysql/weibo_accounts"
)

type WeiboAccount struct{}

func CreateWeiboAccountTable() interfaces.Migration {
	return &WeiboAccount{}
}

func (t *WeiboAccount) Up() error {
	return mysql.Schema.Create(weibo_accounts.AccountTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("keyword", 255).Comment("关键词").Index().IndexName("idx_keyword")
		table.String("ico", 255).Nullable().Comment("ico地址")
		table.String("name", 255).Nullable().Comment("微博账号名")
		table.String("href", 100).Comment("微博账号地址")
		table.String("info", 2500).Nullable()
		table.String("tags", 255).Nullable().Comment("行业类别")
		table.Comment("天眼查-微博信息")
	})
}

func (t *WeiboAccount) Down() error {
	return mysql.Schema.DropIfExists(weibo_accounts.AccountTableName)
}
