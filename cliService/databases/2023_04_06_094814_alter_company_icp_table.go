package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateAlterCompanyIcpTable())
// }

type AlterCompanyIcpTable struct{}

func CreateAlterCompanyIcpTable() interfaces.Migration {
	return &AlterCompanyIcpTable{}
}

func (t *AlterCompanyIcpTable) Up() error {
	return mysql.Schema.Table("company_icp", func(table interfaces.Blueprint) {
		table.CustomSql("ADD `status` TINYINT NOT NULL DEFAULT '1' COMMENT \"备案状态: 1/2 正常/注销\"")
		table.CustomSql("ADD `source` VARCHAR(128) DEFAULT '' COMMENT \"备案信息来源\"")
	})
}

func (t *AlterCompanyIcpTable) Down() error {
	return mysql.Schema.Table("company_icp", func(table interfaces.Blueprint) {
		table.DropColumn("status")
		table.DropColumn("source")
	})
}
