package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type ZeroZoneMiniAppTable struct{}

func CreateZeroZoneMiniAppTable() interfaces.Migration {
	return &ZeroZoneMiniAppTable{}
}

func (t *ZeroZoneMiniAppTable) Up() error {
	return mysql.Schema.Create("zero_zone_mini_app", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("name", 512).Comment("公众号名称").Index()
		table.String("wechat_id", 125).Comment("wechat_id").Index()
		table.String("app_id", 1024).Comment("app_id").Nullable()
		table.String("company_name", 512).Comment("账号主体").Index().Nullable()
		table.String("introduction", 1024).Comment("描述").Nullable()
		table.String("icp", 128).Comment("icp备案号").Nullable()
		table.String("platform", 512).Comment("平台").Index().Nullable()
		table.String("icon_url", 2000).Comment("icon_url").Nullable()
		table.TableComment("小程序数据总库")
		table.Timestamps()
	})
}

func (t *ZeroZoneMiniAppTable) Down() error {
	return mysql.Schema.DropIfExists("zero_zone_mini_app")
}
