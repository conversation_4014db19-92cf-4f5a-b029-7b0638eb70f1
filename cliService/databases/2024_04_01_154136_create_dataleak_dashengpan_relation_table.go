package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
	dashengpan "micro-service/middleware/mysql/dataleak_dashengpan"
)

type DataleakDashengpanRelationTable struct{}

func CreateDataleakDashengpanRelationTable() interfaces.Migration {
	return &DataleakDashengpanRelationTable{}
}

func (t *DataleakDashengpanRelationTable) Up() error {
	return mysql.Schema.Create(dashengpan.RelationTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.BigInt("task_id", 22).Comment("任务id")
		table.BigInt("result_id", 22).Comment("结果id")
		table.Index("task_id", "result_id").IndexName("idx_task_result")
		table.Timestamps()
	})
}

func (t *DataleakDashengpanRelationTable) Down() error {
	return mysql.Schema.DropIfExists(dashengpan.RelationTableName)
}
