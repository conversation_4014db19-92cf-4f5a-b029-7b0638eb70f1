package migrations

import (
	ds "micro-service/middleware/mysql/domain_search"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// 域名搜索任务 新建表迁移
type createDomainSearchTask20231102 struct{}

func CreateDomainSearchTaskTable() interfaces.Migration {
	return &createDomainSearchTask20231102{}
}

func (t *createDomainSearchTask20231102) Up() error {
	return mysql.Schema.Create(ds.TaskTable, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("user_id", 22).Comment("用户ID")
		table.BigInt("safe_user_id", 22).Nullable().Comment("安服用户ID")
		table.BigInt("company_id", 22).Nullable().Comment("企业ID")
		table.BigInt("parent_id", 22).Nullable().Comment("父级任务ID")
		table.Integer("has_children", 1).Nullable().Default(0).Comment("是否有子任务 0/1 否/是")
		table.Integer("status", 1).Nullable().Default(ds.StatusProcessing).Comment("任务状态: 1/2 进行中/已完成")
		table.String("progress", 5).Nullable().Default("0").Comment("进度百分比")
		table.Integer("domain_type", 1).Default(0).Comment("域名类型：1/2/3 主域名/子域名/混合(不拆分)")
		table.String("search", 5000).Nullable().Default("").Comment("搜索域名(json格式存储)")
		table.Index("user_id").IndexName("idx_user_id")
		table.Index("parent_id").IndexName("idx_parent_id")
		table.TableComment("域名搜索任务")
	})
}

func (t *createDomainSearchTask20231102) Down() error {
	return mysql.Schema.DropIfExists(ds.TaskTable)
}
