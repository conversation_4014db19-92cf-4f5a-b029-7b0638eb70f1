package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	"micro-service/middleware/mysql/apps"
)

type AlterApps20230726 struct{}

func CreateAlterAppsTable20230726() interfaces.Migration {
	return &AlterApps20230726{}
}

func (t *AlterApps20230726) Up() error {
	return mysql.Schema.Table(apps.AppsTable, func(table interfaces.Blueprint) {
		table.Text("yyb_origin_info").Nullable().Comment("腾讯应用宝软件原始信息")
	})
}

func (t *AlterApps20230726) Down() error {
	return mysql.Schema.Table(apps.AppsTable, func(table interfaces.Blueprint) {
		table.DropColumn("yyb_origin_info")
	})
}
