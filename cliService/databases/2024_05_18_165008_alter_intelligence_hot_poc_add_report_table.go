package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AlterIntelligenceHotPocAddReportTable struct{}

func CreateAlterIntelligenceHotPocAddReportTable() interfaces.Migration {
	return &AlterIntelligenceHotPocAddReportTable{}
}

func (t *AlterIntelligenceHotPocAddReportTable) Up() error {
	return mysql.Schema.Table("intelligence_hot_poc", func(table interfaces.Blueprint) {
		table.DateTime("creation_time").Nullable().Comment("创建时间")
		table.String("download_link", 255).Nullable().Comment("文件下载链接")
		table.String("filename", 255).Nullable().Comment("文件名")
		table.String("hash", 255).Nullable().Comment("文件hash")
		table.String("tags", 255).Nullable().Comment("事件标签")
		table.String("local_link", 255).Nullable().Comment("原始报告文件本地链接")
		table.String("masked_report_local_link", 255).Nullable().Comment("脱敏报告文件本地链接")
		table.Integer("masked_report_created_id", 64).Nullable().Comment("脱敏报告文件创建者ID")
		table.DateTime("masked_report_updated_time").Nullable().Comment("脱敏报告文件更新时间")
	})
}

func (t *AlterIntelligenceHotPocAddReportTable) Down() error {
	return nil
}
