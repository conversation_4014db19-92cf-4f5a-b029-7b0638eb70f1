package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type CreateApiQuotaConfigTable struct{}

func CreateApiQuotaConfigTableMigration() interfaces.Migration {
	return &CreateApiQuotaConfigTable{}
}

func (t *CreateApiQuotaConfigTable) Up() error {
	return mysql.Schema.Create("api_quota_config", func(table interfaces.Blueprint) {
		table.Id("id", 20)
		table.String("api_path", 255).Comment("API路径（支持通配符）").Index()
		table.String("method", 10).Default("*").Comment("HTTP方法（GET/POST/*）")
		table.Integer("cost_multiplier", 11).Unsigned().Default(1).Comment("扣费倍率")
		table.String("description", 500).Comment("配置说明").Nullable()
		table.Integer("status", 4).Default(1).Comment("状态：1启用，2禁用").Index()
		table.TableComment("API配额扣费配置表")
		table.Timestamps()
	}).Seed([]map[string]interface{}{
		// 备案相关API (1倍)
		{"api_path": "/api/v1/beian/company/:company_name", "method": "GET", "cost_multiplier": "1", "description": "企业名称备案企业查询", "status": "1"},
		{"api_path": "/api/v1/beian/domain/:domain", "method": "GET", "cost_multiplier": "1", "description": "域名备案域名查询", "status": "1"},
		{"api_path": "/api/v1/beian/icp/:icp", "method": "GET", "cost_multiplier": "1", "description": "ICP备案号备案ICP查询", "status": "1"},

		// 查子域名
		{"api_path": "/api/v1/chaziyu/domain/:domain", "method": "GET", "cost_multiplier": "1", "description": "查子域名查询", "status": "1"},

		// 线索扩展API (1倍)
		{"api_path": "/api/v1/clue/expand/cert", "method": "POST", "cost_multiplier": "1", "description": "线索扩展-证书", "status": "1"},
		{"api_path": "/api/v1/clue/expand/company", "method": "POST", "cost_multiplier": "1", "description": "线索扩展-企业", "status": "1"},
		{"api_path": "/api/v1/clue/expand/domain", "method": "POST", "cost_multiplier": "1", "description": "线索扩展-域名", "status": "1"},
		{"api_path": "/api/v1/clue/expand/icp", "method": "POST", "cost_multiplier": "1", "description": "线索扩展-ICP", "status": "1"},
		{"api_path": "/api/v1/clue/expand/ip", "method": "POST", "cost_multiplier": "1", "description": "线索扩展-IP", "status": "1"},
		{"api_path": "/api/v1/clue/expand/result", "method": "POST", "cost_multiplier": "1", "description": "线索扩展结果", "status": "1"},
		{"api_path": "/api/v1/clue/expand/subdomain", "method": "POST", "cost_multiplier": "1", "description": "线索扩展-子域名", "status": "1"},

		// 爬虫截图 (1倍)
		{"api_path": "/api/v1/crawler/screenshot", "method": "POST", "cost_multiplier": "1", "description": "网页截图", "status": "1"},

		// 数字资产API
		{"api_path": "/api/v1/digital/android_app", "method": "POST", "cost_multiplier": "1", "description": "安卓应用检测", "status": "1"},
		{"api_path": "/api/v1/digital/android_app/:task_id", "method": "GET", "cost_multiplier": "1", "description": "安卓应用任务查询", "status": "1"},
		{"api_path": "/api/v1/digital/android_app/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "安卓应用结果查询", "status": "1"},
		{"api_path": "/api/v1/digital/appstore/:company_name", "method": "GET", "cost_multiplier": "1", "description": "应用商店查询", "status": "1"},
		{"api_path": "/api/v1/digital/mini_app/task/", "method": "POST", "cost_multiplier": "1", "description": "小程序检测", "status": "1"},
		{"api_path": "/api/v1/digital/mini_app/task/:task_id", "method": "GET", "cost_multiplier": "1", "description": "小程序任务查询", "status": "1"},
		{"api_path": "/api/v1/digital/mini_app/task/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "小程序结果查询", "status": "1"},
		{"api_path": "/api/v1/digital/wechat/task/", "method": "POST", "cost_multiplier": "1", "description": "微信检测", "status": "1"},
		{"api_path": "/api/v1/digital/wechat/task/:task_id", "method": "GET", "cost_multiplier": "1", "description": "微信任务查询", "status": "1"},
		{"api_path": "/api/v1/digital/wechat/task/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "微信结果查询", "status": "1"},

		// DLP相关API
		{"api_path": "/api/v1/dlp/baidu/task", "method": "POST", "cost_multiplier": "1", "description": "DLP百度检测", "status": "1"},
		{"api_path": "/api/v1/dlp/baidu/task/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP百度任务查询", "status": "1"},
		{"api_path": "/api/v1/dlp/baidu/task/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP百度结果查询", "status": "1"},
		{"api_path": "/api/v1/dlp/dashengpan/task", "method": "POST", "cost_multiplier": "1", "description": "DLP大圣盘检测", "status": "1"},
		{"api_path": "/api/v1/dlp/dashengpan/task/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP大圣盘任务查询", "status": "1"},
		{"api_path": "/api/v1/dlp/dashengpan/task/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP大圣盘结果查询", "status": "1"},
		{"api_path": "/api/v1/dlp/doc88/task", "method": "POST", "cost_multiplier": "1", "description": "DLP道客88检测", "status": "1"},
		{"api_path": "/api/v1/dlp/doc88/task/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP道客88任务查询", "status": "1"},
		{"api_path": "/api/v1/dlp/doc88/task/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP道客88结果查询", "status": "1"},
		{"api_path": "/api/v1/dlp/douin/task", "method": "POST", "cost_multiplier": "1", "description": "豆丁文档检测", "status": "1"},
		{"api_path": "/api/v1/dlp/douin/task/:task_id", "method": "GET", "cost_multiplier": "1", "description": "豆丁文档检测任务查询", "status": "1"},
		{"api_path": "/api/v1/dlp/douin/task/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "豆丁文档检测结果查询", "status": "1"},
		{"api_path": "/api/v1/dlp/gitcode/task", "method": "POST", "cost_multiplier": "1", "description": "DLP GitCode检测", "status": "1"},
		{"api_path": "/api/v1/dlp/gitcode/task/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP GitCode任务查询", "status": "1"},
		{"api_path": "/api/v1/dlp/gitcode/task/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP GitCode结果查询", "status": "1"},
		{"api_path": "/api/v1/dlp/gitee/task", "method": "POST", "cost_multiplier": "1", "description": "DLP Gitee检测", "status": "1"},
		{"api_path": "/api/v1/dlp/gitee/task/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP Gitee任务查询", "status": "1"},
		{"api_path": "/api/v1/dlp/gitee/task/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP Gitee结果查询", "status": "1"},
		{"api_path": "/api/v1/dlp/github/task", "method": "POST", "cost_multiplier": "1", "description": "DLP GitHub检测", "status": "1"},
		{"api_path": "/api/v1/dlp/github/task/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP GitHub任务查询", "status": "1"},
		{"api_path": "/api/v1/dlp/github/task/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP GitHub结果查询", "status": "1"},
		{"api_path": "/api/v1/dlp/magicalsearch/task", "method": "POST", "cost_multiplier": "1", "description": "DLP魔法搜索检测", "status": "1"},
		{"api_path": "/api/v1/dlp/magicalsearch/task/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP魔法搜索任务查询", "status": "1"},
		{"api_path": "/api/v1/dlp/magicalsearch/task/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP魔法搜索结果查询", "status": "1"},
		{"api_path": "/api/v1/dlp/miaosou/task", "method": "POST", "cost_multiplier": "1", "description": "DLP秒搜检测", "status": "1"},
		{"api_path": "/api/v1/dlp/miaosou/task/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP秒搜任务查询", "status": "1"},
		{"api_path": "/api/v1/dlp/miaosou/task/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP秒搜结果查询", "status": "1"},
		{"api_path": "/api/v1/dlp/postman/task", "method": "POST", "cost_multiplier": "1", "description": "DLP Postman检测", "status": "1"},
		{"api_path": "/api/v1/dlp/postman/task/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP Postman任务查询", "status": "1"},
		{"api_path": "/api/v1/dlp/postman/task/result/:task_id", "method": "GET", "cost_multiplier": "1", "description": "DLP Postman结果查询", "status": "1"},

		// 其他工具API (1倍)
		{"api_path": "/api/v1/dnschecker/:domain", "method": "GET", "cost_multiplier": "1", "description": "DNS检查", "status": "1"},
		{"api_path": "/api/v1/domain_search", "method": "GET", "cost_multiplier": "1", "description": "URL_API数据搜索查询", "status": "1"},
		{"api_path": "/api/v1/domain_search", "method": "POST", "cost_multiplier": "1", "description": "URL_API数据搜索提交", "status": "1"},
		{"api_path": "/api/v1/domain_search/result", "method": "GET", "cost_multiplier": "1", "description": "URL_API数据搜索结果", "status": "1"},

		// FOFA相关API (1倍)
		{"api_path": "/api/v1/fofa/domain/update", "method": "POST", "cost_multiplier": "1", "description": "FOFA域名更新", "status": "1"},
		{"api_path": "/api/v1/fofa/hot/:count", "method": "GET", "cost_multiplier": "1", "description": "FOFA热门查询", "status": "1"},
		{"api_path": "/api/v1/fofa/puredns", "method": "GET", "cost_multiplier": "1", "description": "FOFA纯DNS查询", "status": "1"},
		{"api_path": "/api/v1/fofa/query", "method": "POST", "cost_multiplier": "1", "description": "FOFA查询", "status": "1"},
		{"api_path": "/api/v1/fofa/query_count", "method": "GET", "cost_multiplier": "1", "description": "FOFA查询计数", "status": "1"},

		// Hunter API (1倍)
		{"api_path": "/api/v1/hunter/query", "method": "POST", "cost_multiplier": "1", "description": "Hunter查询", "status": "1"},

		// IP138 API
		{"api_path": "/api/v1/ip138/ip", "method": "POST", "cost_multiplier": "1", "description": "IP138查询", "status": "1"},

		// 企查查API (1倍)
		{"api_path": "/api/v1/qcc/basic_detail/:search", "method": "GET", "cost_multiplier": "1", "description": "企查查基本详情", "status": "1"},
		{"api_path": "/api/v1/qcc/branch_list/:search", "method": "GET", "cost_multiplier": "1", "description": "企查查分支列表", "status": "1"},
		{"api_path": "/api/v1/qcc/company_search/:search", "method": "GET", "cost_multiplier": "1", "description": "企查查企业搜索", "status": "1"},
		{"api_path": "/api/v1/qcc/investment_through/:search", "method": "GET", "cost_multiplier": "1", "description": "企查查投资穿透", "status": "1"},

		// 子域名和Whois
		{"api_path": "/api/v1/subdomain/burst", "method": "POST", "cost_multiplier": "1", "description": "子域名爆破", "status": "1"},
		{"api_path": "/api/v1/whois/domain/:domain", "method": "GET", "cost_multiplier": "1", "description": "Whois域名查询", "status": "1"},

		// 默认兜底规则（优先级最低）
		{"api_path": "*", "method": "*", "cost_multiplier": "1", "description": "默认扣费倍率（兜底规则）", "status": "1"},
	}...)
}

func (t *CreateApiQuotaConfigTable) Down() error {
	return mysql.Schema.DropIfExists("api_quota_config")
}
