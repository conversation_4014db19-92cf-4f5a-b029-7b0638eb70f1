package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateScanCertTable())
// }

type ScanCertTable struct{}

func CreateScanCertTable() interfaces.Migration {
	return &ScanCertTable{}
}

func (t *ScanCertTable) Up() error {
	return mysql.Schema.Create("scan_cert", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("domain", 255).Comment("域名").Unique()
		table.String("domain_ex", 255).Comment("协议+域名").Nullable()
		table.String("issuer_cn", 255).Comment("IssuerCn").Nullable()
		table.String("subject_org", 255).Comment("subject_org").Nullable()
		table.String("not_before", 255).Comment("not_before").Nullable()
		table.String("issuer_cns", 255).Comment("issuer_cns").Nullable()
		table.LongText("cert").Comment("cert").Nullable()
		table.String("sig_alth", 255).Comment("sig_alth").Nullable()
		table.String("not_after", 255).Comment("not_after").Nullable()
		table.String("subject_cn", 255).Comment("subject_cn").Nullable()
		table.String("issuer_org", 255).Comment("issuer_org").Nullable()
		table.String("v", 255).Comment("v").Nullable()
		table.String("valid_type", 255).Comment("valid_type").Nullable()
		table.Boolean("is_valid").Comment("is_valid").Default(0)
		table.String("sn", 255).Comment("sn").Nullable()
		table.String("subject_key", 255).Comment("subject_key").Nullable()
		table.String("cert_date", 255).Comment("cert_date").Nullable()
		table.TableComment("证书扫描")
		table.Timestamps()
	})
}

func (t *ScanCertTable) Down() error {
	return mysql.Schema.DropIfExists("scan_cert")
}
