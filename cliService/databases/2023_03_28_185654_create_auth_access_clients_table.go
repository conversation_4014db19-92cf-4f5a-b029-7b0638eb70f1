package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateAuthAccessClientsTable())
// }

type AuthAccessClientsTable struct{}

func CreateAuthAccessClientsTable() interfaces.Migration {
	return &AuthAccessClientsTable{}
}

func (t *AuthAccessClientsTable) Up() error {
	return mysql.Schema.Create("auth_access_clients", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("company_name", 255).Default("").Comment("企业名称")
		table.String("domain", 512).Comment("授权域名").Nullable()
		table.BigInt("user_id", 22).Default(0).Nullable().Comment("用户ID").Index()
		table.Boolean("status").Comment("客户端状态:0/1 禁用/启用").Default(1)
		table.String("client_id", 512).Comment("客户端ID").Nullable().Index()
		table.String("secret", 512).Comment("SecretKey").Nullable().Index()
		table.Text("data").Comment("ClientInfo").Nullable()
		table.Boolean("type").Comment("客户端类型").Nullable()
		table.LongText("scope").Comment("scope").Nullable()
		table.String("desc", 512).Comment("客户端说明").Nullable()
		table.Integer("public", 1).Default(0).Comment("架构 1/x86  其余是arm架构")
		table.DateTime("expired_at").Comment("过期时间").Index()
		table.String("ip_whitelist", 1000).Comment("IP白名单").Nullable()
		table.TableComment("三方信息表")
		table.Timestamps()
	}).Seed([]map[string]interface{}{
		{"id": "1", "company_name": "本地化", "user_id": "1", "secret": "BBV", "client_id": "AAV", "scope": "*", "status": "1", "expired_at": "5023-12-13 10:45:24"},
	}...)
}

func (t *AuthAccessClientsTable) Down() error {
	return mysql.Schema.DropIfExists("auth_access_clients")
}
