package migrations

import (
	docin "micro-service/middleware/mysql/dataleak_docin"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateDataLeakDocinResultTable())
// }

type DataLeakDocinResultTable struct{}

func CreateDataLeakDocinResultTable() interfaces.Migration {
	return &DataLeakDocinResultTable{}
}

func (*DataLeakDocinResultTable) Up() error {
	return mysql.Schema.Create(docin.ResultTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务ID")
		table.String("doc_url", 500).Default("''").Comment("地址")
		table.String("doc_title", 2000).Default("''").Comment("标题")
		table.String("screenshot", 255).Default("截图地址")
		table.Index("task_id").IndexName("idx_task")
	})
}

func (*DataLeakDocinResultTable) Down() error {
	return mysql.Schema.DropIfExists(docin.ResultTableName)
}
