package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AlterIntelligenceHotPocTable struct{}

func CreateAlterIntelligenceHotPocTable() interfaces.Migration {
	return &AlterIntelligenceHotPocTable{}
}

func (t *AlterIntelligenceHotPocTable) Up() error {
	return mysql.Schema.Table("intelligence_hot_poc", func(table interfaces.Blueprint) {
		table.Text("es_query").Nullable().Comment("ES查询语句")
		table.String("es_index", 255).Nullable().Comment("ES查询索引")
		table.String("es_keywords", 255).Nullable().Comment("ES查询关键字")
	})
}

func (t *AlterIntelligenceHotPocTable) Down() error {
	return nil

}
