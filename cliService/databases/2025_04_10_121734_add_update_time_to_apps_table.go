package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AddUpdateTimeToAppsTable struct{}

func CreateAddUpdateTimeToAppsTable() interfaces.Migration {
	return &AddUpdateTimeToAppsTable{}
}

func (t *AddUpdateTimeToAppsTable) Up() error {
	return mysql.Schema.Table("apps", func(table interfaces.Blueprint) {
		table.DateTime("update_time").Nullable().Comment("第三方平台更新时间")
	})
}

func (t *AddUpdateTimeToAppsTable) Down() error {
	return nil
}
