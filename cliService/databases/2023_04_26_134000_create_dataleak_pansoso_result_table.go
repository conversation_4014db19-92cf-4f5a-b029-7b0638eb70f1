package migrations

import (
	pss "micro-service/middleware/mysql/dataleak_pansoso"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateDataLeakPansosoResultTable())
// }

type DataLeakPansosoResultTable struct{}

func CreateDataLeakPansosoResultTable() interfaces.Migration {
	return &DataLeakPansosoResultTable{}
}

func (*DataLeakPansosoResultTable) Up() error {
	return mysql.Schema.Create(pss.ResultTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务ID")
		table.String("file_name", 500).Default("''").Comment("文件名")
		table.String("origin_url", 255).Default("''").Comment("来源地址")
		table.String("file_url", 500).Default("''").Comment("地址")
		table.String("screenshot", 255).Default("截图地址")
		table.Index("task_id").IndexName("idx_task")
	})
}

func (*DataLeakPansosoResultTable) Down() error {
	return mysql.Schema.DropIfExists(pss.ResultTableName)
}
