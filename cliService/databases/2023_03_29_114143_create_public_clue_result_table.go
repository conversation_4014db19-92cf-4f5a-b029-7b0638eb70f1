package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreatePublicClueResultTable())
// }

type PublicClueResultTable struct{}

func CreatePublicClueResultTable() interfaces.Migration {
	return &PublicClueResultTable{}
}

func (t *PublicClueResultTable) Up() error {
	return mysql.Schema.Create("public_clue_result", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("clue_content", 255).Comment("线索内容").Unique()
		table.Boolean("clue_type").Comment("线索类型 1:domain, 2:icp 3:cert 4:icon")
		table.String("comment", 255).Comment("线索备注")
		table.String("company_name_cn", 255).Comment("线索的企业名称-中文").Nullable()
		table.String("company_name_en", 255).Comment("线索的企业名称-英文").Nullable()
		table.BigInt("hash", 22).Comment("logo hash值").Default(0)
		table.BigInt("img_path", 22).Comment("img id").Default(0)
		table.TableComment("公共线索库结果表")
		table.Timestamps()
	})
}

func (t *PublicClueResultTable) Down() error {
	return mysql.Schema.DropIfExists("public_clue_result")
}
