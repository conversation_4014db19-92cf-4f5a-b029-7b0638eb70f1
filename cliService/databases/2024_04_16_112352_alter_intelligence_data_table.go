package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AlterIntelligenceDataTableAddLocallink struct{}

func CreateAlterIntelligenceDataTableAddLocallink() interfaces.Migration {
	return &AlterIntelligenceDataTableAddLocallink{}
}

func (t *AlterIntelligenceDataTableAddLocallink) Up() error {
	return mysql.Schema.Table("intelligence_data", func(table interfaces.Blueprint) {
		table.String("local_link", 255).Comment("原始报告文件本地链接")
		table.String("masked_report_local_link", 255).Nullable().Comment("脱敏报告文件本地链接")
		table.Integer("masked_report_created_id", 64).Nullable().Comment("脱敏报告文件创建者ID")
		table.DateTime("masked_report_updated_time").Nullable().Comment("脱敏报告文件更新时间")
	})
}

func (t *AlterIntelligenceDataTableAddLocallink) Down() error {
	return nil

}
