package migrations

import (
	"github.com/panda843/go-migrate/config"
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

func init() {
	config.Migrations = append(config.Migrations, CreateAlterScanPocsTable())
}

type AlterScanPocsTable struct{}

func CreateAlterScanPocsTable() interfaces.Migration {
	return &AlterScanPocsTable{}
}

func (t *AlterScanPocsTable) Up() error {
	return mysql.Schema.Table("scan_pocs", func(table interfaces.Blueprint) {
		table.Integer("from", 4).Default(1).Comment("漏洞来源 1是系统 2是自定义")
	})
}

func (t *AlterScanPocsTable) Down() error {
	return nil
}
