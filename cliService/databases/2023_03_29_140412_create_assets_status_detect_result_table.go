package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateAssetsStatusDetectResultTable())
// }

type AssetsStatusDetectResultTable struct{}

func CreateAssetsStatusDetectResultTable() interfaces.Migration {
	return &AssetsStatusDetectResultTable{}
}

func (t *AssetsStatusDetectResultTable) Up() error {
	return mysql.Schema.Create("assets_status_detect_result", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("ip", 255).Comment("IP")
		table.BigInt("port", 22).Comment("PORT")
		table.Boolean("online_state").Comment("1:在线，2:离线")
		table.BigInt("task_id", 22).Unsigned().Comment("资产状态检测任务表id").Index()
		table.TableComment("资产状态检测结果表")
		table.Timestamps()
		table.DeletedAt(true)
	})
}

func (t *AssetsStatusDetectResultTable) Down() error {
	return mysql.Schema.DropIfExists("assets_status_detect_result")
}
