package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	postman "micro-service/middleware/mysql/dataleak_postman"
)

type DataleakPostmanRelationTable struct{}

func CreateDataleakPostmanRelationTable() interfaces.Migration {
	return &DataleakPostmanRelationTable{}
}

func (t *DataleakPostmanRelationTable) Up() error {
	return mysql.Schema.Create(postman.RelationTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.BigInt("task_id", 22).Comment("任务id")
		table.BigInt("result_id", 22).Comment("结果id")
		table.Index("task_id", "result_id").IndexName("idx_task_result")
		table.Timestamps()
	})
}

func (t *DataleakPostmanRelationTable) Down() error {
	return mysql.Schema.DropIfExists(postman.RelationTableName)
}
