package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	"micro-service/middleware/mysql/dataleak_pansoso"
)

type pansosoRelation struct{}

func CreatePansosoRelationTable() interfaces.Migration {
	return &pansosoRelation{}
}

func (t *pansosoRelation) Up() error {
	return mysql.Schema.Create(dataleak_pansoso.RelationTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务id")
		table.BigInt("result_id", 22).Comment("结果id")
		table.Index("task_id", "result_id").IndexName("idx_task_result")
		table.TableComment("盘搜搜-任务与结果关系表")
	})
}

func (t *pansosoRelation) Down() error {
	return mysql.Schema.DropIfExists(dataleak_pansoso.RelationTableName)
}
