package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AlterFofaCountTable struct{}

func CreateAlterFofaCountTable() interfaces.Migration {
	return &AlterFofaCountTable{}
}

func (t *AlterFofaCountTable) Up() error {
	return mysql.Schema.Table("fofa_count", func(table interfaces.Blueprint) {
		table.BigInt("org", 10).Comment("org")
	})

}

func (t *AlterFofaCountTable) Down() error {
	return nil
}
