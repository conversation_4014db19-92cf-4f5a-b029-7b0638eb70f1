package migrations

import (
	wp "micro-service/middleware/mysql/dataleak_56wangpan"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateData56wangpanTaskTable())
// }

type DataLeak56wangpanTaskTable struct{}

func CreateData56wangpanTaskTable() interfaces.Migration {
	return &DataLeak56wangpanTaskTable{}
}

func (*DataLeak56wangpanTaskTable) Up() error {
	return mysql.Schema.Create(wp.TaskTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("keyword", 255).Comment("关键词")
		table.String("keyword_hash", 255).Comment("关键词hash值")
		table.Integer("status", 1).Default(1).Comment("任务状态: 1-进行中, 2-完成")
		table.Decimal("progress", 10, 2).Default(0).Comment("任务进度")
		table.Index("keyword_hash").IndexName("idx_keyword_hash")
	})
}

func (*DataLeak56wangpanTaskTable) Down() error {
	return mysql.Schema.DropIfExists(wp.TaskTableName)
}
