package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateGeneralCluesTable())
// }

type GeneralCluesTable struct{}

func CreateGeneralCluesTable() interfaces.Migration {
	return &GeneralCluesTable{}
}

func (t *GeneralCluesTable) Up() error {
	return mysql.Schema.Create("general_clues", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Integer("type", 11).Comment("线索类型: 1/2/3/4/5/6 证书/ICP/LOGO/关键词/域名/IP").Index()
		table.String("company_name", 512).Comment("企业名称").Nullable().Index()
		table.String("content", 512).Comment("线索内容").Index()
		table.Integer("hash", 11).Comment("ICON HASH").Default(0)
		table.String("platform", 512).Comment("平台").Nullable()
		table.String("source", 512).Comment("来源").Nullable()
		table.Boolean("confirmed").Comment("用户已确认").Default(0)
		table.Boolean("cert_valid").Comment("证书是否有效").Default(0)
		table.TableComment("公共线索库")
		table.Timestamps()
	})
}

func (t *GeneralCluesTable) Down() error {
	return mysql.Schema.DropIfExists("general_clues")
}
