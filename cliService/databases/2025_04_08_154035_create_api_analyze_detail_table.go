package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type ApiAnalyzeDetailTable struct{}

func CreateApiAnalyzeDetailTable() interfaces.Migration {
	return &ApiAnalyzeDetailTable{}
}

func (t *ApiAnalyzeDetailTable) Up() error {
	return mysql.Schema.Create("api_analyze_detail", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.Integer("task_id", 22).Comment("任务ID")
		table.LongText("api_list").Comment("API列表")
		table.LongText("js_list").Comment("JS列表")
		table.LongText("external_url").Comment("外部URL列表")
		table.Index("task_id").IndexName("idx_task_id")
	})
}

func (t *ApiAnalyzeDetailTable) Down() error {
	return mysql.Schema.DropIfExists("api_analyze_detail")
}
