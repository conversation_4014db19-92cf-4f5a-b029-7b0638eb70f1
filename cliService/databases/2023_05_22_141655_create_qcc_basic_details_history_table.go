package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateQccBasicDetailsHistoryTable())
// }

type QccBasicDetailsHistoryTable struct{}

func CreateQccBasicDetailsHistoryTable() interfaces.Migration {
	return &QccBasicDetailsHistoryTable{}
}

func (t *QccBasicDetailsHistoryTable) Up() error {
	return mysql.Schema.Create("qcc_basic_details_history", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("keyword", 512).Index().Comment("关键字")
		table.String("status", 32).Comment("状态")
		table.String("message", 512).Comment("提示信息")
		table.String("order_number", 128).Comment("订单编号")
		table.Timestamps()
	})
}

func (t *QccBasicDetailsHistoryTable) Down() error {
	return mysql.Schema.DropIfExists("qcc_basic_details_history")
}
