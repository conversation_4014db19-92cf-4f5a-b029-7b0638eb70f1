package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type ZeroZoneMiniAppRelationTable struct{}

func CreateZeroZoneMiniAppRelationTable() interfaces.Migration {
	return &ZeroZoneMiniAppRelationTable{}
}

func (t *ZeroZoneMiniAppRelationTable) Up() error {
	return mysql.Schema.Create("mini_app_relation", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.Integer("mini_app_history_id", 22).Comment("任务id")
		table.Integer("mini_app_id", 22).Comment("小程序记录id")
	})
}

func (t *ZeroZoneMiniAppRelationTable) Down() error {
	return mysql.Schema.DropIfExists("mini_app_relation")
}
