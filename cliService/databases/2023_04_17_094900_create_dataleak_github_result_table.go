package migrations

import (
	"micro-service/middleware/mysql/dataleak_github"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateDataLeakGithubResultTable())
// }

type DataLeakGithubResultTable struct{}

func CreateDataLeakGithubResultTable() interfaces.Migration {
	return &DataLeakGithubResultTable{}
}

func (*DataLeakGithubResultTable) Up() error {
	return mysql.Schema.Create(dataleak_github.ResultTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务ID")
		table.String("repo_name", 1000).Nullable().Comment("仓库名称")
		table.Text("repo_url").Nullable().Comment("仓库地址")
		table.String("repo_desc", 2000).Nullable().Comment("仓库描述")
		table.Text("code_url").Nullable()
		table.Text("screen_shot").Nullable()
		table.String("sha", 255).Nullable().Comment("sha")
		table.Text("code_snippet").Comment("代码片段")
		table.String("language", 300).Nullable()
		table.Index("task_id").IndexName("idx_task")
	})
}

func (*DataLeakGithubResultTable) Down() error {
	return mysql.Schema.DropIfExists(dataleak_github.ResultTableName)
}
