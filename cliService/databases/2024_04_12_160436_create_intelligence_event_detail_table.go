package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type IntelligenceEventDetailTable struct{}

func CreateIntelligenceEventDetailTable() interfaces.Migration {
	return &IntelligenceEventDetailTable{}
}

func (t *IntelligenceEventDetailTable) Up() error {
	return mysql.Schema.Create("intelligence_event_detail", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.Integer("event_id", 22).Comment("事件ID")
		table.String("related_device", 255).Nullable().Comment("关联设备")
		table.String("vulnerability", 255).Nullable().Comment("漏洞名称")
		table.String("risk_level", 255).Nullable().Comment("风险级别")
		table.String("risk_type", 255).Nullable().Comment("风险类型")
		table.Boolean("is_vulnerable").Nullable().Comment("是否存在漏洞")
		table.DateTime("first_detected").Nullable().Comment("漏洞首次探测时间")
		table.DateTime("last_detected").Nullable().Comment("漏洞最后探测时间")
		table.String("ip_address", 255).Comment("IP地址")
		table.String("port", 255).Comment("端口")
		table.String("protocol", 255).Comment("协议")
		table.String("country", 255).Comment("国家")
		table.String("city", 255).Comment("城市")
		table.String("url", 255).Comment("网址")
		table.String("case", 255).Comment("所属案件")
		table.String("object", 255).Comment("所属对象")
		table.Boolean("is_cdn").Comment("是否为CDN")
		table.String("tags", 255).Comment("标签")
		table.String("os", 255).Comment("操作系统")
		table.String("status_code", 255).Comment("状态码")
		table.String("title", 500).Comment("标题")
		table.String("domain", 255).Comment("域名")
		table.String("certificate", 255).Comment("证书持有者")
		table.String("organization", 255).Comment("证书组织")
		table.String("institution", 255).Nullable().Comment("机构名称")
		table.String("component", 255).Comment("组件名称")
		table.String("category", 255).Comment("组件分类")
		table.String("icon", 255).Comment("icon")
		table.String("fingerprint", 255).Comment("网站指纹")
		table.String("asset_count", 255).Comment("风险资产数")
		table.String("vuln_asset_cnt", 255).Comment("漏洞资产数")
		table.DateTime("last_updated").Comment("最后更新时间")
		table.Comment("情报-事件专项详情表")
	})
}

func (t *IntelligenceEventDetailTable) Down() error {
	return mysql.Schema.DropIfExists("intelligence_event_detail")
}
