package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
	"micro-service/middleware/mysql/request_api_record"
)

type RquestApiRecordTable struct{}

func CreateRquestApiRecordTable() interfaces.Migration {
	return &RquestApiRecordTable{}
}

func (t *RquestApiRecordTable) Up() error {
	return mysql.Schema.Create(request_api_record.TableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("client_id", 200).Nullable().Default("''").Comment("客户端的id").Index()
		table.String("api_path", 500).Nullable().Default("''").Comment("请求的api路径").Index()
		table.String("method", 200).Nullable().Default("''").Comment("请求方法")
		table.MediumText("param").Nullable().Comment("请求的参数")
	})
}

func (t *RquestApiRecordTable) Down() error {
	return mysql.Schema.DropIfExists(request_api_record.TableName)
}
