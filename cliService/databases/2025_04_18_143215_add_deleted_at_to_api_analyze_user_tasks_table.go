package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AddDeletedAtToApiAnalyzeUserTasksTable struct{}

func CreateAddDeletedAtToApiAnalyzeUserTasksTable() interfaces.Migration {
	return &AddDeletedAtToApiAnalyzeUserTasksTable{}
}

func (t *AddDeletedAtToApiAnalyzeUserTasksTable) Up() error {
	return mysql.Schema.Table("api_analyze_user_tasks", func(table interfaces.Blueprint) {
		table.DateTime("deleted_at").Nullable().Comment("删除时间")
	})
}

func (t *AddDeletedAtToApiAnalyzeUserTasksTable) Down() error {
	return nil
}
