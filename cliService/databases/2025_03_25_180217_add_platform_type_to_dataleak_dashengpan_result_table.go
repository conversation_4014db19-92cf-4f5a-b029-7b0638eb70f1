package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AddPlatformTypeToDataleakDashengpanResultTable struct{}

func CreateAddPlatformTypeToDataleakDashengpanResultTable() interfaces.Migration {
	return &AddPlatformTypeToDataleakDashengpanResultTable{}
}

func (t *AddPlatformTypeToDataleakDashengpanResultTable) Up() error {
	return mysql.Schema.Table("dataleak_dashengpan_result", func(table interfaces.Blueprint) {
		table.Integer("platform_type", 1).Comment("网盘类型ID")
	})
}

func (t *AddPlatformTypeToDataleakDashengpanResultTable) Down() error {
	return mysql.Schema.Table("dataleak_dashengpan_result", func(table interfaces.Blueprint) {

	})
}
