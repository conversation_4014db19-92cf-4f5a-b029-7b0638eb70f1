package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateDomainBurstsTable())
// }

type DomainBurstsTable struct{}

func CreateDomainBurstsTable() interfaces.Migration {
	return &DomainBurstsTable{}
}

func (t *DomainBurstsTable) Up() error {
	return mysql.Schema.Create("domain_bursts", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("from_domain", 300).Comment("爆破目标域名").Index()
		table.String("domain", 300).Comment("爆破结果域名")
		table.String("company_name", 512).Comment("企业名称").Nullable().Index()
		table.DateTime("burst_time").Comment("爆破时间")
		table.TableComment("域名爆破结果总表")
		table.Timestamps()
	})
}

func (t *DomainBurstsTable) Down() error {
	return mysql.Schema.DropIfExists("domain_bursts")
}
