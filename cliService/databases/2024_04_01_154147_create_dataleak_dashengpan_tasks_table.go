package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
	dashengpan "micro-service/middleware/mysql/dataleak_dashengpan"
)

type DataleakDashengpanTasksTable struct{}

func CreateDataleakDashengpanTasksTable() interfaces.Migration {
	return &DataleakDashengpanTasksTable{}
}

func (t *DataleakDashengpanTasksTable) Up() error {
	return mysql.Schema.Create(dashengpan.TaskTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("keyword", 255).Comment("关键词")
		table.String("keyword_hash", 255).Comment("关键词hash值")
		table.Integer("status", 1).Default(1).Comment("任务状态: 1-进行中, 2-完成")
		table.Decimal("progress", 10, 2).Default(0).Comment("任务进度")
		table.Index("keyword_hash").IndexName("idx_keyword_hash")
	})
}

func (t *DataleakDashengpanTasksTable) Down() error {
	return mysql.Schema.DropIfExists(dashengpan.TaskTableName)
}
