package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateUrlStatusDetectTaskTable())
// }

type UrlStatusDetectTaskTable struct{}

func CreateUrlStatusDetectTaskTable() interfaces.Migration {
	return &UrlStatusDetectTaskTable{}
}

func (t *UrlStatusDetectTaskTable) Up() error {
	return mysql.Schema.Create("url_status_detect_task", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.BigInt("total", 22).Comment("总数据")
		table.BigInt("processed_data", 22).Comment("被处理过的数据")
		table.Boolean("progress_state").Comment("处理状态 1:进行中 2:完成")
		table.BigInt("online_assets", 22).Comment("在线url")
		table.BigInt("unonline_assets", 22).Comment("离线url")
		table.String("user_name", 255).Comment("发起人")
		table.BigInt("user_id", 22).Unsigned().Comment("用户id")
		table.String("file_path", 255).Comment("下载路径").Nullable()
		table.TableComment("url状态检测任务表")
		table.Timestamps()
		table.DeletedAt(true)
	})
}

func (t *UrlStatusDetectTaskTable) Down() error {
	return mysql.Schema.DropIfExists("url_status_detect_task")
}
