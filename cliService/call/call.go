package call

import (
	"bytes"
	"context"
	"encoding/json"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
	"os"
	"strings"
	"time"

	"micro-service/cliService/command"

	"github.com/urfave/cli/v2"
	"go-micro.dev/v4"
	"go-micro.dev/v4/client"
)

func init() {
	command.GetFCLI().Register(&cli.Command{
		Name:   "call",
		Usage:  "调用RPC服务,例: foradar_cli call foradar.crawler Crawler.Get '{\"url\":\"https://www.baidu.com\",\"method\":\"CURL:GET\"}'",
		Action: RunCall,
	})
}

// RunCall calls a service endpoint and prints its response. Exits on error.
func RunCall(ctx *cli.Context) error {
	args := ctx.Args().Slice()
	if len(args) < 2 {
		return cli.ShowSubcommandHelp(ctx)
	}

	service := args[0]
	endpoint := args[1]
	req := strings.Join(args[2:], " ")
	if req == "" {
		req = `{}`
	}

	d := json.NewDecoder(strings.NewReader(req))
	d.UseNumber()

	var creq map[string]interface{}
	if err := d.Decode(&creq); err != nil {
		return err
	}

	srv := micro.NewService(
		micro.Registry(cfg.GetInstance().GetConsulReg()),
		micro.Address(utils.GetListenAddress(cfg.LoadCommon().Network)),
	)
	srv.Init()
	c := srv.Client()

	request := c.NewRequest(service, endpoint, creq, client.WithContentType("application/json"))
	var response map[string]interface{}

	if err := c.Call(context.Background(), request, &response, client.WithRequestTimeout(60*time.Second)); err != nil {
		return err
	}
	jsonStr, err := json.Marshal(response)
	if err != nil {
		panic(err)
	}
	var prettyJSON bytes.Buffer
	indentErr := json.Indent(&prettyJSON, jsonStr, "", "\t")
	if indentErr != nil {
		panic(err)
	}
	_, _ = os.Stdout.WriteString(prettyJSON.String())
	return nil
}
