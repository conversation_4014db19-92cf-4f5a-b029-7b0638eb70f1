package scop

import (
	"errors"
	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/urfave/cli/v2"
	"gorm.io/gorm"
	"micro-service/cliService/command"
	"micro-service/middleware/mysql/auth_access_scope"
	"os"
)

func init() {
	command.GetFCLI().Register(&cli.Command{
		Name:   "scope",
		Usage:  "Auth权限之Scope管理",
		Action: RunCall,
		Subcommands: []*cli.Command{
			{
				Name:    "list",
				Usage:   "Scope列表",
				Aliases: []string{"l"},
				Action:  RunList,
			},
			// {
			//	Name:   "add",
			//	Usage:  "添加Scope,例: foradar_cli scope add \"Scope名称\", \"Scope代码\", \"Scope状态:0/1 禁用/启用\", \"Scope说明\"",
			//	Action: RunAdd,
			// },
			// {
			//	Name:   "del",
			//	Usage:  "删除Scope,例: foradar_cli del Scope代码",
			//	Action: RunDel,
			// },
		},
	})
}

// RunCall calls a service endpoint and prints its response. Exits on error.
func RunCall(ctx *cli.Context) error {
	args := ctx.Args().Slice()
	if len(args) < 1 {
		return cli.ShowSubcommandHelp(ctx)
	}
	return nil
}

// RunList calls a service endpoint and prints its response. Exits on error.
func RunList(ctx *cli.Context) error {
	t := table.NewWriter()
	t.SetOutputMirror(os.Stdout)
	t.AppendHeader(table.Row{"序号", "Scope名称", "Scope代码", "Scope状态", "Scope说明"})
	scopes, _, err := auth_access_scope.NewAuthAccessScopeModel().List(0, 0)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			panic(err)
		}
	}
	if len(scopes) == 0 {
		rowConfigAutoMerge := table.RowConfig{AutoMerge: true}
		t.AppendRow([]interface{}{"暂无数据", "暂无数据", "暂无数据", "暂无数据", "暂无数据"}, rowConfigAutoMerge)
		t.AppendSeparator()
	} else {
		for _, scope := range scopes {
			statusText := ""
			if scope.Status == 0 {
				statusText = "禁用"
			} else {
				statusText = "正常"
			}
			t.AppendRow([]interface{}{scope.Id, scope.Name, scope.Scope, statusText, scope.Desc})
			t.AppendSeparator()
		}
	}
	t.Render()
	return nil
}

//
// // RunAdd RunList calls a service endpoint and prints its response. Exits on error.
// func RunAdd(ctx *cli.Context) error {
//	args := ctx.Args().Slice()
//	if len(args) < 3 {
//		return cli.ShowSubcommandHelp(ctx)
//	}
//	status, err := strconv.Atoi(args[2])
//	if err != nil {
//		panic(err)
//	}
//	var scope models.AuthAccessScope
//	err = models.GetAuthAccessScopeModel().Where(&models.AuthAccessScope{Scope: args[1]}).Find(&scope).Error
//	if err != nil {
//		panic(err)
//	} else if errors.Is(err, gorm.ErrRecordNotFound) {
//		err = models.GetAuthAccessScopeModel().DB.Attrs(&models.AuthAccessScope{
//			Name:   args[0],
//			Desc:   args[3],
//			Scope:  args[1],
//			Status: status,
//		}).FirstOrCreate(&models.AuthAccessScope{
//			Scope: args[1],
//		}).Error
//		if err != nil {
//			return err
//		}
//	} else {
//		scope.Name = args[0]
//		scope.Scope = args[1]
//		scope.Desc = args[3]
//		scope.Status = status
//		models.GetAuthAccessScopeModel().Where("scope", args[1]).Save(&scope)
//	}
//	return RunList(ctx)
// }
//
// // RunDel RunList calls a service endpoint and prints its response. Exits on error.
// func RunDel(ctx *cli.Context) error {
//	ctx.Args()
//	args := ctx.Args().Slice()
//	if len(args) < 1 {
//		return cli.ShowSubcommandHelp(ctx)
//	}
//	models.GetAuthAccessScopeModel().Where("scope", args[0]).Delete(&models.AuthAccessScope{})
//	return RunList(ctx)
// }
