package rule_engine

import (
	"github.com/urfave/cli/v2"
	"micro-service/cliService/command"
	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
	"time"
)

const (
	RiskRulesUserTableName = "risk_rules_user"
	RuleRelationTableName  = "builtin_rules_relation"
)

type RiskRulesUser struct {
	UserId           int        `gorm:"user_id"`
	CompanyId        int        `gorm:"company_id"`
	Type             int        `gorm:"type"`
	Status           int        `gorm:"status"`
	Node             string     `gorm:"node"`
	LastestFoundTime *time.Time `gorm:"lastest_found_time"`
	Enable           int        `gorm:"enable"`
}

type BuiltinRuleRelation struct {
	ID               uint       `gorm:"primaryKey"`
	CreatedAt        time.Time  `gorm:"created_at"`
	UpdatedAt        time.Time  `gorm:"updated_at"`
	UserId           int        `gorm:"user_id"`
	CompanyId        int        `gorm:"company_id"`
	RuleId           int        `gorm:"rule_id"`
	Status           int        `gorm:"status"`
	Node             string     `gorm:"node"`
	LastestFoundTime *time.Time `gorm:"lastest_found_time"`
	Enable           int        `gorm:"enable"`
}

func init() {
	command.GetFCLI().Register(&cli.Command{
		Name:   "rule_engine",
		Usage:  "将risk_rules_user表中数据迁移到builtin_rules_relation表中",
		Action: MigrateData,
	})
}

func MigrateData(ctx *cli.Context) error {
	db := mysql.GetInstance(cfg.LoadMysql())
	var RiskRuleUsers []RiskRulesUser
	RiskRuleUsers = nil
	//从risk_rule_user表中取出数据
	if err := db.Table(RiskRulesUserTableName).Where("type <= ?", 18).Scan(&RiskRuleUsers).Error; err != nil {
		return err
	}
	builtinRuleRelation := make([]BuiltinRuleRelation, len(RiskRuleUsers))
	// 将记录提取到map中
	for i := 0; i < len(RiskRuleUsers); i++ {
		builtinRuleRelation[i].RuleId = RiskRuleUsers[i].Type
		builtinRuleRelation[i].UserId = RiskRuleUsers[i].UserId
		builtinRuleRelation[i].CompanyId = RiskRuleUsers[i].CompanyId
		builtinRuleRelation[i].Status = RiskRuleUsers[i].Status
		builtinRuleRelation[i].Node = RiskRuleUsers[i].Node
		builtinRuleRelation[i].LastestFoundTime = RiskRuleUsers[i].LastestFoundTime
		builtinRuleRelation[i].Enable = RiskRuleUsers[i].Enable
	}
	// 截断表
	if err := db.Unscoped().Exec("TRUNCATE TABLE builtin_rules_relation").Error; err != nil {
		return err
	}
	ll := utils.ListSplit(builtinRuleRelation, 200)
	for x := range ll {
		if err := db.Table(RuleRelationTableName).Create(ll[x]).Error; err != nil {
			return err
		}
	}
	return nil
}
