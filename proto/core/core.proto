syntax = "proto3";

package core;

import "core/core_list.proto";

option go_package = "./proto;core";

service Core {
	// -------------------ICP
	// ICP查询
	rpc Icp(IcpRequest) returns (IcpResponse) {}
	rpc IcpByChild(IcpRequest) returns (IcpResponse) {}
	// 企业名称查询
	rpc CompanyName(IcpCompanyNameRequest) returns (IcpResponse) {}
	// 域名查询
	rpc Domain(IcpDomainRequest) returns (IcpResponse) {}
	// 查单独域名
	rpc DomainOnly(IcpDomainRequest) returns (IcpResponse) {}
	// 工信部ICP备案查询：小程序、应用、快应用
	rpc IcpAppByIcp(IcpAppRequest) returns (IcpAppResponse) {} // ICP备案号
	rpc IcpAppByCompanyName(IcpAppRequest) returns (IcpAppResponse) {} // 企业名称
	rpc IcpAppByAppName(IcpAppRequest) returns (IcpAppResponse) {} // 应用名称

	// ---------------------Digital
	// 数字资产-appstore
	rpc DigitalAppstore(DigitalCompanyNameRequest) returns (DigitalAppstoreResponse) {}
	// 数字资产-微信公众号
	rpc DigitalSougouWeChat(SougouRequest) returns (SougouResponse) {}
	//	数字资产总库任务模式-创建任务
	rpc DigitalWechatTaskUpsert(GitHubCodeRequest) returns (GitHubCodeRequest) {}
	//	数字资产总库任务模式-获取任务详情
	rpc DigitalWechatTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {}
	// 数字资产总库任务模式-获取任务结果
	rpc DigitalWechatTaskResult(GitHubCodeRequest) returns (DigitalWechatAccounts) {}
	// app总库列表
	rpc DigitalAppsList(DigitalAppsRequest) returns (DigitalAppsResponse) {}
	// app总库列表
	rpc DigitalWechatList(DigitalWechatRequest) returns (DigitalWechatResponse) {}
	// 数字资产-Android app任务模式-创建任务
	rpc DigitalAndroidAppTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {}
	// 数字资产-Android app任务模式-任务详情
	rpc DigitalAndroidAppTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {}
	// 数字资产-Android app任务模式-任务结果
	rpc DigitalAndroidAppTaskResult(GitHubCodeRequest) returns (DigitalAppstoreResponse) {}


	// ---------------------IP138
	// ip138查询域名
	rpc Ip138ByDomain(Ip138DomainRequest) returns (Ip138DomainResponse) {}
	// ip138
	rpc Ip138ByIp(Ip138IpRequest) returns (Ip138IpResponse) {}

	// Whois
	rpc Whois(WhoisDomainRequest) returns (WhoisResponse) {}
	// 子域名查询
	rpc Subdomain(SubdomainRequest) returns (SubdomainResponse) {}

	// ---------------------DLP数据泄露
	// Github Code
	rpc DlpGitHubByCode(GitHubCodeRequest) returns (GitHubCodeResponse)  {}
	// Gitee Repo
	rpc DlpGiteeByRepo(GitHubCodeRequest) returns (GitHubCodeResponse)  {}
	// 百度网盘 56网盘
	rpc Dlp56NetDisk(NetDiskRequest) returns (NetDiskResponse)  {}
	// 百度网盘 盘搜搜
	rpc DlpPanSoSoNetDisk(NetDiskRequest) returns (NetDiskResponse)  {}
	//  百度文库 Keyword
	rpc DlpBaiduLibrary(BaiduLibraryRequest) returns (BaiduLibraryResponse)  {}
	// 豆丁文档 Keyword
	rpc DlpDouin(BaiduLibraryRequest) returns (BaiduLibraryResponse)  {}
	// 语雀文档
	rpc DlpYuque(YuequeLibraryRequest) returns (YuequeLibraryResponse)  {}

	// 数据泄露总库模式
	// 数据泄露-GitHub
	rpc DlpGitHubTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc DlpGitHubTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc DlpGitHubTaskResult(GitHubCodeRequest) returns (GitHubCodeResultResponse) {} // 获取任务结果
	// 数据泄露-Gitee
	rpc DlpGiteeTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc DlpGiteeTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc DlpGiteeTaskResult(GitHubCodeRequest) returns (GitHubCodeResultResponse) {} // 获取任务结果
	// 数据泄露-百度文库
	rpc DlpBaiduLibraryTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc DlpBaiduLibraryTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc DlpBaiduLibraryTaskResult(GitHubCodeRequest) returns (BaiduLibraryResultResponse) {} // 获取任务结果
	// 数据泄露-豆丁
	rpc DlpDocinTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc DlpDocinTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc DlpDocinTaskResult(GitHubCodeRequest) returns (DocinResultResponse) {} // 获取任务结果
	// 数据泄露-56网盘
	rpc Dlp56WangpanTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc Dlp56WangpanTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc Dlp56WangpanTaskResult(GitHubCodeRequest) returns (Wangpan56ResultResponse) {} // 获取任务结果
	// 数据泄露-PanSoso
	rpc DlpPanSosoTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc DlpPanSosoTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc DlpPanSosoTaskResult(GitHubCodeRequest) returns (Wangpan56ResultResponse) {} // 获取任务结果
	// 数据泄露-道客巴巴
	rpc DlpDoc88TaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc DlpDoc88TaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc DlpDoc88TaskResult(GitHubCodeRequest) returns (Doc88ResultResponse) {} // 获取任务结果
	//数据泄露-奇妙搜索
	rpc DlpMagicalSearchTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {}// 创建任务
	rpc DlpMagicalSearchTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} //获取任务信息
	rpc DlpMagicalSearchTaskResult(GitHubCodeRequest) returns (Wangpan56ResultResponse) {} //获取任务结果
	// 数据泄露-Postman
	rpc DlpPostmanTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc DlpPostmanTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc DlpPostmanTaskResult(GitHubCodeRequest) returns (GitHubCodeResultResponse) {} // 获取任务结果
	// 数据泄露-秒搜网盘
	rpc DlpMiaosouTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc DlpMiaosouTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc DlpMiaosouTaskResult(GitHubCodeRequest) returns (Wangpan56ResultResponse) {} // 获取任务结果
	//数据泄露-大圣盘
	rpc DlpDashengpanTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {}// 创建任务
	rpc DlpDashengpanTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} //获取任务信息
	rpc DlpDashengpanTaskResult(GitHubCodeRequest) returns (Wangpan56ResultResponse) {} //获取任务结果


	// --------fofa
	// fofa查询
	rpc FofaQuery(FofaQueryRequest) returns (FofaQueryResponse) {}
	// fofa count查询
	rpc FofaQueryCount(FofaQueryCountRequest) returns (FofaQueryCountResponse) {}
	// fofa assets all count
	rpc FofaAllAssetsCount(FofaAllAssetsCountRequest) returns(FofaAllAssetsCountResponse){}
	// fofa host查询
	rpc FofaHost(FofaHostRequest) returns (FofaHostResponse) {}
	// 搜索引擎----子域名
	rpc EngineWebSite(EngineSearchRequest) returns (EngineSearchResponse) {}
	// Whois基本信息
	rpc WhoisBasic(WhoisDomainBasicRequest) returns (WhoisBasicResponse) {}
	// 热点数据
	rpc FofaQueryHot(FofaQueryHotRequest) returns (FofaQueryHotResponse) {}
	//域名解析
	rpc FofaPureDns(FofaPureDnsRequest) returns (FofaPureDnsResponse) {}
	//fofa的账号到期时间
	rpc FofaAccountInfo(Empty) returns (FofaAccountResponse) {}
	// 创建探测任务
	rpc CreateDetectionTask(FofaDetectionRequest) returns (FofaDetectionResponse) {}
	// 创建扫描任务
	rpc CreateScanTask(FofaScanTaskRequest) returns (FofaDetectionResponse) {}
	// 获取任务状态
	rpc GetTaskStatus(FofaTaskStatusRequest) returns (FofaTaskStatusResponse) {}
	// 获取任务结果
	rpc GetTaskResult(FofaTaskStatusRequest) returns (FofaTaskResultResponse) {}

	// --------------------企查查--------------------
	// 企查查分支机构信息
	rpc QCCBranchResult(QCCBranchRequest) returns(QCCBranchResponse){}
	// 企查查企业搜索
	rpc QCCNameSearch(QCCNameSearchRequest) returns(QCCNameSearchResponse) {}
	// 企业对外投资穿透
	rpc QCCInvestmentThrough(QCCInvestmentThroughRequest) returns(QCCInvestmentThroughResponse) {}
  // 企业工商照面
	rpc QCCGetBasicDetailsByName(QCCNameSearchRequest) returns(QCCGetBasicDetailsByNameResponse) {}
	// 企业工商照面模糊搜索
	rpc QCCGetBasicDetailSearch(QCCBasicDetailSearchRequest) returns(QCCBasicDetailSearchResponse) {}
	// 天眼查-微博
	rpc TYCWeiboAccountSearch(QCCNameSearchRequest) returns(TYCWeiboAccountsResponse) {}

	// ---------------------企业线索库
	// 控股公司数量统计
	rpc HoldingCompanyCount(HoldingCompanyCountRequest) returns (HoldingCompanyCountResponse) {}

	// 根据资产header或banner信息获取CVE漏洞编号
	rpc CVEVulnsByIPAssets(CVEVulnsByIPAssetsRequest) returns (CVEVulnsByIPAssetsResponse) {}

	// ---------------------资产总库
	// 推荐资产任务
	rpc AddRecommendTask(RecommendTaskRequest) returns (RecommendTaskResponse) {}
	// 推荐资产进度
	rpc GetRecommendProcess(RecommendProcessRequest) returns (RecommendProcessResponse) {}
	// 推荐资产结果
	rpc GetRecommendResult(RecommendResultRequest) returns (RecommendResultResponse) {}

	// ---------------------线索总库
	// 扩展任务列表
	rpc ExpandClueTaskList(ExpandClueTaskListRequest) returns (ExpandClueTaskListResponse) {}
	// 企业名称扩展线索
	rpc ExpandCompanyName(ExpandKeywordRequest) returns (ExpandClueResponse) {}
	// ICP扩展线索
	rpc ExpandIcp(ExpandKeywordRequest) returns (ExpandClueResponse) {}
	// 域名扩展线索
	rpc ExpandDomain(ExpandKeywordRequest) returns (ExpandClueResponse) {}
	// 子域名扩展线索
	rpc ExpandSubDomain(ExpandKeywordRequest) returns (ExpandClueResponse) {}
	// 关键字扩展线索
	rpc ExpandKeyword(ExpandKeywordRequest) returns (ExpandClueResponse) {}
	// IP扩展线索
	rpc ExpandIp(ExpandKeywordRequest) returns (ExpandClueResponse) {}
	// 证书扩展线索
	rpc ExpandCert(ExpandKeywordRequest) returns (ExpandClueResponse) {}
	// ICON扩展线索
	rpc ExpandIcon(ExpandIconRequest) returns (ExpandClueResponse) {}
	// 获取扩展结果
	rpc GetExpandResult(ExpandResultRequest) returns (ExpandResultResponse) {}
	// 获取线索列表
	rpc GetClueList(ClueListRequest) returns (ClueListResponse) {}
	// 更新线索信息
	rpc UpdateClueInfo(ClueInfo) returns (ClueInfo) {}
	// 根据关键字更新线索信息
	rpc UpdateByKeyword(UpdateClueByKeywordRequest) returns (ClueInfo) {}
	// 根据关键字更新线索信息
	rpc CreateClue(ClueInfo) returns (ClueInfo) {}
	// IP扩展线索
	rpc SearchByCompanyName(ExpandKeywordSearchRequest) returns (ExpandResultResponse) {}
	// 批量更新线索记录
	rpc CluesBatchUpdate(ClueListRequest) returns(Empty) {}
	rpc ClueFilterGroup(ClueListRequest) returns(ClueFilterGroupResponse) {}

	// --------------------CDN
	rpc CheckIsCdn(IsCdnRequest) returns (IsCdnResponse) {}

	// --------------------情报
	rpc InformationDomain(InformationDomainRequest) returns (InformationDomainResponse) {}
	// ------------------IP情报
	rpc InformationIp(InformationIpRequest) returns (InformationIpResponse) {}

	rpc IpDomainHistory(IpDomainHistoryRequest) returns (IpDomainHistoryResponse) {} // IP的域名历史信息

	// Engine Rules 引擎规则
	rpc EngineRuleList(EngineRuleListRequest) returns (EngineRuleListResponse) {} // 规则列表
	rpc EngineRuleCategory(EngineRuleCategoryRequest) returns (EngineRuleCategoryResponse) {} // 规则分类
	rpc EngineRuleCreate(EngineRuleCreateRequest) returns (Empty) {} // 新建规则
	rpc EngineRuleUpdate(EngineRuleUpdateRequest) returns (Empty) {} // 更新规则
	rpc EngineRuleUpdateStatus(EngineRuleListRequest) returns (Empty) {} // 更新规则状态
	rpc EngineRuleDelete(EngineRuleListRequest) returns (Empty) {} // 删除规则

	// 关键词管理
	rpc KeywordManageDataSync(KeywordManageDataSyncRequest) returns (KeywordManageDataSyncResponse) {} // 关键词管理数据同步

	// --------------------资产发现
	// 创建任务
	rpc DiscoverCreateTask(DiscoverCreateTaskRequest) returns (DiscoverCreateTaskResponse) {}
	// 任务进度
	rpc DiscoverTaskProcess(DiscoverTaskRequest) returns (DiscoverTaskProcessResponse) {}
	// 任务线索
	rpc DiscoverTaskClues(DiscoverTaskCluesRequest) returns (ClueListResponse) {}
	// 任务结果
	rpc DiscoverTaskResult(DiscoverTaskResultRequest) returns (DiscoverTaskResultResponse) {}


	// --------------------资产发现/集团公司
	// 创建任务
	rpc DiscoverCreateGroupTask(DiscoverCreateGroupTaskRequest) returns (DiscoverCreateTaskResponse) {}
	// 任务进度
	rpc DiscoverGroupTaskProcess(DiscoverGroupTaskRequest) returns (DiscoverTaskProcessResponse) {}
	// 任务线索
	rpc DiscoverGroupTaskClues(DiscoverGroupTaskCluesRequest) returns (ClueListResponse) {}
	// 任务结果
	rpc DiscoverGroupTaskResult(DiscoverGroupTaskResultRequest) returns (DiscoverTaskResultResponse) {}
	// 集团架构
	rpc DiscoverGroupTaskOrgs(DiscoverGroupTaskCluesRequest) returns (DiscoverGroupTaskOrgsResponse) {}

	// --------------------情报中心
	// 热点POC列表
	rpc IntelligenceHotPocList(IntelligenceHotPocListRequest) returns (IntelligenceHotPocListResponse) {}
	// 新建热点POC
	rpc IntelligenceHotPocCreate(IntelligenceHotPocCreateRequest) returns (Empty) {}
	// 更新热点POC
	rpc IntelligenceHotPocUpdate(IntelligenceHotPocUpdateRequest) returns (Empty) {}
	// 删除热点POC
	rpc IntelligenceHotPocDelete(IntelligenceHotPocDeleteRequest) returns (Empty) {}

	// 仿冒列表
	rpc IntelligenceFakeList(IntelligenceFakeListRequest) returns (IntelligenceFakeListResponse) {}
	// 新建仿冒
	rpc IntelligenceFakeCreate(IntelligenceFakeCreateRequest) returns (Empty) {}
	// 更新仿冒
	rpc IntelligenceFakeUpdate(IntelligenceFakeUpdateRequest) returns (Empty) {}
	// 删除仿冒
	rpc IntelligenceFakeDelete(IntelligenceFakeDeleteRequest) returns (Empty) {}

	// 威胁列表
	rpc IntelligenceThreatList(IntelligenceThreatListRequest) returns (IntelligenceThreatListResponse) {}
	// 新建威胁
	rpc IntelligenceThreatCreate(IntelligenceThreatCreateRequest) returns (Empty) {}
	// 更新威胁
	rpc IntelligenceThreatUpdate(IntelligenceThreatUpdateRequest) returns (Empty) {}
	// 删除威胁
	rpc IntelligenceThreatDelete(IntelligenceThreatDeleteRequest) returns (Empty) {}

	// 其他情报列表
	rpc IntelligenceOtherList(IntelligenceOtherListRequest) returns (IntelligenceOtherListResponse) {}
	// 新建其他情报
	rpc IntelligenceOtherCreate(IntelligenceOtherCreateRequest) returns (Empty) {}
	// 更新其他情报
	rpc IntelligenceOtherUpdate(IntelligenceOtherUpdateRequest) returns (Empty) {}
	// 删除其他情报
	rpc IntelligenceOtherDelete(IntelligenceOtherDeleteRequest) returns (Empty) {}

	// 公共线索库企业下拉接口
	rpc ClueCompanyDropList(ClueCompanyDropListRequest) returns (ClueCompanyDropListResponse);
}


message IntelligenceOtherDeleteRequest{
	repeated uint64 id = 13; // @tag form:"id" validate:"required,dive,gt=0" zh:"ID"
}

message IntelligenceOtherUpdateRequest {
	uint64 id = 1; // @tag form:"id" validate:"required" zh:"ID"
	string url = 3; // @tag form:"url" validate:"omitempty" zh:"url"
	string platform = 4; // @tag form:"platform" validate:"omitempty" zh:"平台"
	string keyword = 5; // @tag form:"keyword" validate:"omitempty" zh:"关键词"
	string poster = 6; // @tag form:"poster" validate:"omitempty" zh:"发帖人"
	string title = 7; // @tag form:"title" validate:"omitempty" zh:"标题"
	string sample = 8; // @tag form:"sample" validate:"omitempty" zh:"样本"
	string screenshot = 11; // @tag form:"screenshot" validate:"omitempty" zh:"截图"
	string article_id = 12; // @tag form:"article_id" validate:"omitempty" zh:"文章ID"
	string article_context = 13; // @tag form:"article_context" validate:"omitempty" zh:"文章内容"
	string article_created_at = 14; // @tag form:"article_created_at" validate:"omitempty" zh:"收录时间"
	string found_at = 15; // @tag form:"found_at" validate:"omitempty" zh:"发现时间"
	string company = 16; // @tag form:"company" validate:"omitempty" zh:"关联企业"
	uint32 is_public = 17; // @tag form:"is_public" validate:"omitempty" zh:"是否公开"
}

message IntelligenceOtherCreateRequest {
	string url = 3; // @tag form:"url" validate:"omitempty" zh:"url"
	string platform = 4; // @tag form:"platform" validate:"omitempty" zh:"平台"
	string keyword = 5; // @tag form:"keyword" validate:"omitempty" zh:"关键词"
	string poster = 6; // @tag form:"poster" validate:"omitempty" zh:"发帖人"
	string title = 7; // @tag form:"title" validate:"omitempty" zh:"标题"
	string sample = 8; // @tag form:"sample" validate:"omitempty" zh:"样本"
	string screenshot = 11; // @tag form:"screenshot" validate:"omitempty" zh:"截图"
	string article_id = 12; // @tag form:"article_id" validate:"omitempty" zh:"文章ID"
	string article_context = 13; // @tag form:"article_context" validate:"omitempty" zh:"文章内容"
	string article_created_at = 14; // @tag form:"article_created_at" validate:"omitempty" zh:"收录时间"
	string found_at = 15; // @tag form:"found_at" validate:"omitempty" zh:"发现时间"
	string company = 16; // @tag form:"company" validate:"omitempty" zh:"关联企业"
	uint32 is_public = 17; // @tag form:"is_public" validate:"omitempty" zh:"是否公开"
}

message IntelligenceOtherListRequest {
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	string url = 3; // @tag form:"url" validate:"omitempty" zh:"url"
	string platform = 4; // @tag form:"platform" validate:"omitempty" zh:"平台"
	string keyword = 5; // @tag form:"keyword" validate:"omitempty" zh:"关键词"
	string poster = 6; // @tag form:"poster" validate:"omitempty" zh:"发帖人"
	string title = 7; // @tag form:"title" validate:"omitempty" zh:"标题"
	string sample = 8; // @tag form:"sample" validate:"omitempty" zh:"样本"
	string screenshot = 11; // @tag form:"screenshot" validate:"omitempty" zh:"截图"
	string article_id = 12; // @tag form:"article_id" validate:"omitempty" zh:"文章ID"
	string article_context = 13; // @tag form:"article_context" validate:"omitempty" zh:"文章内容"
	string company = 16; // @tag form:"company" validate:"omitempty" zh:"关联企业"
	uint32 is_public = 17; // @tag form:"is_public" validate:"omitempty" zh:"是否公开"
	repeated string article_created_at = 14; // @tag form:"article_created_at" validate:"omitempty" zh:"收录时间"
	repeated string found_at = 15; // @tag form:"found_at" validate:"omitempty" zh:"发现时间"
	repeated string created_at = 9; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 10; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
}

message IntelligenceOther {
	uint64 id = 16;
	string url = 1;
	string platform = 2;
	string keyword = 3;
	string poster = 4;
	string title = 5;
	string sample = 6;
	string sample_file_name = 17;
	string screenshot = 7;
	string article_id = 8;
	string article_context = 9;
	string article_created_at = 10;
	string found_at = 11;
	string created_at = 12;
	string updated_at = 13;
	string company = 14;
	uint32 is_public = 15;
}

message IntelligenceOtherListResponse {
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	uint32 per_page = 4;
	repeated IntelligenceOther items = 5;
}
// ---------------------------------------------------

message IntelligenceThreatDeleteRequest{
	repeated uint64 id = 13; // @tag form:"id" validate:"required,dive,gt=0" zh:"ID"
}

message IntelligenceThreatUpdateRequest {
	uint64 id = 12; // @tag form:"id" validate:"required" zh:"ID"
	string url = 1; // @tag form:"url" validate:"omitempty" zh:"风险URL"
	string ip = 2; // @tag form:"ip" validate:"omitempty" zh:"IP"
	string country = 3; // @tag form:"country" validate:"omitempty" zh:"国家"
	string domain = 4; // @tag form:"domain" validate:"omitempty" zh:"域名"
	string type = 5; // @tag form:"type" validate:"omitempty" zh:"风险类型"
	string tags = 6; // @tag form:"tags" validate:"omitempty" zh:"标签"
	uint32 status = 8; // @tag form:"status" validate:"omitempty" zh:"是否在线"
	string source = 9; // @tag form:"source" validate:"omitempty" zh:"数据来源"
	uint32 hit = 10; // @tag form:"hit" validate:"omitempty" zh:"是否命中"
	string found_at = 11; // @tag form:"found_at" validate:"required" zh:"发现时间"
}

message IntelligenceThreatCreateRequest {
	string url = 1; // @tag form:"url" validate:"omitempty" zh:"风险URL"
	string ip = 2; // @tag form:"ip" validate:"omitempty" zh:"IP"
	string country = 3; // @tag form:"country" validate:"omitempty" zh:"国家"
	string domain = 4; // @tag form:"domain" validate:"omitempty" zh:"域名"
	string type = 5; // @tag form:"type" validate:"omitempty" zh:"风险类型"
	string tags = 6; // @tag form:"tags" validate:"omitempty" zh:"标签"
	uint32 status = 8; // @tag form:"status" validate:"omitempty" zh:"是否在线"
	string source = 9; // @tag form:"source" validate:"omitempty" zh:"数据来源"
	uint32 hit = 10; // @tag form:"hit" validate:"omitempty" zh:"是否命中"
	string found_at = 11; // @tag form:"found_at" validate:"required" zh:"发现时间"
}

message IntelligenceThreatListRequest {
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	string url = 3; // @tag form:"url" validate:"omitempty" zh:"风险URL"
	string ip = 4; // @tag form:"ip" validate:"omitempty" zh:"IP"
	string country = 5; // @tag form:"country" validate:"omitempty" zh:"国家"
	string domain = 6; // @tag form:"domain" validate:"omitempty" zh:"域名"
	string type = 7; // @tag form:"type" validate:"omitempty" zh:"风险类型"
	string tags = 8; // @tag form:"tags" validate:"omitempty" zh:"标签"
	uint32 status = 12; // @tag form:"status" validate:"omitempty" zh:"是否在线"
	string source = 13; // @tag form:"source" validate:"omitempty" zh:"数据来源"
	repeated string created_at = 9; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 10; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
	repeated string found_at = 15; // @tag form:"found_at" validate:"omitempty,dive" zh:"发现时间"
}

message IntelligenceThreat {
	uint64 id = 1;
	string url = 2;
	string ip = 3;
	string domain = 4;
	string type = 5;
	string country = 6;
	string tags = 7;
	uint32 status = 9;
	string source = 11;
	string found_at = 12;
	string created_at = 13;
	string updated_at = 14;
}

message IntelligenceThreatListResponse {
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	uint32 per_page = 4;
	repeated IntelligenceThreat items = 5;
}

message IntelligenceFakeDeleteRequest{
	repeated uint64 id = 13; // @tag form:"id" validate:"required,dive,gt=0" zh:"ID"
}

message IntelligenceFakeUpdateRequest {
	uint64 id = 10; // @tag form:"id" validate:"required" zh:"ID"
	string url = 1; // @tag form:"url" validate:"required" zh:"钓鱼URL"
	string ip = 2; // @tag form:"ip" validate:"required" zh:"IP"
	string country = 3; // @tag form:"country" validate:"required" zh:"国家"
	string target = 4; // @tag form:"target" validate:"required" zh:"仿冒目标"
	string title = 5; // @tag form:"title" validate:"required" zh:"标题"
	string cloud_name = 6; // @tag form:"cloud_name" validate:"required" zh:"云厂商"
	uint32 status = 7; // @tag form:"status" validate:"required" zh:"是否在线"
	string source = 8; // @tag form:"source" validate:"required" zh:"数据来源"
	string found_at = 9; // @tag form:"found_at" validate:"required" zh:"发现时间"
}

message IntelligenceFakeCreateRequest {
	string url = 1; // @tag form:"url" validate:"required" zh:"钓鱼URL"
	string ip = 2; // @tag form:"ip" validate:"required" zh:"IP"
	string country = 3; // @tag form:"country" validate:"required" zh:"国家"
	string target = 4; // @tag form:"target" validate:"required" zh:"仿冒目标"
	string title = 5; // @tag form:"title" validate:"required" zh:"标题"
	string cloud_name = 6; // @tag form:"cloud_name" validate:"required" zh:"云厂商"
	uint32 status = 7; // @tag form:"status" validate:"required" zh:"是否在线"
	string source = 8; // @tag form:"source" validate:"required" zh:"数据来源"
	string found_at = 9; // @tag form:"found_at" validate:"required" zh:"发现时间"
}

message IntelligenceFakeListRequest {
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	string url = 3; // @tag form:"url" validate:"omitempty" zh:"钓鱼URL"
	string ip = 4; // @tag form:"ip" validate:"omitempty" zh:"IP"
	string country = 5; // @tag form:"country" validate:"omitempty" zh:"国家"
	string target = 6; // @tag form:"target" validate:"omitempty" zh:"仿冒目标"
	string title = 10; // @tag form:"title" validate:"omitempty" zh:"标题"
	string cloud_name = 11; // @tag form:"cloud_name" validate:"omitempty" zh:"云厂商"
	uint32 status = 12; // @tag form:"status" validate:"omitempty" zh:"是否在线"
	string source = 13; // @tag form:"source" validate:"omitempty" zh:"数据来源"
	repeated string created_at = 7; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 8; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
	repeated string found_at = 9; // @tag form:"found_at" validate:"omitempty,dive" zh:"发现时间"
}

message IntelligenceFake {
	uint64 id = 1;
	string url = 2;
	string ip = 3;
	string title = 4;
	string country = 5;
	string target = 6;
	string cloud_name = 7;
	uint32 status = 8;
	string source = 9;
	string found_at = 10;
	string created_at = 13;
	string updated_at = 14;
}

message IntelligenceFakeListResponse {
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	uint32 per_page = 4;
	repeated IntelligenceFake items = 5;
}

message IntelligenceHotPocDeleteRequest{
	repeated uint64 id = 13; // @tag form:"id" validate:"required,dive,gt=0" zh:"ID"
}

message IntelligenceHotPocUpdateRequest {
	uint64 id = 13; // @tag form:"id" validate:"required" zh:"ID"
	string name = 1; // @tag form:"name" validate:"omitempty" zh:"漏洞名称"
	string cve = 2; // @tag form:"cve" validate:"omitempty" zh:"CVE"
	string cnnvd = 3; // @tag form:"cnnvd" validate:"omitempty" zh:"CNNVD"
	string risk_level = 4; // @tag form:"risk_level" validate:"omitempty" zh:"风险级别"
	string impact_range = 5; // @tag form:"impact_range" validate:"omitempty" zh:"影响范围"
	string impact_product = 6; // @tag form:"impact_product" validate:"omitempty" zh:"影响产品"
	string impact_version = 7; // @tag form:"impact_version" validate:"omitempty" zh:"影响版本"
	string solution = 8; // @tag form:"solution" validate:"omitempty" zh:"解决方案"
	string introduce = 9; // @tag form:"introduce" validate:"omitempty" zh:"漏洞介绍"
	string fofa_query = 10; // @tag form:"fofa_query" validate:"omitempty" zh:"FOFA查询语句"
	uint64 fofa_count = 11; // @tag form:"fofa_count" validate:"omitempty" zh:"影响资产数量"
	string found_at = 12; // @tag form:"found_at" validate:"omitempty" zh:"发现时间"
	repeated string tag=14; // @tag form:"tag" validate:"omitempty,dive" zh:"标签"
}

message IntelligenceHotPocCreateRequest {
	string name = 1; // @tag form:"name" validate:"required" zh:"漏洞名称"
	string cve = 2; // @tag form:"cve" validate:"required" zh:"CVE"
	string cnnvd = 3; // @tag form:"cnnvd" validate:"required" zh:"CNNVD"
	string risk_level = 4; // @tag form:"risk_level" validate:"required" zh:"风险级别"
	string impact_range = 5; // @tag form:"impact_range" validate:"required" zh:"影响范围"
	string impact_product = 6; // @tag form:"impact_product" validate:"required" zh:"影响产品"
	string impact_version = 7; // @tag form:"impact_version" validate:"required" zh:"影响版本"
	string solution = 8; // @tag form:"solution" validate:"required" zh:"解决方案"
	string introduce = 9; // @tag form:"introduce" validate:"required" zh:"漏洞介绍"
	string fofa_query = 10; // @tag form:"fofa_query" validate:"required" zh:"FOFA查询语句"
	uint64 fofa_count = 11; // @tag form:"fofa_count" validate:"required" zh:"影响资产数量"
	string found_at = 12; // @tag form:"found_at" validate:"required" zh:"发现时间"
	repeated string tag=13; // @tag form:"tag" validate:"omitempty,dive" zh:"标签"
}

message IntelligenceHotPocListRequest {
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	string name = 3; // @tag form:"name" validate:"omitempty" zh:"漏洞名称"
	string cve = 4; // @tag form:"cve" validate:"omitempty" zh:"CEV"
	string cnnvd = 5; // @tag form:"cnnvd" validate:"omitempty" zh:"CNNVD"
	string risk_level = 6; // @tag form:"risk_level" validate:"omitempty" zh:"风险级别"
	repeated string created_at = 7; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 8; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
	repeated string found_at = 9; // @tag form:"found_at" validate:"omitempty,dive" zh:"发现时间"
	repeated string tag=10; // @tag form:"tag" validate:"omitempty,dive" zh:"标签"
}

message IntelligenceHotPoc {
	uint64 id = 1;
	string name = 2;
	string cve = 3;
	string cnnvd = 4;
	string impact_range = 5;
	string impact_product = 6;
	string impact_version = 7;
	string solution = 8;
	string introduce = 9;
	string fofa_query = 10;
	uint64 fofa_count = 11;
	string risk_level = 12;
	string created_at = 13;
	string updated_at = 14;
	string found_at = 15;
	repeated string tag=22;
}

message IntelligenceHotPocListResponse {
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	uint32 per_page = 4;
	repeated IntelligenceHotPoc items = 5;
}

message DiscoverCreateTaskResponse{
	string company_name = 1;
}

// --------------------资产发现/集团公司
message DiscoverGroupTaskOrgsResponse {
	message resultUnit {
		string company_name = 1;
		string level = 2;
		string percent = 3;
		string should_capi = 4;
	}
	int64 total = 1;
	int64 current_page = 2;
	int64 per_page = 3;
	repeated resultUnit items = 4;
}
message DiscoverCreateGroupTaskRequest {
	string company_name = 1; // @tag form:"company_name" validate:"required" zh:"企业名称"
	bool is_domain = 5; // @tag form:"is_domain" validate:"omitempty,boolean"  zh:"是否是域名"
	uint32 model = 2; // @tag form:"model" validate:"required,oneof=1 2"  zh:"发现模式"
	float percent = 3; // @tag form:"percent" validate:"required,number,max=100"  zh:"股权比例"
	bool force = 4;// @tag validate:"omitempty,boolean" zh:"强制刷新"
}

message DiscoverGroupTaskRequest {
	string company_name = 1; // @tag form:"company_name" validate:"required,max=512"  zh:"企业名称"
	float percent = 3; // @tag form:"percent" validate:"required,number,max=100"  zh:"股权比例"
	uint32 model = 2; // @tag form:"model" validate:"required,oneof=1 2"  zh:"发现模式"
}

message DiscoverGroupTaskCluesRequest {
	string company_name = 1; // @tag form:"company_name" validate:"omitempty,max=512" zh:"企业名称"
	uint32 model = 2; // @tag form:"model" validate:"required,oneof=1 2"  zh:"发现模式"
	float percent = 3; // @tag form:"percent" validate:"required,number,max=100"  zh:"股权比例"
	int64 page = 4; // @tag form:"page" validate:"required,number" zh:"页数"
	int64 per_page = 5; // @tag form:"per_page" validate:"required,number,max=10000" zh:"条数"
	string keyword = 6; // @tag form:"keyword" validate:"omitempty,max=512" zh:"过滤关键字"
}

message DiscoverGroupTaskResultRequest {
	string company_name = 1; // @tag form:"company_name" validate:"required,max=512"  zh:"企业名称"
	uint32 model = 2; // @tag form:"model" validate:"required,oneof=1 2"  zh:"发现模式"
	float percent = 3; // @tag form:"percent" validate:"required,number,max=100"  zh:"股权比例"
	int64 page = 4; // @tag form:"page" validate:"required,number" zh:"页数"
	int64 per_page = 5; // @tag form:"per_page" validate:"required,number,max=10000" zh:"条数"
	repeated string fields = 6; // @tag form:"fields" validate:"omitempty,dive,oneof=id is_ipv6 ip port url protocol base_protocol title domain subdomain cert icp hash icon clues cert_raw certs_valid cloud_name is_cdn cname isp fid server product source_updated_at created_at updated_at source level company_name is_wildcard_domain" zh:"字段"
	repeated string level = 17;// @tag form:"level" json:"level" validate:"omitempty,dive,oneof=A B C D" zh:"资产级别"
	repeated uint64 clue_id = 18; // @tag form:"clue_id" validate:"omitempty,dive,number" zh:"线索Id"
	repeated string ip = 19;// @tag form:"ip" validate:"omitempty,dive,ip" zh:"IP"
	repeated uint32 port = 20;// @tag form:"port" validate:"omitempty,dive,number" zh:"Port"
	repeated string domain = 21;// @tag form:"domain" validate:"omitempty" zh:"域名"
	repeated string org = 22;// @tag form:"org" validate:"omitempty" zh:"子公司"
	string is_ipv6 = 23;// @tag form:"is_ipv6" validate:"omitempty,oneof=true false" zh:"IPV6"
	string is_wildcard_domain = 24;  // @tag form:"is_wildcard_domain" validate:"omitempty,oneof=true false" zh:"是否是泛解析域名"
}

// --------------------资产发现
message DiscoverCreateTaskRequest {
	string company_name = 1; // @tag form:"company_name" validate:"required" zh:"企业名称"
	uint32 model = 2; // @tag form:"model" validate:"required,oneof=1 2"  zh:"发现模式"
	bool force = 3;// @tag validate:"omitempty,boolean" zh:"强制刷新"
	bool is_domain = 4; // @tag form:"is_domain" validate:"omitempty,boolean"  zh:"是否是域名"
}

message DiscoverTaskRequest {
	string company_name = 1; // @tag form:"company_name" validate:"required,max=512"  zh:"企业名称"
	uint32 model = 2; // @tag form:"model" validate:"required,oneof=1 2"  zh:"发现模式"
}

message DiscoverTaskResultRequest {
	string company_name = 1; // @tag form:"company_name" validate:"required,max=512"  zh:"企业名称"
	uint32 model = 2; // @tag form:"model" validate:"required,oneof=1 2"  zh:"发现模式"
	int64 page = 3; // @tag form:"page" validate:"required,number" zh:"页数"
	int64 per_page = 4; // @tag form:"per_page" validate:"required,number,max=10000" zh:"条数"
	repeated string fields = 5; // @tag form:"fields" validate:"omitempty,dive,oneof=id is_ipv6 ip port url protocol base_protocol title domain subdomain cert icp hash icon clues cert_raw certs_valid cloud_name is_cdn cname isp fid server product source_updated_at created_at updated_at level source is_wildcard_domain" zh:"字段"
	repeated string level = 17;// @tag form:"level" json:"level" validate:"omitempty,dive,oneof=A B C D" zh:"资产级别"
	repeated uint64 clue_id = 18; // @tag form:"clue_id" validate:"omitempty,dive,number" zh:"线索ID"
	repeated string ip = 19;// @tag form:"ip" validate:"omitempty,dive,ip" zh:"IP"
	repeated uint32 port = 20;// @tag form:"port" validate:"omitempty,dive,number" zh:"Port"
	repeated string domain = 21;// @tag form:"domain" validate:"omitempty" zh:"域名"
	string is_ipv6 = 23;// @tag form:"is_ipv6" validate:"omitempty,oneof=true false" zh:"IPV6"
	string is_wildcard_domain = 24;  // @tag form:"is_wildcard_domain" validate:"omitempty,oneof=true false" zh:"是否是泛解析域名"
}

message DiscoverTaskCluesRequest {
	string company_name = 1; // @tag form:"company_name" validate:"omitempty,max=512" zh:"企业名称"
	uint32 model = 2; // @tag form:"model" validate:"required,oneof=1 2"  zh:"发现模式"
	int64 page = 3; // @tag form:"page" validate:"required,number" zh:"页数"
	int64 per_page = 4; // @tag form:"per_page" validate:"required,number,max=10000" zh:"条数"
	string keyword = 5; // @tag form:"keyword" validate:"omitempty,max=512" zh:"过滤关键字"
}

message DiscoverTaskProcessResponse {
	float process = 2;
}

// 线索信息
message DiscoverClue {
	uint64 id = 1;
	string content = 2;  // @tag validate:"required,max=512" zh:"线索"
	int64  hash = 3; // @tag validate:"omitempty" zh:"ICON HASH值"
	uint32 type = 4;// @tag validate:"required,number,oneof=1 2 3 4 5 6 7" zh:"线索类型"
	string company_name = 5; // @tag validate:"omitempty,max=512" zh:"企业名称"
	string update_at = 6;
	uint64 parentId = 7;
	DiscoverClue parent_clue = 8;
}

// 资产信息
message DiscoverAsset{
	string id = 1;
	repeated DiscoverClue clues = 2;
	string fid = 3;
	string cname = 4;
	string product = 5;
	string product_category = 6;
	string icon = 7;
	string server = 8;
	string as_number = 9;
	string as_org = 10;
	string country_name = 11;
	string region = 12;
	string city = 13;
	string source_updated_at = 14;
	string created_at = 15;
	string updated_at = 16;
	bool is_ipv6 = 17;
	string cert = 18;
	string title = 19;
	string cert_raw = 20;
	string protocol = 21;
	string icp = 22;
	string icon_hash = 24;
	string base_protocol  = 25;
	string ip = 26;
	string url = 27;
	bool  certs_valid = 28;
	string port = 29;
	string domain = 30;
	string subdomain = 31;
	string cloud_name = 32;
	repeated string company_name = 33;
	bool is_cdn = 34;
	string source = 35;
	string hash = 36;
	string isp = 37;
	string level = 38;
	repeated string Risk = 39;
	bool is_wildcard_domain = 40;
}

message DiscoverTaskResultResponse{
	int64 total = 1;
	int64 current_page = 2;
	int64 per_page = 3;
	repeated DiscoverAsset items = 4;
}


// ----------------------情报
message InformationDomainRequest {
	string domain = 1; // @tag form:"domain" validate:"required" zh:"域名"
}

message InformationDomainResponse {
	string domain = 1;
	repeated string subdomain = 2;
	string registry_time = 3;
	string registry_org = 4;
	string registry_company = 5;
	string contact_name = 6;
	string contact_mobile = 7;
	repeated string ip = 8;
	string root_domain = 9;
	string business_name = 10;
	string cert = 11;
	string icp_number = 12;
	string icp_audit_time = 13;
	string icp_type = 14;
	string icp_company = 15;
	repeated string category_first = 16;
	repeated string category_second = 17;
	repeated string category_company = 18;
	string icon = 19;
	repeated string risk = 20;
	repeated string cert_ip = 21;
}


// --------------------IP情报
message InformationIpRequest {
	string ip = 1; // @tag form:"ip" validate:"required" zh:"IP"
}

message InformationIpResponse {
	string ip = 1;
	repeated string hosts = 2;
	repeated RulePort rule_port = 3;
	repeated string host_name = 4;
	string as_organization = 5;
	repeated string icp_name = 6;
	repeated string category_first = 7;
	repeated string category_second = 8;
	repeated string category_company = 9;
	string city = 10;
	repeated string risk = 11;
	repeated string root_domain = 12;
}
message RulePort {
	uint32 port = 1;
	repeated string rules = 2;
}

// ----------------------CDN
message IsCdnRequest{
	repeated string hosts = 1;
}

message CdnList {
	 string host = 1;
	 bool is_cdn = 2;
}

message IsCdnResponse{
	repeated CdnList cdn_list = 1;
}

message Empty {}

// ----------------ICP
message BeiAn{
	string company_name = 1;
	string company_type = 2;
	string icp = 3;
	string website_url = 4;
	string audit_time = 5;
	uint32 status = 6;
}

message IcpDomainRequest {
	string domain = 1;// @tag form:"domain" validate:"omitempty" zh:"域名"
	bool get_equals = 2;// @tag form:"get_equals" validate:"omitempty,boolean" zh:"获取同备案信息"
	bool get_subdomain = 3;// @tag form:"get_subdomain" validate:"omitempty,boolean" zh:"获取子域名备案"
	bool force = 5; // @tag form:"force" validate:"omitempty,boolean" zh:"强制刷新"
}

message IcpRequest {
	string icp = 1;// @tag form:"icp" validate:"omitempty" zh:"ICP号"
	bool get_equals = 2;// @tag form:"get_equals" validate:"omitempty,boolean" zh:"获取同备案信息"
	bool force = 3; // @tag form:"force" validate:"omitempty,boolean" zh:"强制刷新"
}

message IcpResponse {
	BeiAn info = 1;
	repeated BeiAn equals = 2;
	bool ba = 3;
}

message IcpCompanyNameRequest {
	string company_name = 1;// @tag form:"company_name" validate:"omitempty" zh:"企业名称"
	bool get_equals = 2;// @tag form:"get_equals" validate:"omitempty,boolean" zh:"获取同备案信息"
	bool force = 3; // @tag form:"force" validate:"omitempty,boolean" zh:"强制刷新"
}

// ----------------DLP
message GitHubCodeRequest{
	repeated string keyword = 1;
	uint64 max_total = 2;
	uint64 force = 3; // 是否忽略7天更新, 1是0否
	uint64 task_id = 4; // 任务ID
}

message GitHubCode {
	string repo_url = 1;
	string repo_desc = 2;
	string repo_name = 3;
	string code_url = 4;
	string screenshot = 5;
	string sha = 6;
	string language = 7;
	string code_snippet = 8;
}

message GitHubCodeResponse{
	repeated GitHubCode result = 1;
}

message NetDiskRequest{
	repeated string keyword = 1;
	uint64 max_total = 2;
}

message NetDisk{
	string file_name = 1;
	string url = 2;
	string origin_url = 3;
	string screenshot = 4;
}

message NetDiskResponse{
	repeated NetDisk result = 1;
}

message GitHubTaskInfoResponse{
	int64 task_id = 1; // 任务ID
	repeated string keywords = 2;
	int64 status = 3; // 任务状态
	float progress = 4; // 任务当前进度
}

message GitHubCodeResultResponse{
	repeated GitHubCode items = 1;
}

message BaiduLibraryResultResponse {
	message item {
		uint64 id = 1;
		string address = 2;
		string title = 3;
		string url = 4;
		string screenshot = 5;
	}
	repeated item items = 1;
}

message DocinResultResponse {
	message item {
		uint64 id = 1;
		string title = 2;
		string url = 3;
		string screenshot = 4;
	}
	repeated item items = 1;
}

message Wangpan56ResultResponse {
	message item {
		uint64 id = 1;
		string origin_url = 2;
		string file_name = 3;
		string file_url = 4;
		string screenshot = 5;
	}
	repeated item items = 1;
}

message Doc88ResultResponse {
	message item {
		uint64 id = 1;
		string title = 2;
		string url = 3;
		string upload_date = 4;
	}
	repeated item items = 1;
}

// ------------------------Digital
message DigitalWechatResponse {
	int64 total = 1;
	int64 current_page = 2; //@tag form:"page" validate:"omitempty,number"
	uint32 page = 3; //@tag form:"page"  validate:"omitempty,number"
	uint32 per_page = 4; //@tag form:"per_page"  validate:"omitempty,number"
	repeated Wechat items = 5;
}
message Wechat {
	uint64 id = 1;
	string name  = 2;
	string account = 3;
	string qrcode = 4;
	string company_name = 5;
	string platform = 6;
	uint32 is_online = 7;
	string describe = 8;
	string created_at = 9;
	string updated_at = 10;
}

message DigitalWechatRequest {
	uint32 page = 1; //@tag form:"page" zh:"页数"
	uint32 per_page = 3; //@tag form:"per_page" zh:"条数"
	string name = 2; // @tag form:"name" validate:"omitempty" zh:"公众号名称"
	string account = 5; // @tag form:"category" validate:"omitempty" zh:"账号"
	string company_name = 6; // @tag form:"company_name" validate:"omitempty" zh:"企业名称"
	string platform = 7; // @tag form:"platform" validate:"omitempty" zh:"平台"
	int32 is_online = 10; // @tag form:"is_online" validate:"omitempty" zh:"是否在线"
	repeated string created_at = 11; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 12; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
	string describe = 13; // @tag form:"describe" validate:"omitempty" zh:"功能介绍"
}

message DigitalAppsRequest {
	uint32 page = 1; //@tag form:"page" zh:"页数"
	uint32 per_page = 3; //@tag form:"per_page" zh:"条数"
	string name = 2; // @tag form:"name" validate:"omitempty" zh:"APP名称"
	string category = 5; // @tag form:"category" validate:"omitempty" zh:"分类"
	string company_name = 6; // @tag form:"company_name" validate:"omitempty" zh:"企业名称"
	string platform = 7; // @tag form:"platform" validate:"omitempty" zh:"平台"
	string product = 8; // @tag form:"product" validate:"omitempty" zh:"产品"
	string area = 9; // @tag form:"area" validate:"omitempty" zh:"地区"
	int32 is_online = 10; // @tag form:"is_online" validate:"omitempty" zh:"是否在线"
	repeated string created_at = 11; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 12; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
	string developer = 13; // @tag form:"developer" validate:"omitempty" zh:"开发者"
}

message DigitalAppsResponse {
	int64 total = 1;
	int64 current_page = 2; //@tag form:"page" validate:"omitempty,number"
	uint32 page = 3; //@tag form:"page"  validate:"omitempty,number"
	uint32 per_page = 4; //@tag form:"per_page"  validate:"omitempty,number"
	repeated Appstore items = 5;
}

message Appstore{
	uint64 id = 1;
	string name = 2;
	string logo = 3;
	string url  = 4;
	string category = 5;
	string company_name = 6;
	string platform = 7;
	string product = 8;
	string area = 9;
	int32 is_online = 10;
	string created_at = 11;
	string updated_at = 12;
	string developer = 13;
}

message DigitalCompanyNameRequest {
	string company_name = 1;
}

message DigitalAppstoreResponse{
	repeated Appstore result = 1;
}

//  ------------------------IP138
message Ip138DomainRequest {
	repeated string domain = 1;
}

message Ip138IpRequest {
	repeated string ip = 1;
}

message Ip138DomainResponse {
	message Ip {
		string domain =  1;
		repeated string ips = 3;
	};
	repeated Ip list = 1;
}

message Ip138IpResponse {
	message Ip138Domain {
		string ip =  1;
		repeated ListDomain list = 2;
	}
	message ListDomain{
		string domain = 1;
		string time = 2;
	}
	repeated Ip138Domain list = 1;
}
// -----------------------
message Whois {
	string domain = 1;
	string idna = 2;
	string register = 3;
	string email = 4;
	string mobile = 5;
	repeated string ip = 6;
}

message WhoisResponse{
	repeated Whois whois = 1;
}

message CompanyNameRequest {
	string company_name = 1;
}

message WhoisDomainRequest {
	string domain = 1;
}

message SubdomainRequest{
	string domain=1;
}

message SubdomainResponse{
	repeated string subdomains =1;
}

message Sougou{
	string title = 1;
	string qrcode = 2;
	string owner = 3;
	string account = 4;
}

message SougouRequest {
	string keyword = 1;
}

message SougouResponse{
	repeated Sougou result = 1;
}

message DigitalWechatAccounts {
	message account {
		uint64 id = 1;
		string name = 2;
		string account = 3;
		string qr_code = 4;
		string company_name = 5;
		string platform = 6;
		string is_online = 7;
		string describe = 8;
	}
	repeated account items = 1;
}

message BaiduLibrary{
	string title = 1;
	string url = 2;
	string screenshot = 3;
}

message BaiduLibraryRequest{
	string keyword = 1;
}

message BaiduLibraryResponse{
	repeated BaiduLibrary result = 1;
}

message FofaQueryRequest{
	string qbase64 = 1;
	bool full = 2;
	repeated string field = 3;
	uint32 page = 4;
	uint32 size = 5;
	string date_after = 6;
}

message Asset{
	optional string ip = 1;
	optional string port = 2;
	optional string protocol = 3;
	optional string base_protocol = 4;
	optional string host = 5;
	optional string link = 6;
	optional string domain = 7;
	optional string title = 8;
	optional string icp = 9;
	optional string cert = 10;
	optional string isp = 11;
	optional string fid = 12;
	optional string lastupdatetime = 13;
	optional string icon = 14;
	optional string icon_hash = 15;
	optional string certs_valid = 16;
	optional string cloud_name = 17;
	optional string server = 18;
	optional string cname = 19;
	optional string version = 20;
	optional string product = 21;
	optional string product_category = 22;
	optional string as_number = 23;
	optional string as_organization = 24;
	optional string city = 25;
	optional string status_code = 26;
	optional string banner = 27;
	optional string header = 28;
	optional string longitude = 29;
	optional string latitude = 30;
	optional string region = 31;
	optional string country = 32;
}

message FofaQueryResponse{
	bool error = 1;
	optional uint32 size = 2;
	optional uint32 page = 3;
	repeated Asset sdata = 4;
}

message FofaQueryCountRequest{
	string qbase64 = 1;
	bool full = 2;
	bool can_get_limit_count = 3;
	string date_after = 4;
}

message FofaQueryCountResponse{
	bool error = 1;
	uint32 count = 2;
}

message FofaHostRequest{
	string host = 1;
	bool   hasDetail = 2;
}

message FofaDetectionRequest{
	repeated string targets = 1; // @tag validate:"required" zh:"探测目标"
}
message FofaScanTarget {
	repeated string ip = 1; // @tag validate:"required" zh:"扫描IP"
	string ports = 2; // @tag validate:"required" zh:"扫描端口"
}
message FofaScanTaskRequest{
	repeated FofaScanTarget targets = 1; // @tag validate:"required,dive" zh:"扫描目标"
}
message FofaTaskIdData {
	string id = 1;
}
message FofaDetectionResponse {
	int32 code = 1;
	string message = 2;
	FofaTaskIdData data  = 3;
}

message FofaTaskStatusRequest {
	string id = 1; // @tag form:"id" validate:"required" zh:"任务ID"
}

message FofaTaskResultResponse {
	bytes result = 1;
}
message FofaTaskStatus {
	int64 progress = 1; // 任务进度
	int64 target_num = 2; // 目标数量
	bool result_file = 3; // 结果文件是否也生成
	string state = 4; // 结果状态说明
}
message FofaTaskStatusResponse {
	int32 code = 1;
	string message = 2;
	FofaTaskStatus data = 3;
}

message FofaQueryHotRequest {
	string count = 1;
}

message FofaQueryHotResponse {
	repeated FofaQueryHot data = 1;
}

message FofaQueryHot {
	string title = 1;
	string query_string = 2;
	string code = 3;
}

message FofaPureDnsRequest {
	string domain = 1;//@tag form:"domain"
	uint32 page = 2;//@tag form:"page"
	uint32 size = 3;//@tag form:"size"
}

message PureDnsInfo{
	string host = 1;
	string ip = 2;
	string last_update_time = 3;
}

message FofaPureDnsResponse {
	bool error = 1;
	optional uint32 total = 2;
	optional uint32 page = 3;
	repeated	PureDnsInfo items = 4;
}

message FofaAccountResponse {
	bool error = 1;
	string expiration = 2; // 到期时间
	string email = 3; // 邮箱地址
	bool isvip = 4; // 是否是会员
	optional uint32 remain_api_data = 5; // 当月api请求剩余额度
}

message FofaHostResponse{
	bool   error = 1;
	optional string host = 2;
	optional string ip = 3;
	optional uint32 asn = 4;
	optional string org = 5;
	optional string country_name = 6;
	optional string country_code = 7;
	repeated PortInfo ports = 8;
	repeated string protocol = 9;
	repeated uint32 port = 10;
	repeated string category = 11;
	repeated string product = 12;
	optional string update_time = 13;
}

message PortInfo{
	message ProductInfo{
		string  product = 1;
		string  category = 2;
		uint32  level = 3;
		uint32  sort_hard_code = 4;
		string company = 5;
	}
	uint32   port = 1;
	string   update_time = 2;
	string   protocol = 3;
	repeated ProductInfo products = 4;
}


message FofaAllAssetsCountRequest {}

message FofaCount {
	int64 count = 1;
	string  time = 2;
}

message FofaAllAssetsCountResponse {
	repeated	FofaCount domain = 1;
	repeated	FofaCount icon = 2;
	repeated	FofaCount cert = 3;
	repeated 	FofaCount icp = 4;
}


message EngineSearchRequest{
	string domain = 1;
}

message EngineSearchResponse{
	repeated string subdomains =1;
}

message WhoisDomainBasicRequest {
	string domain = 1;
}

message WhoisBasicResponse{
	string sponsoring_registrar = 1;
	string registrant_name = 2;
	string registrant_mobile = 3;
	string registrant_email = 4;
	string registrant_org = 5;
	string registration_date = 6;
	string expiration_date = 7;
}

message ScanCertRequest {
	repeated string domain = 1;
}

message ScanCertResponse {
	repeated ScanCertData certs = 1;
}

message ScanCertData {
	string Domain = 1;
	string Domain_ex = 2;
	string Issuer_cn = 3;
	repeated string Subject_org = 4;
	string Not_before = 5;
	repeated string Issuer_cns = 6;
	string Raw = 7;
	string Sig_alth = 8;
	string Not_after = 9;
	string Subject_cn = 10;
	repeated string Issuer_org = 11;
	string V = 12;
	string Valid_type = 13;
	bool Is_valid = 14;
	string SN = 15;
	string Subject_key = 16;
	string CertDate = 17;
}

message QCCBranchRequest {
	string keyword = 1;
	string client_id = 2;
}

message QCCBranchResponse{
	message oper {
		string key_no = 1;
		string name = 2;
	}
	message branchInfo {
		string key_no = 1;
		string name = 2;
		string status = 3;
		oper oper_info = 4;
	}
	repeated branchInfo list = 1;
	int64 total = 4;
}

message HoldingCompanyCountRequest {
	repeated string name = 1;
}

message HoldingCompanyCountResponse {
	repeated	HoldingCompanyCount count = 1;
}

message HoldingCompanyCount {
	string name = 1;
	double percent = 2;
}

message QCCBasicDetailSearchRequest {
	string keyword = 1; // @tag form:"keyword" validate:"required" zh:"搜索关键字"
	string province_code = 2; // @tag form:"province_code" validate:"omitempty" zh:"省份"
	string city_code = 3; // @tag form:"city_code" validate:"omitempty,max=6" zh:"城市"
	uint64 per_page = 4; // @tag form:"per_page" validate:"omitempty,number,max=20" zh:"每页条数"
	uint64 page = 5; // @tag form:"page" validate:"omitempty,number" zh:"页数"
	bool force = 6; // @tag form:"force" zh:"强制刷新"
	string client_id = 7;
}

message QCCBasicDetailSearchInfo {
	string key_no = 1;
	string name = 2;
	string credit_code = 3;
	string start_date = 4;
	string oper_name = 5;
	string status = 6;
	string no = 7;
	string address = 8;
}

message QCCBasicDetailSearchResponse {
	repeated QCCBasicDetailSearchInfo items = 1;
	uint64 total = 2;
	uint64 current_page = 3;
	uint64 per_page = 4;
	string message = 5;
	string order_number = 6;
}

message TYCWeiboAccountsResponse {
	message item {
		string ico = 1;
		string name = 2;
		string href = 3;
		string info = 4;
		repeated string tags = 5;
	}
	repeated item items = 1;
	string client_id = 2;
}

message QCCNameSearchRequest {
	bool force = 1; // @tag form:"force"
	string search = 2; // @tag form:"search"
	string client_id = 3;
}

message QCCNameSearchResponse {
	message nameSearchUnit {
		string name = 1;
		string hit_reason = 2;
	}

	int64 verify_result = 1;
	string order_number = 2;
	repeated nameSearchUnit data = 3;
}

message QCCInvestmentThroughRequest {
	string search = 1;
	string client_id = 2;
	int32 force = 3;
}

message QCCInvestmentThroughResponse {
	message resultUnit {
		string name = 1;
		string level = 2;
		string percent = 3;
		string should_capi = 4;
		repeated resultUnit children_list = 5;
	}

	string order_number = 1;
	int64 verify_result = 2;
	repeated resultUnit list = 3;
}

message QCCRevokeInfo {
	string cancel_date = 1;
	string cancel_reason = 2;
	string revoke_date = 3;
	string revoke_reason = 4;
}

message QCCBasicDetail {
	string key_no = 1;
	string name = 2;
	string no = 3;
	string belong_org = 4;
	string oper_id = 5;
	string oper_name = 6;
	string start_date = 7;
	string end_date = 8;
	string status = 9;
	string province = 10;
	string updated_date = 11;
	string credit_code = 12;
	string regist_capi = 13;
	string econ_kind = 14;
	string address = 15;
	string scope = 16;
	string term_start = 17;
	string team_end = 18;
	string check_date = 19;
	string org_no = 20;
	string is_on_stock = 21;
	string stock_number = 22;
	string stock_type = 23;
	repeated QCCOriginalName original_name = 24;
	string image_url = 25;
	string ent_type =  26;
	string rec_cap = 27;
	QCCRevokeInfo revoke_info = 28;
	QCCArea area = 29;
	string area_code = 30;
}

message QCCOriginalName {
	string name = 1;
	string change_date = 2;
}

message QCCArea {
	string province = 1;
	string city = 2;
	string county = 3;
}

message QCCGetBasicDetailsByNameResponse {
	QCCBasicDetail result = 1;
	string status = 2;
	string message = 3;
	string order_number = 4;
}

message YuequeLibraryRequest {
	string keyword = 1;
	int64 page = 2;
}

message	YuequeLibraryResponse {
	int64 total = 1;
	repeated YuequeLibrary data = 2;
}

message YuequeLibrary {
	string name =1;
	string user_id = 2;
	string url = 3;
	string type = 4;
}

message CVEVulnsByIPAssetsRequest {
	message CVEVulnsByIPAssetsUnit {
		string banner = 1; // banner or header
		string protocol = 2; // 协议
		int64 port = 3; // 端口
	}
	repeated CVEVulnsByIPAssetsUnit list = 1;
}

message CVEVulnsByIPAssetsResponse {
	message CVEVulnInfo {
		string cve_id = 1; // CVE编号
		string name = 2; // 漏洞名称
		string cvss_version = 3; // cvss版本: 如果存在3.x版本则取3.x版本，否则取2.x版本数据
		string level = 4; // 漏洞等级
		float score = 5; // 得分
		string description = 6; // 漏洞描述
		string published_date = 7; // 发布日期
		string ip = 8; // 资产IP
		string port = 9; // 资产端口
	}
	
	repeated CVEVulnInfo list = 1;
}

// --------------------------------------线索总库
// 线索信息
message CloudClue {
	string content = 2;  // @tag validate:"required,max=512" zh:"线索"
	int64  hash = 3; // @tag validate:"omitempty" zh:"ICON HASH值"
	uint32 type = 4;// @tag validate:"required,number,oneof=1 2 3 4 5 6 7" zh:"线索类型"
	string company_name = 5; // @tag validate:"omitempty,max=512" zh:"企业名称"
	string update_at = 6;
}
// 资产信息
message cloudAsset{
	string id = 1;
	repeated CloudClue clues = 2;
	string fid = 3;
	string cname = 4;
	string product = 5;
	string product_category = 6;
	string version = 7;
	string server = 8;
	string as_number = 9;
	string as_org = 10;
	string country_name = 11;
	string region = 12;
	string city = 13;
	string source_updated_at = 14;
	string created_at = 15;
	string updated_at = 16;
	bool is_ipv6 = 17;
	string cert = 18;
	string title = 19;
	string protocol = 21;
	string icp = 22;
	string icon = 23;
	string icon_hash = 24;
	string base_protocol  = 25;
	string ip = 26;
	string url = 27;
	bool  certs_valid = 28;
	string port = 29;
	string domain = 30;
	string subdomain = 31;
	string cloud_name = 32;
	string company_name = 33;
	string cert_raw = 34;
	string source = 35;
	repeated uint64 task_id = 36;
	string level = 37;
	repeated string risk = 38;
	int64 hash = 39;
	bool is_wildcard_domain = 40;
}

// 添加推荐资产任务
message RecommendTaskRequest {
	repeated CloudClue clue = 1; // @tag validate:"required,dive" zh:"线索"
	bool force = 2;
}
// 添加推荐资产任务返回
message RecommendTaskResponse{
	uint64 task_id = 1;
}
// 获取资产推荐任务进度
message RecommendProcessRequest{
	uint64 task_id = 1; // @tag validate:"required,number" zh:"任务ID"
}
// 线索信息
message clueProcessInfo {
	uint64 id = 1;
	string content = 2;
	int64  hash = 3;
	uint32 type = 4;
	string company_name = 5;
	uint64 process = 6;
}
// 返回资产推荐任务进度
message RecommendProcessResponse{
	uint64 task_id = 1;
	uint64 process = 2;
	repeated clueProcessInfo clues = 3;
}
// 获取资产推荐任务结果
message RecommendResultRequest{
	repeated uint64 task_id = 1; // @tag form:"task_id" validate:"required,dive,number" zh:"任务ID"
	int64 page = 14; // @tag form:"page" validate:"omitempty,number" zh:"页数"
	int64 per_page = 15; // @tag form:"per_page" validate:"omitempty,number" zh:"条数"
	repeated string fields = 16; // @tag form:"fields" validate:"omitempty,dive,oneof=id is_ipv6 ip port url protocol base_protocol title domain subdomain cert icp hash icon clues cert_raw certs_valid cloud_name is_cdn cname isp fid server product source_updated_at created_at updated_at level source is_wildcard_domain" zh:"字段"
	repeated string level = 17;// @tag form:"level" json:"level" validate:"omitempty,dive,oneof=A B C D" zh:"资产级别"
	repeated string clue_hash = 18; // @tag form:"clue_hash" validate:"omitempty" zh:"线索HASH"
	repeated string ip = 19;// @tag form:"ip" validate:"omitempty,dive,ip" zh:"IP"
	repeated uint32 port = 20;// @tag form:"port" validate:"omitempty,dive,number" zh:"Port"
	repeated string domain = 21;// @tag form:"domain" validate:"omitempty" zh:"域名"
	string is_wildcard_domain = 24;  // @tag form:"is_wildcard_domain" validate:"omitempty,oneof=true false" zh:"是否是泛解析域名"
	string is_ipv6 = 23;// @tag form:"is_ipv6" validate:"omitempty,oneof=true false" zh:"IPV6"
}
// 返回资产推荐任务结果
message RecommendResultResponse{
	int64 total = 1;
	int64 current_page = 2;
	int64 per_page = 3;
	repeated cloudAsset items = 4;
}

// -----------------------------------线索总库
message ExpandClueTaskListRequest {
	string keyword = 1; // @tag form:"keyword" validate:"omitempty" zh:"线索内容"
	uint32 type = 2; // @tag form:"type" validate:"omitempty,number" zh:"线索类型"
	int32 process = 3; // @tag form:"process" validate:"omitempty,number" zh:"进度"
	repeated string created_at = 4; // @tag form:"created_at" validate:"omitempty,dive" zh:"开始执行时间"
	repeated string updated_at = 5; // @tag form:"updated_at" validate:"omitempty,dive" zh:"执行结束时间"
	uint32 page = 6;//@tag validate:"required,number" form:"page" zh:"页数"
	uint32 per_page = 7;//@tag validate:"required,number" form:"per_page" zh:"条数"
}
message ExpandClueTaskInfo {
	uint64 id = 1;
	string keyword = 2;
	uint32 type = 3;
	uint32 process = 4;
	string created_at = 5;
	string updated_at = 6;
}
message ExpandClueTaskListResponse {
	int64 total = 1;
	int64 current_page = 2;
	int64 per_page = 3;
	repeated ExpandClueTaskInfo items = 4;
}

message ClueListRequest {
	uint32 type = 1; // 线索类型
	string company_name = 2; // @tag form:"company_name" validate:"omitempty,max=512" zh:"线索类型"
	string content =  3; // @tag form:"content" validate:"omitempty,min=1,max=512" zh:"线索内容"
	int64 hash = 4; // @tag form:"hash" validate:"omitempty,number" zh:"icon hash"
	repeated string platform = 5; // @tag form:"platform" validate:"omitempty,max=512" zh:"平台"
	string source = 6; // @tag form:"search" validate:"omitempty,max=512" zh:"线索来源"
	uint32 cert_valid = 7; // @tag form:"cert_valid" validate:"omitempty,oneof=0 1" zh:"证书有效性"
	string confirmed = 8; // @tag form:"confirmed" validate:"omitempty,oneof=0 1" zh:"用户确认"
	string created_at_before =  10; // @tag form:"created_at_before" validate:"omitempty,max=20" zh:"创建时间"
	string created_at_after =  11; // @tag form:"created_at_after" validate:"omitempty,max=20" zh:"创建时间"
	repeated string created_at = 12; // @tag validate:"omitempty,dive,required,datetime=2006-01-02" zh:"创建时间"
	string updated_at_before = 13; // @tag form:"updated_at_before" validate:"omitempty,max=20" zh:"更新时间"
	string updated_at_after = 14; // @tag form:"updated_at_after" validate:"omitempty,max=20" zh:"更新时间"
	repeated string updated_at = 15; // @tag validate:"omitempty,dive,required,datetime=2006-01-02" zh:"更新时间"
	int64 page = 16; // @tag form:"page" validate:"omitempty,number" zh:"页数"
	int64 per_page = 17; // @tag form:"per_page" validate:"omitempty,number" zh:"条数"
	string keyword = 18; // @tag form:"keyword" validate:"omitempty,max=512" zh:"过滤关键字"
	repeated uint64 ids = 19; // @tag validate:"omitempty,dive,required" zh:"线索"
	string update_company_name = 20;
	string search = 21; // @tag form:"search"
}

message ClueListResponse {
	int64 total = 1;
	int64 current_page = 2;
	int64 per_page = 3;
	repeated ClueInfo items = 4;
}

message ClueFilterGroupResponse {
	repeated string platform = 1;
	repeated string source = 2;
}

message ClueInfo {
	uint64 id = 1; // @tag validate:"omitempty,number" zh:"线索ID"
	uint32 type = 2; // @tag validate:"omitempty,number,oneof=1 2 3 4 5 6 7" zh:"线索类型"
	string company_name = 3; // @tag validate:"omitempty,max=512" zh:"企业名称"
	string content =  4; // @tag validate:"omitempty,min=1,max=512" zh:"线索内容"
	int64 hash = 5; // @tag validate:"omitempty,number" zh:"图标HASH"
	string  platform = 6; // @tag validate:"omitempty,max=512" zh:"平台"
	string source = 7; // @tag validate:"omitempty,max=512" zh:"线索来源"
	uint32 cert_valid = 8; // @tag validate:"omitempty,oneof=0 1" zh:"证书有效性"
	int32 confirmed = 9; // @tag validate:"omitempty,oneof=0 1" zh:"用户确认"
	string created_at =  10;
	string updated_at = 11;
}

message ExpandKeywordRequest{
	string company_name = 1; // @tag validate:"omitempty,max=512" zh:"企业名称"
	string source = 2; // @tag validate:"omitempty,max=512" zh:"线索来源"
	string keyword = 3; // @tag validate:"required,min=1,max=512" zh:"关键线索"
	uint32 cert_valid = 4; // @tag validate:"omitempty,oneof=0 1" zh:"证书有效性"
	bool force = 5;// @tag validate:"omitempty,boolean" zh:"强制刷新"
}

message ExpandKeywordSearchRequest{
	repeated string company_name = 1; // @tag form:"v" validate:"required,dive,max=512" zh:"企业名称"
}

message ExpandIconRequest{
	string company_name = 1; // @tag form:"company_name" validate:"omitempty,max=512" zh:"企业名称"
	string source = 2; // @tag form:"source" validate:"omitempty,max=512" zh:"线索来源"
	string keyword = 3; // @tag form:"keyword" validate:"required,max=1048576" zh:"关键线索"
	int64 hash = 4; // @tag form:"hash" validate:"omitempty,number" zh:"图标HASH"
	bool force = 5; // @tag form:"force" validate:"omitempty,boolean" zh:"强制刷新"
}

message ExpandResultResponse{
	uint64 process = 1;
	repeated ClueInfo items = 2;
}

message UpdateClueByKeywordRequest{
	uint32 type = 1; // @tag validate:"number,oneof=1 2 3 4 5 6 7" zh:"线索类型"
	string keyword = 2; // @tag validate:"required,min=1,max=512" zh:"线索内容"
	int64 hash = 3; // @tag validate:"omitempty,number" zh:"图标HASH"
	ClueInfo clue = 4; // @tag validate:"required" zh:"更新线索信息"
}

message ExpandClueResponse{
	uint64 task_id = 1;
}

message ExpandResultRequest{
	uint64 task_id = 1; // @tag validate:"required,number" zh:"线索ID"
}

message EngineRuleListRequest {
	uint64 user_id = 1;
	int64 page = 2; // @tag form:"page" validate:"number" zh:"页数"
	int64 per_page = 3; // @tag form:"per_page" validate:"number" zh:"条数"
	int64 operate_company_id = 4; // @tag form:"operate_company_id"
	string keyword = 5; // 关键字检索 @tag form:"keyword"
	string event_type = 6; // 风险规则分类 @tag form:"event_type"
	string  status = 7 ; // 启用禁用 @tag form:"status"
	repeated string updated_at = 8; // 更新时间 @tag form:"updated_at"
	repeated uint64 ids = 9;
	int64 is_order = 10;  // 排序 @tag form:"is_order"
	uint64 identity = 11;	// 身份
	int64 set_status = 12; // @tag validate:"omitempty,number,oneof=1 2" zh:"设置状态"
}

message EngineRuleItem {
	uint64 id = 1;
	string name = 2; // 规则名称
	string content = 3; // 规则内容
	string event_desc = 4; // 事件描述
	string event_impact = 5; // 事件影响
	string event_solution = 6; // 事件解决方案
	int64 status = 7;
	string event_type = 8;
	int64 tag = 9;  // 风险规则等级
	string updated_at = 10;
	uint64 user_id = 11;
}

message EngineRuleListResponse {
	int64 total = 1;
	int64 page = 2; // @tag form:"page" validate:"required,number" zh:"页数"
	int64 per_page = 3; // @tag form:"per_page" validate:"required,number" zh:"条数"
	repeated EngineRuleItem items = 4;
}

message EngineRuleCategoryRequest {
	uint64 user_id = 1;
	int64 operate_company_id = 4; // @tag form:"operate_company_id"
}
message EngineRuleCategoryResponse {
	repeated string event_type = 1;
}

message EngineRuleCreateRequest {
	uint64 user_id = 1;
	uint64 operate_user_id = 2;
	int64 operate_company_id = 3;
	uint64 tag = 4; // @tag validate:"required,number,oneof=1 2 3 4" zh:"风险等级"
	uint64 priority = 5;
	uint64 status = 6; // @tag validate:"required,number,oneof=1 2" zh:"启用状态"
	string name = 7; // @tag validate:"required" zh:"事件名称"
	string content = 8; // @tag validate:"required" zh:"规则内容"
	string event_desc = 9; // 事件/规则描述 @tag validate:"required" zh:"事件描述"
	string event_impact = 10; // 事件危害
	string event_solution = 11; // 事件解决方案
	string event_type = 12; // 风险规则分类 @tag validate:"required,max=30" zh:"风险规则分类"
}

message EngineRuleDeleteRequest {
	repeated uint64 ids = 1;
	uint64 user_id = 2;
	int64 operate_company_id = 3;
	string keyword = 4; // 关键字检索 @tag form:"keyword"
	string event_type = 5; // 风险规则分类 @tag form:"event_type"
	string  status = 6; // 启用禁用 @tag form:"status"
	repeated string updated_at = 7; // 更新时间 @tag form:"updated_at"
	uint64 identity = 8;	// 身份
}

message EngineRuleUpdateRequest {
	uint64 id = 1; // @tag validate:"required,number" zh:"规则"
	uint64 user_id = 2;
	uint64 operate_user_id = 3;
	int64 operate_company_id = 4;
	uint64 tag = 5;
	uint64 priority = 6;
	uint64 status = 7; // @tag validate:"omitempty,number,oneof=1 2" zh:"启用状态"
	string name = 8; // @tag validate:"required" zh:"规则名称"
	string content = 9; // @tag validate:"required" zh:"规则内容"
	string event_desc = 10; // 事件/规则描述 @tag validate:"required" zh:"事件描述"
	string event_impact = 11; // 事件危害
	string event_solution = 12; // 事件解决方案
	string event_type = 13; // 风险规则分类 @tag validate:"required,max=30" zh:"风险规则分类"
	uint64 identity = 14;	// 身份
}

message EngineRuleUpdateStatusRequest {
	repeated uint64 ids = 1;
	uint64 user_id = 2;
	int64 operate_company_id = 3;
	string keyword = 4; // 关键字检索 @tag form:"keyword"
	string event_type = 5; // 风险规则分类 @tag form:"event_type"
	string  status = 6; // 启用禁用 @tag form:"status"
	repeated string updated_at = 7; // 更新时间 @tag form:"updated_at"
	int64 set_status = 8; // @tag validate:"omitempty,number,oneof=1 2" zh:"设置状态"
}

message IpDomainHistoryRequest {
	uint64 user_id = 1;
	repeated string ip = 2; // @tag validate:"required,dive,required,ip"
	bool is_all = 3; // 获取最大数据
}

message IpDomainHistoryResponse {
	message DomainItem {
		string domain = 1;
		string icp_company = 2;
		string found_time = 3;
		int64 source = 4;
	}
	message Item {
		string ip = 1;
		repeated DomainItem items = 2;
	}
	repeated Item items = 1;
}

message KeywordManageDataSyncRequest {
	string name = 1; // @tag validate:"required" zh:"关键字"
}

message KeywordManageDataSyncWechat {
	int64 id = 1;
	string created_at = 2;
	string updated_at = 3;
	string name = 4;
	string account = 5;
	string qrcode = 6;
	string company_name = 7;
	string platform = 8;
	int64 is_online = 9;
	string describe = 10;
}

message KeywordManageDataSyncApp {
	int64 id = 1;
	string created_at = 2;
	string updated_at = 3;
	string name = 4;
	string logo = 5;
	string category = 6;
	string company_name = 7;
	string platform = 8;
	string product = 9;
	string url = 10;
	string area = 11;
	string developer = 12;
	int64 is_online = 13;
}

message KeywordManageDataSyncMiniAPP {
	int64 id = 1;
	string created_at = 2;
	string updated_at = 3;
	string name = 4;
	string account = 5;
	string erweima = 6;
	string logo = 7;
	string company_name = 8;
	string description = 9;
	string origin_id = 10;
	int64 platform = 11;
	int64 is_online = 12;
}

message KeywordManageDataSyncResponse {
	repeated KeywordManageDataSyncApp app = 1;
	repeated KeywordManageDataSyncWechat wechat = 2;
	repeated KeywordManageDataSyncMiniAPP xiaochengxu = 3;
}

message IcpAppRequest {
	string search = 1; // @tag form:"search" validate:"required" zh:"搜索内容"
	int32 app_type = 2; // @tag validate:"required,oneof=1 2 3" zh:"查询类型"
	bool force = 3; // @tag form:"force"
	bool get_equals = 4; // @tag form:"get_equals"
}

message IcpAppResponse {
	message EquityBasic {
		string name = 1;
		string app_name = 2;
		string icp = 3;
		int32 status = 4;
	}
	EquityBasic info = 1;
	repeated EquityBasic equals = 2;
	bool ba = 3;
}