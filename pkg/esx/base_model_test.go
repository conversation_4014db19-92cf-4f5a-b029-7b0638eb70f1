package esx

import (
	"fmt"
	"github.com/goccy/go-json"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestUnmarshalJSON(t *testing.T) {
	tests := []struct {
		name           string
		jsonData       []byte
		expectedESTime ESTime
		expectedError  error
	}{
		{
			name:     "正常时间解析",
			jsonData: []byte(`"2024-01-01 12:00:00"`),
			// 北京时间 2024-01-01 12:00:00 等于 UTC 时间 2024-01-01 04:00:00
			expectedESTime: ESTime{Time: time.Date(2024, 1, 1, 4, 0, 0, 0, time.UTC)},
			expectedError:  nil,
		},
		{
			name:           "空时间处理",
			jsonData:       []byte(`""`),
			expectedESTime: ESTime{Time: time.Time{}},
			expectedError:  nil,
		},
		{
			name:           "时间格式错误",
			jsonData:       []byte(`"2024-01-01 12:00"`),
			expectedESTime: ESTime{Time: time.Time{}},
			expectedError:  &time.ParseError{},
		},
		{
			name:           "时间解析错误",
			jsonData:       []byte(`"2024-02-30 12:00:00"`),
			expectedESTime: ESTime{Time: time.Time{}},
			expectedError:  &time.ParseError{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var est ESTime
			err := json.Unmarshal(tt.jsonData, &est)

			assert.Equal(t, tt.expectedError != nil, err != nil)

			if tt.expectedError == nil {
				assert.Equal(t, tt.expectedESTime.Unix(), est.Unix())
			}
		})
	}
}

func TestMarshalJSON(t *testing.T) {
	tests := []struct {
		name          string
		timeInput     time.Time
		expectedBytes []byte
		expectedError error
	}{
		{
			name:          "正常时间序列化",
			timeInput:     time.Date(2024, 1, 1, 12, 0, 0, 0, time.Local),
			expectedBytes: []byte(fmt.Sprintf(`"%s"`, time.Date(2024, 1, 1, 12, 0, 0, 0, time.Local).Format(TimeFormat))),
			expectedError: nil,
		},
		{
			name:          "零时间序列化",
			timeInput:     time.Time{},
			expectedBytes: []byte("null"),
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			est := ESTime{Time: tt.timeInput}
			result, err := est.MarshalJSON()

			// 检查错误
			assert.Equal(t, tt.expectedError != nil, err != nil)

			// 检查结果
			assert.Equal(t, tt.expectedBytes, result)
		})
	}
}
