package microx

import (
	"github.com/stretchr/testify/assert"
	"go-micro.dev/v4/client"
	"testing"
	"time"
)

func TestSetTimeoutDur(t *testing.T) {
	testCases := []struct {
		name                  string
		cli                   time.Duration
		server                time.Duration
		expectedCliTimeout    time.Duration // 期望的客户端超时值
		expectedServerTimeout time.Duration // 期望的服务端超时值
		expectedOptionsLength int           // 期望返回的选项数量
	}{
		{
			name:                  "正常值_客户端和服务端都大于5秒",
			cli:                   10 * time.Second,
			server:                15 * time.Second,
			expectedCliTimeout:    10 * time.Second,
			expectedServerTimeout: 15 * time.Second,
			expectedOptionsLength: 2,
		},
		{
			name:                  "边界值_客户端和服务端都等于5秒",
			cli:                   5 * time.Second,
			server:                5 * time.Second,
			expectedCliTimeout:    5 * time.Second,
			expectedServerTimeout: 5 * time.Second,
			expectedOptionsLength: 2,
		},
		{
			name:                  "小于阈值_客户端小于5秒",
			cli:                   3 * time.Second,
			server:                10 * time.Second,
			expectedCliTimeout:    5 * time.Second, // 应该被调整为5秒
			expectedServerTimeout: 10 * time.Second,
			expectedOptionsLength: 2,
		},
		{
			name:                  "小于阈值_服务端小于5秒",
			cli:                   10 * time.Second,
			server:                3 * time.Second,
			expectedCliTimeout:    10 * time.Second,
			expectedServerTimeout: 5 * time.Second, // 应该被调整为5秒
			expectedOptionsLength: 2,
		},
		{
			name:                  "小于阈值_客户端和服务端都小于5秒",
			cli:                   2 * time.Second,
			server:                1 * time.Second,
			expectedCliTimeout:    5 * time.Second, // 应该被调整为5秒
			expectedServerTimeout: 5 * time.Second, // 应该被调整为5秒
			expectedOptionsLength: 2,
		},
		{
			name:                  "零值_客户端和服务端都为0",
			cli:                   0,
			server:                0,
			expectedCliTimeout:    5 * time.Second, // 应该被调整为5秒
			expectedServerTimeout: 5 * time.Second, // 应该被调整为5秒
			expectedOptionsLength: 2,
		},
		{
			name:                  "负值_客户端和服务端都为负数",
			cli:                   -5 * time.Second,
			server:                -10 * time.Second,
			expectedCliTimeout:    5 * time.Second, // 应该被调整为5秒
			expectedServerTimeout: 5 * time.Second, // 应该被调整为5秒
			expectedOptionsLength: 2,
		},
		{
			name:                  "大值_测试较大的超时值",
			cli:                   5 * time.Minute,
			server:                10 * time.Minute,
			expectedCliTimeout:    5 * time.Minute,
			expectedServerTimeout: 10 * time.Minute,
			expectedOptionsLength: 2,
		},
		{
			name:                  "混合值_一个大一个小",
			cli:                   100 * time.Second,
			server:                1 * time.Second,
			expectedCliTimeout:    100 * time.Second,
			expectedServerTimeout: 5 * time.Second, // 应该被调整为5秒
			expectedOptionsLength: 2,
		},
		{
			name:                  "毫秒级_小于5秒的毫秒值",
			cli:                   4500 * time.Millisecond,
			server:                3200 * time.Millisecond,
			expectedCliTimeout:    5 * time.Second, // 应该被调整为5秒
			expectedServerTimeout: 5 * time.Second, // 应该被调整为5秒
			expectedOptionsLength: 2,
		},
		{
			name:                  "微秒级_极小的时间值",
			cli:                   100 * time.Microsecond,
			server:                500 * time.Microsecond,
			expectedCliTimeout:    5 * time.Second, // 应该被调整为5秒
			expectedServerTimeout: 5 * time.Second, // 应该被调整为5秒
			expectedOptionsLength: 2,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 调用被测试的函数
			options := SetTimeoutDur(tc.cli, tc.server)

			// 验证返回的选项数量
			assert.Equal(t, tc.expectedOptionsLength, len(options), "返回的选项数量应该正确")

			// 创建CallOptions来测试选项的效果
			callOpts := &client.CallOptions{}

			// 应用第一个选项（客户端超时选项）
			if len(options) > 0 {
				options[0](callOpts)
				assert.Equal(t, tc.expectedCliTimeout, callOpts.RequestTimeout, "客户端请求超时应该正确设置")
				assert.Equal(t, tc.expectedCliTimeout, callOpts.DialTimeout, "客户端拨号超时应该正确设置")
			}

			// 重置CallOptions来测试第二个选项
			callOpts = &client.CallOptions{}

			// 应用第二个选项（服务端超时选项）
			if len(options) > 1 {
				options[1](callOpts)
				assert.Equal(t, tc.expectedServerTimeout, callOpts.ConnectionTimeout, "服务端连接超时应该正确设置")
			}

			// 测试所有选项一起应用的效果
			callOptsAll := &client.CallOptions{}
			for _, opt := range options {
				opt(callOptsAll)
			}

			// 验证所有超时都被正确设置
			assert.Equal(t, tc.expectedCliTimeout, callOptsAll.RequestTimeout, "请求超时应该正确")
			assert.Equal(t, tc.expectedCliTimeout, callOptsAll.DialTimeout, "拨号超时应该正确")
			assert.Equal(t, tc.expectedServerTimeout, callOptsAll.ConnectionTimeout, "连接超时应该正确")
		})
	}
}

// TestSetTimeoutDurReturnType 测试SetTimeoutDur返回类型
func TestSetTimeoutDurReturnType(t *testing.T) {
	options := SetTimeoutDur(10*time.Second, 20*time.Second)

	// 验证返回的是[]client.CallOption类型
	assert.IsType(t, []client.CallOption{}, options, "应该返回[]client.CallOption类型")

	// 验证每个选项都是函数类型
	for i, opt := range options {
		assert.NotNil(t, opt, "选项%d不应该为nil", i)

		// 验证选项可以被调用
		callOpts := &client.CallOptions{}
		assert.NotPanics(t, func() {
			opt(callOpts)
		}, "选项%d应该可以被安全调用", i)
	}
}

// TestSetTimeoutDurMinimumValues 专门测试最小值逻辑
func TestSetTimeoutDurMinimumValues(t *testing.T) {
	testCases := []struct {
		name   string
		cli    time.Duration
		server time.Duration
	}{
		{"客户端为1秒", 1 * time.Second, 10 * time.Second},
		{"服务端为1秒", 10 * time.Second, 1 * time.Second},
		{"客户端为4秒", 4 * time.Second, 10 * time.Second},
		{"服务端为4秒", 10 * time.Second, 4 * time.Second},
		{"客户端为5秒", 5 * time.Second, 10 * time.Second},
		{"服务端为5秒", 10 * time.Second, 5 * time.Second},
		{"客户端为4.9秒", 4900 * time.Millisecond, 10 * time.Second},
		{"服务端为4.9秒", 10 * time.Second, 4900 * time.Millisecond},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			options := SetTimeoutDur(tc.cli, tc.server)

			callOpts := &client.CallOptions{}

			// 应用所有选项
			for _, opt := range options {
				opt(callOpts)
			}

			// 验证最小值逻辑
			expectedCliTimeout := tc.cli
			if expectedCliTimeout <= 5*time.Second {
				expectedCliTimeout = 5 * time.Second
			}

			expectedServerTimeout := tc.server
			if expectedServerTimeout <= 5*time.Second {
				expectedServerTimeout = 5 * time.Second
			}

			assert.Equal(t, expectedCliTimeout, callOpts.RequestTimeout)
			assert.Equal(t, expectedCliTimeout, callOpts.DialTimeout)
			assert.Equal(t, expectedServerTimeout, callOpts.ConnectionTimeout)
		})
	}
}

// TestSetTimeoutDurEdgeCases 测试边界情况
func TestSetTimeoutDurEdgeCases(t *testing.T) {
	testCases := []struct {
		name   string
		cli    time.Duration
		server time.Duration
	}{
		{"最大时间值", time.Duration(1<<63 - 1), time.Duration(1<<63 - 1)},
		{"纳秒级时间", 1 * time.Nanosecond, 1 * time.Nanosecond},
		{"一个为0一个正常", 0, 30 * time.Second},
		{"一个正常一个为0", 30 * time.Second, 0},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 应该不会panic
			assert.NotPanics(t, func() {
				options := SetTimeoutDur(tc.cli, tc.server)
				assert.Equal(t, 2, len(options), "应该返回2个选项")

				// 验证选项可以正常工作
				callOpts := &client.CallOptions{}
				for _, opt := range options {
					opt(callOpts)
				}

				// 基本验证
				assert.NotZero(t, callOpts.RequestTimeout, "请求超时应该被设置")
				assert.NotZero(t, callOpts.DialTimeout, "拨号超时应该被设置")
				assert.NotZero(t, callOpts.ConnectionTimeout, "连接超时应该被设置")
			})
		})
	}
}

// TestSetTimeoutDurConcurrency 测试并发安全性
func TestSetTimeoutDurConcurrency(t *testing.T) {
	const goroutines = 100
	const iterations = 10

	done := make(chan bool, goroutines)

	for i := 0; i < goroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()

			for j := 0; j < iterations; j++ {
				cli := time.Duration((id+j)%20+1) * time.Second
				server := time.Duration((id*2+j)%30+1) * time.Second

				options := SetTimeoutDur(cli, server)
				assert.Equal(t, 2, len(options), "并发调用应该返回正确数量的选项")

				// 验证选项可以正常工作
				callOpts := &client.CallOptions{}
				for _, opt := range options {
					opt(callOpts)
				}

				// 基本验证
				assert.NotZero(t, callOpts.RequestTimeout, "请求超时应该被设置")
				assert.NotZero(t, callOpts.DialTimeout, "拨号超时应该被设置")
				assert.NotZero(t, callOpts.ConnectionTimeout, "连接超时应该被设置")
			}
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < goroutines; i++ {
		<-done
	}
}

// TestSetTimeoutDurConsistency 测试函数行为一致性
func TestSetTimeoutDurConsistency(t *testing.T) {
	// 多次调用相同参数应该得到相同结果
	cli := 8 * time.Second
	server := 12 * time.Second

	var results [][]client.CallOption
	for i := 0; i < 10; i++ {
		options := SetTimeoutDur(cli, server)
		results = append(results, options)
	}

	// 验证所有结果都有相同的长度
	for i, result := range results {
		assert.Equal(t, 2, len(result), "第%d次调用应该返回2个选项", i)

		// 验证选项的效果一致
		callOpts := &client.CallOptions{}
		for _, opt := range result {
			opt(callOpts)
		}

		assert.Equal(t, cli, callOpts.RequestTimeout, "第%d次调用的请求超时应该一致", i)
		assert.Equal(t, cli, callOpts.DialTimeout, "第%d次调用的拨号超时应该一致", i)
		assert.Equal(t, server, callOpts.ConnectionTimeout, "第%d次调用的连接超时应该一致", i)
	}
}

func TestSetTimeout(t *testing.T) {
	testCases := []struct {
		name                  string
		cli                   int
		server                int
		expectedCliTimeout    int // 期望的客户端超时值
		expectedServerTimeout int // 期望的服务端超时值
		expectedOptionsLength int // 期望返回的选项数量
	}{
		{
			name:                  "正常值_客户端和服务端都大于5",
			cli:                   10,
			server:                15,
			expectedCliTimeout:    10,
			expectedServerTimeout: 15,
			expectedOptionsLength: 2,
		},
		{
			name:                  "边界值_客户端和服务端都等于5",
			cli:                   5,
			server:                5,
			expectedCliTimeout:    5,
			expectedServerTimeout: 5,
			expectedOptionsLength: 2,
		},
		{
			name:                  "小于阈值_客户端小于5",
			cli:                   3,
			server:                10,
			expectedCliTimeout:    5, // 应该被调整为5
			expectedServerTimeout: 10,
			expectedOptionsLength: 2,
		},
		{
			name:                  "小于阈值_服务端小于5",
			cli:                   10,
			server:                3,
			expectedCliTimeout:    10,
			expectedServerTimeout: 5, // 应该被调整为5
			expectedOptionsLength: 2,
		},
		{
			name:                  "小于阈值_客户端和服务端都小于5",
			cli:                   2,
			server:                1,
			expectedCliTimeout:    5, // 应该被调整为5
			expectedServerTimeout: 5, // 应该被调整为5
			expectedOptionsLength: 2,
		},
		{
			name:                  "零值_客户端和服务端都为0",
			cli:                   0,
			server:                0,
			expectedCliTimeout:    5, // 应该被调整为5
			expectedServerTimeout: 5, // 应该被调整为5
			expectedOptionsLength: 2,
		},
		{
			name:                  "负值_客户端和服务端都为负数",
			cli:                   -5,
			server:                -10,
			expectedCliTimeout:    5, // 应该被调整为5
			expectedServerTimeout: 5, // 应该被调整为5
			expectedOptionsLength: 2,
		},
		{
			name:                  "大值_测试较大的超时值",
			cli:                   300,
			server:                600,
			expectedCliTimeout:    300,
			expectedServerTimeout: 600,
			expectedOptionsLength: 2,
		},
		{
			name:                  "混合值_一个大一个小",
			cli:                   100,
			server:                1,
			expectedCliTimeout:    100,
			expectedServerTimeout: 5, // 应该被调整为5
			expectedOptionsLength: 2,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 调用被测试的函数
			options := SetTimeout(tc.cli, tc.server)

			// 验证返回的选项数量
			assert.Equal(t, tc.expectedOptionsLength, len(options), "返回的选项数量应该正确")

			// 创建CallOptions来测试选项的效果
			callOpts := &client.CallOptions{}

			// 应用第一个选项（客户端超时选项）
			if len(options) > 0 {
				options[0](callOpts)
				expectedCliDuration := time.Duration(tc.expectedCliTimeout) * time.Second
				assert.Equal(t, expectedCliDuration, callOpts.RequestTimeout, "客户端请求超时应该正确设置")
				assert.Equal(t, expectedCliDuration, callOpts.DialTimeout, "客户端拨号超时应该正确设置")
			}

			// 重置CallOptions来测试第二个选项
			callOpts = &client.CallOptions{}

			// 应用第二个选项（服务端超时选项）
			if len(options) > 1 {
				options[1](callOpts)
				expectedServerDuration := time.Duration(tc.expectedServerTimeout) * time.Second
				assert.Equal(t, expectedServerDuration, callOpts.ConnectionTimeout, "服务端连接超时应该正确设置")
			}

			// 测试所有选项一起应用的效果
			callOptsAll := &client.CallOptions{}
			for _, opt := range options {
				opt(callOptsAll)
			}

			// 验证所有超时都被正确设置
			expectedCliDuration := time.Duration(tc.expectedCliTimeout) * time.Second
			expectedServerDuration := time.Duration(tc.expectedServerTimeout) * time.Second

			assert.Equal(t, expectedCliDuration, callOptsAll.RequestTimeout, "请求超时应该正确")
			assert.Equal(t, expectedCliDuration, callOptsAll.DialTimeout, "拨号超时应该正确")
			assert.Equal(t, expectedServerDuration, callOptsAll.ConnectionTimeout, "连接超时应该正确")
		})
	}
}

// TestSetTimeoutReturnType 测试SetTimeout返回类型
func TestSetTimeoutReturnType(t *testing.T) {
	options := SetTimeout(10, 20)

	// 验证返回的是[]client.CallOption类型
	assert.IsType(t, []client.CallOption{}, options, "应该返回[]client.CallOption类型")

	// 验证每个选项都是函数类型
	for i, opt := range options {
		assert.NotNil(t, opt, "选项%d不应该为nil", i)

		// 验证选项可以被调用
		callOpts := &client.CallOptions{}
		assert.NotPanics(t, func() {
			opt(callOpts)
		}, "选项%d应该可以被安全调用", i)
	}
}

// TestSetTimeoutMinimumValues 专门测试最小值逻辑
func TestSetTimeoutMinimumValues(t *testing.T) {
	testCases := []struct {
		name   string
		cli    int
		server int
	}{
		{"客户端为1", 1, 10},
		{"服务端为1", 10, 1},
		{"客户端为4", 4, 10},
		{"服务端为4", 10, 4},
		{"客户端为5", 5, 10},
		{"服务端为5", 10, 5},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			options := SetTimeout(tc.cli, tc.server)

			callOpts := &client.CallOptions{}

			// 应用所有选项
			for _, opt := range options {
				opt(callOpts)
			}

			// 验证最小值逻辑
			expectedCliTimeout := tc.cli
			if expectedCliTimeout <= 5 {
				expectedCliTimeout = 5
			}

			expectedServerTimeout := tc.server
			if expectedServerTimeout <= 5 {
				expectedServerTimeout = 5
			}

			assert.Equal(t, time.Duration(expectedCliTimeout)*time.Second, callOpts.RequestTimeout)
			assert.Equal(t, time.Duration(expectedCliTimeout)*time.Second, callOpts.DialTimeout)
			assert.Equal(t, time.Duration(expectedServerTimeout)*time.Second, callOpts.ConnectionTimeout)
		})
	}
}

// TestSetTimeoutConcurrency 测试并发安全性
func TestSetTimeoutConcurrency(t *testing.T) {
	const goroutines = 100
	const iterations = 10

	done := make(chan bool, goroutines)

	for i := 0; i < goroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()

			for j := 0; j < iterations; j++ {
				cli := (id+j)%20 + 1
				server := (id*2+j)%30 + 1

				options := SetTimeout(cli, server)
				assert.Equal(t, 2, len(options), "并发调用应该返回正确数量的选项")

				// 验证选项可以正常工作
				callOpts := &client.CallOptions{}
				for _, opt := range options {
					opt(callOpts)
				}

				// 基本验证
				assert.NotZero(t, callOpts.RequestTimeout, "请求超时应该被设置")
				assert.NotZero(t, callOpts.DialTimeout, "拨号超时应该被设置")
				assert.NotZero(t, callOpts.ConnectionTimeout, "连接超时应该被设置")
			}
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < goroutines; i++ {
		<-done
	}
}

type mockCallOptions struct {
	connectionTimeout time.Duration
}

func (m *mockCallOptions) WithConnectionTimeout(d time.Duration) client.CallOption {
	return func(o *client.CallOptions) {
		o.ConnectionTimeout = d
	}
}

// Test ServerTimeout function
func TestServerTimeout(t *testing.T) {
	testCases := []struct {
		name        string
		timeout     int
		expectedDur time.Duration
	}{
		{
			name:        "timeout above threshold",
			timeout:     10,
			expectedDur: 10 * time.Second,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			option := ServerTimeout(tc.timeout)
			callOptions := &client.CallOptions{}
			option(callOptions)
			assert.Equal(t, tc.expectedDur, callOptions.ConnectionTimeout)
		})
	}
}

// Test ServerTimeoutDur function
func TestServerTimeoutDur(t *testing.T) {
	testCases := []struct {
		name        string
		dur         time.Duration
		expectedDur time.Duration
	}{
		{
			name:        "duration above threshold",
			dur:         10 * time.Second,
			expectedDur: 10 * time.Second,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			option := ServerTimeoutDur(tc.dur)
			callOptions := &client.CallOptions{}
			option(callOptions)
			assert.Equal(t, tc.expectedDur, callOptions.ConnectionTimeout)
		})
	}
}
