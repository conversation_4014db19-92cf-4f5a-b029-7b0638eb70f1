package errx

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewError(t *testing.T) {
	e := NewError(ERROR, ErrUnknown)

	code, msg := DecodeCode(e)
	assert.Equal(t, ERROR, code)
	assert.Equal(t, msg, ErrUnknown.Error())

	code, _ = DecodeCode(nil)
	assert.Equal(t, SUCCESS, code)
}

func TestDecodeCode(t *testing.T) {
	e := NewError(ERROR+1, errors.New("测试错误"))

	code, msg := DecodeCode(e)
	assert.Equal(t, ERROR+1, code)
	assert.Equal(t, "测试错误", msg)
}

func TestDecodeCodeWithMsg(t *testing.T) {
	e := NewError(ERROR, ErrUnknown)

	_, m := DecodeCode(e, "自定义错误")
	assert.Equal(t, "自定义错误", m)
}

func TestIfError(t *testing.T) {
	err := errors.New("error for test")
	assert.Equal(t, IfError(true, err, nil), err)

	assert.Equal(t, IfError(false, err, nil), nil)
}

func TestErrString(t *testing.T) {
	assert.Equal(t, ErrString(nil), "")

	err := errors.New("error for test")
	assert.Equal(t, ErrString(err), err.Error())
}
func TestIsFrontEnd(t *testing.T) {
	tests := []struct {
		name string
		err  error
		want bool
	}{
		{
			name: "ErrFrontEndDuplicatedName",
			err:  ErrFrontEndDuplicatedName,
			want: true,
		},
		{
			name: "ErrFrontEndDuplicatedCompanyName",
			err:  ErrFrontEndDuplicatedCompanyName,
			want: true,
		},
		{
			name: "ErrFrontEndDuplicatedMobile",
			err:  ErrFrontEndDuplicatedMobile,
			want: true,
		},
		{
			name: "ErrFrontEndDuplicatedEmail",
			err:  ErrFrontEndDuplicatedEmail,
			want: true,
		},
		{
			name: "ErrFrontEndNoPermissions",
			err:  ErrFrontEndNoPermissions,
			want: true,
		},
		{
			name: "ErrUnknown",
			err:  ErrUnknown,
			want: false,
		},
		{
			name: "ErrSearchCantEmpty",
			err:  ErrSearchCantEmpty,
			want: false,
		},
		{
			name: "ErrFrontEndInternal",
			err:  ErrFrontEndInternal,
			want: false,
		},
		{
			name: "ErrFrontEndParam",
			err:  ErrFrontEndParam,
			want: false,
		},
		{
			name: "ErrFrontEndNoData",
			err:  ErrFrontEndNoData,
			want: false,
		},
		{
			name: "ErrFrontEndGetUserInfoFailed",
			err:  ErrFrontEndGetUserInfoFailed,
			want: false,
		},
		{
			name: "ErrFrontEndGetDataFailed",
			err:  ErrFrontEndGetDataFailed,
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsFrontEnd(tt.err); got != tt.want {
				t.Errorf("IsFrontEnd() = %v, want %v", got, tt.want)
			}
		})
	}
}
