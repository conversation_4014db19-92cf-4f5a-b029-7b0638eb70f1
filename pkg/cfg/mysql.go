package cfg

const (
	LogDebugMode   = "debug"
	LogReleaseMode = "release"
	LogTestMode    = "test"
)

type MySql struct {
	Address      string `json:"address"`
	Port         uint   `json:"port"`
	UserName     string `json:"username"`
	Password     string `json:"password"`
	Database     string `json:"database"`
	CharSet      string `json:"charset"`
	MaxIdleConns int    `json:"max-idle-conns"`
	MaxOpenConns int    `json:"max-open-conns"`
	LogMode      string `json:"log-mode"`
}

// LoadMysql 加载Mysql配置
func LoadMysql() MySql {
	// _ = config.Get("mysql").Scan(&GetInstance().MySql)
	RefCfgDefVal()
	return GetInstance().MySql
}
