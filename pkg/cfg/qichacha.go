package cfg

type QCC struct {
	APIUrl              string `json:"api_url"`
	Key                 string `json:"key"`
	Secret<PERSON>ey           string `json:"secret_key"`
	Switch              bool   `json:"switch"`
	BasicDetailCacheDay uint64 `json:"basic_detail_cache_day"`
}

// LoadQcc 加载qichacha配置
func LoadQcc() QCC {
	// _ = config.Get("qichacha").Scan(&GetInstance().QiChaCha)
	RefCfgDefVal()
	return GetInstance().QiChaCha
}
