package cfg

import (
	"fmt"
	"log"
	"sync"

	consulConfig "github.com/go-micro/plugins/v4/config/source/consul"
	"github.com/go-micro/plugins/v4/registry/consul"
	"github.com/hashicorp/consul/api"
	"go-micro.dev/v4/config"
	"go-micro.dev/v4/registry"
)

type Consul struct {
	Address string `json:"address"`
	Port    uint   `json:"port"`
	Prefix  string `json:"prefix"`
	Token   string `json:"token"`
}

var onceConsul sync.Once

var singleConsul registry.Registry

func (cfg Cfg) GetConsulReg() registry.Registry {
	if singleConsul == nil {
		onceConsul.Do(
			func() {
				singleConsul = consul.NewRegistry(
					consul.Config(&api.Config{
						Address: fmt.Sprintf("%s:%d", cfg.Consul.Address, cfg.Consul.Port),
						Token:   cfg.Consul.Token,
					}))
			},
		)
	}
	return singleConsul
}

// LoadConsul 加载consul配置
func LoadConsul() Consul {
	RefCfgDefVal()
	return GetInstance().Consul
}

func OnInitConfigCenter(c Consul) {
	loadConsulConfig(
		fmt.Sprintf("%s:%d", c.Address, c.Port),
		c.Prefix,
		c.Token,
	)
}

// loadConsulConfig 获取配置中心的配置
func loadConsulConfig(addr string, prefix string, token string) {
	// 添加配置中心
	// 配置中心使用consul key/value模式
	consulSource := consulConfig.NewSource(
		// 设置配置中心地址
		consulConfig.WithAddress(addr),
		// 设置Token
		consulConfig.WithToken(token),
		// 设置前缀，不设置默认为 /micro/config
		consulConfig.WithPrefix(prefix),
		// 是否移除前缀，这里设置为true：表示可以不带前缀直接获取对应配置
		consulConfig.StripPrefix(true),
	)
	if err := config.Load(consulSource); err != nil {
		// consul加载失败，不报panic，仅打印日志
		log.Printf("consul_address: %s, consul_prefix: %s, consul_token: %s, consul config center loadConsulConfig failed: %v\n", addr, prefix, token, err)
	}
}
