package yuque

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/google/go-querystring/query"
	"io"
	"net/http"
	"net/url"
	"time"
)

const (
	baseURL        = "https://www.yuque.com"
	exploitBaseURL = "https://www.yuque.com/api"
)

type Client struct {
	BaseURL string
	Token   string
	Client  *http.Client
}

func NewDefaultClient(token string) *Client {
	transport := http.Transport{
		IdleConnTimeout: 30 * time.Second,
	}

	c := NewClient(&http.Client{
		Transport: &transport,
	}, token)
	return c
}

func NewClient(client *http.Client, token string) *Client {
	if client == nil {
		client = http.DefaultClient
	}

	return &Client{
		BaseURL: baseURL,
		Client:  client,
		Token:   token,
	}
}

func (c *Client) NewRequest(method string, path string, params interface{}, body io.Reader) (*http.Request, error) {
	u, err := url.Parse(c.BaseURL + path)
	if err != nil {
		return nil, err
	}

	return c.newRequest(method, u, params, body)
}

func (c *Client) newRequest(method string, u *url.URL, params interface{}, body io.Reader) (*http.Request, error) {
	qs, err := query.Values(params)
	if err != nil {
		return nil, err
	}

	u.RawQuery = qs.Encode()
	fmt.Println(u.String())
	req, err := http.NewRequest(method, u.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("X-Auth-Token", c.Token)

	return req, nil
}

func (c *Client) Do(ctx context.Context, req *http.Request, destination interface{}) error {
	return c.DoWithErrorHandling(ctx, req, destination, getErrorFromResponse)
}

func (c *Client) DoWithErrorHandling(
	ctx context.Context,
	req *http.Request,
	destination interface{},
	errHandler ErrorHandler,
) error {
	resp, err := c.do(ctx, req)
	if err != nil {
		return err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return errHandler(resp)
	}

	if destination == nil {
		return nil
	}

	return c.parseResponse(destination, resp.Body)
}

func (c *Client) do(ctx context.Context, req *http.Request) (*http.Response, error) {
	if ctx != nil {
		req = req.WithContext(ctx)
	}

	return c.Client.Do(req)
}

func (c *Client) parseResponse(destination interface{}, body io.Reader) error {
	var err error

	if w, ok := destination.(io.Writer); ok {
		_, err = io.Copy(w, body)
	} else {
		decoder := json.NewDecoder(body)
		err = decoder.Decode(destination)
	}

	return err
}
