package yuque

import (
	"context"
	"time"
)

var (
	HostSearchPath = "/api/v2/search"
)

type SearchRequest struct {
	Page  int64  `url:"offset,omitempty"`
	Query string `url:"q,omitempty"`
	Type  string `url:"type,omitempty"`
	Scope string `url:"scope,omitempty"`
	Tab   string `url:"tab,omitempty"`
}

type SearchResponse struct {
	Meta struct {
		Total int `json:"total"`
	} `json:"meta"`
	Data []struct {
		Id      int    `json:"id"`
		Type    string `json:"type"`
		Title   string `json:"title"`
		Summary string `json:"summary"`
		Url     string `json:"url"`
		Info    string `json:"info"`
		Target  struct {
			Id                int         `json:"id"`
			Slug              string      `json:"slug"`
			Title             string      `json:"title"`
			Description       *string     `json:"description"`
			UserId            int         `json:"user_id"`
			BookId            int         `json:"book_id"`
			Format            string      `json:"format"`
			Public            int         `json:"public"`
			Status            int         `json:"status"`
			ViewStatus        int         `json:"view_status"`
			ReadStatus        int         `json:"read_status"`
			LikesCount        int         `json:"likes_count"`
			ReadCount         int         `json:"read_count"`
			CommentsCount     int         `json:"comments_count"`
			ContentUpdatedAt  time.Time   `json:"content_updated_at"`
			CreatedAt         time.Time   `json:"created_at"`
			UpdatedAt         time.Time   `json:"updated_at"`
			PublishedAt       time.Time   `json:"published_at"`
			FirstPublishedAt  time.Time   `json:"first_published_at"`
			DraftVersion      int         `json:"draft_version"`
			LastEditorId      int         `json:"last_editor_id"`
			WordCount         int         `json:"word_count"`
			Cover             *string     `json:"cover"`
			CustomDescription interface{} `json:"custom_description"`
			Hits              int         `json:"hits"`
			LastEditor        interface{} `json:"last_editor"`
			Book              struct {
				Id               int       `json:"id"`
				Type             string    `json:"type"`
				Slug             string    `json:"slug"`
				Name             string    `json:"name"`
				UserId           int       `json:"user_id"`
				Description      string    `json:"description"`
				Public           int       `json:"public"`
				ContentUpdatedAt time.Time `json:"content_updated_at"`
				UpdatedAt        time.Time `json:"updated_at"`
				CreatedAt        time.Time `json:"created_at"`
				Namespace        string    `json:"namespace"`
				User             struct {
					Id             int       `json:"id"`
					Type           string    `json:"type"`
					Login          string    `json:"login"`
					Name           string    `json:"name"`
					Description    *string   `json:"description"`
					AvatarUrl      string    `json:"avatar_url"`
					FollowersCount int       `json:"followers_count"`
					FollowingCount int       `json:"following_count"`
					CreatedAt      time.Time `json:"created_at"`
					UpdatedAt      time.Time `json:"updated_at"`
					Serializer     string    `json:"_serializer"`
				} `json:"user"`
				Serializer string `json:"_serializer"`
			} `json:"book"`
			Serializer string `json:"_serializer"`
		} `json:"target"`
		Serializer string `json:"_serializer"`
	} `json:"data"`
}

func (c *Client) Search(ctx context.Context, options *SearchRequest) (*SearchResponse, error) {
	var found SearchResponse
	req, err := c.NewRequest("GET", HostSearchPath, options, nil)
	if err != nil {
		return nil, err
	}

	if err := c.Do(ctx, req, &found); err != nil {
		return nil, err
	}

	return &found, nil
}
