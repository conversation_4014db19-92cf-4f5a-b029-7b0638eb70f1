package ip138

import (
	"context"
	core "micro-service/coreService/proto"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	"micro-service/pkg/utils"
	"net/http"
	"slices"
	"strings"
	"time"

	"go.uber.org/zap"
)

// 错误类型枚举
type ErrorType int

const (
	ErrorTypeNetwork   ErrorType = iota // 网络错误
	ErrorTypeRateLimit                  // 限流错误
	ErrorTypeTemporary                  // 其他临时错误
	ErrorTypePermanent                  // 永久错误
)

// 重试策略配置
type RetryConfig struct {
	MaxRetries int           // 最大重试次数
	BaseDelay  time.Duration // 基础延迟时间
	MaxDelay   time.Duration // 最大延迟时间
	Multiplier float64       // 延迟倍数（用于指数退避）
}

// 重试策略管理器
type RetryManager struct {
	NetworkConfig   RetryConfig // 网络错误重试配置
	RateLimitConfig RetryConfig // 限流错误重试配置
	TemporaryConfig RetryConfig // 临时错误重试配置
}

// 自适应延迟控制器
type AdaptiveDelayController struct {
	BaseDelay       time.Duration // 基础延迟
	CurrentDelay    time.Duration // 当前延迟
	SuccessCount    int           // 连续成功次数
	ResponseTimeSum time.Duration // 响应时间累计
	RequestCount    int           // 请求计数
}

// 初始化重试管理器
func NewRetryManager() *RetryManager {
	return &RetryManager{
		NetworkConfig: RetryConfig{
			MaxRetries: 3,
			BaseDelay:  1 * time.Second,
			MaxDelay:   4 * time.Second,
			Multiplier: 2.0,
		},
		RateLimitConfig: RetryConfig{
			MaxRetries: 5,
			BaseDelay:  5 * time.Second,
			MaxDelay:   25 * time.Second,
			Multiplier: 1.0, // 线性递增
		},
		TemporaryConfig: RetryConfig{
			MaxRetries: 2,
			BaseDelay:  1 * time.Second,
			MaxDelay:   1 * time.Second,
			Multiplier: 1.0,
		},
	}
}

type Ip138IpItem struct {
	Ip   string     `json:"ip"`
	List []IpDomain `json:"list"`
}

type IpDomain struct {
	Domain string `json:"domain"`
	Time   string `json:"time"`
}
type Ip138DomainItem struct {
	Domain string   `json:"domain"`
	Ips    []string `json:"ips"`
}

// 初始化自适应延迟控制器
func NewAdaptiveDelayController() *AdaptiveDelayController {
	baseDelay := 300 * time.Millisecond
	return &AdaptiveDelayController{
		BaseDelay:    baseDelay,
		CurrentDelay: baseDelay,
		SuccessCount: 0,
	}
}

// 错误分类函数
func classifyError(err error) ErrorType {
	if err == nil {
		return ErrorTypePermanent
	}

	errStr := strings.ToLower(err.Error())

	// 网络相关错误
	if strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "connection") ||
		strings.Contains(errStr, "network") ||
		strings.Contains(errStr, "dial") {
		return ErrorTypeNetwork
	}

	// 限流相关错误
	if strings.Contains(errStr, "rate limit") ||
		strings.Contains(errStr, "too many requests") ||
		strings.Contains(errStr, "quota") ||
		strings.Contains(errStr, "throttle") {
		return ErrorTypeRateLimit
	}

	// HTTP 429状态码也视为限流
	if strings.Contains(errStr, "429") {
		return ErrorTypeRateLimit
	}

	// 其他可能的临时错误
	if strings.Contains(errStr, "server") ||
		strings.Contains(errStr, "unavailable") ||
		strings.Contains(errStr, "internal") {
		return ErrorTypeTemporary
	}

	// 默认为永久错误
	return ErrorTypePermanent
}

// 计算重试延迟时间
func (rm *RetryManager) calculateDelay(errorType ErrorType, attempt int) time.Duration {
	var config RetryConfig

	switch errorType {
	case ErrorTypeNetwork:
		config = rm.NetworkConfig
	case ErrorTypeRateLimit:
		config = rm.RateLimitConfig
	case ErrorTypeTemporary:
		config = rm.TemporaryConfig
	default:
		return 0 // 永久错误不重试
	}

	if attempt >= config.MaxRetries {
		return 0
	}

	var delay time.Duration
	if config.Multiplier > 1.0 {
		// 指数退避
		delay = time.Duration(float64(config.BaseDelay) * (config.Multiplier * float64(attempt)))
	} else {
		// 线性递增
		delay = config.BaseDelay * time.Duration(attempt+1)
	}

	if delay > config.MaxDelay {
		delay = config.MaxDelay
	}

	return delay
}

// 执行带重试的操作
func (rm *RetryManager) executeWithRetry(ctx context.Context, operation func() error) error {
	var lastErr error

	for attempt := 0; attempt < 10; attempt++ { // 最大尝试次数设置为较大值，实际由具体错误类型控制
		lastErr = operation()

		if lastErr == nil {
			return nil // 成功，无需重试
		}

		errorType := classifyError(lastErr)
		if errorType == ErrorTypePermanent {
			return lastErr // 永久错误，不重试
		}

		delay := rm.calculateDelay(errorType, attempt)
		if delay == 0 {
			break // 超过最大重试次数
		}

		log.Infof("[fofa]重试第%d次，错误类型:%d，延迟:%v，错误:%v", attempt+1, errorType, delay, lastErr)

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// 继续重试
		}
	}

	return lastErr
}

// 更新自适应延迟
func (adc *AdaptiveDelayController) updateDelay(responseTime time.Duration, success bool, err error) {
	adc.RequestCount++

	if success {
		adc.SuccessCount++
		adc.ResponseTimeSum += responseTime

		// 连续成功超过3次，减少延迟
		if adc.SuccessCount > 3 {
			adc.CurrentDelay = time.Duration(float64(adc.CurrentDelay) * 0.8)
			if adc.CurrentDelay < 100*time.Millisecond {
				adc.CurrentDelay = 100 * time.Millisecond
			}
		}

		// 响应时间过长，增加延迟
		avgResponseTime := adc.ResponseTimeSum / time.Duration(adc.RequestCount)
		if avgResponseTime > 2*time.Second {
			adc.CurrentDelay = time.Duration(float64(adc.CurrentDelay) * 1.5)
		}
	} else {
		// 失败时重置成功计数，并根据错误类型增加延迟
		adc.SuccessCount = 0
		errorType := classifyError(err)
		if errorType == ErrorTypeRateLimit {
			adc.CurrentDelay = time.Duration(float64(adc.CurrentDelay) * 3)
		} else if errorType == ErrorTypeNetwork {
			adc.CurrentDelay = time.Duration(float64(adc.CurrentDelay) * 1.2)
		}
	}
}

// 获取当前延迟时间
func (adc *AdaptiveDelayController) getCurrentDelay() time.Duration {
	return adc.CurrentDelay
}

// QueryIp 通过ip138查询ip
// domainsWhiteList 白名单,只有domainsWhiteList中的域名才会返回
// subDomainsBlackList 黑名单,如果查询结果在subDomainsBlackList中，则不返回
func QueryIp(ctx context.Context, ips []string, domainsWhiteList, subDomainsBlackList []string) ([]Ip138IpItem, error) {
	// 初始化重试管理器和自适应延迟控制器
	retryManager := NewRetryManager()
	delayController := NewAdaptiveDelayController()

	log.Infof("[ip138]开始查询ip138数据，查询语句:%s", ips)

	// 构建请求
	req := &core.Ip138IpRequest{
		Ip: ips,
	}

	var rsp *core.Ip138IpResponse
	var lastErr error

	// 使用重试机制执行请求
	operation := func() error {
		rsp = &core.Ip138IpResponse{}
		if cfg.IsLocalClient() {
			// 本地化通过HTTP调用saas
			return core.HttpClient(http.MethodPost, "/api/v1/ip138/ip", req, rsp)
		} else {
			// 否则rpc微服务调用
			var err error
			rsp, err = core.GetProtoCoreClient().Ip138ByIp(ctx, req, microx.SetTimeout(30, 30)...)
			return err
		}
	}

	lastErr = retryManager.executeWithRetry(ctx, operation)
	responseTime := time.Since(time.Now())

	// 更新自适应延迟
	delayController.updateDelay(responseTime, lastErr == nil, lastErr)

	// 处理请求结果
	if lastErr != nil {
		log.Errorf("[ip138]数据获取失败，查询语句:%s，错误:%v", ips, lastErr)
		return nil, lastErr
	}

	ip138IpItems := make([]Ip138IpItem, 0)
	for _, ip := range rsp.List {
		ip138IpItems = append(ip138IpItems, Ip138IpItem{
			Ip: ip.Ip,
			List: func() []IpDomain {
				list := make([]IpDomain, 0)
				for _, domain := range ip.List {
					list = append(list, IpDomain{
						Domain: domain.Domain,
						Time:   domain.Time,
					})
				}
				list = filterIpDomain(list, subDomainsBlackList, domainsWhiteList)
				return list
			}(),
		})
	}

	log.Infof("[ip138]数据获取成功，查询语句:%s，响应时间:%v", ips, responseTime)
	return ip138IpItems, nil
}

// filterIpDomain 过滤ip域名（优化了过滤逻辑，减少重复检查）
func filterIpDomain(ip138Results []IpDomain, subDomainsBlackList, domainsWhiteList []string) []IpDomain {
	validDomains := make([]IpDomain, 0)
	processedDomains := make(map[string]bool)

	// 构建快速查找Map
	existSubdomainsMap := make(map[string]bool)
	for _, subdomain := range subDomainsBlackList {
		existSubdomainsMap[subdomain] = true
	}

	domainsWhiteListMap := make(map[string]bool)
	for _, domain := range domainsWhiteList {
		domainsWhiteListMap[domain] = true
	}

	for _, domains := range ip138Results {
		domain := domains.Domain
		// 第一层过滤：去重过滤
		if processedDomains[domain] || existSubdomainsMap[domain] || slices.Contains(subDomainsBlackList, domain) {
			log.Debug("已存在子域名，跳过", zap.String("domain", domain))
			continue
		}

		// 第二层过滤：杂音域名过滤
		if len(domainsWhiteList) > 0 {
			topDomain := utils.GetTopDomain(domain)
			if !domainsWhiteListMap[topDomain] {
				log.Debug("过滤杂音域名", zap.String("domain", domain), zap.String("topDomain", topDomain))
				continue
			}
		}

		validDomains = append(validDomains, domains)
		processedDomains[domain] = true
		log.Debug("添加有效域名", zap.String("domain", domain))
	}

	return validDomains
}

// QueryDomain 通过ip138查询域名
func QueryDomain(ctx context.Context, domains []string) ([]Ip138DomainItem, error) {
	// 初始化重试管理器和自适应延迟控制器
	retryManager := NewRetryManager()
	delayController := NewAdaptiveDelayController()

	log.Infof("[ip138]开始查询ip138域名数据，查询语句:%s", domains)

	// 构建请求
	req := &core.Ip138DomainRequest{
		Domain: domains,
	}

	var rsp *core.Ip138DomainResponse
	var lastErr error

	// 使用重试机制执行请求
	operation := func() error {
		rsp = &core.Ip138DomainResponse{}
		if cfg.IsLocalClient() {
			// 本地化通过HTTP调用saas
			return core.HttpClient(http.MethodPost, "/api/v1//ip138/domain", req, rsp)
		} else {
			// 否则rpc微服务调用
			var err error
			rsp, err = core.GetProtoCoreClient().Ip138ByDomain(ctx, req, microx.SetTimeout(30, 30)...)
			return err
		}
	}

	lastErr = retryManager.executeWithRetry(ctx, operation)
	responseTime := time.Since(time.Now())

	// 更新自适应延迟
	delayController.updateDelay(responseTime, lastErr == nil, lastErr)

	// 处理请求结果
	if lastErr != nil {
		log.Errorf("[ip138]数据获取失败，查询语句:%s，错误:%v", domains, lastErr)
		return nil, lastErr
	}

	ip138DomainItems := make([]Ip138DomainItem, 0)
	for _, domain := range rsp.List {
		ip138DomainItems = append(ip138DomainItems, Ip138DomainItem{
			Domain: domain.Domain,
			Ips:    domain.Ips,
		})
	}

	log.Infof("[ip138]数据获取成功，查询语句:%s，响应时间:%v", domains, responseTime)
	return ip138DomainItems, nil
}
