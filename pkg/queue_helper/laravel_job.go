package queue_helper

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"strings"
	"sync"
	"time"

	goRedis "github.com/go-redis/redis/v8"
	"github.com/google/uuid"
)

// LaravelQueue 表示Laravel队列的配置
type LaravelQueue struct {
	Queue            string // 队列名称，例如 "queues:default"
	Connection       string // 连接名称，例如 "redis"
	Prefix           string // 键前缀，例如 "foradar_horizon"
	laravelJobClient *goRedis.Client
	once             sync.Once
}

// 创建新的Laravel队列实例
func NewLaravelQueue(queue string, connection string) *LaravelQueue {
	return &LaravelQueue{
		Queue:      queue,
		Connection: connection,
	}
}

// getClient 获取Redis客户端实例
func (l *LaravelQueue) getClient() *goRedis.Client {
	if l.laravelJobClient == nil {
		l.once.Do(func() {
			redisCfg := cfg.LoadRedis()
			log.Infof("[laravel_job] client is nil, loading config: %+v", redisCfg)

			// 创建Redis客户端，确保数据库索引为0
			client := redis.GetInstance(redisCfg)
			options := client.Options()
			if options.DB != 0 {
				log.Warnf("[laravel_job] Redis数据库索引不是0，正在切换到0索引 (当前: %d)", options.DB)
				newOptions := *options
				newOptions.DB = 0
				l.laravelJobClient = goRedis.NewClient(&newOptions)
				log.Infof("[laravel_job] 已创建新的Redis客户端，数据库索引: 0")
			} else {
				l.laravelJobClient = client
				log.Infof("[laravel_job] 使用现有Redis客户端，数据库索引: 0")
			}
		})
	}
	return l.laravelJobClient
}

// DispatchJob 将任务推送到Laravel队列
// queue: 队列名称，例如 "queues:default" 或 "default"
// jobClass: 任务类名，例如 "App\\Jobs\\CreateSensitiveKeywordJob"
// args: 任务参数，按照Laravel任务构造函数的参数顺序
func DispatchJob(ctx context.Context, queue string, jobClass string, args []interface{}) error {
	// 记录函数调用信息，用于调试
	log.Infof("[laravel_job] DispatchJob 被调用: queue=%s, jobClass=%s, args=%+v", queue, jobClass, args)

	// 处理队列名称
	queueName := queue
	if queue == "" {
		queueName = "default" // 默认队列名
	}

	// 保存原始队列名，用于job数据
	originalQueue := queueName

	// 确保队列名称格式正确
	if !strings.HasPrefix(queueName, "foradar_queues:") {
		queueName = "foradar_queues:" + queueName
	}

	// 创建Laravel队列实例
	laravelQueue := NewLaravelQueue(queueName, "redis")
	// 构建Laravel任务数据
	payload, _, err := BuildLaravelJobPayload(jobClass, args, originalQueue)
	if err != nil {
		log.Errorf("[laravel_job] build payload error: %v", err)
		return err
	}

	// 将任务推送到队列
	err = laravelQueue.pushToQueue(ctx, payload)
	if err != nil {
		return err
	}
	return nil

}

// BuildLaravelJobPayload 构建Laravel任务数据
func BuildLaravelJobPayload(jobClass string, args []interface{}, queueName string) (string, string, error) {
	// 获取当前时间
	now := time.Now()

	// 生成随机UUID作为任务ID
	jobUuid := generateUUID()

	// 使用新的序列化器生成PHP序列化的命令字符串
	commandStr := SerializePHPJob(jobClass, args)

	// Laravel任务数据结构 - 完全匹配Laravel格式
	job := map[string]interface{}{
		"uuid":          jobUuid,
		"displayName":   jobClass,
		"job":           "Illuminate\\Queue\\CallQueuedHandler@call",
		"maxTries":      nil, // 使用nil而不是null()
		"maxExceptions": nil, // 使用nil而不是null()
		"backoff":       nil, // 使用nil而不是null()
		"timeout":       nil, // 使用nil而不是null()
		"retryUntil":    nil, // 使用nil而不是null()
		"data": map[string]interface{}{
			"commandName": jobClass,
			"command":     commandStr,
		},
		"id":            jobUuid,
		"attempts":      1, // 初始为1，确保与Laravel预期一致
		"type":          "job",
		"pushedAt":      fmt.Sprintf("%.4f", float64(now.UnixNano())/1e9),
		"queue":         queueName,                // 使用传入的队列名
		"failOnTimeout": false,                    // Laravel内部使用
		"silenced":      false,                    // Laravel内部使用
		"tags":          map[string]interface{}{}, // Laravel内部使用的标签
	}

	// 序列化任务数据
	serialized, err := json.Marshal(job)
	if err != nil {
		return "", "", err
	}

	// 添加日志以便于调试
	log.Infof("[buildLaravelJobPayload] 任务: %s, 队列: %s, UUID: %s, Payload长度: %d字节",
		jobClass, queueName, jobUuid, len(serialized))

	// 输出部分payload内容用于调试
	if len(serialized) > 1000 {
		log.Infof("[buildLaravelJobPayload] Payload(截断): %s...", string(serialized)[:1000])
	} else {
		log.Infof("[buildLaravelJobPayload] Payload: %s", string(serialized))
	}

	return string(serialized), jobUuid, nil
}

// generateUUID 生成与Laravel兼容的UUID
func generateUUID() string {
	u, err := uuid.NewRandom()
	if err != nil {
		// 如果生成失败，使用当前时间戳作为备选
		return fmt.Sprintf("fallback-%d", time.Now().UnixNano())
	}
	return u.String()
}

// pushToQueue 将任务推送到队列
func (l *LaravelQueue) pushToQueue(ctx context.Context, payload string) error {
	client := l.getClient()

	// 使用RPUSH将任务添加到队列
	_, err := client.RPush(ctx, l.Queue, payload).Result()
	if err != nil {
		log.Errorf("[laravel_job] push to queue error: %v", err)
		return err
	}

	_, err1 := client.RPush(ctx, "foradar_queues:default:notify", 1).Result()
	if err1 != nil {
		log.Errorf("[laravel_job] push to queue error: %v", err1)
		return err1
	}

	return nil
}
