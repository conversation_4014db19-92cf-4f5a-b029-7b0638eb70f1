package queue_helper

import (
	"micro-service/middleware/mysql/scan_task"
)

type CreateLogEventPayload struct {
	UserId    uint64 `json:"user_id"`
	Content   string `json:"content"`
	Type      int    `json:"type"`
	Ip        string `json:"ip"`
	CompanyId uint64 `json:"company_id"`
	ModelType int    `json:"model_type"`
}

// DetectGolangCluesJob
type DetectGolangCluesJobPayload struct {
	ClueCompanyArray []string `json:"clue_company_array"`
	UserId           uint64   `json:"user_id"`
	CompanyId        uint64   `json:"company_id"`
	TaskId           uint64   `json:"task_id"`
	GroupId          uint64   `json:"group_id"`
	IsForce          bool     `json:"is_force"`
	PreIds           []uint64 `json:"pre_ids"`
	SceneIds         []uint64 `json:"scene_ids"`
}

// ExpandCluesJobPayload 扩展线索任务参数
type ExpandCluesJobPayload struct {
	UserId           uint64 `json:"user_id"`             // 用户ID
	ClueTaskId       uint64 `json:"clue_task_id"`        // 线索任务ID
	DetectTaskId     uint64 `json:"detect_task_id"`      // 测绘任务ID
	IsIntellectMode  int32  `json:"is_intellect_mode"`   // 是否智能模式
	IsFakeClueExpend int32  `json:"is_fake_clue_expend"` // 是否是仿冒钓鱼的线索扩展流程
	ClueType         int32  `json:"clue_type"`           // 线索类型
}

// UpdateAssetsLevelTaskPayload 任务数据
type UpdateAssetsLevelTaskPayload struct {
	UserID       uint64 `json:"user_id"`
	OperatorID   uint64 `json:"operator_id"`
	GroupID      uint64 `json:"group_id"`
	Flag         string `json:"flag"`
	DetectTaskId string `json:"detect_task_id"`
}

// UpdateAssetsLevelCodePayload 任务数据
type UpdateAssetsLevelCodePayload struct {
	UserID    uint64 `json:"user_id"`
	SubDomain string `json:"sub_domain"`
	Protocol  string `json:"protocol"`
	AssetId   string `json:"asset_id"`
	Url       string `json:"url"`
}

// UpdateIpAndPortOnlineStateJobPayload 更新IP端口在线状态任务数据
type UpdateIpAndPortOnlineStateJobPayload struct {
	UserId int `json:"user_id"`
	TaskId int `json:"task_id"`
}

// ShadowAssetsTagJobPayload 影子资产标记任务数据
type ShadowAssetsTagJobPayload struct {
	UserId       int `json:"user_id"`
	TaskId       int `json:"task_id"`
	DetectTaskId int `json:"detect_task_id"`
}

// UpdateIpAndPortOfflineStateJobPayload 任务载荷
type UpdateIpAndPortOfflineStateJobPayload struct {
	UserId int64                `json:"user_id"` // 用户ID
	TaskId uint64               `json:"task_id"` // 任务ID
	Task   *scan_task.ScanTasks `json:"task"`    // 任务对象
}

// // RecommendAssetJobPayload 推荐资产任务参数
// type RecommendAssetJobPayload struct {
// 	TaskName     string   `json:"task_name"`      // 任务名称
// 	Flag         string   `json:"flag"`           // 任务标识
// 	UserId       uint64   `json:"user_id"`        // 用户ID
// 	ClueIds      []uint64 `json:"clue_ids"`       // 线索ID列表
// 	OperatorId   uint64   `json:"operator_id"`    // 操作者ID
// 	DetectTaskId uint64   `json:"detect_task_id"` // 测绘任务ID
// 	IsForce      bool     `json:"is_force"`       // 是否强制执行
// }

// ConfirmGeneralCluesJobPayload 确认线索总库任务参数
type ConfirmGeneralCluesJobPayload struct {
	UserId  uint64 `json:"user_id"`  // 用户ID
	GroupId uint64 `json:"group_id"` // 分组ID
}
type TaskIdPayload struct {
	TaskId uint64 `json:"task_id"` // 扫描任务ID，scan_tasks表的id
	UserId uint64 `json:"user_id"` // 用户ID
}

// StatisticsLoginAssetsJobPayload 统计登录页面资产任务数据
type StatisticsLoginAssetsJobPayload struct {
	UserId uint64 `json:"user_id"` // 用户ID
	TaskId uint64 `json:"task_id"` // 任务ID
}

// TagAssetsJobPayload 标记资产tags任务数据
type TagAssetsJobPayload struct {
	UserId uint64 `json:"user_id"` // 用户ID
	TaskId uint64 `json:"task_id"` // 任务ID
}

// UpdateIpAndPortAssetCompanyNameJobPayload 更新IP端口资产公司名称任务数据
type UpdateIpAndPortAssetCompanyNameJobPayload struct {
	UserId    uint64 `json:"user_id"`    // 用户ID
	TaskId    uint64 `json:"task_id"`    // 任务ID
	CompanyId uint64 `json:"company_id"` // 公司ID
}

// UpdateRiskIpCountJobPayload 更新风险IP数量任务数据
type UpdateRiskIpCountJobPayload struct {
	UserId     uint64 `json:"user_id"`      // 用户ID
	IsTestUnit bool   `json:"is_test_unit"` // 是否为测试单元
}

// UpdateRiskTypeAssetJobPayload 更新风险类型资产任务数据
type UpdateRiskTypeAssetJobPayload struct {
	UserId       uint64      `json:"user_id"`        // 用户ID
	TaskId       interface{} `json:"task_id"`        // 任务ID，可能是单个uint64或[]uint64
	CompanyId    uint64      `json:"company_id"`     // 公司ID
	DetectTaskId *uint64     `json:"detect_task_id"` // 测绘任务ID，可为空
}

// TaskParam 任务参数
type TaskParam struct {
	Name                string `json:"name"`                 // 任务名称
	Bandwidth           int    `json:"bandwidth"`            // 扫描带宽
	ProtocolConcurrency int    `json:"protocol_concurrency"` // 并发数
	PingSwitch          int    `json:"ping_switch"`          // Ping开关
	WebLogoSwitch       int    `json:"web_logo_switch"`      // 网站Logo开关
	ScanType            int    `json:"scan_type"`            // 扫描类型
	IpType              int    `json:"ip_type"`              // IP类型
	PortGroupIds        uint64 `json:"port_group_ids"`       // 端口组ID列表
}

// StandingAssetsScanJobPayload 台账资产扫描任务数据
type StandingAssetsScanJobPayload struct {
	Ips        []string `json:"ips"`        // IP列表
	UserId     uint64   `json:"user_id"`    // 用户ID
	CompanyId  string   `json:"company_id"` // 公司ID
	ScanParams struct {
		TaskParam           TaskParam `json:"task_param"`            // 任务参数
		IsDefinePort        int       `json:"is_define_port"`        // 是否自定义端口
		TaskFrom            int       `json:"task_from"`             // 任务来源
		DefinePorts         []uint64  `json:"define_ports"`          // 自定义端口列表
		DefinePortProtocols []uint64  `json:"define_port_protocols"` // 自定义端口协议列表
	} `json:"scan_params"` // 扫描参数
	OpUserId  *uint64  `json:"op_user_id"` // 操作用户ID，可选
	DomainArr []string `json:"domain_arr"` // 域名列表
}

// ExtractAssetCluesJobPayload 提取资产线索任务参数
type ExtractAssetCluesJobPayload struct {
	UserID              uint64                 `json:"user_id"`
	Param               map[string]interface{} `json:"param"`
	DetectAssetsTasksID uint64                 `json:"detect_assets_tasks_id,omitempty"`
	DetectAssetsGroupID uint64                 `json:"detect_assets_group_id,omitempty"`
}

// AssetsImportDataJobPayload 资产数据导入任务参数
type AssetsImportDataJobPayload struct {
	AssetsData map[string][]map[string]interface{} `json:"assets_data"` // IP -> assets array (与PHP格式一致)
	UserId     uint64                              `json:"user_id"`     // 用户ID
	Status     string                              `json:"status"`      // 状态
	CompanyId  uint64                              `json:"company_id"`  // 公司ID，可为空
}

// RecommendAssetJobPayload 推荐资产任务参数
type RecommendAssetJobPayload struct {
	RecommendRecordId string `json:"recommend_record_id"` // foradar_recommend_record表的id
	UserId            uint64 `json:"user_id"`             // 用户ID
	DetectTaskId      uint64 `json:"detect_task_id"`      // 测绘任务ID
}

// OneForAllJobPayload 查询OneForAll任务参数
type OneForAllJobPayload struct {
	Domain string `json:"domain"`
	Title  string `json:"title"`
	UserId string `json:"user_id"`
}

// TableAssetsDomainsSyncJobPayload 台账同步域名任务
type TableAssetsDomainsSyncJobPayload struct {
	UserID                     uint64   `json:"user_id"`
	TaskID                     []uint64 `json:"task_id"` // 任务ID
	From                       int      `json:"from"`
	GroupID                    []uint64 `json:"groupId"`
	DomainTaskID               uint64   `json:"domain_task_id"`
	ImportDomains              []string `json:"import_domains"`
	Flag                       string   `json:"flag"`
	DetectTaskID               uint64   `json:"detect_task_id"`
	OrganizationDiscoverTaskID uint64   `json:"organization_discover_task_id"`
	OrganizationID             uint64   `json:"organization_id"`
	DetectTaskCompanyName      []string `json:"detect_task_company_name"`
	BrustDomainDetectTaskID    uint64   `json:"brust_domain_detect_task_id"`
	Ips                        []string `json:"-"` // 内部使用，无需传入
}

// DetectDirectOperateJobPayload 直接操作任务参数
type DetectDirectOperateJobPayload struct {
	UserId       uint64 `json:"user_id"`        // 用户ID
	DetectTaskId uint64 `json:"detect_task_id"` // 测绘任务ID
	TaskName     string `json:"task_name"`      // 任务名称
}

// CreateDetectTaskJobPayload 创建测绘任务参数
type CreateDetectTaskJobPayload struct {
	UserId uint64 `json:"user_id"` // 用户ID
}

// ICPQueryJobPayload ICP查询任务载荷
type ICPQueryJobPayload struct {
	ClueID      uint64 `json:"clue_id"`
	QueryType   string `json:"query_type"` // "domain", "icp", "subdomain"
	QueryValue  string `json:"query_value"`
	UserId      uint64 `json:"user_id"`
	GroupId     uint64 `json:"group_id"`
	IsSubdomain bool   `json:"is_subdomain"`
}

// NewDetectGolangCluesJobPayload 创建 DetectGolangCluesJobPayload 实例
func NewDetectGolangCluesJobPayload(clueCompanyArray []string, userId uint64, companyId uint64, taskId uint64, groupId uint64, isForce bool, preIds []uint64, sceneIds []uint64) *DetectGolangCluesJobPayload {
	return &DetectGolangCluesJobPayload{
		ClueCompanyArray: clueCompanyArray,
		UserId:           userId,
		CompanyId:        companyId,
		TaskId:           taskId,
		GroupId:          groupId,
		IsForce:          isForce,
		PreIds:           preIds,
		SceneIds:         sceneIds,
	}
}

func NewCreateLogEventPayload(userId uint64, content string, ip string, companyId uint64, modelType int, t int) *CreateLogEventPayload {
	return &CreateLogEventPayload{
		UserId:    userId,
		Content:   content,
		Ip:        ip,
		CompanyId: companyId,
		ModelType: modelType,
		Type:      t,
	}
}

func NewUpdateAssetsLevelTask(userID, operatorID, groupID uint64, flag string, detectTaskId string) *UpdateAssetsLevelTaskPayload {
	return &UpdateAssetsLevelTaskPayload{
		UserID:       userID,
		OperatorID:   operatorID,
		GroupID:      groupID,
		Flag:         flag,
		DetectTaskId: detectTaskId,
	}
}
