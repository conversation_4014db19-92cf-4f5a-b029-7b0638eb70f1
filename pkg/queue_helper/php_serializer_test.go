package queue_helper

import (
	"bytes"
	"strings"
	"testing"

	"micro-service/pkg/cfg"
	"micro-service/pkg/log"

	"github.com/stretchr/testify/assert"
)

// 初始化测试环境
func init() {
	cfg.InitLoadCfg()
	log.Init()
}

func TestDefaultPHPJobSerializer_Serialize(t *testing.T) {
	t.Run("serialize with string args", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\TestJob",
			PropertyNames: []string{"user_id", "message"},
		}

		args := []interface{}{123, "hello world"}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "App\\Jobs\\TestJob")
		assert.Contains(t, result, "user_id")
		assert.Contains(t, result, "message")
		assert.Contains(t, result, "hello world")

		// 验证PHP序列化格式
		assert.True(t, strings.HasPrefix(result, "O:"))
		assert.True(t, strings.HasSuffix(result, "}"))
	})

	t.Run("serialize with empty args", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\EmptyJob",
			PropertyNames: []string{},
		}

		args := []interface{}{}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "App\\Jobs\\EmptyJob")
		assert.Contains(t, result, ":0:{}")
	})

	t.Run("serialize with nil args", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\NilJob",
			PropertyNames: []string{"data"},
		}

		args := []interface{}{nil}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "App\\Jobs\\NilJob")
		assert.Contains(t, result, "data")
		assert.Contains(t, result, "N;") // PHP null
	})

	t.Run("serialize with boolean args", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\BoolJob",
			PropertyNames: []string{"is_active", "is_disabled"},
		}

		args := []interface{}{true, false}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "is_active")
		assert.Contains(t, result, "is_disabled")
		assert.Contains(t, result, "b:1;") // PHP true
		assert.Contains(t, result, "b:0;") // PHP false
	})

	t.Run("serialize with numeric args", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\NumericJob",
			PropertyNames: []string{"int_val", "float_val", "uint_val"},
		}

		args := []interface{}{42, 3.14, uint64(100)}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "int_val")
		assert.Contains(t, result, "float_val")
		assert.Contains(t, result, "uint_val")
		assert.Contains(t, result, "i:42;")
		assert.Contains(t, result, "d:3.14;")
		assert.Contains(t, result, "i:100;")
	})

	t.Run("serialize with array args", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\ArrayJob",
			PropertyNames: []string{"items"},
		}

		args := []interface{}{[]string{"item1", "item2", "item3"}}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "items")
		assert.Contains(t, result, "a:3:{") // PHP array with 3 elements
		assert.Contains(t, result, "item1")
		assert.Contains(t, result, "item2")
		assert.Contains(t, result, "item3")
	})

	t.Run("serialize with map args", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\MapJob",
			PropertyNames: []string{"config"},
		}

		config := map[string]interface{}{
			"timeout": 30,
			"retry":   true,
			"name":    "test_config",
		}
		args := []interface{}{config}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "config")
		assert.Contains(t, result, "a:3:{") // PHP array with 3 elements
		assert.Contains(t, result, "timeout")
		assert.Contains(t, result, "retry")
		assert.Contains(t, result, "test_config")
	})

	t.Run("serialize with more args than property names", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\ExtraArgsJob",
			PropertyNames: []string{"user_id"},
		}

		args := []interface{}{123, "extra_arg1", "extra_arg2"}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "user_id")
		assert.Contains(t, result, "param1") // 自动生成的参数名
		assert.Contains(t, result, "param2") // 自动生成的参数名
		assert.Contains(t, result, "extra_arg1")
		assert.Contains(t, result, "extra_arg2")
	})

	t.Run("serialize with complex nested data", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\ComplexJob",
			PropertyNames: []string{"data"},
		}

		complexData := map[string]interface{}{
			"users": []interface{}{
				map[string]interface{}{"id": 1, "name": "Alice"},
				map[string]interface{}{"id": 2, "name": "Bob"},
			},
			"settings": map[string]interface{}{
				"enabled": true,
				"count":   10,
			},
		}
		args := []interface{}{complexData}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "data")
		assert.Contains(t, result, "users")
		assert.Contains(t, result, "settings")
		assert.Contains(t, result, "Alice")
		assert.Contains(t, result, "Bob")
	})
}

func TestSerializeValue(t *testing.T) {
	t.Run("serialize nil", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, nil)
		result := buffer.String()
		assert.Equal(t, "N;", result)
	})

	t.Run("serialize string", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, "test string")
		result := buffer.String()
		assert.Equal(t, "s:11:\"test string\";", result)
	})

	t.Run("serialize empty string", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, "")
		result := buffer.String()
		assert.Equal(t, "s:0:\"\";", result)
	})

	t.Run("serialize integer", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, 42)
		result := buffer.String()
		assert.Equal(t, "i:42;", result)
	})

	t.Run("serialize negative integer", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, -123)
		result := buffer.String()
		assert.Equal(t, "i:-123;", result)
	})

	t.Run("serialize uint64", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, uint64(18446744073709551615))
		result := buffer.String()
		assert.Equal(t, "i:18446744073709551615;", result)
	})

	t.Run("serialize float", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, 3.14159)
		result := buffer.String()
		assert.Equal(t, "d:3.14159;", result)
	})

	t.Run("serialize float as integer", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, 42.0)
		result := buffer.String()
		assert.Equal(t, "i:42;", result) // 整数值的浮点数应该序列化为整数
	})

	t.Run("serialize boolean true", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, true)
		result := buffer.String()
		assert.Equal(t, "b:1;", result)
	})

	t.Run("serialize boolean false", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, false)
		result := buffer.String()
		assert.Equal(t, "b:0;", result)
	})

	t.Run("serialize empty slice", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, []string{})
		result := buffer.String()
		assert.Equal(t, "a:0:{}", result)
	})

	t.Run("serialize slice with elements", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, []int{1, 2, 3})
		result := buffer.String()
		assert.Equal(t, "a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}", result)
	})

	t.Run("serialize empty map", func(t *testing.T) {
		var buffer bytes.Buffer
		serializeValue(&buffer, map[string]interface{}{})
		result := buffer.String()
		assert.Equal(t, "a:0:{}", result)
	})

	t.Run("serialize map with elements", func(t *testing.T) {
		var buffer bytes.Buffer
		data := map[string]interface{}{
			"key1": "value1",
			"key2": 42,
		}
		serializeValue(&buffer, data)
		result := buffer.String()

		// 由于map的遍历顺序不确定，我们检查包含的内容
		assert.Contains(t, result, "a:2:{")
		assert.Contains(t, result, "key1")
		assert.Contains(t, result, "value1")
		assert.Contains(t, result, "key2")
		assert.Contains(t, result, "i:42;")
		assert.True(t, strings.HasSuffix(result, "}"))
	})
}

func TestPHPJobRegistry(t *testing.T) {
	t.Run("register and get php job", func(t *testing.T) {
		jobClass := "App\\Jobs\\TestRegistryJob"
		propertyNames := []string{"user_id", "task_id"}

		RegisterPHPJob(jobClass, propertyNames)

		serializer := GetPHPJobSerializer(jobClass)
		assert.NotNil(t, serializer)

		// 验证序列化器的属性
		defaultSerializer, ok := serializer.(*DefaultPHPJobSerializer)
		assert.True(t, ok)
		assert.Equal(t, jobClass, defaultSerializer.JobClass)
		assert.Equal(t, propertyNames, defaultSerializer.PropertyNames)
	})

	t.Run("register custom php job", func(t *testing.T) {
		jobClass := "App\\Jobs\\CustomJob"
		customSerializer := &DefaultPHPJobSerializer{
			JobClass:      jobClass,
			PropertyNames: []string{"custom_prop"},
		}

		RegisterCustomPHPJob(jobClass, customSerializer)

		retrievedSerializer := GetPHPJobSerializer(jobClass)
		assert.NotNil(t, retrievedSerializer)
		assert.Same(t, customSerializer, retrievedSerializer)
	})

	t.Run("get non-existent job serializer", func(t *testing.T) {
		jobClass := "App\\Jobs\\NonExistentJob"

		serializer := GetPHPJobSerializer(jobClass)
		assert.NotNil(t, serializer)

		// 应该返回默认序列化器
		defaultSerializer, ok := serializer.(*DefaultPHPJobSerializer)
		assert.True(t, ok)
		assert.Equal(t, jobClass, defaultSerializer.JobClass)
		assert.Equal(t, []string{}, defaultSerializer.PropertyNames)
	})

	t.Run("concurrent registration", func(t *testing.T) {
		numJobs := 50
		done := make(chan bool, numJobs)

		// 并发注册多个任务
		for i := 0; i < numJobs; i++ {
			go func(id int) {
				jobClass := "App\\Jobs\\ConcurrentJob" + string(rune(id))
				propertyNames := []string{"prop" + string(rune(id))}

				RegisterPHPJob(jobClass, propertyNames)

				// 验证注册成功
				serializer := GetPHPJobSerializer(jobClass)
				assert.NotNil(t, serializer)

				done <- true
			}(i)
		}

		// 等待所有注册完成
		for i := 0; i < numJobs; i++ {
			<-done
		}

		// 验证所有任务都已注册
		for i := 0; i < numJobs; i++ {
			jobClass := "App\\Jobs\\ConcurrentJob" + string(rune(i))
			serializer := GetPHPJobSerializer(jobClass)
			assert.NotNil(t, serializer)
		}
	})

	t.Run("override existing job", func(t *testing.T) {
		jobClass := "App\\Jobs\\OverrideJob"

		// 第一次注册
		RegisterPHPJob(jobClass, []string{"prop1"})
		serializer1 := GetPHPJobSerializer(jobClass)

		// 第二次注册（覆盖）
		RegisterPHPJob(jobClass, []string{"prop2", "prop3"})
		serializer2 := GetPHPJobSerializer(jobClass)

		// 验证已被覆盖
		assert.NotSame(t, serializer1, serializer2)

		defaultSerializer2, ok := serializer2.(*DefaultPHPJobSerializer)
		assert.True(t, ok)
		assert.Equal(t, []string{"prop2", "prop3"}, defaultSerializer2.PropertyNames)
	})
}

func TestSerializePHPJob(t *testing.T) {
	t.Run("serialize registered job", func(t *testing.T) {
		jobClass := "App\\Jobs\\SerializeTestJob"
		propertyNames := []string{"user_id", "message"}

		RegisterPHPJob(jobClass, propertyNames)

		args := []interface{}{123, "test message"}
		result := SerializePHPJob(jobClass, args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, jobClass)
		assert.Contains(t, result, "user_id")
		assert.Contains(t, result, "message")
		assert.Contains(t, result, "test message")
	})

	t.Run("serialize unregistered job", func(t *testing.T) {
		jobClass := "App\\Jobs\\UnregisteredJob"
		args := []interface{}{456, "unregistered"}

		result := SerializePHPJob(jobClass, args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, jobClass)
		assert.Contains(t, result, "param0") // 自动生成的参数名
		assert.Contains(t, result, "param1")
		assert.Contains(t, result, "unregistered")
	})

	t.Run("serialize with empty args", func(t *testing.T) {
		jobClass := "App\\Jobs\\EmptyArgsJob"
		args := []interface{}{}

		result := SerializePHPJob(jobClass, args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, jobClass)
		assert.Contains(t, result, ":0:{}")
	})

	t.Run("serialize with nil args", func(t *testing.T) {
		jobClass := "App\\Jobs\\NilArgsJob"
		var args []interface{} = nil

		result := SerializePHPJob(jobClass, args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, jobClass)
		assert.Contains(t, result, ":0:{}")
	})
}

func TestEdgeCases(t *testing.T) {
	t.Run("empty job class name", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "",
			PropertyNames: []string{"prop"},
		}

		args := []interface{}{"test"}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.True(t, strings.HasPrefix(result, "O:0:\"\""))
	})

	t.Run("very long job class name", func(t *testing.T) {
		longJobClass := strings.Repeat("VeryLongJobClassName", 100)
		serializer := &DefaultPHPJobSerializer{
			JobClass:      longJobClass,
			PropertyNames: []string{"prop"},
		}

		args := []interface{}{"test"}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, longJobClass)
	})

	t.Run("special characters in job class", func(t *testing.T) {
		jobClass := "App\\Jobs\\测试任务🚀"
		serializer := &DefaultPHPJobSerializer{
			JobClass:      jobClass,
			PropertyNames: []string{"测试属性"},
		}

		args := []interface{}{"测试值💻"}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, jobClass)
		assert.Contains(t, result, "测试属性")
		assert.Contains(t, result, "测试值💻")
	})

	t.Run("very long string argument", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\LongStringJob",
			PropertyNames: []string{"long_text"},
		}

		longString := strings.Repeat("This is a very long string. ", 1000)
		args := []interface{}{longString}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "long_text")
		assert.Contains(t, result, longString)
	})

	t.Run("deeply nested data structures", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\DeepNestedJob",
			PropertyNames: []string{"nested_data"},
		}

		// 创建深度嵌套的数据结构
		deepData := map[string]interface{}{
			"level1": map[string]interface{}{
				"level2": map[string]interface{}{
					"level3": []interface{}{
						map[string]interface{}{
							"level4": "deep value",
						},
					},
				},
			},
		}

		args := []interface{}{deepData}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "nested_data")
		assert.Contains(t, result, "level1")
		assert.Contains(t, result, "level2")
		assert.Contains(t, result, "level3")
		assert.Contains(t, result, "level4")
		assert.Contains(t, result, "deep value")
	})

	t.Run("mixed type array", func(t *testing.T) {
		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\MixedArrayJob",
			PropertyNames: []string{"mixed_array"},
		}

		mixedArray := []interface{}{
			"string",
			42,
			true,
			nil,
			[]string{"nested", "array"},
			map[string]interface{}{"nested": "map"},
		}

		args := []interface{}{mixedArray}
		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "mixed_array")
		assert.Contains(t, result, "string")
		assert.Contains(t, result, "i:42;")
		assert.Contains(t, result, "b:1;")
		assert.Contains(t, result, "N;")
		assert.Contains(t, result, "nested")
	})

	t.Run("large number of arguments", func(t *testing.T) {
		// 创建大量属性名
		propertyNames := make([]string, 1000)
		args := make([]interface{}, 1000)
		for i := 0; i < 1000; i++ {
			propertyNames[i] = "prop" + string(rune(i))
			args[i] = "value" + string(rune(i))
		}

		serializer := &DefaultPHPJobSerializer{
			JobClass:      "App\\Jobs\\ManyArgsJob",
			PropertyNames: propertyNames,
		}

		result := serializer.Serialize(args)

		assert.NotEmpty(t, result)
		assert.Contains(t, result, "App\\Jobs\\ManyArgsJob")
		assert.Contains(t, result, ":1000:{") // 1000个参数
	})
}
