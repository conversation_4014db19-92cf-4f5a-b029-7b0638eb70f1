package queue_helper

import (
	"bytes"
	"fmt"
	"micro-service/pkg/log"
	"reflect"
	"strings"
	"sync"
)

// PHPJobSerializer PHP任务序列化器接口
type PHPJobSerializer interface {
	// Serialize 序列化PHP任务
	Serialize(args []interface{}) string
}

// DefaultPHPJobSerializer 默认的PHP任务序列化器
type DefaultPHPJobSerializer struct {
	JobClass      string
	PropertyNames []string
}

// Serialize 序列化PHP任务
func (s *DefaultPHPJobSerializer) Serialize(args []interface{}) string {
	// 获取类名长度
	classNameLength := len(s.JobClass)

	// 构建序列化字符串的前半部分
	phpCode := fmt.Sprintf("O:%d:\"%s\":%d:{", classNameLength, s.JobClass, len(args))

	// 使用字节数组处理每个参数，确保空字节正确
	var buffer bytes.Buffer
	buffer.WriteString(phpCode)

	for i, arg := range args {
		propName := ""
		if i < len(s.PropertyNames) {
			propName = s.PropertyNames[i]
		} else {
			propName = fmt.Sprintf("param%d", i)
		}

		// 写入属性名部分 - 私有属性格式 \0*\0propertyName
		// 计算实际长度：3（\0*\0）+ len(propName)
		actualLength := 3 + len(propName)
		buffer.WriteString(fmt.Sprintf("s:%d:\"", actualLength))
		buffer.WriteByte(0)   // 空字节
		buffer.WriteByte('*') // 星号
		buffer.WriteByte(0)   // 空字节
		buffer.WriteString(propName)
		buffer.WriteString("\";")

		// 根据参数类型添加不同的序列化格式
		serializeValue(&buffer, arg)
	}

	// 闭合序列化字符串
	buffer.WriteString("}")

	// 获取最终结果
	result := buffer.String()

	// 记录序列化结果的十六进制表示（用于调试）
	// 始终记录十六进制表示，用于调试
	if log.GetLogger() != nil {
		hexBytes := make([]string, len(result))
		for i, b := range []byte(result) {
			hexBytes[i] = fmt.Sprintf("%02x", b)
		}
		log.Debugf("[phpSerializer] 序列化结果的十六进制表示: %s", strings.Join(hexBytes, " "))
	}

	return result
}

// 序列化单个值
func serializeValue(buffer *bytes.Buffer, value interface{}) {
	if value == nil {
		buffer.WriteString("N;")
		return
	}

	// 使用反射处理不同类型
	v := reflect.ValueOf(value)
	switch v.Kind() {
	case reflect.String:
		str := value.(string)
		buffer.WriteString(fmt.Sprintf("s:%d:\"%s\";", len(str), str))
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		buffer.WriteString(fmt.Sprintf("i:%d;", v.Int()))
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		buffer.WriteString(fmt.Sprintf("i:%d;", v.Uint()))
	case reflect.Float32, reflect.Float64:
		floatVal := v.Float()
		// 如果是整数值的浮点数，转为整数
		if floatVal == float64(int(floatVal)) {
			buffer.WriteString(fmt.Sprintf("i:%d;", int(floatVal)))
		} else {
			buffer.WriteString(fmt.Sprintf("d:%g;", floatVal))
		}
	case reflect.Bool:
		if v.Bool() {
			buffer.WriteString("b:1;")
		} else {
			buffer.WriteString("b:0;")
		}
	case reflect.Map:
		// 处理PHP数组
		serializeMap(buffer, v)
	case reflect.Slice, reflect.Array:
		// 处理PHP索引数组
		serializeArray(buffer, v)
	default:
		// 未知类型，序列化为null
		buffer.WriteString("N;")
	}
}

// 序列化Map为PHP关联数组
func serializeMap(buffer *bytes.Buffer, v reflect.Value) {
	keys := v.MapKeys()
	buffer.WriteString(fmt.Sprintf("a:%d:{", len(keys)))

	for _, key := range keys {
		// 序列化键
		serializeValue(buffer, key.Interface())
		// 序列化值
		serializeValue(buffer, v.MapIndex(key).Interface())
	}

	buffer.WriteString("}")
}

// 序列化数组为PHP索引数组
func serializeArray(buffer *bytes.Buffer, v reflect.Value) {
	length := v.Len()
	buffer.WriteString(fmt.Sprintf("a:%d:{", length))

	for i := 0; i < length; i++ {
		// 序列化索引
		buffer.WriteString(fmt.Sprintf("i:%d;", i))
		// 序列化值
		serializeValue(buffer, v.Index(i).Interface())
	}

	buffer.WriteString("}")
}

// PHPJobRegistry PHP任务注册表
type PHPJobRegistry struct {
	serializers map[string]PHPJobSerializer
	mu          sync.RWMutex
}

// 全局PHP任务注册表实例
var globalPHPJobRegistry = &PHPJobRegistry{
	serializers: make(map[string]PHPJobSerializer),
}

// RegisterPHPJob 注册PHP任务序列化器---在php的job中 protectd的public的序列化不一样，现在模拟都是 protected的清空
func RegisterPHPJob(jobClass string, propertyNames []string) {
	globalPHPJobRegistry.mu.Lock()
	defer globalPHPJobRegistry.mu.Unlock()

	globalPHPJobRegistry.serializers[jobClass] = &DefaultPHPJobSerializer{
		JobClass:      jobClass,
		PropertyNames: propertyNames,
	}

	// 添加对logger是否为nil的检查，防止在日志系统初始化前调用导致空指针异常
	if log.GetLogger() != nil {
		log.Infof("[PHPJobRegistry] 已注册PHP任务: %s, 属性: %v", jobClass, propertyNames)
	}
}

// RegisterCustomPHPJob 注册自定义PHP任务序列化器
func RegisterCustomPHPJob(jobClass string, serializer PHPJobSerializer) {
	globalPHPJobRegistry.mu.Lock()
	defer globalPHPJobRegistry.mu.Unlock()

	globalPHPJobRegistry.serializers[jobClass] = serializer

	// 添加对logger是否为nil的检查，防止在日志系统初始化前调用导致空指针异常
	if log.GetLogger() != nil {
		log.Infof("[PHPJobRegistry] 已注册自定义PHP任务序列化器: %s", jobClass)
	}
}

// GetPHPJobSerializer 获取PHP任务序列化器
func GetPHPJobSerializer(jobClass string) PHPJobSerializer {
	globalPHPJobRegistry.mu.RLock()
	defer globalPHPJobRegistry.mu.RUnlock()

	serializer, ok := globalPHPJobRegistry.serializers[jobClass]
	if !ok {
		// 如果没有找到对应的序列化器，创建一个默认的
		if log.GetLogger() != nil {
			log.Warnf("[PHPJobRegistry] 未找到任务 %s 的序列化器，使用默认序列化器", jobClass)
		}
		return &DefaultPHPJobSerializer{
			JobClass:      jobClass,
			PropertyNames: []string{},
		}
	}

	return serializer
}

// SerializePHPJob 序列化PHP任务
func SerializePHPJob(jobClass string, args []interface{}) string {
	serializer := GetPHPJobSerializer(jobClass)
	result := serializer.Serialize(args)

	// 添加对logger是否为nil的检查，防止在日志系统初始化前调用导致空指针异常
	if log.GetLogger() != nil {
		log.Infof("[SerializePHPJob] 序列化任务 %s 参数: %+v", jobClass, args)
		log.Infof("[SerializePHPJob] 序列化结果: %s", result)
	}

	return result
}
