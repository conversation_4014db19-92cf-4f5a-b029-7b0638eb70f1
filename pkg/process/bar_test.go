package process

import (
	"testing"
	"time"
)

func TestGetPercent(t *testing.T) {
	bar := NewBar(50, 100)
	expectedPercent := 50
	actualPercent := bar.GetPercent()
	if actualPercent != expectedPercent {
		t.<PERSON><PERSON><PERSON>("期望百分比为 %d，实际为 %d", expectedPercent, actualPercent)
	}
}

func TestGetTime(t *testing.T) {
	bar := NewBar(0, 100)
	bar.StartAt = time.Now().Add(-time.Second * 61) // 模拟开始时间
	expectedTime := "1m 1s"
	actualTime := bar.GetTime()
	if actualTime != expectedTime {
		t.Errorf("期望时间为 %s，实际为 %s", expectedTime, actualTime)
	}
}

func TestReset(t *testing.T) {
	bar := NewBar(50, 100)
	resetCalled := false
	bar.Reset(func() {
		resetCalled = true
	})
	if bar.Current != 0 {
		t.Errorf("期望Current重置为0，实际为 %d", bar.Current)
	}
	if !resetCalled {
		t.<PERSON><PERSON>rf("Reset回调未被调用")
	}
}

func TestAdd(t *testing.T) {
	bar := NewBar(50, 100)
	addCalled := false
	bar.Add(10, func() {
		addCalled = true
	})
	if bar.Current != 60 {
		t.Errorf("期望Current增加到60，实际为 %d", bar.Current)
	}
	if !addCalled {
		t.Errorf("Add回调未被调用")
	}
}

func TestFinish(t *testing.T) {
	bar := NewBar(50, 100)
	finishCalled := false
	bar.Finish(func() {
		finishCalled = true
	})
	if bar.Current != 100 {
		t.Errorf("期望Current设置为Total(100)，实际为 %d", bar.Current)
	}
	if !finishCalled {
		t.Errorf("Finish回调未被调用")
	}
}

func TestNewBarWithGraph(t *testing.T) {
	bar := NewBarWithGraph(0, 100, "*")
	if bar.graph != "*" {
		t.Errorf("期望graph为*，实际为 %s", bar.graph)
	}
}
