package fofa

import (
	"context"
	"fmt"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	"net/http"
	"time"

	pb "micro-service/coreService/proto"

	"github.com/juju/ratelimit"
)

var (
	rateLimiter *ratelimit.Bucket // 全局QPS限流器
)

func init() {
	// 创建限流器，每2秒允许1个请求，桶容量为1
	rateLimiter = ratelimit.NewBucketWithQuantum(time.Second*2, 1, 1)
}

func HunterQuery(ctx context.Context, hunterQuerystr string, start string) ([]*pb.Asset, error) {
	// 限流
	waitTime := rateLimiter.Take(1)
	if waitTime > 0 {
		log.Warnf("[hunter]全局限流控制，等待时间:%v，当前QPS限制:%d", waitTime, 1)
		time.Sleep(waitTime)
	}

	today := time.Now()
	var result *pb.FofaQueryResponse
	var err error
	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		url := "/api/v1/hunter/query"
		body := map[string]interface{}{
			"search":     hunterQuerystr,
			"page":       1,
			"page_size":  100,
			"start_time": start,
			"end_time":   today.Format(time.DateOnly),
		}
		err = pb.HttpClient(http.MethodPost, url, body, &result)
	} else {
		req := &pb.HunterQueryRequest{
			Search:    hunterQuerystr,
			Page:      1,
			PageSize:  100,
			StartTime: start,
			EndTime:   today.Format(time.DateOnly),
		}
		// 调用hunter查询，此处已经在非本地化判断逻辑中
		result, err = pb.GetProtoCoreClient().HunterQuery(context.Background(), req, microx.SetTimeout(30, 29)...)
	}
	if err != nil {
		return nil, err
	}
	if result == nil || result.Sdata == nil {
		return nil, fmt.Errorf("get hunter query result response is nil")
	}
	return result.Sdata, nil
}
