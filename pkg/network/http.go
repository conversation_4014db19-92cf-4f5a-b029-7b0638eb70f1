package network

import (
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"

	"micro-service/middleware/redis"
	"micro-service/pkg/cache"
	"micro-service/pkg/log"
)

const (
	StatusOriginUnreachable = 523 // Cloudflare定义的"Origin Unreachable"
	StatusLoopDetected      = 508 // "Loop Detected"
)

// GetUrlStatusCode 获取URL的HTTP状态码
// 参数：
// - url: 要检查的URL
// - id: 资产ID，用于日志记录
// 返回：HTTP状态码
func GetUrlStatusCode(url string, id string) int {
	ctx := context.Background()

	// 记录开始
	log.Debugf("[GetUrlStatusCode]开始获取状态码: url=%s, id=%s", url, id)

	// 缓存读取优化
	var statusCode int
	cacheKey := cache.GetCacheKey("status_code", url)
	if redis.GetCache(cacheKey, &statusCode) {
		log.Debugf("[GetUrlStatusCode]缓存命中: url=%s, id=%s, statusCode=%d", url, id, statusCode)
		return statusCode
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 8 * time.Second, // 总超时控制
		Transport: &http.Transport{
			DisableKeepAlives: true,
			// TLSClientConfig:   &tls.Config{InsecureSkipVerify: true}, // 跳过TLS验证
		},
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= 3 { // 最大重定向次数
				return fmt.Errorf("too many redirects")
			}
			return nil
		},
	}

	var resp *http.Response
	defer func() {
		// 释放资源
		if resp != nil && resp.Body != nil {
			_ = resp.Body.Close()
		}
		// 记录结束
		log.Debugf("[GetUrlStatusCode]结束: url=%s, id=%s, statusCode=%d", url, id, statusCode)
	}()

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		statusCode = StatusOriginUnreachable
		log.WithContextWarnf(ctx, "[GetUrlStatusCode]创建请求失败: url=%s, id=%s, error=%v, statusCode=%d", url, id, err, statusCode)

		// 缓存创建请求失败的状态码
		cacheExpire := 6 * time.Hour
		if !redis.SetCache(cacheKey, cacheExpire, statusCode) {
			log.WithContextErrorf(ctx, "[GetUrlStatusCode]创建请求失败状态码缓存写入失败: url=%s, id=%s, statusCode=%d", url, id, statusCode)
		} else {
			log.Debugf("[GetUrlStatusCode]创建请求失败状态码缓存写入成功: url=%s, id=%s, statusCode=%d, expire=%v", url, id, statusCode, cacheExpire)
		}

		return statusCode
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36")

	// 发送请求
	resp, err = client.Do(req)
	if err != nil {
		// 处理不同类型的错误
		switch {
		case isTimeoutError(err):
			statusCode = 504 // Gateway Timeout
		case isTooManyRedirects(err):
			statusCode = StatusLoopDetected
		case isConnectionError(err):
			statusCode = StatusOriginUnreachable
		default:
			statusCode = 0
		}
		log.WithContextWarnf(ctx, "[GetUrlStatusCode]请求失败: url=%s, id=%s, error=%v, statusCode=%d", url, id, err, statusCode)

		// 缓存错误状态码 - 使用较短的缓存时间
		var cacheExpire time.Duration
		if statusCode == 0 {
			cacheExpire = 5 * time.Hour // 状态码为0使用5小时缓存
		} else {
			cacheExpire = 10 * time.Hour // 其他错误状态码使用10小时缓存
		}

		if !redis.SetCache(cacheKey, cacheExpire, statusCode) {
			log.WithContextErrorf(ctx, "[GetUrlStatusCode]错误状态码缓存写入失败: url=%s, id=%s, statusCode=%d", url, id, statusCode)
		} else {
			log.Debugf("[GetUrlStatusCode]错误状态码缓存写入成功: url=%s, id=%s, statusCode=%d, expire=%v", url, id, statusCode, cacheExpire)
		}

		return statusCode
	}

	// 获取状态码
	statusCode = resp.StatusCode

	// 缓存写入 - 包括状态码为0的情况
	// 状态码为0通常表示网络错误，也应该缓存以避免重复的无效请求
	var cacheExpire time.Duration
	if statusCode == 0 {
		// 状态码为0的情况使用较短的缓存时间，避免长时间缓存错误状态
		cacheExpire = 10 * time.Hour
	} else {
		// 正常状态码使用较长的缓存时间
		cacheExpire = 7 * 24 * time.Hour
	}

	if !redis.SetCache(cacheKey, cacheExpire, statusCode) {
		log.WithContextErrorf(ctx, "[GetUrlStatusCode]缓存写入失败: url=%s, id=%s, statusCode=%d", url, id, statusCode)
	} else {
		log.Debugf("[GetUrlStatusCode]缓存写入成功: url=%s, id=%s, statusCode=%d, expire=%v", url, id, statusCode, cacheExpire)
	}

	return statusCode
}

// isTimeoutError 判断是否为超时错误
func isTimeoutError(err error) bool {
	var netErr net.Error
	if errors.As(err, &netErr) && netErr.Timeout() {
		return true
	}
	return false
}

// isTooManyRedirects 判断是否为重定向过多错误
func isTooManyRedirects(err error) bool {
	return strings.Contains(err.Error(), "too many redirects")
}

// isConnectionError 判断是否为连接错误
func isConnectionError(err error) bool {
	return strings.Contains(err.Error(), "connection refused") ||
		strings.Contains(err.Error(), "no such host") ||
		strings.Contains(err.Error(), "connection reset")
}
