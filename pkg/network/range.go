package network

import (
	"fmt"
	"net"
	"strings"
)

// IPRange 表示一个IP范围
type IPRange struct {
	FirstIP net.IP
	LastIP  net.IP
}

// ParseRange 解析IP范围
// 支持以下格式:
// - ***********-***********0
// - 192.168.1.*
// - ***********/24
func ParseRange(rangeStr string) (*IPRange, error) {
	// 检查是否是CIDR格式
	if strings.Contains(rangeStr, "/") {
		ipnet, err := ParseCIDR(rangeStr)
		if err != nil {
			return nil, err
		}

		firstIP, lastIP := GetNetworkRange(ipnet)
		return &IPRange{
			FirstIP: firstIP,
			LastIP:  lastIP,
		}, nil
	}

	// 检查是否包含通配符 *
	if strings.Contains(rangeStr, "*") {
		return parseWildcardRange(rangeStr)
	}

	// 检查是否是范围格式 x.x.x.x-y.y.y.y
	if strings.Contains(rangeStr, "-") {
		return parseHyphenRange(rangeStr)
	}

	// 单个IP
	ip := net.ParseIP(rangeStr)
	if ip == nil {
		return nil, fmt.Errorf("invalid IP address: %s", rangeStr)
	}

	return &IPRange{
		FirstIP: ip,
		LastIP:  ip,
	}, nil
}

// parseWildcardRange 解析带通配符的IP范围
func parseWildcardRange(rangeStr string) (*IPRange, error) {
	// 替换通配符为最小值和最大值
	parts := strings.Split(rangeStr, ".")
	if len(parts) != 4 {
		return nil, fmt.Errorf("invalid IP format: %s", rangeStr)
	}

	// 构建最小IP和最大IP
	minParts := make([]string, 4)
	maxParts := make([]string, 4)

	for i, part := range parts {
		if part == "*" {
			minParts[i] = "0"
			maxParts[i] = "255"
		} else {
			minParts[i] = part
			maxParts[i] = part
		}
	}

	minIP := net.ParseIP(strings.Join(minParts, "."))
	maxIP := net.ParseIP(strings.Join(maxParts, "."))

	if minIP == nil || maxIP == nil {
		return nil, fmt.Errorf("invalid IP range: %s", rangeStr)
	}

	return &IPRange{
		FirstIP: minIP,
		LastIP:  maxIP,
	}, nil
}

// parseHyphenRange 解析带连字符的IP范围
func parseHyphenRange(rangeStr string) (*IPRange, error) {
	parts := strings.Split(rangeStr, "-")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid range format: %s", rangeStr)
	}

	// 解析开始IP
	startIP := net.ParseIP(strings.TrimSpace(parts[0]))
	if startIP == nil {
		return nil, fmt.Errorf("invalid start IP: %s", parts[0])
	}

	// 解析结束IP
	endPart := strings.TrimSpace(parts[1])
	var endIP net.IP

	// 检查结束部分是否是简写形式（如***********-5）
	if !strings.Contains(endPart, ".") {
		// 简写形式，需要从开始IP构建完整的结束IP
		startParts := strings.Split(parts[0], ".")
		if len(startParts) != 4 {
			return nil, fmt.Errorf("invalid IP format: %s", parts[0])
		}

		// 构建完整的结束IP
		endIP = net.ParseIP(fmt.Sprintf("%s.%s.%s.%s", startParts[0], startParts[1], startParts[2], endPart))
	} else {
		// 完整形式
		endIP = net.ParseIP(endPart)
	}

	if endIP == nil {
		return nil, fmt.Errorf("invalid end IP: %s", parts[1])
	}

	// 确保开始IP小于等于结束IP
	if startIP.To4() != nil && endIP.To4() != nil {
		startInt := IPToUint32(startIP.To4())
		endInt := IPToUint32(endIP.To4())

		if startInt > endInt {
			return nil, fmt.Errorf("start IP must be less than or equal to end IP")
		}
	}

	return &IPRange{
		FirstIP: startIP,
		LastIP:  endIP,
	}, nil
}

// Contains 检查IP是否在范围内
func (r *IPRange) Contains(ip net.IP) bool {
	// 仅支持同类型IP比较
	if (r.FirstIP.To4() != nil) != (ip.To4() != nil) {
		return false
	}

	// IPv4比较
	if r.FirstIP.To4() != nil {
		ipInt := IPToUint32(ip.To4())
		firstInt := IPToUint32(r.FirstIP.To4())
		lastInt := IPToUint32(r.LastIP.To4())

		return ipInt >= firstInt && ipInt <= lastInt
	}

	// IPv6比较（简化实现）
	return false
}

// GetNetworks 获取范围内的最佳CIDR网络列表
func (r *IPRange) GetNetworks() ([]string, error) {
	// 仅支持IPv4
	if r.FirstIP.To4() == nil || r.LastIP.To4() == nil {
		return nil, fmt.Errorf("only IPv4 is supported")
	}

	return GetNetworksInRange(r.FirstIP, r.LastIP)
}

// Count 计算范围内的IP数量
func (r *IPRange) Count() uint64 {
	// 仅支持IPv4
	if r.FirstIP.To4() == nil || r.LastIP.To4() == nil {
		return 0
	}

	firstInt := IPToUint32(r.FirstIP.To4())
	lastInt := IPToUint32(r.LastIP.To4())

	return uint64(lastInt - firstInt + 1)
}

// Enumerate 枚举范围内的所有IP
func (r *IPRange) Enumerate() []string {
	// 仅支持IPv4
	if r.FirstIP.To4() == nil || r.LastIP.To4() == nil {
		return nil
	}

	var ips []string

	firstInt := IPToUint32(r.FirstIP.To4())
	lastInt := IPToUint32(r.LastIP.To4())

	for i := firstInt; i <= lastInt; i++ {
		ip := Uint32ToIP(i)
		ips = append(ips, ip.String())
	}

	return ips
}
