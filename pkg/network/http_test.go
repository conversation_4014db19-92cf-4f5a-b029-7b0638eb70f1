package network

import (
	"errors"
	"testing"
)

func TestIsTimeoutError(t *testing.T) {

	// 创建非超时错误
	nonTimeoutErr := errors.New("non timeout error")
	if isTimeoutError(nonTimeoutErr) {
		t.<PERSON>rror("Expected false for non timeout error")
	}

	// 创建非 net.Error 的错误
	nonNetErr := errors.New("non net error")
	if isTimeoutError(nonNetErr) {
		t.<PERSON>r("Expected false for non net error")
	}
}

func TestIsTooManyRedirects(t *testing.T) {
	// 创建包含 "too many redirects" 的错误
	redirectErr := errors.New("too many redirects")
	if !isTooManyRedirects(redirectErr) {
		t.Error("Expected true for too many redirects error")
	}

	// 创建不包含 "too many redirects" 的错误
	nonRedirectErr := errors.New("non redirect error")
	if isTooManyRedirects(nonRedirectErr) {
		t.Error("Expected false for non redirect error")
	}
}

func TestIsConnectionError(t *testing.T) {
	// 创建包含 "connection refused" 的错误
	refusedErr := errors.New("connection refused")
	if !isConnectionError(refusedErr) {
		t.Error("Expected true for connection refused error")
	}

	// 创建包含 "no such host" 的错误
	noHostErr := errors.New("no such host")
	if !isConnectionError(noHostErr) {
		t.Error("Expected true for no such host error")
	}

	// 创建包含 "connection reset" 的错误
	resetErr := errors.New("connection reset")
	if !isConnectionError(resetErr) {
		t.Error("Expected true for connection reset error")
	}

	// 创建不包含上述任何字符串的错误
	nonConnectionErr := errors.New("non connection error")
	if isConnectionError(nonConnectionErr) {
		t.Error("Expected false for non connection error")
	}
}

func TestGetUrlStatusCodeCaching(t *testing.T) {
	// 这个测试主要验证代码逻辑，不依赖Redis连接
	t.Log("测试GetUrlStatusCode函数的缓存逻辑改进")

	// 测试无效URL - 应该返回错误状态码
	invalidURL := "http://definitely-invalid-domain-12345.test"
	statusCode := GetUrlStatusCode(invalidURL, "test-id")

	// 验证返回了错误状态码（不是默认的0，而是具体的错误码）
	if statusCode == 0 {
		t.Logf("返回状态码0 - 这种情况现在也会被缓存")
	} else if statusCode == StatusOriginUnreachable || statusCode == 504 {
		t.Logf("返回错误状态码: %d - 这种情况现在也会被缓存", statusCode)
	} else {
		t.Logf("返回状态码: %d", statusCode)
	}

	// 测试格式错误的URL
	invalidFormatURL := "invalid-url-format"
	statusCode2 := GetUrlStatusCode(invalidFormatURL, "test-id-2")
	t.Logf("格式错误URL返回状态码: %d", statusCode2)

	// 验证状态码不为空
	if statusCode2 == StatusOriginUnreachable {
		t.Logf("格式错误URL正确返回StatusOriginUnreachable: %d", statusCode2)
	}
}
