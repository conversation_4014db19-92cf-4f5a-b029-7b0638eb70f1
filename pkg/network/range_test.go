package network

import (
	"net"
	"testing"
)

func TestParseWildcardRange(t *testing.T) {
	tests := []struct {
		name        string
		rangeStr    string
		expected    *IPRange
		errExpected bool
	}{
		{
			name:     "单个IP地址",
			rangeStr: "***********",
			expected: &IPRange{
				FirstIP: net.ParseIP("***********"),
				LastIP:  net.ParseIP("***********"),
			},
			errExpected: false,
		},
		{
			name:     "单个通配符",
			rangeStr: "192.168.1.*",
			expected: &IPRange{
				FirstIP: net.ParseIP("***********"),
				LastIP:  net.ParseIP("*************"),
			},
			errExpected: false,
		},
		{
			name:     "多个通配符",
			rangeStr: "192.168.*.*",
			expected: &IPRange{
				FirstIP: net.ParseIP("***********"),
				LastIP:  net.ParseIP("***************"),
			},
			errExpected: false,
		},
		{
			name:        "无效IP格式",
			rangeStr:    "192.168.1",
			expected:    nil,
			errExpected: true,
		},
		{
			name:        "无效IP段",
			rangeStr:    "192.168.1.256",
			expected:    nil,
			errExpected: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result, err := parseWildcardRange(tc.rangeStr)
			if tc.errExpected {
				if err == nil {
					t.Errorf("期望错误，但实际没有错误")
				}
				return
			}

			if err != nil {
				t.Fatalf("未期望错误，但实际有错误: %v", err)
			}

			if result.FirstIP.String() != tc.expected.FirstIP.String() {
				t.Errorf("期望的起始IP为 %s，实际为 %s", tc.expected.FirstIP.String(), result.FirstIP.String())
			}

			if result.LastIP.String() != tc.expected.LastIP.String() {
				t.Errorf("期望的结束IP为 %s，实际为 %s", tc.expected.LastIP.String(), result.LastIP.String())
			}
		})
	}
}

func TestIPRange_Contains(t *testing.T) {
	ipRange := &IPRange{
		FirstIP: net.ParseIP("***********"),
		LastIP:  net.ParseIP("*************"),
	}

	tests := []struct {
		name     string
		ip       string
		expected bool
	}{
		{
			name:     "IP在范围内",
			ip:       "***********00",
			expected: true,
		},
		{
			name:     "IP不在范围内",
			ip:       "*************",
			expected: false,
		},
		{
			name:     "IPv6地址",
			ip:       "2001:0db8:85a3::8a2e:0370:7334",
			expected: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			ip := net.ParseIP(tc.ip)
			result := ipRange.Contains(ip)
			if result != tc.expected {
				t.Errorf("期望结果为 %v，实际结果为 %v", tc.expected, result)
			}
		})
	}
}

func TestIPRange_Count(t *testing.T) {
	ipRange := &IPRange{
		FirstIP: net.ParseIP("***********"),
		LastIP:  net.ParseIP("*************"),
	}

	tests := []struct {
		name     string
		expected uint64
	}{
		{
			name:     "计算IP数量",
			expected: 256,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			count := ipRange.Count()
			if count != tc.expected {
				t.Errorf("期望IP数量为 %d，实际为 %d", tc.expected, count)
			}
		})
	}
}
