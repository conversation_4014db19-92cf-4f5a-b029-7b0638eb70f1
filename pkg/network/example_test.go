package network

import (
	"fmt"
	"net"
	"reflect"
	"testing"
)

// ExampleSplitIP 展示如何使用SplitIP函数
func ExampleSplitIP() {
	// 处理IPv4
	ips := []string{
		"***********",
		"***********/24",
		"192.168.1.*",
	}

	result := SplitIP(ips, false)
	fmt.Printf("IPv4结果数量: %d\n", len(result))

	// 处理IPv6
	ipsv6 := []string{
		"2001:db8::1",
	}

	resultv6 := SplitIP(ipsv6, true)
	fmt.Printf("IPv6结果数量: %d\n", len(resultv6))

	// Output:
	// IPv4结果数量: 3
	// IPv6结果数量: 1
}

// ExampleCheckHasInsideIP 展示如何使用CheckHasInsideIP函数
func ExampleCheckHasInsideIP() {
	// 包含内网IP
	ips1 := []string{
		"*******",
		"***********",
	}

	result1 := CheckHasInsideIP(ips1)
	fmt.Printf("检测到内网IP: %t\n", result1 != "")

	// 不包含内网IP
	ips2 := []string{
		"*******",
		"*******",
	}

	result2 := CheckHasInsideIP(ips2)
	fmt.Printf("检测到内网IP: %t\n", result2 != "")

	// Output:
	// 检测到内网IP: true
	// 检测到内网IP: false
}

// ExampleEnumerateNetworkIPs 展示如何使用EnumerateNetworkIPs函数
func ExampleEnumerateNetworkIPs() {
	// 解析CIDR
	ips, err := EnumerateNetworkIPs("***********/30")
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		return
	}

	fmt.Printf("网络中的IP数量: %d\n", len(ips))

	// 解析单个IP
	ips2, err := EnumerateNetworkIPs("***********")
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		return
	}

	fmt.Printf("单个IP结果数量: %d\n", len(ips2))

	// Output:
	// 网络中的IP数量: 4
	// 单个IP结果数量: 1
}

func TestGetAllIPs(t *testing.T) {
	tests := []struct {
		name       string
		networkStr string
		expected   []string
		err        error
	}{
		{
			name:       "valid_ipv4_cidr",
			networkStr: "***********/24",
			expected: func() []string {
				ip, _ := ParseCIDR("***********/24")
				firstIP, lastIP := GetNetworkRange(ip)
				var ips []string
				start := IPToUint32(firstIP.To4())
				end := IPToUint32(lastIP.To4())
				for i := start; i <= end; i++ {
					ips = append(ips, Uint32ToIP(i).String())
				}
				return ips
			}(),
			err: nil,
		},
		{
			name:       "valid_ipv6_cidr",
			networkStr: "2001:db8::/64",
			expected:   []string{"2001:db8::"},
			err:        nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetAllIPs(tt.networkStr)
			if !reflect.DeepEqual(err, tt.err) {
				t.Errorf("Test %s: expected error %v, got %v", tt.name, tt.err, err)
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("Test %s: expected %v, got %v", tt.name, tt.expected, result)
			}
		})
	}

	// 测试单个 IP 地址的 CIDR
	t.Run("single_ip_cidr", func(t *testing.T) {
		result, err := GetAllIPs("***********/32")
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}
		expected := []string{"***********"}
		if !reflect.DeepEqual(result, expected) {
			t.Errorf("Expected %v, got %v", expected, result)
		}
	})
}

func TestGetIPsInNetwork(t *testing.T) {
	tests := []struct {
		name     string
		cidr     string
		expected []string
	}{
		{
			name:     "单个IP地址",
			cidr:     "***********/32",
			expected: []string{"***********"},
		},
		{
			name:     "IPv4网络",
			cidr:     "***********/24",
			expected: generateIPv4Range("***********", "*************"),
		},
		{
			name:     "IPv6网络",
			cidr:     "2001:0db8:85a3::/48",
			expected: []string{"2001:db8:85a3::"},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			ips, err := GetIPsInNetwork(tc.cidr)
			if err != nil {
				t.Fatalf("未期望错误，但实际有错误: %v", err)
			}

			if len(ips) != len(tc.expected) {
				t.Errorf("期望IP数量为 %d，实际为 %d", len(tc.expected), len(ips))
			}

			for i, ip := range ips {
				if ip != tc.expected[i] {
					t.Errorf("第 %d 个IP期望为 %s，实际为 %s", i+1, tc.expected[i], ip)
				}
			}
		})
	}
}

// 辅助函数：生成IPv4范围内的所有IP地址
func generateIPv4Range(startIPStr, endIPStr string) []string {
	startIP := net.ParseIP(startIPStr)
	endIP := net.ParseIP(endIPStr)

	if startIP == nil || endIP == nil {
		return nil
	}

	if startIP.To4() == nil || endIP.To4() == nil {
		return nil
	}

	start := IPToUint32(startIP.To4())
	end := IPToUint32(endIP.To4())

	var ips []string
	for i := start; i <= end; i++ {
		ip := Uint32ToIP(i)
		ips = append(ips, ip.String())
	}

	return ips
}

func TestCIDRToNetmask(t *testing.T) {
	tests := []struct {
		name      string
		prefixLen int
		expected  string
	}{
		{
			name:      "prefix length 0",
			prefixLen: 0,
			expected:  "0.0.0.0",
		},
		{
			name:      "prefix length 8",
			prefixLen: 8,
			expected:  "*********",
		},
		{
			name:      "prefix length 16",
			prefixLen: 16,
			expected:  "***********",
		},
		{
			name:      "prefix length 24",
			prefixLen: 24,
			expected:  "*************",
		},
		{
			name:      "prefix length 32",
			prefixLen: 32,
			expected:  "***************",
		},
		{
			name:      "prefix length invalid (-1)",
			prefixLen: -1,
			expected:  "",
		},
		{
			name:      "prefix length invalid (33)",
			prefixLen: 33,
			expected:  "",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result := CIDRToNetmask(tc.prefixLen)
			if result != tc.expected {
				t.Errorf("期望结果为 %s，实际结果为 %s", tc.expected, result)
			}
		})
	}
}

func TestNetmaskToCIDR(t *testing.T) {
	tests := []struct {
		name        string
		netmask     string
		expected    int
		errExpected bool
	}{
		{
			name:        "valid netmask *********",
			netmask:     "*********",
			expected:    8,
			errExpected: false,
		},
		{
			name:        "valid netmask ***********",
			netmask:     "***********",
			expected:    16,
			errExpected: false,
		},
		{
			name:        "valid netmask *************",
			netmask:     "*************",
			expected:    24,
			errExpected: false,
		},
		{
			name:        "valid netmask ***************",
			netmask:     "***************",
			expected:    32,
			errExpected: false,
		},
		{
			name:        "invalid netmask format",
			netmask:     "255.0.0",
			expected:    0,
			errExpected: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result, err := NetmaskToCIDR(tc.netmask)
			if tc.errExpected {
				if err == nil {
					t.Errorf("期望错误，但实际没有错误")
				}
				return
			}

			if err != nil {
				t.Fatalf("未期望错误，但实际有错误: %v", err)
			}

			if result != tc.expected {
				t.Errorf("期望结果为 %d，实际结果为 %d", tc.expected, result)
			}
		})
	}
}
