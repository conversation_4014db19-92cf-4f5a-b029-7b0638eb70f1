package cert

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"micro-service/coreService/handler/icp"
	corePb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/cert_assets"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/pkg/crawler"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

// CertAssetService 证书资产服务
type CertAssetService struct{}

// CertInfo 证书信息结构体
type CertInfo struct {
	SN         string   `json:"sn"`
	IssuerCN   string   `json:"issuer_cn"`
	IssuerCNs  []string `json:"issuer_cns"`
	IssuerOrg  []string `json:"issuer_org"`
	Raw        string   `json:"raw"`
	CertNum    int      `json:"cert_num"`
	SigAlth    string   `json:"sig_alth"`
	SubjectCN  string   `json:"subject_cn"`
	SubjectOrg []string `json:"subject_org"`
	SubjectKey string   `json:"subject_key"`
	ValidType  string   `json:"valid_type"`
	IsValid    bool     `json:"is_valid"`
	Version    string   `json:"v"`
	CertDate   string   `json:"cert_date"`
	NotBefore  string   `json:"not_before"`
	NotAfter   string   `json:"not_after"`
	Domain     string   `json:"domain"`
}

// ForadarAsset 资产信息结构体
type ForadarAsset struct {
	ID              string    `json:"id"`
	IP              string    `json:"ip"`
	Port            int       `json:"port"`
	Protocol        string    `json:"protocol"`
	UserID          uint64    `json:"user_id"`
	CompanyID       uint64    `json:"company_id"`
	Status          int       `json:"status"`
	Type            int       `json:"type"`
	Domain          string    `json:"domain"`
	Subdomain       string    `json:"subdomain"`
	ClueCompanyName []string  `json:"clue_company_name"`
	Cert            *CertInfo `json:"cert"`
	CreatedAt       time.Time `json:"created_at"`
}

// 资产状态常量
const (
	StatusDefault = 0 // 疑似资产
	StatusClaimed = 1 // 已认领
	StatusThreat  = 2 // 威胁
	StatusOffline = 3 // 离线
)

// 资产类型常量
const (
	TypeRecommend = 1 // 推荐结果
)

// DealCertAsset 处理证书维度数据
// 参数：
// - fAsset: 资产信息
// - detectAssetsTasksID: 检测任务ID（可选）
func (s *CertAssetService) DealCertAsset(ctx context.Context, fAsset *ForadarAsset, detectAssetsTasksID *uint64) error {
	// 疑似资产证书去除
	if fAsset.Status == StatusDefault && fAsset.Type == TypeRecommend {
		log.WithContextInfof(ctx, "[CertAssetService]疑似推荐资产跳过证书处理: ip=%s, user_id=%d", fAsset.IP, fAsset.UserID)
		return nil
	}

	log.WithContextInfof(ctx, "[CertAssetService]开始处理证书资产: ip=%s, user_id=%d, assets_id=%s",
		fAsset.IP, fAsset.UserID, fAsset.ID)

	certInfo := fAsset.Cert
	if certInfo == nil {
		log.WithContextInfof(ctx, "[CertAssetService]证书信息为空，跳过处理: assets_id=%s", fAsset.ID)
		return nil
	}

	// 如果有检测任务ID，需要校验证书有效性
	if detectAssetsTasksID != nil {
		isValid, err := s.checkValid(ctx, certInfo, *detectAssetsTasksID)
		if err != nil {
			log.WithContextErrorf(ctx, "[CertAssetService]校验证书有效性失败: %v", err)
			return err
		}
		if !isValid {
			log.WithContextInfof(ctx, "[CertAssetService]证书校验不通过，跳过处理: subject_cn=%s, assets_id=%s, subject_org=%v, detect_task_id=%d",
				certInfo.SubjectCN, fAsset.ID, certInfo.SubjectOrg, *detectAssetsTasksID)
			return nil
		}
		log.WithContextInfof(ctx, "[CertAssetService]证书校验通过，继续处理: subject_cn=%s, assets_id=%s, subject_org=%v, detect_task_id=%d",
			certInfo.SubjectCN, fAsset.ID, certInfo.SubjectOrg, *detectAssetsTasksID)
	}

	// 如果证书序列号不为空，处理证书数据
	if certInfo.SN != "" {
		err := s.processCertAsset(ctx, fAsset, certInfo, detectAssetsTasksID)
		if err != nil {
			log.WithContextErrorf(ctx, "[CertAssetService]处理证书资产失败: %v", err)
			return err
		}
	}

	return nil
}

// getCompanyName 获取企业名称
func (s *CertAssetService) getCompanyName(subjectCn string, subjectOrg []string) (string, error) {
	// 默认使用subject_org
	name := strings.Join(subjectOrg, ",")
	if subjectCn != "" {
		// 获取顶级域名
		topDomain := utils.GetTopDomain(subjectCn)
		if topDomain != "" {
			resp := &corePb.IcpResponse{}
			err := icp.Domain(context.Background(), &corePb.IcpDomainRequest{
				Domain: topDomain,
			}, resp)
			if err != nil {
				log.Warnf("[CertAssetService]获取企业名称失败,domain:%s,error:%v", topDomain, err)
				name = ""
			}
			if resp.Info != nil && resp.Info.CompanyName != "" {
				name = resp.Info.CompanyName
			}
		}
	}

	// 如果ICP查询没有结果，使用subject_org
	return name, nil
}

// processCertAsset 处理证书资产
func (s *CertAssetService) processCertAsset(ctx context.Context, fAsset *ForadarAsset, certInfo *CertInfo, detectAssetsTasksID *uint64) error {
	// 解析证书原始数据
	certParse := s.parseCertRaw(certInfo.Raw)

	// 处理时间转换（UTC时间加8小时）
	var notBefore, notAfter string
	if certInfo.NotBefore != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", certInfo.NotBefore); err == nil {
			notBefore = t.Add(8 * time.Hour).Format("2006-01-02 15:04:05")
		}
	}
	if certInfo.NotAfter != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", certInfo.NotAfter); err == nil {
			notAfter = t.Add(8 * time.Hour).Format("2006-01-02 15:04:05")
		}
	}

	// 处理检测任务ID
	var detectTaskID uint64
	if detectAssetsTasksID != nil {
		detectTaskID = *detectAssetsTasksID
	}

	// 获取企业名称
	companyName, err := s.getCompanyName(certInfo.SubjectCN, certInfo.SubjectOrg)
	if err != nil {
		log.WithContextErrorf(ctx, "[CertAssetService]获取企业名称失败: %v", err)
	}

	// 创建或更新证书资产记录
	certAssetData := map[string]interface{}{
		"company_name":   companyName,
		"issuer_cn":      certInfo.IssuerCN,
		"issuer_cns":     strings.Join(certInfo.IssuerCNs, ","),
		"issuer_org":     strings.Join(certInfo.IssuerOrg, ","),
		"issuer_ou":      certParse["issuer_ou"],
		"cert":           certInfo.Raw,
		"cert_num":       certInfo.CertNum,
		"sig_alth":       certInfo.SigAlth,
		"subject_cn":     certInfo.SubjectCN,
		"subject_org":    strings.Join(certInfo.SubjectOrg, ","),
		"subject_ou":     "",
		"subject_key":    certInfo.SubjectKey,
		"valid_type":     certInfo.ValidType,
		"is_valid":       certInfo.IsValid,
		"version":        certInfo.Version,
		"sha256":         "",
		"sha1":           "",
		"detect_task_id": detectTaskID,
		"cert_date":      certInfo.CertDate,
		"not_before":     notBefore,
		"not_after":      notAfter,
	}

	// 查找或创建证书资产
	certAssetModel, err := s.findOrCreateCertAsset(ctx, certInfo.SN, fAsset.UserID, fAsset.CompanyID, certAssetData)
	if err != nil {
		return fmt.Errorf("创建证书资产失败: %v", err)
	}

	// 处理域名关联
	err = s.processCertDomains(ctx, fAsset, uint64(certAssetModel.ID), certInfo)
	if err != nil {
		return fmt.Errorf("处理证书域名关联失败: %v", err)
	}

	return nil
}

// findOrCreateCertAsset 查找或创建证书资产
func (s *CertAssetService) findOrCreateCertAsset(ctx context.Context, sn string, userID, companyID uint64, data map[string]interface{}) (*cert_assets.CertAssets, error) {
	// 查找现有记录
	existing, err := mysql.NewDSL[cert_assets.CertAssets]().QueryByParams([][]interface{}{
		{"sn", "=", sn},
		{"user_id", "=", userID},
		{"company_id", "=", companyID},
	})
	if err != nil {
		return nil, fmt.Errorf("查询证书资产失败: %v", err)
	}

	if len(existing) > 0 {
		// 返回现有记录
		return &existing[0], nil
	}

	// 创建新记录
	newCertAsset := &cert_assets.CertAssets{
		Sn:           sn,
		UserId:       userID,
		CompanyId:    companyID,
		CompanyName:  safeString(data["company_name"]),
		IssuerCn:     safeString(data["issuer_cn"]),
		IssuerCns:    safeString(data["issuer_cns"]),
		IssuerOrg:    safeString(data["issuer_org"]),
		IssuerOu:     safeString(data["issuer_ou"]),
		Cert:         safeString(data["cert"]),
		CertNum:      safeInt(data["cert_num"]),
		SigAlth:      safeString(data["sig_alth"]),
		SubjectCn:    safeString(data["subject_cn"]),
		SubjectOrg:   safeString(data["subject_org"]),
		SubjectOu:    safeString(data["subject_ou"]),
		SubjectKey:   safeString(data["subject_key"]),
		ValidType:    safeString(data["valid_type"]),
		IsValid:      safeBool(data["is_valid"]),
		Version:      safeString(data["version"]),
		Sha256:       safeString(data["sha256"]),
		Sha1:         safeString(data["sha1"]),
		DetectTaskId: safeUint64(data["detect_task_id"]),
		CertDate:     safeString(data["cert_date"]),
		NotBefore:    safeString(data["not_before"]),
		NotAfter:     safeString(data["not_after"]),
	}

	_, err = mysql.NewDSL[*cert_assets.CertAssets]().Create(newCertAsset)
	if err != nil {
		return nil, fmt.Errorf("创建证书资产记录失败: %v", err)
	}

	return newCertAsset, nil
}

// processCertDomains 处理证书域名关联
func (s *CertAssetService) processCertDomains(ctx context.Context, fAsset *ForadarAsset, certAssetID uint64, certInfo *CertInfo) error {
	// 收集域名
	uniqueDomains := make([]string, 0)
	if fAsset.Subdomain != "" {
		uniqueDomains = append(uniqueDomains, fAsset.Subdomain)
	}

	// uniqueDomains := utils.ListDistinctNonZero(domains)

	log.WithContextInfof(ctx, "[CertAssetService]处理证书域名关联: ip=%s, user_id=%d, domains=%v",
		fAsset.IP, fAsset.UserID, uniqueDomains)

	currentTime := time.Now().Add(-3 * time.Second)
	isUpdate := false

	// 处理每个域名
	for _, domain := range uniqueDomains {
		certDomainModel, err := s.findOrCreateCertDomain(ctx, fAsset, certAssetID, domain, certInfo)
		if err != nil {
			log.WithContextErrorf(ctx, "[CertAssetService]处理证书域名失败: domain=%s, error=%v", domain, err)
			continue
		}

		if certDomainModel.UpdatedAt.After(currentTime) {
			isUpdate = true
		}
	}

	// 如果没有域名，创建一个无域名的记录
	if len(uniqueDomains) == 0 {
		certDomainModel, err := s.findOrCreateCertDomain(ctx, fAsset, certAssetID, "", certInfo)
		if err != nil {
			log.WithContextErrorf(ctx, "[CertAssetService]处理无域名证书记录失败: error=%v", err)
		} else if certDomainModel.UpdatedAt.After(currentTime) {
			isUpdate = true
		}
	}

	// 如果有更新，更新证书资产的时间戳
	if isUpdate {
		_, err := mysql.NewDSL[cert_assets.CertAssets]().UpdateByID(certAssetID, map[string]interface{}{
			"updated_at": time.Now(),
		})
		if err != nil {
			log.WithContextErrorf(ctx, "[CertAssetService]更新证书资产时间戳失败: %v", err)
		}
	}

	return nil
}

// findOrCreateCertDomain 查找或创建证书域名关联
func (s *CertAssetService) findOrCreateCertDomain(ctx context.Context, fAsset *ForadarAsset, certAssetID uint64, domain string, certInfo *CertInfo) (*cert_assets.CertIpDomains, error) {
	// 构建查询条件
	conditions := [][]interface{}{
		{"user_id", "=", fAsset.UserID},
		{"company_id", "=", fAsset.CompanyID},
		{"cert_assets_id", "=", certAssetID},
		{"ip", "=", fAsset.IP},
		{"port", "=", fAsset.Port},
	}

	if domain != "" {
		conditions = append(conditions, []interface{}{"domain", "=", domain})
	} else {
		conditions = append(conditions, []interface{}{"domain", "=", ""})
	}

	// 查找现有记录
	existing, err := mysql.NewDSL[cert_assets.CertIpDomains]().QueryByParams(conditions)
	if err != nil {
		return nil, fmt.Errorf("查询证书域名关联失败: %v", err)
	}

	if len(existing) > 0 {
		return &existing[0], nil
	}

	// 验证证书有效性
	var isValid bool
	if domain != "" {
		isValid = crawler.CertCompareIsValid(certInfo.SN, domain, fAsset.Port, fAsset.Protocol)
	} else {
		isValid = false
	}

	// 创建新记录
	newCertDomain := &cert_assets.CertIpDomains{
		UserId:       fAsset.UserID,
		CompanyId:    fAsset.CompanyID,
		CertAssetsId: certAssetID,
		Domain:       domain,
		Ip:           fAsset.IP,
		Port:         int64(fAsset.Port),
		IsValid:      boolToInt(isValid),
	}

	_, err = mysql.NewDSL[*cert_assets.CertIpDomains]().Create(newCertDomain)
	if err != nil {
		return nil, fmt.Errorf("创建证书域名关联失败: %v", err)
	}

	return newCertDomain, nil
}

// parseCertRaw 解析证书原始数据
func (s *CertAssetService) parseCertRaw(certString string) map[string]interface{} {
	certParseInfo := make(map[string]interface{})

	// 提取：Organizational Unit 颁发者组织单位
	re := regexp.MustCompile(`Organizational Unit: (.*)`)
	matches := re.FindStringSubmatch(certString)
	if len(matches) > 1 {
		issuerOU := strings.TrimSpace(matches[1])
		if issuerOU != "" {
			certParseInfo["issuer_ou"] = issuerOU
		}
	}

	return certParseInfo
}

// checkValid 校验证书的CN是否在线索库，或者O是否在单位测绘的企业名称里面
func (s *CertAssetService) checkValid(ctx context.Context, certInfo *CertInfo, detectAssetsTasksID uint64) (bool, error) {
	// 获取检测任务的组ID
	detectTask, err := mysql.NewDSL[detect_assets_tasks.DetectAssetsTask]().FindByID(detectAssetsTasksID)
	if err != nil {
		return false, fmt.Errorf("获取检测任务失败: %v", err)
	}

	groupID := detectTask.GroupId

	// 获取线索域名列表
	clueDomains, err := mysql.NewDSL[clues.Clue]().QueryByParams([][]interface{}{
		{"group_id", "=", groupID},
		{"status", "=", clues.CLUE_PASS_STATUS},
		{"is_deleted", "=", clues.NOT_DELETE},
		{"type", "in", []interface{}{clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN}},
	})
	if err != nil {
		return false, fmt.Errorf("获取线索域名失败: %v", err)
	}

	// 提取域名内容
	clueContents := make([]string, 0, len(clueDomains))
	for _, c := range clueDomains {
		clueContents = append(clueContents, c.Content)
	}

	// 检查证书域名是否在线索中
	certDomain := utils.GetTopDomain(certInfo.SubjectCN)
	if certDomain != "" {
		for _, content := range clueContents {
			if certDomain == content {
				return true, nil
			}
		}
	}

	// 检查企业名称
	confirmCompanyList := detectTask.ConfirmCompanyList
	var companyNames []string
	if confirmCompanyList != "" {
		err := json.Unmarshal([]byte(confirmCompanyList), &companyNames)
		if err != nil {
			log.WithContextWarnf(ctx, "[CertAssetService]解析企业名称列表失败: %v", err)
		}
	}

	// 处理企业名称（添加小写和去除特殊字符的版本）
	processedCompanyNames := make([]string, 0, len(companyNames)*2)
	for _, name := range companyNames {
		processedCompanyNames = append(processedCompanyNames, name)
		// 添加处理后的名称（小写，去除空格和括号）
		processed := strings.ToLower(strings.ReplaceAll(
			strings.ReplaceAll(
				strings.ReplaceAll(
					strings.ReplaceAll(
						strings.ReplaceAll(name, " ", ""),
						"(", ""),
					")", ""),
				"（", ""),
			"）", ""))
		processedCompanyNames = append(processedCompanyNames, processed)
	}

	// 去重
	uniqueCompanyNames := uniqueStrings(processedCompanyNames)

	// 获取证书的组织名称
	var thisOName string
	if len(certInfo.SubjectOrg) > 0 {
		thisOName = certInfo.SubjectOrg[0]
	}

	if thisOName == "" {
		return false, nil
	}

	// 处理证书组织名称
	replaceName := strings.ToLower(strings.ReplaceAll(
		strings.ReplaceAll(
			strings.ReplaceAll(
				strings.ReplaceAll(
					strings.ReplaceAll(thisOName, " ", ""),
					"(", ""),
				")", ""),
			"（", ""),
		"）", ""))

	// 检查是否匹配
	for _, companyName := range uniqueCompanyNames {
		if thisOName == companyName || replaceName == companyName {
			return true, nil
		}
	}

	return false, nil
}

// 辅助函数
func safeString(data interface{}) string {
	if data == nil {
		return ""
	}
	switch v := data.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case int32:
		return strconv.FormatInt(int64(v), 10)
	case int16:
		return strconv.FormatInt(int64(v), 10)
	case int8:
		return strconv.FormatInt(int64(v), 10)
	}
	return fmt.Sprintf("%v", data)
}

func safeInt(data interface{}) int {
	if data == nil {
		return 0
	}
	switch v := data.(type) {
	case int:
		return v
	case int64:
		return int(v)
	case int32:
		return int(v)
	case int16:
		return int(v)
	case int8:
		return int(v)
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i
		}
	}
	return 0
}

func safeBool(data interface{}) int {
	if data == nil {
		return 0
	}
	switch v := data.(type) {
	case bool:
		if v {
			return 1
		}
		return 0
	case int:
		if v > 0 {
			return 1
		}
		return 0
	case string:
		if v == "true" || v == "1" {
			return 1
		}
		return 0
	}
	return 0
}

func safeUint64(data interface{}) uint64 {
	if data == nil {
		return 0
	}
	switch v := data.(type) {
	case uint64:
		return v
	case int64:
		if v >= 0 {
			return uint64(v)
		}
		return 0
	case int:
		if v >= 0 {
			return uint64(v)
		}
		return 0
	case string:
		if i, err := strconv.ParseUint(v, 10, 64); err == nil {
			return i
		}
	}
	return 0
}

func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

func uniqueStrings(arr []string) []string {
	keys := make(map[string]bool)
	var list []string
	for _, entry := range arr {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}

// NewCertAssetService 创建证书资产服务实例
func NewCertAssetService() *CertAssetService {
	return &CertAssetService{}
}
