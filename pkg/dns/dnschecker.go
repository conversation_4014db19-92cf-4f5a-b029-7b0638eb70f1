package dns

import (
	"context"
	"fmt"
	core "micro-service/coreService/proto"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	"net/http"
)

func GetDnsCheckerResult(ctx context.Context, domain string) ([]string, error) {
	if domain == "" {
		return nil, nil
	}

	var result *core.DnscheckerResponse
	var err error

	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		url := "/api/v1/dnschecker/" + domain
		result = &core.DnscheckerResponse{}
		err = core.HttpClient(http.MethodGet, url, nil, result)
	} else {
		// 否则rpc微服务调用
		param := &core.DnscheckerDomainRequest{
			Domain: domain,
		}
		result, err = core.GetProtoCoreClient().DnsChecker(ctx, param, microx.SetTimeout(30, 30-1)...)
		if err != nil {
			return nil, err
		}

		if result == nil {
			return nil, fmt.Errorf("get DNS checker result response is nil")
		}
	}

	if err != nil {
		log.Error("GetDnsCheckerResult", "查询失败", err, map[string]interface{}{
			"domain": domain,
		})
		return nil, err
	}

	return result.Ip, nil
}
