package dns

import (
	"fmt"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"net"
	"testing"

	microZap "github.com/go-micro/plugins/v4/logger/zap"
	"github.com/hashicorp/go-hclog"
	toZap "github.com/zaffka/zap-to-hclog"
	"go-micro.dev/v4/logger"
	"go.uber.org/zap"

	"github.com/stretchr/testify/assert"
)

func init() {
	cfg.InitLoadCfg()
	// 初始化日志
	log.Init()
	zap.ReplaceGlobals(log.GetLogger())
	hclog.SetDefault(toZap.Wrap(log.GetLogger()))
	z, _ := microZap.NewLogger(microZap.WithConfig(log.GetZapConfig()))
	logger.DefaultLogger = z
	// Mysql&Redis
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
}
func TestIsValidDomain(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		expected bool
	}{
		{"空域名", "", false},
		{"有效域名", "example.com", true},
		{"带子域名", "sub.example.com", true},
		{"带连字符", "my-domain.com", true},
		{"带数字", "123.com", true},
		{"超长域名", "a" + string(make([]byte, 256)), false},
		{"非法字符", "example@.com", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidDomain(tt.domain)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsIPv4MappedIPv6(t *testing.T) {
	tests := []struct {
		name     string
		ip       net.IP
		expected bool
	}{
		{
			name:     "IPv4映射的IPv6",
			ip:       net.ParseIP("::ffff:***********"),
			expected: true,
		},
		{
			name:     "普通IPv6",
			ip:       net.ParseIP("2001:db8::1"),
			expected: false,
		},
		{
			name:     "IPv4地址",
			ip:       net.ParseIP("***********"),
			expected: true,
		},
		{
			name:     "空IP",
			ip:       nil,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsIPv4MappedIPv6(tt.ip)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestExpandIPv6(t *testing.T) {
	tests := []struct {
		name     string
		ipStr    string
		expected net.IP
	}{
		{"有效IPv6", "2001:db8::1", net.ParseIP("2001:db8::1")},
		{"压缩格式", "2001:db8::", net.ParseIP("2001:db8::")},
		{"无效IP", "invalid", nil},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ExpandIPv6(tt.ipStr)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCompleteIPv6(t *testing.T) {
	tests := []struct {
		name     string
		ipStr    string
		expected string
	}{
		{"有效IPv6", "2001:db8::1", "2001:db8::1"},
		{"压缩格式", "2001:db8::", "2001:db8::"},
		{"无效IP", "invalid", "invalid"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CompleteIPv6(tt.ipStr)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetAAAARecords(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		validate func(t *testing.T, records []string)
	}{
		//{
		//	name:   "有效域名",
		//	domain: "example.com",
		//	validate: func(t *testing.T, records []string) {
		//		assert.NotNil(t, records)
		//		assert.Greater(t, len(records), 0)
		//		// 验证返回的IP地址格式
		//		for _, record := range records {
		//			ip := net.ParseIP(record)
		//			assert.NotNil(t, ip)
		//			assert.True(t, ip.To16() != nil)
		//		}
		//	},
		//},
		{
			name:   "无效域名",
			domain: "invalid.domain",
			validate: func(t *testing.T, records []string) {
				assert.Nil(t, records)
			},
		},
		{
			name:   "空域名",
			domain: "",
			validate: func(t *testing.T, records []string) {
				assert.Nil(t, records)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetAAAARecords(tt.domain)
			tt.validate(t, result)
		})
	}
}

func TestGetARecords(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		validate func(t *testing.T, records []string)
	}{
		{
			name:   "有效域名",
			domain: "example.com",
			validate: func(t *testing.T, records []string) {
				assert.NotNil(t, records)
				assert.Greater(t, len(records), 0)
				// 验证返回的IP地址格式
				for _, record := range records {
					ip := net.ParseIP(record)
					assert.NotNil(t, ip)
					assert.True(t, ip.To4() != nil)
				}
			},
		},
		{
			name:   "无效域名",
			domain: "invalid.domain",
			validate: func(t *testing.T, records []string) {
				assert.Nil(t, records)
			},
		},
		{
			name:   "空域名",
			domain: "",
			validate: func(t *testing.T, records []string) {
				assert.Nil(t, records)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetARecords(tt.domain)
			tt.validate(t, result)
		})
	}
}

func TestGetDNSCNAMERecords(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		server   string
		expected []string
		err      error
	}{
		{
			name:   "有效域名，有CNAME记录",
			domain: "www.example.com",
			server: "*******:53",
			expected: []string{
				"www.example.com-v4.edgesuite.net",
			},
			err: nil,
		},
		{
			name:     "有效域名，无CNAME记录",
			domain:   "nonexistent.example.com",
			server:   "*******:53",
			expected: nil,
			err:      nil, // 不预期特定错误，仅验证无CNAME记录
		},
		{
			name:   "自定义DNS服务器",
			domain: "www.example.com",
			server: "1.1.1.1:53",
			expected: []string{
				"www.example.com-v4.edgesuite.net",
			},
			err: nil,
		},
		{
			name:     "域名无效",
			domain:   "invalid.domain#",
			server:   "*******:53",
			expected: nil,
			err:      fmt.Errorf("invalid domain: invalid.domain#"),
		},
		{
			name:     "空域名",
			domain:   "",
			server:   "*******:53",
			expected: nil,
			err:      fmt.Errorf("invalid domain: "),
		},
		{
			name:     "DNS查询失败",
			domain:   "failure.example.com",
			server:   "*******:53",
			expected: nil,
			err:      nil, // 不预期特定错误，仅验证查询失败
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			records, err := GetDNSCNAMERecords(tt.domain, tt.server)
			if tt.err != nil {
				if err == nil {
					t.Errorf("期望返回错误: %v，实际未返回错误", tt.err)
				} else if err.Error() != tt.err.Error() {
					t.Errorf("期望错误: %v，实际错误: %v", tt.err, err)
				}
			} else {
				if err != nil {
					t.Logf("查询失败: %v", err)
				}
				if len(records) == 0 {
					t.Logf("无CNAME记录")
				} else {
					t.Logf("CNAME记录: %v", records)
				}
			}
		})
	}
}

func TestGetCNAMERecords(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		ns       []string
		expected []string
	}{

		{
			name:     "有效域名，无CNAME记录",
			domain:   "nonexistent.example.com",
			ns:       []string{"*******:53"},
			expected: []string{},
		},
		{
			name:     "域名无效",
			domain:   "invalid.domain#",
			ns:       []string{"*******:53"},
			expected: []string{},
		},
		{
			name:     "空域名",
			domain:   "",
			ns:       []string{"*******:53"},
			expected: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetCNAMERecords(tt.domain, tt.ns...)
			if len(result) != len(tt.expected) {
				t.Errorf("期望返回 %v，实际返回 %v", tt.expected, result)
			}
			for i := range result {
				if result[i] != tt.expected[i] {
					t.Errorf("期望返回 %v，实际返回 %v", tt.expected, result)
				}
			}
		})
	}

	// 测试 DNS 查询失败的情况
	name := "DNS查询失败"
	domain := "failure.example.com"
	ns := []string{"*******:53"}
	t.Run(name, func(t *testing.T) {
		result := GetCNAMERecords(domain, ns...)
		if len(result) != 0 {
			t.Errorf("期望返回空列表，实际返回 %v", result)
		}
	})
}
