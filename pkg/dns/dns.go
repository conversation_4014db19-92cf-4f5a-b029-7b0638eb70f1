package dns

import (
	"fmt"
	"net"
	"strings"
	"time"

	"micro-service/middleware/redis"
	"micro-service/pkg/cache"

	"github.com/miekg/dns"
)

// GetAAAARecords 获取域名的IPv6地址,支持缓存
// domain: 要查询的域名
// nameserver: 可选的DNS服务器地址
func GetAAAARecords(domain string, nameserver ...string) []string {
	if !IsValidDomain(domain) {
		fmt.Printf("GetAAAARecords - 当前域名不符合域名格式规范，跳过 domain: %s\n", domain)
		return nil
	}

	// 尝试从缓存获取
	var list []string
	cacheKey := cache.GetCacheKey("DNS_AAAA", domain)
	if redis.GetCache(cacheKey, &list) {
		fmt.Printf("GetAAAARecords - 缓存命中 - domain: %s cache %v\n", domain, list)

		// 如果缓存的是空数组，直接返回
		if len(list) == 0 {
			fmt.Printf("GetAAAARecords - 缓存显示无记录 - domain: %s\n", domain)
			return nil
		}

		// 补全IPv6格式并过滤
		var newList []string
		for _, val := range list {
			if ip := net.ParseIP(val); ip != nil && ip.To16() != nil {
				// 判断是否是IPv4映射的IPv6
				if IsIPv4MappedIPv6(ExpandIPv6(val)) {
					continue
				}
				newList = append(newList, CompleteIPv6(val))
			}
		}
		return newList
	}

	// 缓存未命中,执行DNS查询
	msg, err := performDNSQuery(domain, dns.TypeAAAA, nameserver...)
	if err != nil {
		fmt.Printf("GetAAAARecords - DNS查询失败 - domain: %s error: %v\n", domain, err)
		// DNS查询失败时也缓存空数组，避免频繁查询
		if ret := redis.SetCache(cacheKey, 72*time.Hour, []string{}); !ret {
			fmt.Printf("Failed to cache DNS empty records: %v\n", ret)
		}
		return nil
	}
	records := extractAAAARecords(msg)

	// 过滤和格式化IPv6地址
	var ipRecords []net.IP
	for _, record := range records {
		if ip := net.ParseIP(record); ip != nil {
			ipRecords = append(ipRecords, ip)
		}
	}

	records = make([]string, 0, len(ipRecords))
	for _, ip := range ipRecords {
		if ip.To16() != nil {
			records = append(records, ip.String())
		}
	}

	// 将结果缓存到Redis,设置3天过期，无论有结果还是无结果都缓存
	if ret := redis.SetCache(cacheKey, 72*time.Hour, records); !ret {
		// 记录错误但不影响返回结果
		fmt.Printf("Failed to cache DNS records: %v\n", ret)
	}
	fmt.Printf("GetAAAARecords - 缓存结果 - domain: %s records: %d\n", domain, len(records))

	return records
}

// IsValidDomain 检查域名是否有效
func IsValidDomain(domain string) bool {
	if domain == "" {
		return false
	}
	// 检查域名长度
	if len(domain) > 255 {
		return false
	}
	// 检查是否包含非法字符
	for _, c := range domain {
		if !((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9') || c == '.' || c == '-') {
			return false
		}
	}
	return true
}

// IsIPv4MappedIPv6 检查是否为IPv4映射的IPv6地址
func IsIPv4MappedIPv6(ip net.IP) bool {
	if ip == nil {
		return false
	}
	ipv4 := ip.To4()
	if ipv4 == nil {
		return false
	}
	return ip[0] == 0 && ip[1] == 0 && ip[2] == 0 && ip[3] == 0 && ip[4] == 0 && ip[5] == 0 && ip[6] == 0 && ip[7] == 0 && ip[8] == 0 && ip[9] == 0 && ip[10] == 0xff && ip[11] == 0xff
}

// ExpandIPv6 展开IPv6地址
func ExpandIPv6(ipStr string) net.IP {
	return net.ParseIP(ipStr)
}

// CompleteIPv6 补全IPv6地址格式
func CompleteIPv6(ipStr string) string {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return ipStr
	}
	return ip.String()
}

// GetARecords 获取域名的IPv4地址,支持缓存
// domain: 要查询的域名
// nameserver: 可选的DNS服务器地址
func GetARecords(domain string, nameserver ...string) []string {
	if !IsValidDomain(domain) {
		fmt.Printf("GetARecords - 当前域名不符合域名格式规范，跳过 domain: %s\n", domain)
		return nil
	}

	// 尝试从缓存获取
	var list []string
	cacheKey := cache.GetCacheKey("DNS_A", domain)
	if redis.GetCache(cacheKey, &list) {
		fmt.Printf("GetARecords - 缓存命中 - domain: %s cache %v\n", domain, list)
		return list
	}

	// 缓存未命中,执行DNS查询
	msg, err := performDNSQuery(domain, dns.TypeA, nameserver...)
	if err != nil {
		fmt.Printf("GetARecords - DNS查询失败 - domain: %s error: %v\n", domain, err)
		// DNS查询失败时也缓存空数组，避免频繁查询
		if ret := redis.SetCache(cacheKey, 72*time.Hour, []string{}); !ret {
			fmt.Printf("Failed to cache DNS empty records: %v\n", ret)
		}
		return nil
	}
	records := extractARecords(msg)

	// 过滤和格式化IPv4地址
	var ipRecords []net.IP
	for _, record := range records {
		if ip := net.ParseIP(record); ip != nil && ip.To4() != nil {
			ipRecords = append(ipRecords, ip)
		}
	}

	records = make([]string, 0, len(ipRecords))
	for _, ip := range ipRecords {
		records = append(records, ip.String())
	}

	// 将结果缓存到Redis,设置3天过期，无论有结果还是无结果都缓存
	if ret := redis.SetCache(cacheKey, 72*time.Hour, records); !ret {
		// 记录错误但不影响返回结果
		fmt.Printf("Failed to cache DNS records: %v\n", ret)
	}
	fmt.Printf("GetARecords - 缓存结果 - domain: %s records: %d\n", domain, len(records))

	return records
}

// performDNSQuery 执行通用DNS查询逻辑
func performDNSQuery(domain string, queryType uint16, nameserver ...string) (*dns.Msg, error) {
	if !IsValidDomain(domain) {
		return nil, fmt.Errorf("invalid domain: %s", domain)
	}

	// 创建DNS客户端
	c := new(dns.Client)
	m := new(dns.Msg)
	m.SetQuestion(dns.Fqdn(domain), queryType)

	// 设置DNS服务器
	var ns string
	if len(nameserver) > 0 && nameserver[0] != "" {
		ns = nameserver[0]
	} else {
		ns = "*******:53" // 默认使用Google DNS
	}

	// 发送DNS查询
	r, _, err := c.Exchange(m, ns)
	if err != nil {
		return nil, err
	}

	// 检查响应状态
	if r.Rcode != dns.RcodeSuccess {
		return nil, fmt.Errorf("DNS query failed with status: %d", r.Rcode)
	}

	return r, nil
}

// extractAAAARecords 从DNS响应中提取AAAA记录
func extractAAAARecords(msg *dns.Msg) []string {
	var records []string
	for _, ans := range msg.Answer {
		if aaaa, ok := ans.(*dns.AAAA); ok {
			records = append(records, aaaa.AAAA.String())
		}
	}
	return records
}

// extractARecords 从DNS响应中提取A记录
func extractARecords(msg *dns.Msg) []string {
	var records []string
	for _, ans := range msg.Answer {
		if a, ok := ans.(*dns.A); ok {
			records = append(records, a.A.String())
		}
	}
	return records
}

// GetDNSCNAMERecords 获取域名的CNAME记录
func GetDNSCNAMERecords(domain string, nameserver ...string) ([]string, error) {
	if !IsValidDomain(domain) {
		return nil, fmt.Errorf("invalid domain: %s", domain)
	}

	// 创建DNS客户端
	c := new(dns.Client)
	m := new(dns.Msg)
	m.SetQuestion(dns.Fqdn(domain), dns.TypeCNAME)

	// 设置DNS服务器
	var ns string
	if len(nameserver) > 0 && nameserver[0] != "" {
		ns = nameserver[0]
	} else {
		ns = "*******:53" // 默认使用Google DNS
	}

	// 发送DNS查询
	r, _, err := c.Exchange(m, ns)
	if err != nil {
		return nil, err
	}

	// 检查响应状态
	if r.Rcode != dns.RcodeSuccess {
		return nil, fmt.Errorf("DNS query failed with status: %d", r.Rcode)
	}

	// 提取CNAME记录
	var records []string
	for _, ans := range r.Answer {
		if cname, ok := ans.(*dns.CNAME); ok {
			// 去掉最后的点号
			target := cname.Target
			if len(target) > 0 && target[len(target)-1] == '.' {
				target = target[:len(target)-1]
			}
			records = append(records, target)
		}
	}

	return records, nil
}

// GetCNAMERecords 获取域名的CNAME记录，对应PHP中的dns_get_record($urlDomain, DNS_CNAME)
// domain: 要查询的域名
// nameserver: 可选的DNS服务器地址
func GetCNAMERecords(domain string, nameserver ...string) []string {
	// 处理包含端口号的域名，提取纯域名部分
	cleanDomain := domain
	if strings.Contains(domain, ":") {
		// 分离域名和端口号
		if host, _, err := net.SplitHostPort(domain); err == nil {
			cleanDomain = host
		} else {
			// 如果分离失败，尝试简单的字符串分割
			parts := strings.Split(domain, ":")
			if len(parts) > 0 {
				cleanDomain = parts[0]
			}
		}
	}

	if !IsValidDomain(cleanDomain) {
		fmt.Printf("GetCNAMERecords - 当前域名不符合域名格式规范，跳过 domain: %s (cleaned: %s)\n", domain, cleanDomain)
		return []string{}
	}

	// 执行DNS查询时使用清理后的域名
	records, err := GetDNSCNAMERecords(cleanDomain, nameserver...)
	if err != nil {
		fmt.Printf("GetCNAMERecords - DNS查询失败 - domain: %s (cleaned: %s) error: %v\n", domain, cleanDomain, err)
		return []string{}
	}

	return records
}
