package ginx

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"micro-service/pkg/utils"
)

func QueryArray(ctx *gin.Context, key string, filterZero ...bool) []string {
	nz := utils.ListFirstEle(filterZero)
	if array := ctx.QueryArray(key); len(array) > 0 {
		var values = make([]string, 0, len(array))
		for _, v := range array {
			if nz && v == "" {
				continue
			}
			values = append(values, v)
		}
		return values
	}

	m := ctx.QueryMap(key)
	if len(m) == 0 {
		return nil
	}

	values := make([]string, 0, len(m))
	mLen := len(m)
	for i := 0; i < mLen; i++ {
		v, _ := m[strconv.Itoa(i)]
		if nz && v == "" {
			continue
		}
		values = append(values, v)
	}
	return values
}
