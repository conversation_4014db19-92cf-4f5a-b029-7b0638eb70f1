package ginx

import (
	"github.com/gin-gonic/gin"
	"github.com/goccy/go-json"
	"github.com/stretchr/testify/assert"
	"net/http"
	"net/http/httptest"
	"testing"
)

//	func TestQueryArray(t *testing.T) {
//		u, err := url.Parse("/api/v1/user?x=123&x=234")
//		assert.Nil(t, err)
//
//		var ctx = new(gin.Context)
//		ctx.Request = &http.Request{
//			Method: http.MethodGet,
//			URL:    u,
//		}
//		r := QueryArray(ctx, "x", true)
//		assert.Equal(t, []string{"123", "234"}, r)
//
//		u, err = url.Parse("/api/v1/user?x[0]=123&x[1]=234")
//		assert.Nil(t, err)
//		ctx.Request.URL = u
//		r = QueryArray(ctx, "x", true)
//		assert.Equal(t, []string{"123", "234"}, r)
//	}
func TestQueryArray(t *testing.T) {
	// 设置测试用例
	tests := []struct {
		name          string
		queryParams   string
		key           string
		filterZero    []bool
		expectedValue []string
	}{
		{
			name:          "正常查询参数",
			queryParams:   "key=val1&key=val2",
			key:           "key",
			filterZero:    []bool{},
			expectedValue: []string{"val1", "val2"},
		},

		{
			name:          "查询参数为 map 类型",
			queryParams:   "key[0]=val1&key[1]=val2",
			key:           "key",
			filterZero:    []bool{},
			expectedValue: []string{"val1", "val2"},
		},
	}

	// 创建一个新的 Gin 路由引擎
	r := gin.New()

	// 定义一个测试处理器
	r.GET("/test", func(c *gin.Context) {
		// 调用 QueryArray 函数
		values := QueryArray(c, "key")
		c.JSON(http.StatusOK, gin.H{"values": values})
	})

	// 执行每个测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建一个新的 HTTP 请求
			req, _ := http.NewRequest("GET", "/test", nil)

			// 设置查询参数
			req.URL.RawQuery = tt.queryParams

			// 创建一个新的 HTTP 响应记录器
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 检查响应状态码
			assert.Equal(t, http.StatusOK, w.Code)

			// 解析响应体
			var response map[string]interface{}
			json.Unmarshal(w.Body.Bytes(), &response)

			// 检查返回值是否符合预期
			values, ok := response["values"].([]interface{})
			if !ok {
				t.Errorf("期望返回值为数组，但实际返回值为 %v", response["values"])
				return
			}

			// 将返回值转换为字符串切片
			var actualValue []string
			for _, v := range values {
				actualValue = append(actualValue, v.(string))
			}

			assert.Equal(t, tt.expectedValue, actualValue)
		})
	}
}
