package metadata

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go-micro.dev/v4/metadata"
)

func TestGetString(t *testing.T) {
	testCases := []struct {
		name     string
		ctx      context.Context
		key      string
		expected string
		err      error
	}{
		{
			name:     "metadata exists and key exists",
			ctx:      metadata.NewContext(context.Background(), metadata.Metadata{"key": "value"}),
			key:      "key",
			expected: "value",
			err:      nil,
		},
		{
			name:     "metadata exists but key does not exist",
			ctx:      metadata.NewContext(context.Background(), metadata.Metadata{"otherKey": "value"}),
			key:      "key",
			expected: "",
			err:      errors.New("missing metadata: key"),
		},
		{
			name:     "metadata does not exist",
			ctx:      context.Background(),
			key:      "key",
			expected: "",
			err:      errors.New("missing metadata"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual, err := GetString(tc.ctx, tc.key)
			if tc.err != nil {
				assert.EqualError(t, err, tc.err.Error())
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tc.expected, actual)
		})
	}
}

func TestGetInt64(t *testing.T) {
	testCases := []struct {
		name     string
		ctx      context.Context
		key      string
		expected int64
		err      error
	}{
		{
			name:     "metadata exists and key exists with valid int",
			ctx:      metadata.NewContext(context.Background(), metadata.Metadata{"key": "123"}),
			key:      "key",
			expected: 123,
			err:      nil,
		},
		{
			name:     "metadata exists and key exists with invalid int",
			ctx:      metadata.NewContext(context.Background(), metadata.Metadata{"key": "notanint"}),
			key:      "key",
			expected: 0,
			err:      errors.New("strconv.ParseInt: parsing \"notanint\": invalid syntax"),
		},
		{
			name:     "metadata exists but key does not exist",
			ctx:      metadata.NewContext(context.Background(), metadata.Metadata{"otherKey": "value"}),
			key:      "key",
			expected: 0,
			err:      errors.New("missing metadata: key"),
		},
		{
			name:     "metadata does not exist",
			ctx:      context.Background(),
			key:      "key",
			expected: 0,
			err:      errors.New("missing metadata"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual, err := GetInt64(tc.ctx, tc.key)
			if tc.err != nil {
				assert.EqualError(t, err, tc.err.Error())
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tc.expected, actual)
		})
	}
}

func TestMustString(t *testing.T) {
	testCases := []struct {
		name string
		ctx  context.Context
		key  string
	}{
		{
			name: "metadata exists but key does not exist",
			ctx:  metadata.NewContext(context.Background(), metadata.Metadata{"otherKey": "value"}),
			key:  "key",
		},
		{
			name: "metadata does not exist",
			ctx:  context.Background(),
			key:  "key",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer func() {
				if r := recover(); r == nil {
					t.Fatal("Expected panic but didn't get one")
				}
			}()

			MustString(tc.ctx, tc.key)
		})
	}

	// Test successful case without panic
	ctx := metadata.NewContext(context.Background(), metadata.Metadata{"key": "value"})
	assert := require.New(t)
	assert.Equal("value", MustString(ctx, "key"))
}

func TestMustInt64(t *testing.T) {
	testCases := []struct {
		name string
		ctx  context.Context
		key  string
	}{

		{
			name: "metadata exists and key exists with invalid int",
			ctx:  metadata.NewContext(context.Background(), metadata.Metadata{"key": "notanint"}),
			key:  "key",
		},
		{
			name: "metadata exists but key does not exist",
			ctx:  metadata.NewContext(context.Background(), metadata.Metadata{"otherKey": "value"}),
			key:  "key",
		},
		{
			name: "metadata does not exist",
			ctx:  context.Background(),
			key:  "key",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer func() {
				if r := recover(); r == nil {
					t.Fatal("Expected panic but didn't get one")
				}
			}()

			MustInt64(tc.ctx, tc.key)
		})
	}

	// Test successful case without panic
	ctx := metadata.NewContext(context.Background(), metadata.Metadata{"key": "123"})
	assert := require.New(t)
	assert.Equal(int64(123), MustInt64(ctx, "key"))
}
