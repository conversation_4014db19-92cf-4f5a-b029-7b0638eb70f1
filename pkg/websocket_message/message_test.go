package websocket_message

import (
	microZap "github.com/go-micro/plugins/v4/logger/zap"
	"github.com/hashicorp/go-hclog"
	toZap "github.com/zaffka/zap-to-hclog"
	"go-micro.dev/v4/logger"
	"go.uber.org/zap"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"os"
	"testing"
)

func TestSend(t *testing.T) {
	cfg.InitLoadCfg()
	// 初始化日志
	log.Init()
	zap.ReplaceGlobals(log.GetLogger())
	hclog.SetDefault(toZap.Wrap(log.GetLogger()))
	z, _ := microZap.NewLogger(microZap.WithConfig(log.GetZapConfig()))
	logger.DefaultLogger = z
	// Mysql&Redis
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	// [
	//                        'flag' =>$this->flag, 'progress' => round($progress, 2), 'status'=>$status,'count' => $resultCount,'user_id'=>$this->userId ?? null,
	//                        'target'=> $ip,'us' => Carbon::now()->diffAsCarbonInterval($this->startAt)->toArray(),'total' => $this->total,
	//                        'task_name' => object_get($record, 'task_name'),'group_id' => object_get($record, 'group_id'),
	//                        'use_seconds' => time() - strtotime($this->startAt ?? ''),
	//                        'task_id' => $this->expendId ?? 0
	//                    ]
	conf := cfg.LoadRabbitMq()
	amqpUrl := cfg.GetRabbitMqAddr(conf)
	log.Infof("amqpUrl:%s", amqpUrl)
	hn, _ := os.Hostname()
	if hn == "" {
		ip := utils.GetNetworkIp(cfg.LoadCommon().Network)
		if ip == "" {
			hn = "unknown"
		} else {
			hn = ip
		}
	}
	_, err := GetWsMqWorker(amqpUrl, "topic.ws.exchange", "queue.ws."+hn, "ws.notice", func(uid int64, data map[string]interface{}) error {
		return nil
	})
	if err != nil {
		log.Errorf("[websocket_message] 创建消费者失败: %v", err)
	}
	err = PublishSuccess(574, "recommend_progress", map[string]interface{}{
		"progress": 0.5,
		"status":   1,
		"count":    1,
		"user_id":  574,
		"target":   "***********",
		"us": map[string]interface{}{
			"h":     0,
			"i":     0,
			"s":     0,
			"total": 0,
		},
		"total":       1,
		"task_name":   "recommend_progress",
		"group_id":    1,
		"use_seconds": 1,
		"task_id":     1,
		"flag":        "recommend_progress",
	})
	if err != nil {
		log.Errorf("[websocket_message] 发送消息失败: %v", err)
	}
}
