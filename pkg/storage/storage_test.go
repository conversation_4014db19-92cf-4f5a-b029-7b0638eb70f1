package storage

import (
	"fmt"
	"micro-service/pkg/log"
	"testing"
)

func Test_SaveUrlMmh3(t *testing.T) {
	log.Init()
	path, hash := SaveUrlMmh3("", "-*********", true)
	println(path, hash)
}

func TestFullPath(t *testing.T) {

	var url = "/api/v1/files/eyJpdiI6Ing5YmJjZ3BhYUNwcVBkVTE2S3ltaWc9PSIsInZhbHVlIjoiV0F5R3plZ2QyWmFhbW0reUZlbEltRHV6M0l4ZUQyM21Ganp2Skd4MThHWXVIRjBWbWFVVHNDR2VEYk9HdVZVSEZVSFpkM1llcXVNK3o4ejFVOWhjZC9jNmhaSVdaTjcvWHdITzBEZnJFSUtxK2p1alUxTFNsckpyaFlBVEE3L1htSXhLZXo2UHJhN2dsVENPZjB5R21UNFpJMXBWOFNzZkZsaTZNMzRFTlhnVU9wQkt4SGVWeUxIQjBVKzExbmlWTmgyTkR1ZGNLNkJxcThlb3MyakMvdDNqYncxRERPNEdka3RIVnF6d0twQT0iLCJtYWMiOiJjZGI1MTY3MGE5YjM4MzQ1YzQwZTY0Y2EyYjE4YmIyZmRlM2E1OWJjNjAxNjU4YzFmNDI0MTk4MmIxZjQ4MDFlIiwidGFnIjoiIn0.ico"
	url = GetLocalFileFullPath(url)
	fmt.Println(url)

}

func TestParseDownloadUrl(t *testing.T) {
	data, err := ParseDownloadUrl("/api/v1/files/eyJpdiI6InhQc2kyb3pCSk4zNGJQNm1MRHJ2WEE9PSIsInZhbHVlIjoibWRFZzMwbzFOaVZEOWdoa2hlU3UyNlZQSlNNK3IyYlhxT2xoS1IycGRlWEVYQktra3BpVCtpbkdzQ3BnTU43OFlGeG43MmpYL3hmaHR0U2JFRzF2MWcrZmNIRVRoQk0vcDliblJOcUlrMVp2Yjl0bG9MaFdCYjlPRXVudGJHdVJ0UTNxbTdjcWd3YnVRejJyTUMyK0lFakhoUS9VbE5ldkluaC92bVFUVm5NRiswWnI1MDBIT3pFVW1uOXNmNWZLL3hyRmJ0Sk9idTVqUEhtSEsrdExaUT09IiwibWFjIjoiM2Q2ZWE3N2Y2NjMxZTFlOTAxMWEwMDk4MGUxN2Q4N2EyYmVjMzRiZDBlYWEyZTg2YWIxZWE2YWM4NTA2ZDNlOSIsInRhZyI6IiJ9.png")
	if err != nil {
		t.Error(err)
	}
	fmt.Printf("%v", data)

	data, err = ParseDownloadUrl("http://10.10.10.189:9999/api/v1/files/eyJpdiI6IjJ3akZZYTYxK1E4UHQrOWY4RFJOdUE9PSIsInZhbHVlIjoiMnN5WjlhOTdLRnlpb3dPemtxOU5EZ2NpT1ZQdkVNQlgyOUthUjlWbUVuVE5vd0hYVjdqd2swSXBmSlE2WTFhdEFxYUtKWi9FQmdnL2NGYTlrUDlxMlA5aXNSdTFMS2VtamhZclhaSjZYRFJsR0V2NmJYcE5zbzlFeXNZWFlhRmxpaEM0d0VubmV3c2xJdWlIQm5uYnpFMnI2bXB5VEl0VkRpYXFYSGtGeW9VejlKb0IwamhDMVV0cDg3aVM0a21RIiwibWFjIjoiMDBjMmJmYTk1YTliOTg2ZDZjNmFjODBlOGUxZTAzZjgzODVjMzNkYzZiMDFhNWMxZDY1OTE0MWUwNjE1MDI5MSIsInRhZyI6IiJ9.png")
	if err != nil {
		t.Error(err)
	}
	fmt.Printf("%v", data)
}

//func ExampleGenAPIDownloadPath() {
//	s := GenAPIDownloadPath("资产核查任务", "app/public/asset_audit_123.xlsx")
//	fmt.Println(s)
//	// Output: /api/v1/files/eyJpdiI6Ikl5cndoRTJyZUtYVE5pY2xEREh2Rnc9PSIsIm1hYyI6ImQyOWNiNzYzM2QzMTdjYjE5MmI4ZGEyNTg4OGU3YjJjM2Y1NTczZWRmODBjNDU4NGEzMWRlZjdmNTE4MTUxMWEiLCJ2YWx1ZSI6ImxyUFpNTXljcGh3dVFydEZWTDkwQkZGY3M1QUg3RUNibllHQ3RFZUhTZlFyNEx6bytFTXhXNmxpeEVES1FOM3VHU1JCSjJtZGhLOEVqVXF2bGdteXA3cjY3L0EwREJtT3lYYnVCaHh5VGZSSnd5ZEh4MWMyUjM2Z21iNlEvdTNEZjd3SGI4cDRTZFhHRWNqNFZYM0FKREVMU2QrakFUVW9jZGhqOSsrYkNjUT0ifQ==.xlsx
//}

//	func TestSaveIntelligenceReport(t *testing.T) {
//		cfg.InitLoadCfg()
//		log.Init()
//		mysql.GetInstance(cfg.LoadMysql())
//
//		type args struct {
//			fileContent []byte
//			fileName    string
//		}
//		var testNum = time.Now().Nanosecond()
//		tests := []struct {
//			name string
//			args args
//			want string
//		}{
//			{
//				name: "empty content",
//				args: args{
//					fileContent: []byte(""),
//					fileName:    "empty.txt",
//				},
//				want: "",
//			},
//			{
//				name: "normal",
//				args: args{
//					fileContent: []byte("test content"),
//					fileName:    fmt.Sprintf("test%v.txt", testNum),
//				},
//				want: fmt.Sprintf("app/public/download/test%v.txt", testNum),
//			},
//			{
//				//这个用例依赖前一个normal
//				name: "exist file",
//				args: args{
//					fileContent: []byte("test content"),
//					fileName:    fmt.Sprintf("test%v.txt", testNum),
//				},
//				want: fmt.Sprintf("app/public/download/test%v.txt", testNum),
//			},
//		}
//
//		for _, tt := range tests {
//			got := SaveIntelligenceReport(tt.args.fileContent, tt.args.fileName)
//			if got != tt.want {
//				t.Errorf("SaveIntelligenceReport(%v, %v) = %v, want %v", tt.args.fileContent, tt.args.fileName, got, tt.want)
//			}
//		}
//	}
