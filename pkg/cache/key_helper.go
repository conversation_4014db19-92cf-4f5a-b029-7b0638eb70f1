package cache

import "fmt"

const (
	PrefixCache  = "cache"  // 缓存前缀,cache是通用的缓存数据
	PrefixConfig = "config" // 配置前缀,config是通用的配置数据
	PrefixLock   = "lock"   // 锁前缀,lock是通用的锁数据
)

// GetCacheKey 获取缓存key
// module: 模块名,key: 缓存key
func GetCacheKey(module, key string) string {
	return fmt.Sprintf("%s:%s:%s", PrefixCache, module, key)
}

func GetConfigKey(key string) string {
	return fmt.Sprintf("%s:%s", PrefixConfig, key)
}

func GetLockKey(module, key string) string {
	return fmt.Sprintf("%s:%s:%s", PrefixLock, module, key)
}
