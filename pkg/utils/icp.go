package utils

import (
	"fmt"
	"regexp"
)

var (
	childIcpReg  = regexp.MustCompile(`^[滇陇京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新蜀][A-Z0-9]{1,}-\d+-\d+[A-z]{1}$`)
	parentIcpReg = regexp.MustCompile(`^[滇陇京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新蜀][A-Z0-9]{1,}-\d+`)
)

func AppIcpCheck(icp string) (parent, child string, err error) {
	child = childIcpReg.FindString(icp)
	parent = parentIcpReg.FindString(icp)

	if child == "" && parent == "" {
		return "", "", fmt.Errorf("%s不是有效的ICP备案号", icp)
	}

	if parent != "" && child == "" {
		return parent, "", nil
	}
	return parent, child, nil
}
