package utils

import (
	"net"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsIPv4(t *testing.T) {
	cases := []struct {
		in  string
		exp bool
	}{
		{in: "127.0.0.1", exp: true},
		{in: "*******", exp: true},
		{in: "***************", exp: true},
		{in: "255.255.256.255", exp: false},
		{in: "::1", exp: false},
		{in: "127.0.1::1", exp: false},
		{in: "hello.word", exp: false},
		{in: "2001:0db8:85a3:0000:0000:8a2e:0370:7334", exp: false},
		{in: "1.2.3.-4", exp: false},
		{in: "256.0.0.1", exp: false},
	}
	for _, v := range cases {
		b := IsIPv4(v.in)
		assert.Equal(t, v.exp, b)
	}
}

func TestIsPrivateIP(t *testing.T) {
	cases := []struct {
		name    string
		ip      string
		expIsIP bool
		expPriv bool
	}{
		// 非IP地址测试
		{name: "non-ip", ip: "not.an.ip", expIsIP: false, expPriv: false},
		{name: "empty", ip: "", expIsIP: false, expPriv: false},
		{name: "invalid-format", ip: "***********00", expIsIP: false, expPriv: false},

		// IPv4公有地址
		{name: "ipv4-public-1", ip: "*******", expIsIP: true, expPriv: false},
		{name: "ipv4-public-2", ip: "***************", expIsIP: true, expPriv: false},

		// IPv4私有地址
		{name: "ipv4-private-a", ip: "********", expIsIP: true, expPriv: true},
		{name: "ipv4-private-b", ip: "**********", expIsIP: true, expPriv: true},
		{name: "ipv4-private-c", ip: "***********", expIsIP: true, expPriv: true},
		{name: "ipv4-private-carrier", ip: "**********", expIsIP: true, expPriv: false},
		{name: "ipv4-private-loopback", ip: "127.0.0.1", expIsIP: true, expPriv: false},

		// IPv4边界情况
		{name: "ipv4-private-min-a", ip: "10.0.0.0", expIsIP: true, expPriv: true},
		{name: "ipv4-private-max-a", ip: "**************", expIsIP: true, expPriv: true},
		{name: "ipv4-private-min-b", ip: "**********", expIsIP: true, expPriv: true},
		{name: "ipv4-private-max-b", ip: "**************", expIsIP: true, expPriv: true},
		{name: "ipv4-private-min-c", ip: "***********", expIsIP: true, expPriv: true},
		{name: "ipv4-private-max-c", ip: "***************", expIsIP: true, expPriv: true},
		{name: "ipv4-public-edge", ip: "**************", expIsIP: true, expPriv: false},

		// IPv6公有地址
		{name: "ipv6-public-1", ip: "2001:4860:4860::8888", expIsIP: true, expPriv: false},
		{name: "ipv6-public-2", ip: "2607:f8b0:4005:80a::2004", expIsIP: true, expPriv: false},

		// IPv6私有地址
		{name: "ipv6-private-ula", ip: "fc00::1", expIsIP: true, expPriv: true},
		{name: "ipv6-private-link", ip: "fe80::1", expIsIP: true, expPriv: false},
		{name: "ipv6-private-loopback", ip: "::1", expIsIP: true, expPriv: false},
		{name: "ipv6-private-doc", ip: "fd12:3456:789a::1", expIsIP: true, expPriv: true},

		// IPv6边界情况
		{name: "ipv6-private-min", ip: "fc00::", expIsIP: true, expPriv: true},
		{name: "ipv6-private-max", ip: "fdff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", expIsIP: true, expPriv: true},
		{name: "ipv6-public-edge", ip: "fbff:ffff::", expIsIP: true, expPriv: false},
		{name: "ipv6-compressed", ip: "::ffff:********", expIsIP: true, expPriv: true}, // IPv4映射地址

	}
	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			isIP, isPrivate := IsPrivateIP(tc.ip)
			assert.Equal(t, tc.expIsIP, isIP, "IP: %s", tc.ip)
			assert.Equal(t, tc.expPriv, isPrivate, "IP: %s", tc.ip)
		})
	}
}

func TestIPContains(t *testing.T) {
	cases := []struct {
		name     string
		ips      string
		target   string
		expected bool
	}{
		// 无效目标IP测试
		{name: "invalid-target", ips: "***********", target: "not.an.ip", expected: false},
		{name: "empty-target", ips: "***********", target: "", expected: false},
		{name: "out-of-range", ips: "***********", target: "***********56", expected: false},

		// 单个IP匹配测试 (IPv4)
		{name: "ipv4-match", ips: "***********", target: "***********", expected: true},
		{name: "ipv4-mismatch", ips: "***********", target: "***********", expected: false},
		{name: "ipv4-different-format", ips: "***************", target: "***********", expected: false},

		// 单个IP匹配测试 (IPv6)
		{name: "ipv6-match", ips: "2001:db8::1", target: "2001:db8::1", expected: true},
		{name: "ipv6-mismatch", ips: "2001:db8::1", target: "2001:db8::2", expected: false},
		{name: "ipv6-compressed-match", ips: "2001:db8::1", target: "2001:db8::1", expected: true},

		// CIDR范围匹配测试 (IPv4)
		{name: "cidr-ipv4-contained", ips: "***********/24", target: "***********00", expected: true},
		{name: "cidr-ipv4-boundary-low", ips: "***********/24", target: "***********", expected: true},
		{name: "cidr-ipv4-boundary-high", ips: "***********/24", target: "***********55", expected: true},
		{name: "cidr-ipv4-outside", ips: "***********/24", target: "***********", expected: false},
		{name: "cidr-ipv4-subnet", ips: "10.0.0.0/8", target: "**************", expected: true},
		{name: "cidr-ipv4-single", ips: "***********/32", target: "***********", expected: true},

		// CIDR范围匹配测试 (IPv6)
		{name: "cidr-ipv6-contained", ips: "2001:db8::/64", target: "2001:db8::abcd", expected: true},
		{name: "cidr-ipv6-boundary-low", ips: "2001:db8::/64", target: "2001:db8::", expected: true},
		{name: "cidr-ipv6-boundary-high", ips: "2001:db8::/64", target: "2001:db8::ffff:ffff:ffff:ffff", expected: true},
		{name: "cidr-ipv6-outside", ips: "2001:db8::/64", target: "2001:db9::1", expected: false},
		{name: "cidr-ipv6-compressed", ips: "2001:db8::/64", target: "2001:0db8::0001", expected: true},
		{name: "cidr-ipv6-single", ips: "2001:db8::1/128", target: "2001:db8::1", expected: true},

		// 无效CIDR格式测试
		{name: "invalid-cidr-format", ips: "***********/33", target: "***********", expected: false},
		{name: "invalid-cidr-ipv6", ips: "2001:db8::/129", target: "2001:db8::1", expected: false},
		{name: "malformed-cidr", ips: "***********/24/extra", target: "***********", expected: false},
		{name: "non-cidr-with-slash", ips: "invalid/format", target: "***********", expected: false},

		// 混合IP类型测试
		{name: "ipv4-in-ipv6-cidr", ips: "::ffff:0:0/96", target: "***********", expected: true},
		{name: "ipv6-in-ipv4-cidr", ips: "***********/24", target: "2001:db8::1", expected: false},
		{name: "ipv4-mapped", ips: "::ffff:***********/120", target: "***********", expected: true},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			result := IPContains(tc.ips, tc.target)
			assert.Equal(t, tc.expected, result,
				"IPs: %s, Target: %s", tc.ips, tc.target)
		})
	}
}

func Test_CIDRToIPRange(t *testing.T) {
	cases := []struct {
		name     string
		cidr     string
		expected []string
		err      bool
	}{
		// 有效 IPv4 CIDR
		{
			name:     "ipv4-small-range",
			cidr:     "***********/30",
			expected: []string{"***********", "***********", "***********", "***********"},
			err:      false,
		},
		{
			name:     "ipv4-single-ip",
			cidr:     "***********/32",
			expected: []string{"***********"},
			err:      false,
		},
		{
			name:     "ipv4-class-a",
			cidr:     "10.0.0.0/8",
			expected: []string{"10.0.0.0"}, // 只验证第一个IP，避免生成完整列表
			err:      false,
		},
		{
			name:     "ipv4-network-address",
			cidr:     "***********/24",
			expected: []string{"***********", "***********", "***********55"},
			err:      false,
		},

		// 有效 IPv6 CIDR
		{
			name:     "ipv6-small-range",
			cidr:     "2001:db8::/126",
			expected: []string{"2001:db8::", "2001:db8::1", "2001:db8::2", "2001:db8::3"},
			err:      false,
		},
		{
			name:     "ipv6-single-ip",
			cidr:     "2001:db8::1/128",
			expected: []string{"2001:db8::1"},
			err:      false,
		},
		{
			name:     "ipv6-large-range",
			cidr:     "2001:db8::/120",
			expected: []string{"2001:db8::", "2001:db8::1", "2001:db8::ff"},
			err:      false,
		},

		// 无效 CIDR
		{name: "invalid-format", cidr: "***********/33", err: true},
		{name: "invalid-ipv6", cidr: "2001:db8::/129", err: true},
		{name: "non-cidr", cidr: "not-a-cidr", err: true},
		{name: "empty", cidr: "", err: true},
		{name: "ip-only", cidr: "***********", err: true},
		{name: "mask-only", cidr: "/24", err: true},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := CIDRToIPRange(tc.cidr)

			if tc.err {
				assert.Error(t, err, "CIDR: %s", tc.cidr)
				return
			}

			assert.NoError(t, err, "CIDR: %s", tc.cidr)

			//// 对于大范围CIDR，只验证部分IP
			//if len(tc.expected) > 0 && len(tc.expected) < 4 {
			//	// 验证关键点：首IP、第二个IP、末IP
			//	assert.Equal(t, tc.expected[0], result[0], "First IP mismatch")
			//	if len(result) > 1 {
			//		assert.Equal(t, tc.expected[1], result[1], "Second IP mismatch")
			//	}
			//	if len(tc.expected) > 2 {
			//		assert.Equal(t, tc.expected[2], result[len(result)-1], "Last IP mismatch")
			//	}
			//} else {
			//	// 对于小范围CIDR，验证完整列表
			//	assert.Equal(t, tc.expected, result, "IP list mismatch")
			//}

			// 验证IP数量是否正确
			if !tc.err && len(tc.expected) > 0 {
				_, ipNet, _ := net.ParseCIDR(tc.cidr)
				ones, bits := ipNet.Mask.Size()
				expectedCount := 1 << (bits - ones)
				assert.Equal(t, expectedCount, len(result), "IP count mismatch")
			}
		})
	}
}

// 测试大范围CIDR的性能和正确性
func Test_CIDRToIPRange_Large(t *testing.T) {
	t.Run("ipv4-large-range", func(t *testing.T) {
		cidr := "***********/24" // 256个IP
		result, err := CIDRToIPRange(cidr)
		assert.NoError(t, err)

		// 验证数量
		assert.Equal(t, 256, len(result))

		// 验证首尾IP
		assert.Equal(t, "***********", result[0])
		assert.Equal(t, "*************", result[255])

		// 验证中间IP
		assert.Equal(t, "*************", result[128])
	})

	t.Run("ipv6-large-range", func(t *testing.T) {
		cidr := "2001:db8::/120" // 256个IP
		result, err := CIDRToIPRange(cidr)
		assert.NoError(t, err)

		// 验证数量
		assert.Equal(t, 256, len(result))

		// 验证首尾IP
		assert.Equal(t, "2001:db8::", result[0])
		assert.Equal(t, "2001:db8::ff", result[255])

		// 验证中间IP
		assert.Equal(t, "2001:db8::80", result[128])
	})
}

// 测试边界情况
func Test_CIDRToIPRange_EdgeCases(t *testing.T) {
	t.Run("ipv4-max-mask", func(t *testing.T) {
		cidr := "***********/32"
		result, err := CIDRToIPRange(cidr)
		assert.NoError(t, err)
		assert.Equal(t, []string{"***********"}, result)
	})

	t.Run("ipv4-min-mask", func(t *testing.T) {
		cidr := "0.0.0.0/0" // 整个IPv4空间（4,294,967,296个IP）
		_, err := CIDRToIPRange(cidr)
		assert.Error(t, err) // 实际中应避免，但测试错误处理
	})

	t.Run("ipv6-max-mask", func(t *testing.T) {
		cidr := "2001:db8::1/128"
		result, err := CIDRToIPRange(cidr)
		assert.NoError(t, err)
		assert.Equal(t, []string{"2001:db8::1"}, result)
	})

	t.Run("ipv4-broadcast", func(t *testing.T) {
		cidr := "***********55/24"
		result, err := CIDRToIPRange(cidr)
		assert.NoError(t, err)
		assert.Contains(t, result, "***********55")
	})
}

func TestIsIPv6(t *testing.T) {
	cases := []struct {
		in  string
		exp bool
	}{
		{in: "127.0.0.1", exp: false},
		{in: "*******", exp: false},
		{in: "***************", exp: false},
		{in: "::1", exp: true},
		{in: "127.0.1::1", exp: false},
		{in: "hello.word", exp: false},
		{in: "2001:0db8:85a3:0000:0000:8a2e:0370:7334", exp: true},
		{in: "2606:4700:3031::ac43:c635", exp: true},
	}
	for _, v := range cases {
		b := IsIPv6(v.in)
		assert.Equal(t, v.exp, b)
	}
}

func TestIPv6Full(t *testing.T) {
	cases := []struct {
		in, exp string
	}{
		{in: "127.0.0.1", exp: "127.0.0.1"},
		{in: "::1", exp: "0000:0000:0000:0000:0000:0000:0000:0001"},
		{in: "2001:db8::1:2:3", exp: "2001:0db8:0000:0000:0000:0001:0002:0003"},
		{in: "2001:db8::1:02:03", exp: "2001:0db8:0000:0000:0000:0001:0002:0003"},
		{in: "hello", exp: ""},
	}
	for _, v := range cases {
		result := IPExpanded(v.in)
		assert.Equal(t, v.exp, result)
	}
}

func TestIsValidatePort(t *testing.T) {
	cases := []struct {
		in  string
		exp bool
	}{
		{in: "", exp: false},
		{in: "1", exp: true},
		{in: "8080", exp: true},
		{in: "65535", exp: true},
		{in: "65536", exp: false},
		{in: "hello", exp: false},
	}
	for _, v := range cases {
		b := IsValidatePort(v.in)
		assert.Equal(t, v.exp, b)
	}
}

func TestDomainFromUrl(t *testing.T) {
	cases := []struct {
		in, exp string
	}{
		{in: "www.baidu.com", exp: "www.baidu.com"},
		{in: "https://www.baidu.com", exp: "www.baidu.com"},
		{in: "@www.baidu.com/%12fjasoifij/#$43", exp: "www.baidu.com"},
		{in: "https://127.0.0.1", exp: ""},
		{in: "http://baidu.com:443", exp: "baidu.com"},
		{in: "baidu.com:443", exp: "baidu.com"},
	}
	for _, v := range cases {
		s := DomainFromUrl(v.in)
		assert.Equal(t, v.exp, s)
	}
}

func TestFindRootDomain(t *testing.T) {
	cases := []struct {
		in, exp string
		isErr   bool
	}{
		{in: "www.baidu.com", exp: "baidu.com", isErr: false},
		{in: "https://www.baidu.com", exp: "baidu.com", isErr: false},
		{in: "https://abc.www.china.com.cn", exp: "china.com.cn", isErr: false},
		{in: "www.google.com", exp: "google.com", isErr: false},
		{in: "https://cloud.git.gobies.org", exp: "gobies.org", isErr: false},
		{in: "subdomain.example.co.uk", exp: "example.co.uk", isErr: false},
		{in: "www.subdomain.domain.com", exp: "domain.com", isErr: false},
		{in: "http://sub.domain.co.jp", exp: "domain.co.jp", isErr: false},
		{in: "localhost", exp: "", isErr: true},
		{in: "***********", exp: "", isErr: true},
		{in: "::1", exp: "", isErr: true},
		{in: "http://***********", exp: "", isErr: true},
	}

	for _, v := range cases {
		t.Run(v.in, func(t *testing.T) {
			s, err := FindRootDomain(v.in)
			assert.Equal(t, v.exp, s)
			assert.Equal(t, v.isErr, err != nil)
		})
	}
}

func TestIPv6DomainReplace(t *testing.T) {
	cases := []struct {
		in, exp string
	}{
		{in: "https://baidu.com", exp: "https://baidu.com"},
		{in: "[::1]:80/", exp: "[0000:0000:0000:0000:0000:0000:0000:0001]:80/"},
		{in: "baidu.com/resource", exp: "baidu.com/resource"},
		{in: "http://[::1]:8080/resource", exp: "http://[0000:0000:0000:0000:0000:0000:0000:0001]:8080/resource"},
		{in: "[::01]:8080/resource", exp: "[0000:0000:0000:0000:0000:0000:0000:0001]:8080/resource"},
		{in: "", exp: ""},
		{in: ":/baidu.com/resource", exp: ":/baidu.com/resource"},
		{in: ":[::1]/resource", exp: ":[0000:0000:0000:0000:0000:0000:0000:0001]/resource"},
	}

	for _, v := range cases {
		r := IPv6DomainReplace(v.in)
		assert.Equal(t, v.exp, r)
	}
}

func Test_IsRootDomain(t *testing.T) {
	cases := []struct {
		name string
		in   string
		exp  bool
	}{
		{name: "test-ipv4", in: "127.0.0.1", exp: false},
		{name: "test-ipv6", in: "::1", exp: false},
		{name: "test-empty", in: "", exp: false},
		{name: "test-www.baidu.com", in: "www.baidu.com", exp: false},
		{name: "test-baidu.com", in: "baidu.com", exp: true},
		{name: "test-non-domain", in: "hello world", exp: false},
		{name: "test-china.gov.cn", in: "china.gov.cn", exp: true},
		{name: "test-www.china.gov.cn", in: "www.china.gov.cn", exp: false},
	}

	for _, v := range cases {
		t.Run(v.name, func(t *testing.T) {
			assert.Equal(t, v.exp, IsRootDomain(v.in))
		})
	}
}

func TestGetDNSARecords(t *testing.T) {
	// 定义测试用例（实际DNS查询需要网络连接）
	cases := []struct {
		name        string // 测试用例的名称（描述性文字）
		domain      string // 输入给函数的参数 --》 测试用例
		expectIPs   bool   // 是否期望返回IP地址
		expectError bool   // 是否期望返回错误
	}{
		{
			name:        "valid-domain",
			domain:      "example.com",
			expectIPs:   true,
			expectError: false,
		},
		{
			name:        "non-existent-domain",
			domain:      "nonexistent-domain-12345.example.com", // 输入给函数的参数 --》 测试用例
			expectIPs:   false,
			expectError: true,
		},
		{
			name:        "invalid-domain",
			domain:      "invalid..domain", // 输入给函数的参数 --》 测试用例
			expectIPs:   false,
			expectError: true,
		},
		{
			name:        "empty-domain",
			domain:      "",    // 输入给函数的参数 --》 测试用例
			expectIPs:   false, // 期望无IP
			expectError: false, // 期望无错误（修改这里）
		},
		{
			name:        "ipv4-address",
			domain:      "***********", // 输入给函数的参数 --》 测试用例
			expectIPs:   false,
			expectError: true,
		},
	}

	for _, tc := range cases { // 遍历测试组
		t.Run(tc.name, func(t *testing.T) { // 使用测试组
			ips, err := GetDNSARecords(tc.domain)

			// 验证错误预期
			if tc.expectError {
				assert.Error(t, err, "Expected error for domain: %s", tc.domain)
			} else {
				assert.NoError(t, err, "Unexpected error for domain: %s", tc.domain)
			}

			// 验证IP结果预期
			if tc.expectIPs {
				assert.NotEmpty(t, ips, "Expected IP addresses for domain: %s", tc.domain)
				// 验证返回的是有效IP格式
				for _, ip := range ips {
					assert.NotNil(t, net.ParseIP(ip), "Invalid IP format: %s", ip)
				}
			} else {
				assert.Empty(t, ips, "Unexpected IP addresses for domain: %s", tc.domain)
			}
		})
	}
}
func TestGetDNSAAAARecords(t *testing.T) {
	// 定义测试用例（实际DNS查询需要网络连接）
	cases := []struct {
		name        string
		domain      string
		expectIPs   bool // 是否期望返回IP地址
		expectError bool // 是否期望返回错误
	}{
		{
			name:        "valid-domain-with-AAAA", // 有AAAA记录的域名
			domain:      "ipv6.google.com",        // Google的IPv6测试域名
			expectIPs:   true,
			expectError: false,
		},
		{
			name:        "valid-domain-without-AAAA", // 没有AAAA记录的域名
			domain:      "ipv4only.example.com",      // 使用IANA保留的测试域名
			expectIPs:   false,
			expectError: true, // 域名不存在会返回错误
		},
		{
			name:        "non-existent-domain",
			domain:      "nonexistent-domain-12345.example.com",
			expectIPs:   false,
			expectError: true,
		},
		{
			name:        "invalid-domain",
			domain:      "invalid..domain",
			expectIPs:   false,
			expectError: true,
		},
		{
			name:        "empty-domain",
			domain:      "",
			expectIPs:   false,
			expectError: false,
		},
		{
			name:        "ipv6-address",
			domain:      "2001:4860:4860::8888", // Google DNS的IPv6地址
			expectIPs:   false,
			expectError: true,
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			ips, err := GetDNSAAAARecords(tc.domain)

			// 验证错误预期
			if tc.expectError {
				assert.Error(t, err, "Expected error for domain: %s", tc.domain)
			} else {
				assert.NoError(t, err, "Unexpected error for domain: %s", tc.domain)
			}

			// 验证IP结果预期
			if tc.expectIPs {
				assert.NotEmpty(t, ips, "Expected IP addresses for domain: %s", tc.domain)
				// 验证返回的是有效的IPv6地址
				for _, ip := range ips {
					parsedIP := net.ParseIP(ip)
					assert.NotNil(t, parsedIP, "Invalid IP format: %s", ip)
					assert.NotNil(t, parsedIP.To16(), "Expected IPv6 address, got: %s", ip)
					assert.Nil(t, parsedIP.To4(), "Expected IPv6 address, got IPv4: %s", ip)
				}
			} else {
				assert.Empty(t, ips, "Unexpected IP addresses for domain: %s", tc.domain)
			}
		})
	}
}
func TestCompleteIPv6(t *testing.T) {
	cases := []struct {
		name string
		in   string
		exp  string
	}{
		// 有效IPv6地址（标准格式）
		{name: "full-ipv6", in: "2001:0db8:85a3:0000:0000:8a2e:0370:7334", exp: "2001:db8:85a3::8a2e:370:7334"},
		// 压缩格式IPv6（开头压缩）
		{name: "compressed-start", in: "::1", exp: "::1"},
		// 压缩格式IPv6（中间压缩）
		{name: "compressed-middle", in: "2001:db8::1", exp: "2001:db8::1"},
		// 压缩格式IPv6（结尾压缩）
		{name: "compressed-end", in: "fe80::", exp: "fe80::"},
		// 全零IPv6
		{name: "all-zero", in: "::", exp: "::"},
		// IPv4映射的IPv6地址
		{name: "ipv4-mapped", in: "::ffff:***********", exp: "***********"},
		// 混合大小写输入
		{name: "mixed-case", in: "2001:DB8::aBcD", exp: "2001:db8::abcd"},

		// IPv4地址（应返回原格式）
		{name: "valid-ipv4", in: "***********", exp: "***********"},

		// 无效输入
		{name: "empty-string", in: "", exp: ""},
		{name: "invalid-format", in: "2001::toomany::", exp: ""},
		{name: "non-ip-string", in: "not.an.ip", exp: ""},
		{name: "out-of-range", in: "2001:db8::g", exp: ""},
		{name: "incomplete-ip", in: "2001:db8:", exp: ""},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			got := CompleteIPv6(tc.in)
			assert.Equal(t, tc.exp, got, "Input: %s", tc.in)
		})
	}
}

func TestIsValidDomainStrict(t *testing.T) {
	// 生成超长域名（254字符）
	longDomain := strings.Repeat("a", 254)

	// 生成长标签域名（64字符标签）
	longLabelDomain := strings.Repeat("b", 64) + ".example.com"

	cases := []struct {
		name   string
		domain string
		exp    bool
	}{
		// 空域名和长度测试
		{name: "empty", domain: "", exp: false},
		{name: "too-long", domain: longDomain, exp: false},

		// 纯ASCII域名测试
		{name: "valid-ascii", domain: "example.com", exp: true},
		{name: "valid-subdomain", domain: "www.example.com", exp: true},
		{name: "valid-hyphens", domain: "valid-domain.com", exp: true},
		{name: "single-letter-tld", domain: "example.x", exp: true},
		{name: "invalid-start-hyphen", domain: "-invalid.com", exp: false},
		{name: "invalid-end-hyphen", domain: "invalid-.com", exp: false},
		{name: "invalid-char", domain: "invalid$domain.com", exp: false},
		{name: "long-label", domain: longLabelDomain, exp: false},
		{name: "dot-start", domain: ".example.com", exp: false},
		{name: "dot-end", domain: "example.com.", exp: false}, // 规范允许结尾点但通常不推荐

		// 国际化域名测试 (IDN)
		{name: "valid-idn", domain: "中国.cn", exp: true},
		{name: "valid-idn-mixed", domain: "café.fr", exp: true},
		{name: "valid-idn-cyrillic", domain: "россия.рф", exp: true},
		{name: "valid-idn-arabic", domain: "موقع.وزارة-الاتصالات.مصر", exp: true},
		{name: "invalid-idn-too-long", domain: "www." + strings.Repeat("é", 64) + ".com", exp: false},
		{name: "invalid-idn-chars", domain: "invälid?.com", exp: false},

		// 边界和极端情况
		{name: "min-length", domain: "a.b", exp: true},
		{name: "max-length", domain: strings.Repeat("a", 63) + "." + strings.Repeat("b", 63) + "." + strings.Repeat("c", 63) + ".d", exp: true},
		{name: "all-numeric-tld", domain: "example.123", exp: false},
		{name: "invalid-tld", domain: "example.invalid", exp: true},
		{name: "space-in-domain", domain: "invalid domain.com", exp: false},
		{name: "emoji-domain", domain: "i❤️.ws", exp: true}, // 通常不被接受
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			got := IsValidDomainStrict(tc.domain)
			assert.Equal(t, tc.exp, got, "Domain: %s", tc.domain)
		})
	}
}
