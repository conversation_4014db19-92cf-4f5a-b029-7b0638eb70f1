package utils

import (
	"strings"
)

// ContainsString 检查字符串切片中是否包含指定值
func ContainsString(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// StringsEqualFold 比较两个字符串是否相等（忽略大小写）
func StringsEqualFold(s1, s2 string) bool {
	// 去除所有空格和括号，转换成小写字母
	chars := []string{" ", "(", ")", "（", "）"}
	for _, char := range chars {
		s1 = strings.ReplaceAll(s1, char, "")
		s2 = strings.ReplaceAll(s2, char, "")
	}
	s1 = strings.ToLower(s1)
	s2 = strings.ToLower(s2)

	// 判断两个字符串是否相等
	return s1 == s2
}

// ContainsUint64 检查uint64切片中是否包含指定值
func ContainsUint64(slice []uint64, item uint64) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
