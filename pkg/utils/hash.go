package utils

import (
	"bytes"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/twmb/murmur3"
	"golang.org/x/crypto/bcrypt"
)

func Hash(str string) (string, error) {
	hashed, err := bcrypt.GenerateFromPassword([]byte(str), 12)
	return string(hashed), err
}

// Mmh3Hash32 计算 mmh3 hash
func Mmh3Hash32(raw string) string {
	h32 := murmur3.New32()
	_, _ = h32.Write([]byte(raw))
	return fmt.Sprintf("%d", int32(h32.Sum32()))
}

// Mmh3Base64Encode 计算 base64 的值,mmh3 base64 编码，编码后的数据要求每 76 个字符加上换行符。具体原因 RFC 822 文档上有说明。然后 32 位 mmh3 hash
func Mmh3Base64Encode(braw string) string {
	bckd := base64.StdEncoding.EncodeToString([]byte(braw))
	var buffer bytes.Buffer
	for i := 0; i < len(bckd); i++ {
		ch := bckd[i]
		buffer.WriteByte(ch)
		if (i+1)%76 == 0 {
			buffer.WriteByte('\n')
		}
	}
	buffer.WriteByte('\n')
	return buffer.String()
}

func Md5Hash(val interface{}) string {
	jsonBytes, err := json.Marshal(val)
	if err != nil {
		switch val.(string) {
		default:
			return fmt.Sprintf("%x", md5.Sum([]byte(val.(string))))
		}
	}
	return fmt.Sprintf("%x", md5.Sum(jsonBytes))
}

func Md5bHash(b []byte, isUpper bool) string {
	if len(b) == 0 {
		return ""
	}
	h := md5.Sum(b)
	result := hex.EncodeToString(h[:])

	if isUpper {
		return strings.ToUpper(result)
	}
	return strings.ToLower(result)
}

func Md5sHash(s string, isUpper bool) (h string) {
	return Md5bHash([]byte(s), isUpper)
}

func IsSame(str string, hashed string) bool {
	return bcrypt.CompareHashAndPassword([]byte(hashed), []byte(str)) == nil
}
