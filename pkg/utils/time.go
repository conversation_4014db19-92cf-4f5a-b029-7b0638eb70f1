package utils

import (
	"time"

	"github.com/jinzhu/now"
	"golang.org/x/exp/constraints"
)

const (
	DateTimeLayout        = "2006-01-02 15:04:05"
	DateLayout            = "2006-01-02"
	TimeLayout            = "15:04:05"
	DateIntegerLayout     = "20060102"
	DateTimeIntegerLayout = "20060102150405"
	DateMonthLayout       = "2006-01"

	Day = 24 * time.Hour
)

func ParseTime(s string) (time.Time, error) {
	t, err := time.ParseInLocation(DateTimeLayout, s, time.Now().Location())
	return t, err
}

func PraseStringTime(s string, layout string, d ...time.Time) (time.Time, error) {
	t, err := time.ParseInLocation(layout, s, time.Now().Location())
	if err != nil && len(d) > 0 {
		t = d[0]
	}
	return t, err
}

// CurrentTime return string of time.Now() with 2006-01-02 15:04:05 layout
func CurrentTime() string {
	return time.Now().Format(DateTimeLayout)
}

func TimeFormat(d time.Time, layout ...string) string {
	lo := DateTimeLayout

	if s := ListFirstEle(layout); s != "" {
		lo = layout[0]
	}

	return d.Format(lo)
}

func BeginningOfDay(t time.Time) time.Time {
	d := now.New(t)
	return d.BeginningOfDay()
}

// UnixSecToTime return time.Time of Unix Seconds
func UnixSecToTime[T constraints.Integer](d T) time.Time {
	return time.Unix(int64(d), 0)
}

// UnixMilliToTime return time.Time of Unix Milliseconds
func UnixMilliToTime[T constraints.Integer](d T) time.Time {
	return time.UnixMilli(int64(d))
}

// GenerateTimeBetween return time.Time of Unix Milliseconds
// d[1] will plus a day
func GenerateTimeBetween(d [2]string) (t1 time.Time, t2 time.Time, err error) {
	if d[0] == "" {
		d[0] = "0001-01-01"
	}
	if d[1] == "" {
		d[1] = time.Now().Format(DateLayout)
	}
	t1, err = PraseStringTime(d[0], DateLayout)
	if err != nil {
		return
	}
	t2, err = PraseStringTime(d[1], DateLayout)
	if err != nil {
		return
	}

	t1 = BeginningOfDay(t1)
	t2 = BeginningOfDay(t2.Add(24 * time.Hour))

	return t1, t2, nil
}

// 获取某日期所在季度的第一天
func GetQuarterStartDate(t time.Time) time.Time {
	year := t.Year()
	month := (t.Month()-1)/3*3 + 1 // 计算当前月份属于哪个季度
	return time.Date(year, month, 1, 0, 0, 0, 0, t.Location())
}

// 获取上个季度的第一天
func GetLastQuarterStartDate(t time.Time) time.Time {
	currentQuarterStart := GetQuarterStartDate(t)
	lastQuarterStart := currentQuarterStart.AddDate(0, -3, 0) // 减去3个月得到上个季度
	return lastQuarterStart
}
