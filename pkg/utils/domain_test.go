package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestCheckIdnDomain 测试IDN域名检测功能
func TestCheckIdnDomain(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		expected bool
	}{
		{
			name:     "空域名",
			domain:   "",
			expected: false,
		},
		{
			name:     "英文域名",
			domain:   "example.com",
			expected: false,
		},
		{
			name:     "中文域名",
			domain:   "测试.com",
			expected: true,
		},
		{
			name:     "punycode域名",
			domain:   "xn--0zwm56d.com",
			expected: true,
		},
		{
			name:     "混合域名",
			domain:   "test.测试.com",
			expected: true,
		},
		{
			name:     "带协议的域名",
			domain:   "https://测试.com",
			expected: true,
		},
		{
			name:     "带路径的域名",
			domain:   "测试.com/path",
			expected: true,
		},
		{
			name:     "普通英文子域名",
			domain:   "sub.example.com",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CheckIdnDomain(tt.domain)
			assert.Equal(t, tt.expected, result, "域名: %s", tt.domain)
		})
	}
}

// TestGetIdnDomain 测试IDN域名编码转换功能
func TestGetIdnDomain(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		full     bool
		expected string
	}{
		{
			name:     "空域名",
			domain:   "",
			full:     false,
			expected: "",
		},
		{
			name:     "英文域名_不完整",
			domain:   "example.com",
			full:     false,
			expected: "example.com",
		},
		{
			name:     "英文域名_完整",
			domain:   "example.com",
			full:     true,
			expected: "example.com",
		},
		{
			name:     "中文域名转punycode",
			domain:   "测试.com",
			full:     false,
			expected: "xn--0zwm56d.com",
		},
		{
			name:     "中文域名保持原样",
			domain:   "测试.com",
			full:     true,
			expected: "测试.com",
		},
		{
			name:     "punycode转中文",
			domain:   "xn--0zwm56d.com",
			full:     false,
			expected: "测试.com",
		},
		{
			name:     "punycode转中文_完整",
			domain:   "xn--0zwm56d.com",
			full:     true,
			expected: "测试.com",
		},
		{
			name:     "复杂中文域名",
			domain:   "子域名.测试.com",
			full:     false,
			expected: "xn--eqrt2grvd.xn--0zwm56d.com",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetIdnDomain(tt.domain, tt.full)
			assert.Equal(t, tt.expected, result, "域名: %s, full: %v", tt.domain, tt.full)
		})
	}
}

// TestGetFullDomain 测试URL域名提取功能
func TestGetFullDomain(t *testing.T) {
	tests := []struct {
		name     string
		url      string
		expected string
	}{
		{
			name:     "空URL",
			url:      "",
			expected: "",
		},
		{
			name:     "纯域名",
			url:      "example.com",
			expected: "example.com",
		},
		{
			name:     "带HTTP协议",
			url:      "http://example.com",
			expected: "example.com",
		},
		{
			name:     "带HTTPS协议",
			url:      "https://example.com",
			expected: "example.com",
		},
		{
			name:     "带路径",
			url:      "https://example.com/path/to/page",
			expected: "example.com",
		},
		{
			name:     "带端口",
			url:      "https://example.com:8080",
			expected: "example.com",
		},
		{
			name:     "带端口和路径",
			url:      "https://example.com:8080/path",
			expected: "example.com",
		},
		{
			name:     "子域名",
			url:      "https://sub.example.com",
			expected: "sub.example.com",
		},
		{
			name:     "中文域名",
			url:      "https://测试.com",
			expected: "测试.com",
		},
		{
			name:     "punycode域名",
			url:      "xn--0zwm56d.com",
			expected: "测试.com",
		},
		{
			name:     "IPv6地址",
			url:      "https://[2001:db8::1]:8080/path",
			expected: "[2001:db8::1]",
		},
		{
			name:     "复杂URL",
			url:      "https://user:<EMAIL>:8080/path?query=1#fragment",
			expected: "user:<EMAIL>",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetFullDomain(tt.url)
			assert.Equal(t, tt.expected, result, "URL: %s", tt.url)
		})
	}
}

// TestIsDomain 测试isDomain函数的所有逻辑分支
func TestIsDomain(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		expected bool
	}{
		// 正向测试用例 - 有效域名
		{
			name:     "标准域名",
			domain:   "example.com",
			expected: true,
		},
		{
			name:     "子域名",
			domain:   "www.example.com",
			expected: true,
		},
		{
			name:     "多级子域名",
			domain:   "sub.www.example.com",
			expected: true,
		},
		{
			name:     "短域名",
			domain:   "a.b",
			expected: true,
		},
		{
			name:     "国际域名",
			domain:   "example.co.uk",
			expected: true,
		},
		{
			name:     "数字域名",
			domain:   "123.com",
			expected: true,
		},
		{
			name:     "连字符域名",
			domain:   "my-site.com",
			expected: true,
		},
		{
			name:     "连字符子域名",
			domain:   "sub-domain.example-site.com",
			expected: true,
		},

		// 通配符域名测试
		{
			name:     "有效通配符域名",
			domain:   "*.example.com",
			expected: true,
		},
		{
			name:     "有效通配符多级域名",
			domain:   "*.sub.example.com",
			expected: true,
		},

		// 负向测试用例 - 无效域名
		{
			name:     "空字符串",
			domain:   "",
			expected: false,
		},
		{
			name:     "无点号域名",
			domain:   "example",
			expected: false,
		},
		{
			name:     "只有点号",
			domain:   ".",
			expected: false,
		},
		{
			name:     "以点号开头",
			domain:   ".example.com",
			expected: false,
		},
		{
			name:     "以点号结尾",
			domain:   "example.com.",
			expected: true,
		},
		{
			name:     "连续点号",
			domain:   "example..com",
			expected: false,
		},
		{
			name:     "部分以连字符开头",
			domain:   "-example.com",
			expected: false,
		},
		{
			name:     "部分以连字符结尾",
			domain:   "example-.com",
			expected: false,
		},
		{
			name:     "子域名以连字符开头",
			domain:   "www.-example.com",
			expected: false,
		},
		{
			name:     "子域名以连字符结尾",
			domain:   "www.example-.com",
			expected: false,
		},
		{
			name:     "包含非法字符",
			domain:   "example@.com",
			expected: false,
		},
		{
			name:     "包含空格",
			domain:   "exam ple.com",
			expected: false,
		},
		{
			name:     "包含下划线",
			domain:   "exam_ple.com",
			expected: false,
		},
		{
			name:     "包含中文字符",
			domain:   "测试.com",
			expected: true,
		},

		// 通配符相关的无效用例
		{
			name:     "多个通配符",
			domain:   "*.*.example.com",
			expected: false,
		},
		{
			name:     "通配符位置错误",
			domain:   "sub.*.example.com",
			expected: false,
		},
		{
			name:     "通配符格式错误",
			domain:   "*example.com",
			expected: false,
		},
		{
			name:     "仅通配符",
			domain:   "*",
			expected: false,
		},
		{
			name:     "通配符加点号",
			domain:   "*.",
			expected: false,
		},

		// 边界测试用例
		{
			name:     "63字符部分",
			domain:   "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijk.com",
			expected: true,
		},
		{
			name:     "64字符部分",
			domain:   "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijkl.com",
			expected: false,
		},
		{
			name:     "单字符部分",
			domain:   "a.b.com",
			expected: true,
		},

		// 特殊格式测试
		{
			name:     "IP地址格式",
			domain:   "***********",
			expected: true,
		},
		{
			name:     "IPv6地址格式",
			domain:   "2001:db8::1",
			expected: false,
		},
		{
			name:     "端口号",
			domain:   "example.com:8080",
			expected: false,
		},
		{
			name:     "协议前缀",
			domain:   "http://example.com",
			expected: false,
		},
		{
			name:     "路径后缀",
			domain:   "example.com/path",
			expected: false,
		},

		// 真实域名测试（这些域名应该能通过DNS解析验证）
		{
			name:     "Google域名",
			domain:   "google.com",
			expected: true,
		},
		{
			name:     "GitHub域名",
			domain:   "github.com",
			expected: true,
		},
		{
			name:     "百度域名",
			domain:   "baidu.com",
			expected: true,
		},

		// 不存在但格式正确的域名（应该通过格式验证）
		{
			name:     "不存在的域名但格式正确",
			domain:   "this-domain-should-not-exist-12345.com",
			expected: true,
		},
		{
			name:     "随机格式正确域名",
			domain:   "random-test-domain-999.org",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsDomain(tt.domain)
			assert.Equal(t, tt.expected, result, "域名: %s", tt.domain)
		})
	}
}

// TestIsDomainWildcardSpecialCases 专门测试通配符域名的特殊情况
func TestIsDomainWildcardSpecialCases(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		expected bool
	}{
		{
			name:     "标准通配符域名",
			domain:   "*.example.com",
			expected: true,
		},
		{
			name:     "通配符加多级域名",
			domain:   "*.api.example.com",
			expected: true,
		},
		{
			name:     "通配符加国际域名",
			domain:   "*.example.co.uk",
			expected: true,
		},
		{
			name:     "多个通配符无效",
			domain:   "*.*.example.com",
			expected: false,
		},
		{
			name:     "通配符在中间无效",
			domain:   "api.*.example.com",
			expected: false,
		},
		{
			name:     "通配符在末尾无效",
			domain:   "api.example.*",
			expected: false,
		},
		{
			name:     "单独通配符无效",
			domain:   "*",
			expected: false,
		},
		{
			name:     "通配符后无域名无效",
			domain:   "*.",
			expected: false,
		},
		{
			name:     "通配符格式错误",
			domain:   "**.example.com",
			expected: false,
		},
		{
			name:     "通配符不以点分隔",
			domain:   "*example.com",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsDomain(tt.domain)
			assert.Equal(t, tt.expected, result, "通配符域名: %s", tt.domain)
		})
	}
}

// TestIsDomainBoundaryValues 测试边界值情况
func TestIsDomainBoundaryValues(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		expected bool
	}{
		{
			name:     "最短有效域名",
			domain:   "a.b",
			expected: true,
		},
		{
			name:     "63字符标签",
			domain:   "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijk.com",
			expected: true,
		},
		{
			name:     "64字符标签超限",
			domain:   "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijkl.com",
			expected: false,
		},
		{
			name:     "空标签",
			domain:   ".com",
			expected: false,
		},
		{
			name:     "空标签在中间",
			domain:   "example..com",
			expected: false,
		},
		{
			name:     "末尾空标签",
			domain:   "example.com.",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsDomain(tt.domain)
			assert.Equal(t, tt.expected, result, "边界值域名: %s", tt.domain)
		})
	}
}
