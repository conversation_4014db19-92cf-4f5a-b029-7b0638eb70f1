package image

import (
	"encoding/base64"
	"io/ioutil"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_Base64ToImage(t *testing.T) {
	imageName := "./image.png"
	s := "iVBORw0KGgoAAAANSUhEUgAAAPAAAABQCAMAAAAQlwhOAAAA81BMVEUAAAAiGSxQR1pxaHuVjJ89NEcUCx6OhZhDOk3Eu867ssUMAxZpYHNiWWwiGSzWzeAUCx5gV2rVzN9cU2YlHC9ORViOhZhgV2qIf5KTip04L0IQBxrb0uUKARTLwtUhGCsKARR4b4KCeYzZ0OOBeIubkqVlXG+so7ZJQFNDOk0QBxpUS16upbhlXG91bH9wZ3pZUGNTSl0jGi2ZkKOvprkNBBdoX3JCOUywp7pAN0p1bH8YDyJSSVxCOUyTip0zKj05MENjWm24r8IlHC8YDyKKgZTVzN9nXnG5sMN8c4ack6aDeo2If5KupbgMAxZrYnUbEiWG5QTcAAAAAXRSTlMAQObYZgAABmNJREFUeJzsm2tLKz0QxzOgggVFoWK9gVRbsGjVF3IsaLFYtdae7/91HrqXZCaZXHY3WXvg+SOH0910Mr9MMrnsVvwvpTPrnbtW/fBpZL/1UMXO2ZmN+O5uk4hHIyvxw0M1YuudOLzXUazUj/BSCPEVyYcQXV+7iY+qm3ysUni5XIqvr1aJnXePjioTPz5WI245wj65eY+5i5V4U6tGD3Xo+Jgl3iDV6KFObTpv7Aj/GwIBEa3tRbES0yPNKqxl3qrbCnt7MYhrV++xmhMbttdX8naoUWsY79LvWwJoKC2DWVkBnahvrVcXLseymlMwg8kLShlz3ApLeSJcNnYV5ElIITCIgQRY3knEjbVL3SojnLvi//pkYhAz3zLMAbkiI1xenNVCCdLuLibGEYawzq3zcslYmrMWA3QZYDZLSUx9KFpakXPAOzZjINgvAOizEm9Y9vGZt52jtAjOHa7stbPDEQOXmvI762thxGpE5XXbtt4p+gCJMEVnI2wmJnxHn4fdES7/tR+upOnzCNObxexjHpgIW0wBKQwAd6lmaFZahMEB7RgBMi8gYElvLkXCskgSEQI0ZZrYsO3I55JNJ0bTkLwcmEXSCNUFeNlrzjDb29s2t1RZ7DqgnusIYRngyzhAboEREQJNvBJWXkXMjGG0uLR+eV3u8rIFYoqFhxbXFM5kZhvD4JjJ5Fd/L8IdLajYK5cZOSqZZipmq7UGnBH7EI5/gKJHuNPp8MVs4Xkw7huDGG0ZBoMB74OFN/IRWVbbiCamDh8CW3iLxxyYig5iLeADZgXOr04yRecFIM9srGABESZlzZlYGOWw6bYm4XVVI//SlzSE7b4+98riPvs4wjc1ICpJSxe2CIPa0PJlrIwaIbPEwBG+uUlMbHQmftGjjueA74DmsGVXmvyAxfbS8P5Iz/Ta+UUHoANYfj4lmZgWW+DiMz7m3gHc7AHEz48itkUYjLRLFth6jMghDmmwxQIRZ7tbPYsFHO3VecSEa/lRV40OhgOMlxJlbx6rQVceiQHu66RtII+w6iszZY945gtxDV6UeXBV7IygHXShkMF4PEb3ILeMDsdIv8ftRarV8yT6+FYZTYh9ljjftRgV2wYQ3qPihfUYtYY/wnJRqftCN4eI900RT0N591niHJdJp1aVAxH1T7XXQy0jgD5MoZnNqBXtnOmclFXwJjvVdDqVrSjUn0Ddq/jbt25t5A4dATmJAW1nVSdhxgHQkUgjDKhPIEoQoB3myf1lZmSK2kH9yQlS/fkg6AdnYey51jXMssZ30Q3AfMiaGhzSNLlul9AypyP3oab34Nq27u6hgDwWgiR5waR9gBXdMaPWcFaA/sKOh4q2GTJmhGoNG3CAeX2FZYnwarXSg/Md4n+grpQ7WfsOh0Ozo5DRBgA9nTe7v3JWpDdTGeETnGGKCGtf/f6OR3x1dUU8AhgCyQqMx9DrGcQAYrVyExsnvZnBk5MTmva5cZUgwgJnabJyMkZtFmFuDHt4GWXE9LSs3a2wal8mwsRNz4YnuE41cmSA3aaealVU6lSvPah59azV5NC8AC4X2OB75PD01IT49JQSh0aKAjd6LJJ/OdtClabdrVeZl7z8cMpUz/ikXzDGdLPHIiBggRZ79VuPE/d6yzutXWohpBO6g2QdEyHPgFrlxj7BY3jf39mSi4UkNo8k5DIoIM8ESU7DLWRpnteIcLf0rFiK+B6YVNREhJ7vxNOt/J+x4hDQ7XblJ30hH6Hu4q2gSGPY8fMApNvbklg/ksi86JZhFYDesWuasaQmqLqmcvwAhMgS4fwK6CEFAM/bEL+nMF63DOBWXp/7TekHzFX1Gd+l1MIjuNBF8Jc/P9smfk1g8+KiAnGC+l16fU1CnMBmoZemBty8wcPS9655LL28WIj/RDEfnIg875pHlI33TyTi0IJt8Sqd049xeOPoOYXR8/PzFGZj6Pk5kLhakDaG13w6H8obaRi2rAbvX/w7vD38gedNMdsHKUU27fV6viJp1jcBqjNf+n+K5OUNj/BHYLlg1eDFP0X6G9cbQx8f0Yl13XtLYN6/yYmNK4cRrR8IcX/vJ0ZKzaupL8ThYTzig4ODkAg3UbPXXPv9fvQIp1XTF5n72b/zBha2GtVfXaG8rrf65/N5bce3ttomDpP7dxvzBo5vJm/A7zbSOP5fAAAA//9BQkt8zlFGEAAAAABJRU5ErkJggg=="
	bs, _ := base64.StdEncoding.DecodeString(s)
	err := WriteImage(string(bs), imageName)
	assert.Nil(t, err)
	os.Remove(imageName)
}

func TestToBase64(t *testing.T) {
	testCases := []struct {
		name        string
		fileContent []byte
		want        string
	}{
		{"normal file", []byte("Hello World"), "SGVsbG8gV29ybGQ="},
		{"empty file", []byte(""), ""},
		{"binary file", []byte{0x00, 0x01, 0x02, 0x03}, "AAECAw=="},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			// 创建临时文件
			tmpFile, err := ioutil.TempFile("", "test-")
			if err != nil {
				t.Fatalf("Failed to create temporary file: %v", err)
			}
			defer os.Remove(tmpFile.Name())

			// 写入内容
			if _, err := tmpFile.Write(tt.fileContent); err != nil {
				t.Fatalf("Failed to write to temporary file: %v", err)
			}
			if err := tmpFile.Close(); err != nil {
				t.Fatalf("Failed to close temporary file: %v", err)
			}

			// 调用函数
			got, err := ToBase64(tmpFile.Name())
			if err != nil {
				t.Fatalf("ToBase64() unexpected error: %v", err)
			}

			// 验证结果
			if got != tt.want {
				t.Errorf("ToBase64() = %v, want %v", got, tt.want)
			}
		})
	}

	// 测试不存在的文件
	t.Run("nonexistent file", func(t *testing.T) {
		_, err := ToBase64("nonexistent-file.txt")
		if err == nil {
			t.Errorf("ToBase64() expected error for nonexistent file, but got none")
		}
	})

}
