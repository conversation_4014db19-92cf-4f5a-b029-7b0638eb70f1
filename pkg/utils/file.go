package utils

import (
	"bytes"
	"crypto/md5"
	"crypto/tls"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strconv"
	"strings"

	"micro-service/pkg/cfg"
)

const (
	// FileAccessPathPrefix 文件访问路径前缀
	FileAccessPathPrefix = "/api/v1/files/"
	// RootStorage 根存储路径
	RootStorage = "/data/storage/"
)

// getRootStoragePath 获取根存储路径，优先使用配置中的值
func getRootStoragePath() string {
	// 如果没配置根数据目录时,默认为/data/storage/
	if cfg.LoadCommon().RootStorage == "" {
		return "/data/storage/"
	}
	return cfg.LoadCommon().RootStorage
}

func PathCreate(dir string) error {
	return os.MkdirAll(dir, os.ModePerm)
}

func MkdirWithModePerm(dirPath string, mode os.FileMode) (err error) {
	if len(dirPath) == 0 {
		return errors.New("the dir path is empty")
	}
	if _, err = os.Stat(dirPath); err != nil {
		if !os.IsExist(err) {
			err = os.MkdirAll(dirPath, mode)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// Mkdir create a specific dir
func Mkdir(dirPath string) error {
	return MkdirWithModePerm(dirPath, 0755)
}

// IsDirExist 判断目录是否存在
func IsDirExist(addr string) bool {
	s, err := os.Stat(addr)
	if err != nil {
		return os.IsExist(err)
	}
	return s.IsDir()
}

func FileCreate(content bytes.Buffer, name string) error {
	file, err := os.Create(name)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = file.WriteString(content.String())
	return err
}

// FileDelete removes the named file.
func FileDelete(filePath string) (err error) {
	if filePath != "" {
		err = os.Remove(filePath)
	}
	return err
}

func FileIsExist(fp string) bool {
	_, err := os.Stat(fp)
	return err == nil || os.IsExist(err)
}

// GetExt returns the extension of a given file name.
func GetExt(fileName string) string {
	return path.Ext(fileName)
}

func GetFileName(filePath string) (name string, extension string) {
	ext := GetExt(filePath)
	return strings.TrimSuffix(filepath.Base(filePath), ext), ext
}

// 获取文件大小
func GetFileSize(filename string) int64 {
	var result int64
	filepath.Walk(filename, func(path string, f os.FileInfo, err error) error {
		result = f.Size()
		return nil
	})
	return result
}

// FileSize 获取文件大小
func FileSize(filePath string, fmt byte, bit int) (float64, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}

	size := float64(info.Size()) // bytes
	var f float64
	switch fmt {
	case 'b', 'B':
		f = size
	case 'k', 'K':
		f = size / 1024
	case 'm', 'M':
		f = size / (1024 * 1024)
	case 'g', 'G':
		f = size / (1024 * 1024 * 1024)
	case 't', 'T':
		f = size / (1024 * 1024 * 1024 * 1024)
	default:
		return 0, errors.New("unknown unit")
	}

	if bit < 0 {
		bit = 0
	}
	fs := strconv.FormatFloat(f, 'f', bit, 64)
	f, _ = strconv.ParseFloat(fs, 64)
	return f, nil
}

// DownloadAndEncodeBase64 下载文件并转换为 base64
func DownloadAndEncodeBase64(url string) (string, error) {
	// 创建自定义的 HTTP 客户端，忽略 SSL 验证
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	resp, err := client.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// 转换为 base64
	return base64.StdEncoding.EncodeToString(body), nil
}

// SaveBase64Ico 保存 base64 编码的图片，兼容 PHP saveBase64Ico 逻辑
func SaveBase64Ico(base64Content string, userId uint64) string {
	if userId == 0 {
		userId = 0
	}
	data, err := base64.StdEncoding.DecodeString(base64Content)
	if err != nil {
		return ""
	}
	// 生成文件名
	hash := md5.Sum([]byte(base64Content))
	fileName := hex.EncodeToString(hash[:]) + ".ico"

	// 数据库存储的相对路径（保持不变）
	dbPath := filepath.Join("app", "public", "download", fmt.Sprintf("%d", userId), fileName)

	// 实际文件存储的绝对路径（使用配置的根存储路径）
	actualDir := filepath.Join(getRootStoragePath(), "app", "public", "download", fmt.Sprintf("%d", userId))
	actualFilePath := filepath.Join(actualDir, fileName)

	// 确保目录存在
	if err := os.MkdirAll(actualDir, 0777); err != nil {
		return ""
	}
	// 设置权限
	_ = exec.Command("chmod", "777", "-Rf", actualDir).Run()
	// 保存文件
	if _, err := os.Stat(actualFilePath); os.IsNotExist(err) {
		if err := os.WriteFile(actualFilePath, data, 0644); err != nil {
			return ""
		}
	}
	// 返回数据库路径（相对路径）
	return filepath.ToSlash(dbPath)
}

// GenDownloadUrl 生成下载 URL，兼容 PHP genDownloadUrl 逻辑
func GenDownloadUrl(sourceFile string, fileName string, delete bool) string {
	if sourceFile == "" {
		return ""
	}
	item := DownloadFile{
		Url:    sourceFile,
		Name:   fileName,
		Delete: delete,
	}
	encrypted, err := LaravelEncrypt(item)
	if err != nil {
		return sourceFile
	}
	return filepath.Join(FileAccessPathPrefix, encrypted+path.Ext(sourceFile))
}

// ParseDownloadUrl 解析加密的下载路径，兼容 PHP parseDownloadUrl 逻辑
func ParseDownloadUrl(url string, fullPath bool) string {
	if url == "" {
		return ""
	}
	if strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://") {
		return url
	}

	// 移除前缀
	url = strings.TrimPrefix(url, FileAccessPathPrefix)

	// 移除扩展名
	ext := path.Ext(url)
	url = strings.TrimSuffix(url, ext)

	// 解密
	decrypted, err := LaravelDecrypt(url)
	if err != nil {
		return url
	}

	var info DownloadFile
	if err := json.Unmarshal([]byte(decrypted), &info); err != nil {
		return url
	}

	if fullPath {
		return filepath.Join(RootStorage, info.Url)
	}
	return info.Url
}
