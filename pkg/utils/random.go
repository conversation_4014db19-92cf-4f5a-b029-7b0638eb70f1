package utils

import (
	"math/rand"
	"strconv"
	"time"
)

const (
	numberBytes       = "0123456789"
	alphabetBytes     = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	numberAndAlphabet = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	specialChar       = "#@!&"
)

func randWithCharset(charset string, length int) string {
	if length <= 0 {
		length = 1
	}

	rand.NewSource(time.Now().UnixNano() + int64(rand.Intn(100)))

	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}

func RandString(length int) string {
	return randWithCharset(numberAndAlphabet, length)
}

func <PERSON>Alphabet(length int) string {
	return randWithCharset(alphabetBytes, length)
}

func RandInt(length int) string {
	return randWithCharset(numberBytes, length)
}

func RandWithRange(begin, end uint) int {
	if begin > end {
		begin, end = end, begin
	}
	if begin == end {
		return int(begin)
	}

	diff := end - begin
	rd := rand.Intn(int(diff)) + int(begin)

	return rd
}

// RandFloat return a rand float number
func RandFloat(begin, end uint, bit int) float64 {
	if bit < 0 {
		bit = 0
	}

	base := float64(RandWithRange(begin, end)) + rand.Float64()
	fs := strconv.FormatFloat(base, 'f', bit, 64)
	f, _ := strconv.ParseFloat(fs, 64)

	return f
}

func RandFloat32(begin, end uint, bit int) float32 {
	return float32(RandFloat(begin, end, bit))
}
func RandStringWithSpecialChar(length int) string {
	if length < 7 {
		return randWithCharset(numberAndAlphabet, length)
	}
	return randWithCharset(alphabetBytes, length-5) + randWithCharset(numberBytes, 3) + randWithCharset(specialChar, 2)
}
