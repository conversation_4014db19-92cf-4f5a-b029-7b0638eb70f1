package utils

import (
	"net"
	"strings"
	"unicode"

	"golang.org/x/net/idna"
)

// CheckIdnDomain 检查域名是否是IDN域名
func CheckIdnDomain(domain string) bool {
	if domain == "" {
		return false
	}
	fullDomain := GetFullDomain(domain)
	// 使用golang.org/x/net/idna包来处理punycode编码/解码
	encoded, err := idna.ToASCII(fullDomain)
	if err != nil {
		// 编码失败，可能不是有效的IDN域名
		return false
	}

	decoded, err := idna.ToUnicode(fullDomain)
	if err != nil {
		// 解码失败，可能不是有效的IDN域名
		return false
	}

	// 如果编码后的结果与原域名不同，说明原域名包含非ASCII字符
	if encoded != fullDomain {
		return true
	}

	// 如果解码后的结果与原域名不同，说明原域名是punycode编码的
	if decoded != fullDomain {
		return true
	}

	return false
}

// 获取IDN域名的Punycode编码
func GetIdnDomain(domain string, full bool) string {
	// 如果域名为空，返回空字符串
	if domain == "" {
		return ""
	}

	// 获取完整域名（这里假设就是输入的domain，如果需要可以实现getFullDomain逻辑）
	fullDomain := domain

	// 尝试进行Punycode编码
	encoded, err := idna.ToASCII(fullDomain)
	if err != nil {
		return domain
	}

	// 尝试进行Punycode解码
	decoded, err := idna.ToUnicode(fullDomain)
	if err != nil {
		return domain
	}

	// 如果编码后的域名不等于原域名，说明原域名包含非ASCII字符
	if encoded != fullDomain {
		if full {
			// 返回原始域名（包含unicode字符）
			return fullDomain
		} else {
			// 返回编码后的域名（punycode格式）
			return encoded
		}
	}

	// 如果解码后的域名不等于原域名，说明原域名是punycode格式
	if decoded != fullDomain {
		return decoded
	}

	// 如果都相等，返回原域名
	return domain
}

// GetFullDomain 获取完整域名
func GetFullDomain(url string) string {
	if url == "" {
		return ""
	}
	// 移除协议前缀
	url = strings.Replace(url, "http://", "", 1)
	url = strings.Replace(url, "https://", "", 1)

	// 移除路径部分（如果有）
	if idx := strings.Index(url, "/"); idx != -1 {
		url = url[:idx]
	}

	// 移除端口号（如果有）
	if idx := strings.LastIndex(url, ":"); idx != -1 {
		// 检查是否是IPv6地址
		if !strings.Contains(url, "[") || strings.Index(url, "]") < idx {
			url = url[:idx]
		}
	}

	// 如果URL为空，返回空字符串
	if url == "" {
		return ""
	}

	// 尝试解码punycode域名
	decoded, err := idna.ToUnicode(url)
	if err != nil {
		// 如果解码失败，返回原始URL
		return url
	}

	return decoded
}

// IsDomain 判断域名是否有效
func IsDomain(domain string) bool {
	if domain == "" {
		return false
	}
	// 简单的点号检查
	if !strings.Contains(domain, ".") {
		return false
	}
	// 通配符特殊处理
	if strings.Contains(domain, "*") {
		// 处理通配符域名，如 "*.example.com"
		if strings.Count(domain, "*") != 1 || !strings.HasPrefix(domain, "*.") {
			return false
		}
		// 验证去掉通配符后的域名
		domain = domain[2:]
	}
	// 验证域名各部分
	_, err := net.LookupHost(domain)
	if err == nil {
		return true
	}
	// 确保域名符合基本格式要求
	for _, part := range strings.Split(domain, ".") {
		if len(part) == 0 || len(part) > 63 {
			return false
		}
		if part[0] == '-' || part[len(part)-1] == '-' {
			return false
		}
		for _, r := range part {
			if !unicode.IsLetter(r) && !unicode.IsDigit(r) && r != '-' {
				return false
			}
		}
	}
	return true
}
