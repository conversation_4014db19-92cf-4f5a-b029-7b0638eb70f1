package traces

import (
	"context"
	"fmt"

	"github.com/opentracing/opentracing-go"
	"github.com/uber/jaeger-client-go"
	"github.com/uber/jaeger-client-go/config"
	"io"
	"time"
)

// GetJaegerTracer 创建链路追踪
func GetJaegerTracer(serviceName, addr string) (opentracing.Tracer, io.Closer, error) {
	cfg := &config.Configuration{
		ServiceName: serviceName,
		Sampler: &config.SamplerConfig{
			Type:  jaeger.SamplerTypeConst,
			Param: 1,
		},
		Reporter: &config.ReporterConfig{
			BufferFlushInterval: 1 * time.Second,
			LogSpans:            true,
			LocalAgentHostPort:  addr,
		},
	}
	return cfg.NewTracer()
}

func GetTraceId(ctx context.Context) (string, uint64) {
	span := opentracing.SpanFromContext(ctx)
	if span == nil {
		return "", 0
	}
	if sc, ok := span.Context().(jaeger.SpanContext); ok {
		return fmt.Sprintf("%v", sc.TraceID()), uint64(sc.SpanID())
	}
	return "", 0
}
