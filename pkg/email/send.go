package email

import (
	"crypto/tls"
	"micro-service/pkg/cfg"
	"net/smtp"

	emaillib "github.com/jordan-wright/email"
)

//func SendWithPool(subject, msg string, to []string) error {
//	c := cfg.LoadCommon().Email
//	return email.GetInstance().Send(&emaillib.Email{
//		ReplyTo:     nil,
//		From:        c.Username,
//		To:          to,
//		Bcc:         nil,
//		Cc:          nil,
//		Subject:     subject,
//		Text:        []byte(msg),
//		HTML:        nil,
//		Sender:      "",
//		Headers:     nil,
//		Attachments: nil,
//		ReadReceipt: nil,
//	}, 15*time.Second)
//}

func Send(subject, msg string, to []string) error {
	c := cfg.LoadCommon().Email
	e := &emaillib.Email{
		ReplyTo:     nil,
		From:        c.Username,
		To:          to,
		Bcc:         nil,
		Cc:          nil,
		Subject:     subject,
		Text:        []byte(msg),
		HTML:        nil,
		Sender:      "",
		Headers:     nil,
		Attachments: nil,
		ReadReceipt: nil,
	}

	auth := smtp.PlainAuth("", c.Username, c.Password, c.SmtpHost)
	err := e.SendWithTLS(c.SmtpHost+":"+c.SmtpPort, auth, &tls.Config{ServerName: c.SmtpHost})
	return err
}
