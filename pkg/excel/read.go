package excel

import (
	"errors"

	"micro-service/pkg/utils"

	"github.com/xuri/excelize/v2"
)

var ErrFileNotFound = errors.New("file not found")

// ReadExcel reads the contents of an Excel file and returns the data as a slice of slices of strings.
func ReadExcel(fileName string, sheetName ...string) ([][]string, error) {
	if !utils.FileIsExist(fileName) {
		return nil, ErrFileNotFound
	}

	f, err := excelize.OpenFile(fileName)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	var readSheet string
	if len(sheetName) > 0 {
		readSheet = sheetName[0]
	} else {
		readSheet = f.GetSheetName(0)
	}

	rows, err := f.GetRows(readSheet)
	return rows, err
}
