package excel

import (
	"encoding/json"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPathCheck(t *testing.T) {
	// 设置测试用例
	tests := []struct {
		name     string
		filePath string
		wantErr  bool
	}{
		{
			name:     "文件路径为空",
			filePath: "",
			wantErr:  true,
		},
		{
			name:     "文件扩展名错误",
			filePath: "test.txt",
			wantErr:  true,
		},
		{
			name:     "目录不存在但创建成功",
			filePath: filepath.Join("test_dir", "test.xlsx"),
			wantErr:  false,
		},
		{
			name:     "目录不存在且创建失败",
			filePath: filepath.Join("unwritable_dir", "test.xlsx"),
			wantErr:  false,
		},
		{
			name:     "目录存在且文件路径合法",
			filePath: "test.xlsx",
			wantErr:  false,
		},
	}

	// 创建一个目录用于测试
	testDir := "test_dir"
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("无法创建测试目录: %v", err)
	}
	defer os.RemoveAll(testDir) // 测试完成后清理目录

	// 创建一个不可写目录用于测试
	unwritableDir := "unwritable_dir"
	if err := os.MkdirAll(unwritableDir, 0444); err != nil { // 创建只读目录
		t.Fatalf("无法创建不可写目录: %v", err)
	}
	defer os.Chmod(unwritableDir, 0755) // 测试完成后恢复目录权限
	defer os.RemoveAll(unwritableDir)   // 测试完成后清理目录

	// 执行每个测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := pathCheck(tt.filePath)

			// 检查错误是否符合预期
			if tt.wantErr {
				assert.NotNil(t, err)
				// 检查错误信息是否符合预期
				switch tt.name {
				case "文件路径为空":
					assert.EqualError(t, err, "文件路径不可为空")
				case "文件扩展名错误":
					assert.EqualError(t, err, "文件类型错误")
				case "目录不存在且创建失败":
					if assert.Error(t, err) {
						assert.Contains(t, err.Error(), "permission denied")
					}
				}
			} else {
				assert.Nil(t, err)
			}
		})
	}
}
func TestWriteExcel(t *testing.T) {
	// 设置测试用例
	tests := []struct {
		name     string
		filePath string
		content  [][]string
		wantErr  bool
	}{
		{
			name:     "正常情况",
			filePath: "test.xlsx",
			content:  [][]string{{"Header1", "Header2"}, {"Value1", "Value2"}},
			wantErr:  false,
		},
		{
			name:     "空内容",
			filePath: "test_empty.xlsx",
			content:  [][]string{},
			wantErr:  false,
		},
		{
			name:     "无效文件路径",
			filePath: "invalid_path.xlsx",
			content:  [][]string{{"Header1", "Header2"}, {"Value1", "Value2"}},
			wantErr:  false,
		},
		{
			name:     "文件路径为空",
			filePath: "",
			content:  [][]string{{"Header1", "Header2"}, {"Value1", "Value2"}},
			wantErr:  true,
		},
	}

	// 执行每个测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 如果是无效文件路径测试，则创建一个不可写目录
			if tt.name == "无效文件路径" {
				dir := filepath.Dir(tt.filePath)
				if err := os.MkdirAll(dir, 0444); err != nil { // 创建只读目录
					t.Fatalf("无法创建测试目录: %v", err)
				}
				defer os.Chmod(dir, 0755) // 测试完成后恢复目录权限
				defer os.RemoveAll(dir)   // 测试完成后清理目录
			}

			err := WriteExcel(tt.filePath, tt.content)

			// 检查错误是否符合预期
			if tt.wantErr {
				assert.NotNil(t, err)
			} else {
				assert.Nil(t, err)
				// 检查文件是否创建成功
				if _, err := os.Stat(tt.filePath); os.IsNotExist(err) {
					t.Errorf("文件 %s 未创建成功", tt.filePath)
				} else {
					// 删除测试文件
					os.Remove(tt.filePath)
				}
			}
		})
	}
}

func TestWriteSheetsByOrder(t *testing.T) {
	// 设置测试用例
	tests := []struct {
		name     string
		filePath string
		data     []*WriteByOrder
		wantErr  bool
	}{
		{
			name:     "正常情况 - 多个工作表",
			filePath: "test_multi_sheets.xlsx",
			data: []*WriteByOrder{
				{
					SheetName: "Sheet1",
					Data:      [][]string{{"Header1", "Header2"}, {"Value1", "Value2"}},
				},
				{
					SheetName: "Sheet2",
					Data:      [][]string{{"HeaderA", "HeaderB"}, {"ValueA", "ValueB"}},
				},
			},
			wantErr: false,
		},
		{
			name:     "正常情况 - 单个工作表",
			filePath: "test_single_sheet.xlsx",
			data: []*WriteByOrder{
				{
					SheetName: "Sheet1",
					Data:      [][]string{{"Header1", "Header2"}, {"Value1", "Value2"}},
				},
			},
			wantErr: false,
		},
		{
			name:     "空数据",
			filePath: "test_empty.xlsx",
			data:     []*WriteByOrder{},
			wantErr:  false,
		},
		{
			name:     "文件路径为空",
			filePath: "",
			data: []*WriteByOrder{
				{
					SheetName: "Sheet1",
					Data:      [][]string{{"Header1", "Header2"}, {"Value1", "Value2"}},
				},
			},
			wantErr: true,
		},
	}

	// 执行每个测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var err error

			// 如果是无效文件路径测试，则创建一个不可写目录
			if tt.name == "无效文件路径" {
				dir := filepath.Dir(tt.filePath)
				// 创建目录并设置为只读
				if err := os.MkdirAll(dir, 0444); err != nil {
					t.Fatalf("无法创建测试目录: %v", err)
				}
				defer func() {
					// 测试完成后恢复目录权限并删除目录
					os.Chmod(dir, 0755)
					os.RemoveAll(dir)
				}()
			}

			err = WriteSheetsByOrder(tt.filePath, tt.data...)

			// 检查错误是否符合预期
			if tt.wantErr {
				if err == nil {
					t.Error("期望返回错误，但实际没有返回错误")
				} else {
					assert.NotNil(t, err)
					// 检查是否是权限错误
					if tt.name == "无效文件路径" {
						if !isPermissionError(err) {
							t.Errorf("期望返回权限错误，但实际返回的错误是: %v", err)
						}
					}
				}
			} else {
				assert.Nil(t, err)
				// 检查文件是否创建成功（除了空数据的情况）
				if len(tt.data) > 0 {
					if _, err := os.Stat(tt.filePath); os.IsNotExist(err) {
						t.Errorf("文件 %s 未创建成功", tt.filePath)
					} else {
						// 删除测试文件
						os.Remove(tt.filePath)
					}
				}
			}
		})
	}
}

// isPermissionError 检查错误是否是权限错误
func isPermissionError(err error) bool {
	return err != nil && err.Error() == "permission denied"
}

func Test_excelWriter(t *testing.T) {
	tmp1 := [][]string{
		{"1", "write to excel"},
		{"21", "ont-two-one"},
	}
	tmp2 := [][]string{
		{"2"},
	}
	sheet := map[string][][]string{
		"在线资产": tmp1,
		"离线资产": tmp2,
	}
	err := WriteMultiSheet("./test.xlsx", sheet)
	assert.Nil(t, err)
	if err == nil {
		os.Remove("./test.xlsx")
	}
}

type TestStruct struct {
	Name  string `json:"name"`
	Value int    `json:"value"`
}

// TestLineDecode 测试 LineDecode 函数
func TestLineDecode(t *testing.T) {
	// 设置测试用例
	tests := []struct {
		name     string
		line     []string
		columns  []string
		expected TestStruct
		wantErr  bool
	}{
		{
			name:    "正常情况",
			line:    []string{"Alice", "30"},
			columns: []string{"Name", "Value"},
			expected: TestStruct{
				Name:  "Alice",
				Value: 30,
			},
			wantErr: true,
		},
		{
			name:    "空 line 或 columns",
			line:    []string{},
			columns: []string{},
			expected: TestStruct{
				Name:  "",
				Value: 0,
			},
			wantErr: false,
		},

		{
			name:    "反序列化失败",
			line:    []string{"Alice", "abc"},
			columns: []string{"Name", "Value"},
			expected: TestStruct{
				Name:  "",
				Value: 0,
			},
			wantErr: true,
		},
	}

	// 执行每个测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			item := TestStruct{}
			err := LineDecode(tt.line, tt.columns, &item)

			// 检查错误是否符合预期
			if tt.wantErr {
				assert.NotNil(t, err)
			} else {
				assert.Nil(t, err)
				// 比较结果
				bsExpected, _ := json.Marshal(tt.expected)
				bsActual, _ := json.Marshal(item)
				assert.JSONEq(t, string(bsExpected), string(bsActual))
			}
		})
	}
}
