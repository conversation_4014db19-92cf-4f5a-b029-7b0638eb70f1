package icp

import (
	"context"
	"fmt"
	core "micro-service/coreService/proto"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"net/http"
	"strconv"
	"time"
)

// CompanyClueResponse 企业线索响应
type CompanyClueResponse struct {
	Code    int           `json:"code"`
	Msg     string        `json:"msg"`
	Data    interface{}   `json:"data"`
	TaskID  string        `json:"task_id"`
	Process int           `json:"process"`
	Items   []interface{} `json:"items"`
}

// ExpandCompanyClue 企业线索扩展
func ExpandCompanyClue(ctx context.Context, company string, force bool, userId uint64) (*CompanyClueResponse, error) {
	if company == "" {
		return nil, nil
	}

	log.Info("ExpandCompanyClue", "开始企业线索扩展", map[string]interface{}{
		"company": company,
		"force":   force,
		"user_id": userId,
	})

	var result *CompanyClueResponse
	var err error

	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		url := "/api/v1/digital/company/clue"
		result = &CompanyClueResponse{}
		reqBody := map[string]interface{}{
			"keyword": company,
			"force":   force,
		}
		err = core.HttpClient(http.MethodPost, url, reqBody, result)
	} else {
		// 否则rpc微服务调用
		param := &core.ExpandKeywordRequest{
			Keyword: company,
			Force:   force,
		}

		// 重试3次
		var rsp *core.ExpandClueResponse
		for i := 0; i < 3; i++ {
			rsp, err = core.GetProtoCoreClient().ExpandCompanyName(ctx, param, SetRpcTimeoutOpt(45))
			if err == nil {
				break
			}
			log.Warn("ExpandCompanyClue", "重试", map[string]interface{}{
				"retry": i + 1,
				"error": err,
			})
			time.Sleep(time.Second)
		}

		if err != nil {
			return nil, err
		}

		if rsp == nil {
			return nil, fmt.Errorf("expand company clue response is nil")
		}

		result = &CompanyClueResponse{
			Code:    0,
			Msg:     "success",
			Data:    rsp,
			TaskID:  strconv.FormatUint(rsp.TaskId, 10),
			Process: 0,
			Items:   make([]interface{}, 0),
		}
	}

	if err != nil {
		log.Error("ExpandCompanyClue", "查询失败", err, map[string]interface{}{
			"company": company,
		})
		return nil, err
	}

	return result, nil
}

// GetExpandClueResult 获取企业线索扩展结果
func GetExpandClueResult(ctx context.Context, taskId string) (*CompanyClueResponse, error) {
	if taskId == "" {
		return nil, nil
	}

	log.Info("GetExpandClueResult", "获取企业线索扩展结果", map[string]interface{}{
		"task_id": taskId,
	})

	var result *CompanyClueResponse
	var err error

	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		url := "/api/v1/digital/company/clue/result"
		result = &CompanyClueResponse{}
		reqBody := map[string]interface{}{
			"task_id": taskId,
		}
		err = core.HttpClient(http.MethodGet, url, reqBody, result)
	} else {
		// 否则rpc微服务调用
		taskIdUint, err := strconv.ParseUint(taskId, 10, 64)
		if err != nil {
			return nil, err
		}

		param := &core.ExpandResultRequest{
			TaskId: taskIdUint,
		}
		rsp, err := core.GetProtoCoreClient().GetExpandResult(ctx, param, SetRpcTimeoutOpt(30))
		if err != nil {
			return nil, err
		}

		if rsp == nil {
			return nil, fmt.Errorf("get expand clue result response is nil")
		}

		// 转换Items类型
		items := make([]interface{}, len(rsp.Items))
		for i, item := range rsp.Items {
			items[i] = item
		}

		result = &CompanyClueResponse{
			Code:    0,
			Msg:     "success",
			Data:    rsp,
			TaskID:  taskId,
			Process: int(rsp.Process),
			Items:   items,
		}
	}

	if err != nil {
		log.Error("GetExpandClueResult", "查询失败", err, map[string]interface{}{
			"task_id": taskId,
		})
		return nil, err
	}

	return result, nil
}

// WaitForExpandClueResult 等待企业线索扩展结果
func WaitForExpandClueResult(ctx context.Context, taskId string, timeout time.Duration, interval time.Duration) (*CompanyClueResponse, error) {
	if taskId == "" {
		return nil, nil
	}

	endTime := time.Now().Add(timeout)
	for time.Now().Before(endTime) {
		result, err := GetExpandClueResult(ctx, taskId)
		if err != nil {
			return nil, err
		}

		if result.Process == 100 {
			return result, nil
		}

		time.Sleep(interval)
	}

	return nil, nil
}

// ExpandByIcp 通过ICP扩展线索
func ExpandByIcp(ctx context.Context, keyword string, force bool, userId uint64) (*CompanyClueResponse, error) {
	if keyword == "" {
		return nil, nil
	}

	log.Info("ExpandByIcp", "开始ICP扩展线索", map[string]interface{}{
		"keyword": keyword,
		"force":   force,
		"user_id": userId,
	})

	var result *CompanyClueResponse
	var err error

	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		url := "/api/v1/digital/icp/clue"
		result = &CompanyClueResponse{}
		reqBody := map[string]interface{}{
			"keyword": keyword,
			"force":   force,
		}
		err = core.HttpClient(http.MethodPost, url, reqBody, result)
	} else {
		// 否则rpc微服务调用
		param := &core.ExpandKeywordRequest{
			Keyword: keyword,
			Force:   force,
		}

		rsp, err := core.GetProtoCoreClient().ExpandIcp(ctx, param, SetRpcTimeoutOpt(30))
		if err != nil {
			return nil, err
		}

		if rsp == nil {
			return nil, fmt.Errorf("expand icp response is nil")
		}

		result = &CompanyClueResponse{
			Code:    0,
			Msg:     "success",
			Data:    rsp,
			TaskID:  strconv.FormatUint(rsp.TaskId, 10),
			Process: 0,
			Items:   make([]interface{}, 0),
		}
	}

	if err != nil {
		log.Error("ExpandByIcp", "查询失败", err, map[string]interface{}{
			"keyword": keyword,
		})
		return nil, err
	}

	return result, nil
}

// SearchDbClueByCompanyName 通过企业名称获取数据库线索
func SearchDbClueByCompanyName(ctx context.Context, keyword string, userId uint64) (*CompanyClueResponse, error) {
	if keyword == "" {
		return nil, nil
	}

	log.Info("SearchDbClueByCompanyName", "开始搜索数据库线索", map[string]interface{}{
		"keyword": keyword,
		"user_id": userId,
	})

	var result *CompanyClueResponse
	var err error

	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		url := "/api/v1/digital/company/clue/search"
		result = &CompanyClueResponse{}
		reqBody := map[string]interface{}{
			"keyword": keyword,
		}
		err = core.HttpClient(http.MethodPost, url, reqBody, result)
	} else {
		// 否则rpc微服务调用
		param := &core.ExpandKeywordSearchRequest{
			CompanyName: []string{keyword},
		}

		rsp, err := core.GetProtoCoreClient().SearchByCompanyName(ctx, param, SetRpcTimeoutOpt(30))
		if err != nil {
			return nil, err
		}

		if rsp == nil {
			return nil, fmt.Errorf("search by company name response is nil")
		}

		items := make([]interface{}, len(rsp.Items))
		for i, item := range rsp.Items {
			items[i] = item
		}

		result = &CompanyClueResponse{
			Code:    0,
			Msg:     "success",
			Data:    rsp,
			Process: int(rsp.Process),
			Items:   items,
		}
	}

	if err != nil {
		log.Error("SearchDbClueByCompanyName", "查询失败", err, map[string]interface{}{
			"keyword": keyword,
		})
		return nil, err
	}

	return result, nil
}

// ExpandByIp 通过IP扩展线索
func ExpandByIp(ctx context.Context, keyword string, force bool, userId uint64) (*CompanyClueResponse, error) {
	if keyword == "" {
		return nil, nil
	}

	log.Info("ExpandByIp", "开始IP扩展线索", map[string]interface{}{
		"keyword": keyword,
		"force":   force,
		"user_id": userId,
	})

	var result *CompanyClueResponse
	var err error

	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		url := "/api/v1/digital/ip/clue"
		result = &CompanyClueResponse{}
		reqBody := map[string]interface{}{
			"keyword": keyword,
			"force":   force,
		}
		err = core.HttpClient(http.MethodPost, url, reqBody, result)
	} else {
		// 否则rpc微服务调用
		param := &core.ExpandKeywordRequest{
			Keyword: keyword,
			Force:   force,
		}

		rsp, err := core.GetProtoCoreClient().ExpandIp(ctx, param, SetRpcTimeoutOpt(30))
		if err != nil {
			return nil, err
		}

		if rsp == nil {
			return nil, fmt.Errorf("expand ip response is nil")
		}

		result = &CompanyClueResponse{
			Code:    0,
			Msg:     "success",
			Data:    rsp,
			TaskID:  strconv.FormatUint(rsp.TaskId, 10),
			Process: 0,
			Items:   make([]interface{}, 0),
		}
	}

	if err != nil {
		log.Error("ExpandByIp", "查询失败", err, map[string]interface{}{
			"keyword": keyword,
		})
		return nil, err
	}

	return result, nil
}

// ExpandByDomain 通过根域扩展线索
func ExpandByDomain(ctx context.Context, keyword string, force bool, userId uint64) (*CompanyClueResponse, error) {
	if keyword == "" {
		return nil, nil
	}

	log.Info("ExpandByDomain", "开始根域扩展线索", map[string]interface{}{
		"keyword": keyword,
		"force":   force,
		"user_id": userId,
	})

	var result *CompanyClueResponse
	var err error

	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		url := "/api/v1/digital/domain/clue"
		result = &CompanyClueResponse{}
		reqBody := map[string]interface{}{
			"keyword": keyword,
			"force":   force,
		}
		err = core.HttpClient(http.MethodPost, url, reqBody, result)
	} else {
		// 否则rpc微服务调用
		param := &core.ExpandKeywordRequest{
			Keyword: keyword,
			Force:   force,
		}

		rsp, err := core.GetProtoCoreClient().ExpandDomain(ctx, param, SetRpcTimeoutOpt(30))
		if err != nil {
			return nil, err
		}

		if rsp == nil {
			return nil, fmt.Errorf("expand domain response is nil")
		}

		result = &CompanyClueResponse{
			Code:    0,
			Msg:     "success",
			Data:    rsp,
			TaskID:  strconv.FormatUint(rsp.TaskId, 10),
			Process: 0,
			Items:   make([]interface{}, 0),
		}
	}

	if err != nil {
		log.Error("ExpandByDomain", "查询失败", err, map[string]interface{}{
			"keyword": keyword,
		})
		return nil, err
	}

	return result, nil
}

// ExpandBySubDomain 通过子域扩展线索
func ExpandBySubDomain(ctx context.Context, keyword string, force bool, userId uint64) (*CompanyClueResponse, error) {
	if keyword == "" {
		return nil, nil
	}

	log.Info("ExpandBySubDomain", "开始子域扩展线索", map[string]interface{}{
		"keyword": keyword,
		"force":   force,
		"user_id": userId,
	})

	var result *CompanyClueResponse
	var err error

	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		url := "/api/v1/digital/subdomain/clue"
		result = &CompanyClueResponse{}
		reqBody := map[string]interface{}{
			"keyword": keyword,
			"force":   force,
		}
		err = core.HttpClient(http.MethodPost, url, reqBody, result)
	} else {
		// 否则rpc微服务调用
		param := &core.ExpandKeywordRequest{
			Keyword: keyword,
			Force:   force,
		}

		rsp, err := core.GetProtoCoreClient().ExpandSubDomain(ctx, param, SetRpcTimeoutOpt(30))
		if err != nil {
			return nil, err
		}

		if rsp == nil {
			return nil, fmt.Errorf("expand subdomain response is nil")
		}

		result = &CompanyClueResponse{
			Code:    0,
			Msg:     "success",
			Data:    rsp,
			TaskID:  strconv.FormatUint(rsp.TaskId, 10),
			Process: 0,
			Items:   make([]interface{}, 0),
		}
	}

	if err != nil {
		log.Error("ExpandBySubDomain", "查询失败", err, map[string]interface{}{
			"keyword": keyword,
		})
		return nil, err
	}

	return result, nil
}

// ExpandByCert 通过证书扩展线索
func ExpandByCert(ctx context.Context, keyword string, force bool, userId uint64) (*CompanyClueResponse, error) {
	if keyword == "" {
		return nil, nil
	}

	log.Info("ExpandByCert", "开始证书扩展线索", map[string]interface{}{
		"keyword": keyword,
		"force":   force,
		"user_id": userId,
	})

	var result *CompanyClueResponse
	var err error

	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		url := "/api/v1/digital/cert/clue"
		result = &CompanyClueResponse{}
		reqBody := map[string]interface{}{
			"keyword": keyword,
			"force":   force,
		}
		err = core.HttpClient(http.MethodPost, url, reqBody, result)
	} else {
		// 否则rpc微服务调用
		param := &core.ExpandKeywordRequest{
			Keyword: keyword,
			Force:   force,
		}

		rsp, err := core.GetProtoCoreClient().ExpandCert(ctx, param, SetRpcTimeoutOpt(30))
		if err != nil {
			return nil, err
		}

		if rsp == nil {
			return nil, fmt.Errorf("expand cert response is nil")
		}

		result = &CompanyClueResponse{
			Code:    0,
			Msg:     "success",
			Data:    rsp,
			TaskID:  strconv.FormatUint(rsp.TaskId, 10),
			Process: 0,
			Items:   make([]interface{}, 0),
		}
	}

	if err != nil {
		log.Error("ExpandByCert", "查询失败", err, map[string]interface{}{
			"keyword": keyword,
		})
		return nil, err
	}

	return result, nil
}

// ExpandByIconWithHash 通过Icon扩展线索（支持传递Hash和CompanyName）
func ExpandByIconWithHash(ctx context.Context, iconUrl string, hash int64, companyName string, force bool, userId uint64) (*CompanyClueResponse, error) {
	if iconUrl == "" {
		return nil, nil
	}

	log.Info("ExpandByIconWithHash", "开始Icon扩展线索", map[string]interface{}{
		"icon_url":     iconUrl,
		"hash":         hash,
		"company_name": companyName,
		"force":        force,
		"user_id":      userId,
	})

	var result *CompanyClueResponse
	var err error

	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		url := "/api/v1/digital/icon/clue"
		result = &CompanyClueResponse{}
		reqBody := map[string]interface{}{
			"icon_url":     iconUrl,
			"hash":         hash,
			"company_name": companyName,
			"force":        force,
		}
		err = core.HttpClient(http.MethodPost, url, reqBody, result)
	} else {
		// 否则rpc微服务调用
		param := &core.ExpandIconRequest{
			Keyword:     iconUrl,
			Hash:        hash,
			CompanyName: companyName,
			Force:       force,
		}

		rsp, err := core.GetProtoCoreClient().ExpandIcon(ctx, param, SetRpcTimeoutOpt(30))
		if err != nil {
			return nil, err
		}

		if rsp == nil {
			return nil, fmt.Errorf("expand icon response is nil")
		}

		result = &CompanyClueResponse{
			Code:    0,
			Msg:     "success",
			Data:    rsp,
			TaskID:  strconv.FormatUint(rsp.TaskId, 10),
			Process: 0,
			Items:   make([]interface{}, 0),
		}
	}

	if err != nil {
		log.Error("ExpandByIconWithHash", "查询失败", err, map[string]interface{}{
			"icon_url":     iconUrl,
			"hash":         hash,
			"company_name": companyName,
		})
		return nil, err
	}

	return result, nil
}

// ExpandByKeyword 通过关键字扩展线索
func ExpandByKeyword(ctx context.Context, keyword string, force bool, userId uint64) (*CompanyClueResponse, error) {
	if keyword == "" {
		return nil, nil
	}

	log.Info("ExpandByKeyword", "开始关键字扩展线索", map[string]interface{}{
		"keyword": keyword,
		"force":   force,
		"user_id": userId,
	})

	var result *CompanyClueResponse
	var err error

	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		url := "/api/v1/digital/keyword/clue"
		result = &CompanyClueResponse{}
		reqBody := map[string]interface{}{
			"keyword": keyword,
			"force":   force,
		}
		err = core.HttpClient(http.MethodPost, url, reqBody, result)
	} else {
		// 否则rpc微服务调用
		param := &core.ExpandKeywordRequest{
			Keyword: keyword,
			Force:   force,
		}

		rsp, err := core.GetProtoCoreClient().ExpandKeyword(ctx, param, SetRpcTimeoutOpt(30))
		if err != nil {
			return nil, err
		}

		if rsp == nil {
			return nil, fmt.Errorf("expand keyword response is nil")
		}

		result = &CompanyClueResponse{
			Code:    0,
			Msg:     "success",
			Data:    rsp,
			TaskID:  strconv.FormatUint(rsp.TaskId, 10),
			Process: 0,
			Items:   make([]interface{}, 0),
		}
	}

	if err != nil {
		log.Error("ExpandByKeyword", "查询失败", err, map[string]interface{}{
			"keyword": keyword,
		})
		return nil, err
	}

	return result, nil
}
