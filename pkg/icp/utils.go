package icp

import (
	"strconv"
	"strings"
)

// RemoveIcpNumber 移除ICP后面编号
func RemoveIcpNumber(icp string) string {
	if icp == "" {
		return icp
	}
	icpArr := strings.Split(icp, "-")
	if len(icpArr) != 0 {
		sn := icpArr[len(icpArr)-1]
		if snInt, err := strconv.Atoi(sn); err != nil {
			return icp
		} else {
			if snInt < 9999 {
				icpArr = icpArr[:len(icpArr)-1]
			}
		}
	}
	return strings.Join(icpArr, "-")
}

// GetTopDomain 获取顶级域名
func GetTopDomain(domain string) string {
	if domain == "" {
		return domain
	}
	parts := strings.Split(domain, ".")
	if len(parts) < 2 {
		return domain
	}
	return strings.Join(parts[len(parts)-2:], ".")
}
