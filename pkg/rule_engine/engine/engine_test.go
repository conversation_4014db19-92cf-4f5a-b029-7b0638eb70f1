package engine

//func Test_checkRule(t *testing.T) {
//	cfg.InitLoadCfg()
//	log.Init()
//	mysql.GetInstance(cfg.LoadMysql())
//
//	InitEnginePool()
//
//	const rule = `rule "test rule for engine2"
//	begin
//
//	print("124")
//
//	end
//	`
//	err := UpdateRule(rule)
//	assert.Nil(t, err)
//}
//
//func Test_EnginePool(t *testing.T) {
//	InitEnginePool()
//
//	myEng := GetEngineInstance()
//	assert.NotNil(t, myEng)
//}

//func Test_EnginePool_Delete(t *testing.T) {
//	InitEnginePool()
//
//	fmt.Println("current rules number:", GetEngineInstance().GetRulesNumber())
//
//	err := DeleteByRules("test rule for engine")
//	assert.Nil(t, err)
//
//	fmt.Println("current rules number:", GetEngineInstance().GetRulesNumber())
//}
//
//func Test_EnginePool_Update(t *testing.T) {
//	InitEnginePool()
//
//	err := UpdateRule(`rule "12" begin end`)
//	assert.Nil(t, err)
//}

//type Response struct {
//	At  string
//	Len int
//}

//func Test_Engine(t *testing.T) {
//	InitEnginePool()
//
//	rule := `rule "test_for_str"
//	begin
//
//		println("split:", split("1,2,3",","))
//		println("contains:", contains("12", "13"))
//
//		s = "abc"
//		println(strlen(s) == 3)
//		if startsWith("login", "log") {
//			println("match")
//		}
//		return true
//	end`
//
//	err := UpdateRule(rule)
//	assert.Nil(t, err)
//
//	resp := Response{}
//	data := map[string]any{
//		"resp": &resp,
//	}
//
//	_, err = ExecuteByRuleNames(data, "test_for_str")
//	assert.Nil(t, err)
//	fmt.Printf("%+v\n", resp)
//}
//
//func Test_assert(t *testing.T) {
//	var a any
//	a = true
//
//	if a.(bool) {
//		fmt.Println("a is true")
//	}
//
//	a = "string"
//	fmt.Println(cast.ToBool(a))
//}
