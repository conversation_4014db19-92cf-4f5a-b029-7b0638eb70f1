package engine

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_StrContainsOr(t *testing.T) {
	cases := []struct {
		s        string
		exp      bool
		keywords []string
	}{
		{s: "1232345", keywords: []string{"123", "45"}, exp: true},
		{s: "1232345", keywords: []string{"56", "35"}, exp: false},
		{s: "1232345", keywords: []string{"123", "24"}, exp: true},
	}

	for _, v := range cases {
		got := StrContainsOr(v.s, v.keywords...)
		assert.Equal(t, v.exp, got)
	}
}

func Test_StrContainsAnd(t *testing.T) {
	cases := []struct {
		s        string
		exp      bool
		keywords []string
	}{
		{s: "1232345", keywords: []string{"123", "45"}, exp: true},
		{s: "1232345", keywords: []string{"56", "34"}, exp: false},
		{s: "1232345", keywords: []string{"135", "24"}, exp: false},
	}

	for _, v := range cases {
		got := StrContainsAnd(v.s, v.keywords...)
		assert.Equal(t, v.exp, got)
	}
}

func Test_StrEqualsOr(t *testing.T) {
	cases := []struct {
		s        string
		exp      bool
		keywords []string
	}{
		{s: "123", keywords: []string{"123", "45"}, exp: true},
		{s: "123", keywords: []string{"56123", "34"}, exp: false},
		{s: "1232345", keywords: []string{"135", "24"}, exp: false},
	}

	for _, v := range cases {
		got := StrEqualsOr(v.s, v.keywords...)
		assert.Equal(t, v.exp, got)
	}
}

func Test_StrEquals(t *testing.T) {
	cases := []struct {
		s        string
		exp      bool
		keywords string
	}{
		{s: "1232345", keywords: "1232345", exp: true},
		{s: "1232345", keywords: "34", exp: false},
		{s: "1232345", keywords: "", exp: false},
	}

	for _, v := range cases {
		got := StrEquals(v.s, v.keywords)
		assert.Equal(t, v.exp, got)
	}
}

func TestStrNotContainsAnd(t *testing.T) {
	tests := []struct {
		name     string
		s        string
		keywords []string
		expected bool
	}{
		{
			name:     "字符串不包含任何关键词",
			s:        "hello world",
			keywords: []string{"foo", "bar"},
			expected: true,
		},
		{
			name:     "字符串包含一个关键词",
			s:        "hello world",
			keywords: []string{"hello", "bar"},
			expected: false,
		},
		{
			name:     "字符串包含多个关键词中的一个",
			s:        "hello world",
			keywords: []string{"hello", "world"},
			expected: false,
		},
		{
			name:     "字符串包含多个关键词",
			s:        "hello world",
			keywords: []string{"hello", "world", "foo"},
			expected: false,
		},
		{
			name:     "空字符串",
			s:        "",
			keywords: []string{"foo", "bar"},
			expected: true,
		},
		{
			name:     "空关键词列表",
			s:        "hello world",
			keywords: []string{},
			expected: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result := StrNotContainsAnd(tc.s, tc.keywords...)
			if result != tc.expected {
				t.Errorf("期望结果为 %v，实际为 %v", tc.expected, result)
			}
		})
	}
}
