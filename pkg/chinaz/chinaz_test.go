package chinaz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/joho/godotenv"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
)

const testWhoIsToken = "apiuser_quantity_e322d1b527154346cfd6e99c39284c74_617118b4da01429696e3077ba79425d4"

func Init() {
	godotenv.Load("./../../../.env")

	cfg.InitLoadCfg()
	_ = es.GetInstance(cfg.LoadElastic())
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	log.Init()
}

//func TestClient_ICP(t *testing.T) {
//	Init()
//	d, err := NewDefaultClient(testWhoIsToken).
//		Domain(context.Background(), &ICPRequest{Domain: "baidu.com"})
//	assert.Nil(t, err)
//	S, _ := json.Marshal(d)
//	fmt.Println(string(S))
//}

func TestClient_CompanyName(t *testing.T) {
	Init()
	d, e := NewDefaultClient("apiuser_quantity_1a0cfcd348cdd4fed482782a588e3204_416a1072967c482b808de9f2b5735c2b").CompanyName(context.Background(), &CompanyNameRequest{CompanyName: "京ICP备18024709号"})
	if e != nil {
		fmt.Println(e)
		return
	}
	S, _ := json.Marshal(d)
	fmt.Println(string(S))
}

func TestClient_LishiCompanyName(t *testing.T) {
	Init()
	d, e := NewDefaultClient("apiuser_quantity_869aa7fe89d171aaf04531bdcdffb9ed_168e55f804b14edb888b1a37d65d1460").
		Sponsorsunit(context.Background(), "泰安日报社")
	if e != nil {
		fmt.Println(e)
		return
	}
	S, _ := json.Marshal(d)
	fmt.Println(string(S))
}

func TestClient_LishiDomain(t *testing.T) {
	_ = godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	d, e := NewDefaultClient("apiuser_quantity_1b3ce90389f3e35a9277a743bf7b56de_204584ddce84409b860d7dc3d81159c2").
		LiShiDomain(context.Background(), "chinaz.com")
	if e != nil {
		fmt.Println(e)
		return
	}
	S, _ := json.Marshal(d)
	fmt.Println(string(S))
}
