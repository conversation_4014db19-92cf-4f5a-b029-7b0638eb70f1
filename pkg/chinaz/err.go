package chinaz

import (
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strings"
)

var (
	ErrBodyRead = errors.New("could not read error response")
)

type ErrorHandler func(*http.Response) error

func getErrorFromResponse(r *http.Response) error {
	errorResponse := new(struct {
		Error string `json:"error"`
	})
	message, err := io.ReadAll(r.Body)
	if err == nil {
		if err := json.Unmarshal(message, errorResponse); err == nil {
			return errors.New(errorResponse.Error)
		}
		return errors.New(strings.TrimSpace(string(message)))
	}
	return ErrBodyRead
}
