package chinaz

import (
	"context"
	"micro-service/pkg/log"
	"time"
)

const sponsorsunitPath = "/v1/1001/sponsorsunit"

// 企业单位备案查询
type SponsorsunitRequest struct {
	CompanyName string `url:"companyname"`
}

type SponsorsunitResponse struct {
	StateCode int                  `json:"StateCode"`
	Reason    string               `json:"Reason"`
	Result    []SponsorsunitResult `json:"Result"`
}

type SponsorsunitResult struct {
	Owner       string `json:"Owner"`
	CompanyType string `json:"CompanyType"`
	SiteLicense string `json:"SiteLicense"`
	SiteName    string `json:"SiteName"`
	MainPage    string `json:"MainPage"`
	VerifyTime  string `json:"VerifyTime"`
	Domain      string `json:"Domain"`
}

// 历史企业名称备案查询-站长之家
func (c *Client) Sponsorsunit(ctx context.Context, name string) (*SponsorsunitResponse, error) {
	req, err := c.NewRequest("GET", sponsorsunitPath, &SponsorsunitRequest{name}, nil)
	if err != nil {
		log.Warnf("[chinaz]: Api Lishi CompanyName query-创建请求客户端报错-重试一次站长之家历史通过企业名称备案查询接口,company_name :%s, Error:%v", name, err)
		req, err = c.NewRequest("GET", sponsorsunitPath, &SponsorsunitRequest{name}, nil)
		if err != nil {
			return nil, err
		}
	}
	var found SponsorsunitResponse
	if err := c.Do(ctx, req, &found); err != nil {
		// 重试一次
		log.Warnf("[chinaz]: Api Lishi CompanyName query-请求数据报错-重试一次站长之家历史通过企业名称备案查询接口,company_name :%s, Error:%v", name, err)
		time.Sleep(1000 * time.Millisecond)
		if err = c.Do(ctx, req, &found); err != nil {
			return nil, err
		}
	}
	for x := range found.Result {
		found.Result[x].SiteName = name
	}
	log.Info("[chinaz] Api Lishi CompanyName 历史企业名称备案查询-站长之家 query found: %v , name: %s", found, name)
	return &found, nil
}
