package mq_pool

import (
	"context"
	"fmt"
	amqp "github.com/rabbitmq/amqp091-go"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"sync"
)

// ConnectionPool RabbitMQ 连接池
type ConnectionPool struct {
	connections chan *amqp.Connection
	factory     func() (*amqp.Connection, error)
	mu          sync.RWMutex
	closed      bool
	maxSize     int
	currentSize int
}

// MQPool 全局连接池实例
var (
	mqPool *ConnectionPool
	once   sync.Once
)

// GetMQPool 获取全局 MQ 连接池实例
func GetMQPool() *ConnectionPool {
	once.Do(func() {
		mqConfig := cfg.LoadRabbitMq()
		amqpURL := cfg.GetRabbitMqAddr(mqConfig)

		pool, err := NewConnectionPool(amqpURL, mqConfig.ConnectionPool)
		if err != nil {
			log.Errorf("[MQ Pool] 创建连接池失败: %v", err)
			panic(err)
		}
		mqPool = pool
		log.Infof("[MQ Pool] 连接池初始化成功，大小: %d", mqConfig.ConnectionPool)
	})
	return mqPool
}

// NewConnectionPool 创建新的连接池
func NewConnectionPool(amqpURL string, maxSize int) (*ConnectionPool, error) {
	if maxSize <= 0 {
		maxSize = 5 // 默认连接池大小
	}

	pool := &ConnectionPool{
		connections: make(chan *amqp.Connection, maxSize),
		maxSize:     maxSize,
		factory: func() (*amqp.Connection, error) {
			return amqp.Dial(amqpURL)
		},
	}

	// 预创建一些连接
	for i := 0; i < maxSize/2; i++ {
		conn, err := pool.factory()
		if err != nil {
			log.Warnf("[MQ Pool] 预创建连接失败: %v", err)
			continue
		}
		pool.connections <- conn
		pool.currentSize++
	}

	log.Infof("[MQ Pool] 连接池创建成功，预创建连接数: %d/%d", pool.currentSize, maxSize)
	return pool, nil
}

// Get 从连接池获取连接
func (p *ConnectionPool) Get() (*amqp.Connection, error) {
	p.mu.RLock()
	if p.closed {
		p.mu.RUnlock()
		return nil, fmt.Errorf("connection pool is closed")
	}
	p.mu.RUnlock()

	select {
	case conn := <-p.connections:
		// 检查连接是否有效
		if conn.IsClosed() {
			log.Warnf("[MQ Pool] 获取到已关闭的连接，重新创建")
			p.mu.Lock()
			p.currentSize--
			p.mu.Unlock()
			return p.createNewConnection()
		}
		return conn, nil
	default:
		// 连接池为空，创建新连接
		return p.createNewConnection()
	}
}

// Put 将连接放回连接池
func (p *ConnectionPool) Put(conn *amqp.Connection) {
	if conn == nil || conn.IsClosed() {
		return
	}

	p.mu.RLock()
	if p.closed {
		p.mu.RUnlock()
		_ = conn.Close()
		return
	}
	p.mu.RUnlock()

	select {
	case p.connections <- conn:
		// 成功放回连接池
	default:
		// 连接池已满，关闭连接
		_ = conn.Close()
		p.mu.Lock()
		p.currentSize--
		p.mu.Unlock()
	}
}

// createNewConnection 创建新连接
func (p *ConnectionPool) createNewConnection() (*amqp.Connection, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.currentSize >= p.maxSize {
		return nil, fmt.Errorf("connection pool is full, max size: %d", p.maxSize)
	}

	conn, err := p.factory()
	if err != nil {
		return nil, fmt.Errorf("failed to create connection: %w", err)
	}

	p.currentSize++
	log.Debugf("[MQ Pool] 创建新连接，当前连接数: %d/%d", p.currentSize, p.maxSize)
	return conn, nil
}

// Close 关闭连接池
func (p *ConnectionPool) Close() {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		return
	}

	p.closed = true
	close(p.connections)

	// 关闭所有连接
	for conn := range p.connections {
		_ = conn.Close()
	}

	log.Infof("[MQ Pool] 连接池已关闭")
}

// Stats 获取连接池统计信息
func (p *ConnectionPool) Stats() (available, total, max int) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	return len(p.connections), p.currentSize, p.maxSize
}

// ChannelWrapper 通道包装器，用于自动管理连接
type ChannelWrapper struct {
	channel *amqp.Channel
	conn    *amqp.Connection
	pool    *ConnectionPool
}

// GetChannel 从连接池获取通道
func GetChannel() (*ChannelWrapper, error) {
	pool := GetMQPool()
	conn, err := pool.Get()
	if err != nil {
		return nil, fmt.Errorf("failed to get connection: %w", err)
	}

	ch, err := conn.Channel()
	if err != nil {
		pool.Put(conn) // 连接有问题，放回池中让其他地方处理
		return nil, fmt.Errorf("failed to create channel: %w", err)
	}

	return &ChannelWrapper{
		channel: ch,
		conn:    conn,
		pool:    pool,
	}, nil
}

// Channel 获取原始通道
func (cw *ChannelWrapper) Channel() *amqp.Channel {
	return cw.channel
}

// Close 关闭通道并将连接放回池中
func (cw *ChannelWrapper) Close() error {
	if cw.channel != nil {
		err := cw.channel.Close()
		cw.channel = nil

		// 将连接放回池中
		if cw.pool != nil && cw.conn != nil {
			cw.pool.Put(cw.conn)
			cw.conn = nil
		}

		return err
	}
	return nil
}

// PublishWithContext 发布消息的便捷方法
func (cw *ChannelWrapper) PublishWithContext(ctx context.Context, exchange, key string, mandatory, immediate bool, msg amqp.Publishing) error {
	return cw.channel.PublishWithContext(ctx, exchange, key, mandatory, immediate, msg)
}

// QueueDeclare 声明队列的便捷方法
func (cw *ChannelWrapper) QueueDeclare(name string, durable, autoDelete, exclusive, noWait bool, args amqp.Table) (amqp.Queue, error) {
	return cw.channel.QueueDeclare(name, durable, autoDelete, exclusive, noWait, args)
}

// ExchangeDeclare 声明交换机的便捷方法
func (cw *ChannelWrapper) ExchangeDeclare(name, kind string, durable, autoDelete, internal, noWait bool, args amqp.Table) error {
	return cw.channel.ExchangeDeclare(name, kind, durable, autoDelete, internal, noWait, args)
}

// QueueBind 绑定队列的便捷方法
func (cw *ChannelWrapper) QueueBind(name, key, exchange string, noWait bool, args amqp.Table) error {
	return cw.channel.QueueBind(name, key, exchange, noWait, args)
}

// Consume 消费消息的便捷方法
func (cw *ChannelWrapper) Consume(queue, consumer string, autoAck, exclusive, noLocal, noWait bool, args amqp.Table) (<-chan amqp.Delivery, error) {
	return cw.channel.Consume(queue, consumer, autoAck, exclusive, noLocal, noWait, args)
}

// Qos 设置服务质量的便捷方法
func (cw *ChannelWrapper) Qos(prefetchCount, prefetchSize int, global bool) error {
	return cw.channel.Qos(prefetchCount, prefetchSize, global)
}
