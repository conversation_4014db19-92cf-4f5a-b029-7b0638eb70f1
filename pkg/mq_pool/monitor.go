package mq_pool

import (
	"context"
	"micro-service/pkg/log"
	"time"
)

// Monitor MQ 连接池监控器
type Monitor struct {
	pool     *ConnectionPool
	interval time.Duration
	ctx      context.Context
	cancel   context.CancelFunc
}

// NewMonitor 创建新的监控器
func NewMonitor(pool *ConnectionPool, interval time.Duration) *Monitor {
	ctx, cancel := context.WithCancel(context.Background())
	return &Monitor{
		pool:     pool,
		interval: interval,
		ctx:      ctx,
		cancel:   cancel,
	}
}

// Start 开始监控
func (m *Monitor) Start() {
	go m.run()
}

// Stop 停止监控
func (m *Monitor) Stop() {
	if m.cancel != nil {
		m.cancel()
	}
}

// run 运行监控循环
func (m *Monitor) run() {
	ticker := time.NewTicker(m.interval)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			log.Infof("[MQ Monitor] 监控器已停止")
			return
		case <-ticker.C:
			m.logStats()
		}
	}
}

// logStats 记录连接池统计信息
func (m *Monitor) logStats() {
	if m.pool == nil {
		return
	}

	available, total, max := m.pool.Stats()
	log.Infof("[MQ Monitor] 连接池状态 - 可用连接: %d, 总连接数: %d, 最大连接数: %d, 使用率: %.2f%%", 
		available, total, max, float64(total-available)/float64(max)*100)

	// 如果连接使用率过高，发出警告
	if total > max*8/10 { // 使用率超过80%
		log.Warnf("[MQ Monitor] 连接池使用率过高: %.2f%%, 建议增加连接池大小", 
			float64(total)/float64(max)*100)
	}

	// 如果可用连接过少，发出警告
	if available == 0 && total == max {
		log.Warnf("[MQ Monitor] 连接池已满且无可用连接，可能存在连接泄漏")
	}
}

// StartGlobalMonitor 启动全局连接池监控
func StartGlobalMonitor() {
	pool := GetMQPool()
	monitor := NewMonitor(pool, 30*time.Second) // 每30秒监控一次
	monitor.Start()
	log.Infof("[MQ Monitor] 全局连接池监控已启动")
}
