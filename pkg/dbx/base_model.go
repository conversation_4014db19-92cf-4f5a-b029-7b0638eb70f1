package dbx

import (
	"fmt"
	"time"

	"micro-service/pkg/cfg"
	"micro-service/pkg/log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type Model struct {
	Id        uint64    `gorm:"primaryKey;autoIncrement;comment:ID"`
	CreatedAt time.Time `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
}

type ModelFull struct {
	ID        uint64         `gorm:"primarykey;autoIncrement" json:"id"`
	CreatedAt time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index"`
}

// Deprecated:
var db *gorm.DB

// Deprecated: 迁移到 Model
type BaseModel struct {
	*gorm.DB  `gorm:"-" json:"-"`
	Id        uint64    `gorm:"primaryKey;autoIncrement;comment:'ID'" json:"id"`
	CreatedAt time.Time `gorm:"autoCreateTime;comment:'创建时间'" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime;comment:'更新时间'" json:"updated_at"`
}

// Deprecated: 迁移到initialize/mysql，mysql.GetInstance()
func GetClient() *gorm.DB {
	if db != nil {
		return db
	}
	var err error
	myConf := cfg.LoadMysql()
	// Mysql地址链接
	dns := fmt.Sprintf(
		"%s:%s@(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		myConf.UserName, myConf.Password, myConf.Address, myConf.Port, myConf.Database, myConf.CharSet,
	)
	// 链接数据库
	if db, err = gorm.Open(mysql.Open(dns), &gorm.Config{
		// 禁止创建外键
		DisableForeignKeyConstraintWhenMigrating: true,
	}); err != nil {
		panic(err)
	} else {
		log.Infof("Mysql: %s:%d Connection successful", myConf.Address, myConf.Port)
	} // GORM 使用 database/sql 维护连接池
	sqlDB, err := db.DB()
	if err != nil {
		panic(err)
	}
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)
	return db
}
