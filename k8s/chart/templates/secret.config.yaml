kind: Secret
apiVersion: v1
metadata:
  name: {{ .Values.namespace }}-config
  namespace: {{ .Values.namespace }}
  annotations:
    kubesphere.io/creator: admin
stringData:
  config.json: >-
    {
      "consul": {
        "address":"{{ .Values.consul.ip }}",
        "port": {{ .Values.consul.port }},
        "prefix": "{{ .Values.consul.prefix }}",
        "token": "{{ .Values.consul.token }}"
      },
      "logger": {
        "output_console": true,
        "output_file": {{ .Values.logger.output_file }},
        "file_name": "{{ .Values.logger.file_name }}",
        "level": "{{ .Values.logger.level }}",
        "max_size": 512,
        "max_age": 30,
        "local_time": true,
        "compress": true,
        "max_backups": 5
      }
    }
type: Opaque
